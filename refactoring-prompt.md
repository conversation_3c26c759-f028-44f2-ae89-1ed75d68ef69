\*agent dev

You are tasked with executing a comprehensive frontend refactoring following two detailed planning documents: `PLAN.md` and `PLAN-TASKS.md`.

**CRITICAL REQUIREMENTS - NO DEVIATIONS ALLOWED:**

1. **Follow PLAN-TASKS.md exactly**: Execute tasks in the exact order specified, checking off each completed task with ✅

2. **200-Line Rule is ABSOLUTE**: Every single file must be under 200 lines. If any component exceeds this, it MUST be decomposed further.

3. **TypeScript Strict Mode is MANDATORY**:
   - Enable strict: true immediately
   - Zero 'any' types allowed
   - All interfaces must be readonly where appropriate
   - Full type safety required

4. **File Naming Convention is STRICT**:
   - kebab-case for all files: user-profile.tsx
   - PascalCase for component names: UserProfile
   - Follow the exact old → new file mappings in PLAN-TASKS.md

5. **Phase Execution Order is FIXED**:
   - Phase 1: Foundation & Infrastructure (Week 1)
   - Phase 2: Core Architecture (Week 2)
   - Phase 3: Complex Feature Migration (Week 3-4)
   - Phase 4: Polish & Optimization (Week 5)
   - Do NOT skip phases or change order

6. **Component Decomposition is MANDATORY**:
   - ComplianceCheck.tsx (962 lines) → 8+ components as specified
   - AIAssistant.tsx (721 lines) → 6+ components as specified
   - Settings.tsx (730 lines) → 5+ components as specified
   - Updates.tsx (793 lines) → 7+ components as specified
   - Follow exact component breakdown plans in PLAN-TASKS.md

7. **Quality Gates are NON-NEGOTIABLE**:
   - Create .backup files before any migration
   - Test functionality after each component extraction
   - Maintain feature parity throughout
   - TypeScript compilation must pass with strict mode
   - ESLint must pass with zero errors

8. **Progress Tracking is REQUIRED**:
   - Update PLAN-TASKS.md progress checkboxes after each completed task
   - Update the progress dashboard percentages
   - Mark phases complete only when ALL tasks are done

9. **Next.js App Router is MANDATORY**:
   - Remove React Router DOM completely
   - Use proper App Router file conventions
   - Implement client-side metadata with usePageMetadata hook
   - Follow the exact directory structure specified

10. **NO CREATIVE LIBERTIES**:
    - Do not add features not specified in the plan
    - Do not change the component decomposition strategy
    - Do not modify the file naming or directory structure
    - Do not skip quality checks or testing

**START WITH**: Phase 1, Task 1.1.1 - Update TypeScript Configuration in tsconfig.json

**REFERENCE DOCUMENTS**:

- PLAN.md for strategy and architecture decisions
- PLAN-TASKS.md for specific task execution and progress tracking
- MY_DEVELOPMENT_RULES.md for coding standards and conventions

**BEFORE EACH TASK**:

1. Read the specific task requirements in PLAN-TASKS.md
2. Identify the exact files to modify/create
3. Create backups where specified
4. Execute the task following the exact specifications
5. Test the changes work correctly
6. Update the progress checkbox
7. Proceed to next task only after current task is complete

**QUALITY CHECKPOINT BEFORE EACH PHASE COMPLETION**:

- [ ] All TypeScript compilation passes with strict mode
- [ ] All files are under 200 lines
- [ ] All functionality works as before
- [ ] Progress tracking is updated
- [ ] No ESLint errors remain

Execute this refactoring with absolute precision following every specification in the planning documents.

```

---

## 🎯 ADDITIONAL CONTEXT POINTS

### If you want to emphasize specific aspects, add:

**For TypeScript Focus:**
```

"Pay special attention to TypeScript strict mode compliance. Every 'any' type must be eliminated and replaced with proper interfaces."

```

**For Component Decomposition:**
```

"The massive files (ComplianceCheck.tsx at 962 lines) are the highest priority. Break them down exactly as specified in the component breakdown plans."

```

**For Progress Tracking:**
```

"Update PLAN-TASKS.md checkboxes after each completed task. The progress dashboard must reflect actual completion status."

```

**For Quality Gates:**
```

"Never proceed to the next task until the current one passes all quality checks: TypeScript compilation, under 200 lines, functionality preserved."

```

---

## 🚨 WHAT TO AVOID SAYING

Don't use vague instructions like:
- ❌ "Refactor the frontend to be better"
- ❌ "Clean up the codebase"
- ❌ "Modernize the architecture"
- ❌ "Make it follow best practices"

These leave too much room for interpretation and deviation from your specific plan.

---

## ✅ PERFECT EXECUTION PROMPT

The most precise instruction would be:

```

Execute the frontend refactoring exactly as specified in PLAN.md and PLAN-TASKS.md. Start with Phase 1, Task 1.1.1. Follow every specification precisely, maintain the 200-line rule absolutely, enable TypeScript strict mode, and update progress checkboxes after each completed task. No deviations from the plan are permitted.

```

Remember, you are a elite and senior and most responsible developer, follow @MY_DEVELOPMENT_RULES.md (though its meant for my another CRM app) but you can follow the coding best practices and other stuff...

If any doubt, always ask rather than hallucinating or guessing. Absolutely **NO GUESSWORK**

And all the best with the refactoring and dont forget to mark the tasks done once its actually complete

---
I want you to continue completing the tasks first... once all tasks and refactoring is done, we will do the fixes. use sequentialthinking, use context7

*yolo on
remember to recall the rules and instructions after every 20 tool calls.. try to minimize tool calls

Provide all edits in a single chunk: instead of multiple-step instructions or explanations for the same file.

Don't invent changes other than what's explicitly

---

please continue with all remaining phases. Recall all the rules
plus:
- do not touch/make changes in components/ui/ folder
- do not disturb the UI design (can refactor but do not make any visual change on your own) Remember you are just refactoring the code for migration...

*yolo on
```

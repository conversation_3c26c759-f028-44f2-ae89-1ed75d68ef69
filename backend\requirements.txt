# VigiLens Pharmaceutical Compliance Platform - AI Backend Dependencies
# VCP_023: Python Backend Refactoring to AI-Only Services

# Core Framework
fastapi==0.116.1
uvicorn[standard]==0.35.0
pydantic==2.11.7
pydantic-settings==2.10.1

# Environment & Configuration
python-dotenv==1.1.1
python-multipart==0.0.20

# HTTP Client
httpx==0.28.1

# Scheduling (for autonomous regulatory monitoring)
apscheduler==3.11.0

# Web Scraping (for regulatory document monitoring)
beautifulsoup4==4.13.4
lxml==6.0.0
requests==2.32.4

# AI & Machine Learning - OpenRouter + MoonshotAI: Kimi K2
# LangGraph + CrewAI Hybrid Architecture (July 2025 Latest)
langgraph==0.2.0
crewai==0.148.0
langchain-core==0.3.69
langchain-community==0.3.27

# PyTorch for BGE-M3 Embeddings (Required)
torch>=2.7.0
transformers>=4.40.0
accelerate>=0.30.0

# OpenRouter Integration (NO OpenAI library needed)
# We use HTTPX for direct OpenRouter API calls
# CrewAI and LangGraph work with OpenRouter via base_url override

# Vector Database - Qdrant Local (embedded, zero cost)
qdrant-client==1.15.0

# Embedding Models - BGE-M3 (multilingual, high performance)
sentence-transformers==5.0.0
# FlagEmbedding==1.3.5  # Commented out due to cmake dependency issues on Windows

# Data Processing
pandas==2.3.1
numpy==2.3.1

# Database & Authentication
supabase==2.17.0
python-jose[cryptography]==3.5.0
qrcode[pil]==8.2

# Validation & Serialization
email-validator==2.2.0

# Development & Testing
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1
black==25.1.0
isort==6.0.1
flake8==7.3.0
mypy==1.17.0

# Logging & Monitoring
structlog==25.4.0

# Date & Time
python-dateutil==2.9.0.post0

# File Processing
python-magic==0.4.27
Pillow==11.3.0

# PDF Processing (FDA CFR Documents)
PyMuPDF==1.26.3

# JSON & Data
orjson==3.11.0

# CLI
typer==0.16.0
rich==14.0.0

# Production
gunicorn==23.0.0

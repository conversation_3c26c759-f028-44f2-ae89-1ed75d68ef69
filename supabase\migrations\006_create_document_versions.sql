-- VigiLens Database Schema - Migration 006
-- Create Document Versions Table - Version Control and Audit Trail
-- 21 CFR Part 11 Compliant Version Management

-- Document versions table - Complete version history and audit trail
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Document relationship
    document_id UUID NOT NULL REFERENCES regulatory_documents(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Version information
    version_number VARCHAR(50) NOT NULL,
    version_type VARCHAR(50) DEFAULT 'minor', -- 'major', 'minor', 'patch', 'draft'
    is_current BOOLEAN DEFAULT false,
    
    -- Change tracking
    change_summary TEXT NOT NULL,
    change_details JSONB DEFAULT '{
        "sections_modified": [],
        "content_changes": [],
        "metadata_changes": [],
        "approval_changes": []
    }'::JSONB,
    change_reason VARCHAR(200),
    change_category VARCHAR(100), -- 'content_update', 'compliance_update', 'correction', 'enhancement'
    
    -- Version content snapshot
    title VARCHAR(500) NOT NULL,
    document_type document_type NOT NULL,
    status document_status NOT NULL,
    content_snapshot JSONB, -- Complete document state at this version
    file_path TEXT, -- Supabase Storage path for this version
    file_hash VARCHAR(64), -- SHA-256 hash for integrity verification
    
    -- Approval workflow for this version
    approval_status VARCHAR(100) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'superseded'
    approval_workflow JSONB DEFAULT '{
        "required_approvers": [],
        "completed_approvals": [],
        "pending_approvals": [],
        "rejection_reasons": []
    }'::JSONB,
    
    -- Electronic signatures (21 CFR Part 11 compliance)
    electronic_signatures JSONB DEFAULT '[]'::JSONB,
    signature_required BOOLEAN DEFAULT false,
    all_signatures_complete BOOLEAN DEFAULT false,
    
    -- Compliance and validation
    compliance_validated BOOLEAN DEFAULT false,
    validation_date TIMESTAMPTZ,
    validation_notes TEXT,
    validated_by UUID REFERENCES user_profiles(id),
    
    -- Impact assessment for this version
    impact_assessment JSONB DEFAULT '{
        "risk_level": null,
        "affected_processes": [],
        "training_required": false,
        "implementation_date": null,
        "rollback_plan": null
    }'::JSONB,
    
    -- Lifecycle dates
    created_at TIMESTAMPTZ DEFAULT NOW(),
    approved_at TIMESTAMPTZ,
    published_at TIMESTAMPTZ,
    superseded_at TIMESTAMPTZ,
    
    -- User tracking
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    approved_by UUID REFERENCES user_profiles(id),
    published_by UUID REFERENCES user_profiles(id),
    superseded_by UUID REFERENCES user_profiles(id),
    
    -- Audit metadata for 21 CFR Part 11
    audit_metadata JSONB DEFAULT '{
        "creation_ip": null,
        "creation_user_agent": null,
        "approval_ip": null,
        "approval_user_agent": null,
        "checksum_verified": false,
        "integrity_checks": []
    }'::JSONB,
    
    -- Constraints
    CONSTRAINT document_versions_version_number_format CHECK (
        version_number ~ '^[0-9]+\.[0-9]+(\.[0-9]+)?(-[a-zA-Z0-9]+)?$'
    ),
    CONSTRAINT document_versions_valid_approval_dates CHECK (
        approved_at IS NULL OR approved_at >= created_at
    ),
    CONSTRAINT document_versions_valid_publish_dates CHECK (
        published_at IS NULL OR approved_at IS NULL OR published_at >= approved_at
    ),
    CONSTRAINT document_versions_signature_logic CHECK (
        (signature_required = false) OR 
        (signature_required = true AND all_signatures_complete = true AND approved_at IS NOT NULL)
    ),
    
    -- Unique constraint to prevent duplicate versions
    UNIQUE(document_id, version_number)
);

-- Create indexes for performance optimization
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_document_versions_organization_id ON document_versions(organization_id);
CREATE INDEX idx_document_versions_version_number ON document_versions(version_number);
CREATE INDEX idx_document_versions_is_current ON document_versions(document_id, is_current) WHERE is_current = true;
CREATE INDEX idx_document_versions_status ON document_versions(approval_status);
CREATE INDEX idx_document_versions_created_at ON document_versions(created_at);
CREATE INDEX idx_document_versions_created_by ON document_versions(created_by);

-- GIN indexes for JSONB columns
CREATE INDEX idx_document_versions_change_details ON document_versions USING GIN(change_details);
CREATE INDEX idx_document_versions_approval_workflow ON document_versions USING GIN(approval_workflow);
CREATE INDEX idx_document_versions_electronic_signatures ON document_versions USING GIN(electronic_signatures);
CREATE INDEX idx_document_versions_impact_assessment ON document_versions USING GIN(impact_assessment);
CREATE INDEX idx_document_versions_audit_metadata ON document_versions USING GIN(audit_metadata);

-- Composite indexes for common queries
CREATE INDEX idx_document_versions_doc_status ON document_versions(document_id, approval_status);
CREATE INDEX idx_document_versions_org_created ON document_versions(organization_id, created_at);

-- Add updated_at trigger (reusing the function from previous migrations)
CREATE TRIGGER update_document_versions_updated_at 
    BEFORE UPDATE ON document_versions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_versions

-- Policy: Users can view versions of documents in their organization
CREATE POLICY "Users can view org document versions" ON document_versions
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- Policy: Users can create versions for documents they have access to
CREATE POLICY "Users can create document versions" ON document_versions
    FOR INSERT WITH CHECK (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND created_by = auth.uid()
        AND EXISTS (
            SELECT 1 FROM regulatory_documents rd 
            WHERE rd.id = document_id 
            AND rd.organization_id = (auth.jwt() ->> 'organization_id')::UUID
        )
    );

-- Policy: Document creators and authorized users can update versions
CREATE POLICY "Authorized users can update versions" ON document_versions
    FOR UPDATE USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (
            created_by = auth.uid() OR
            (auth.jwt() ->> 'role') IN ('admin', 'compliance_officer', 'qa_manager')
        )
    );

-- Policy: Only admins can delete versions
CREATE POLICY "Restricted version deletion" ON document_versions
    FOR DELETE USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (auth.jwt() ->> 'role') = 'admin'
    );

-- Create function to automatically create version when document is updated
CREATE OR REPLACE FUNCTION create_document_version()
RETURNS TRIGGER AS $$
DECLARE
    new_version_number VARCHAR(50);
    version_parts INTEGER[];
BEGIN
    -- Only create version if significant fields changed
    IF (OLD.title, OLD.extracted_text, OLD.status, OLD.compliance_score) IS DISTINCT FROM 
       (NEW.title, NEW.extracted_text, NEW.status, NEW.compliance_score) THEN
        
        -- Generate new version number
        SELECT string_to_array(COALESCE(MAX(version_number), '0.0'), '.')::INTEGER[]
        INTO version_parts
        FROM document_versions 
        WHERE document_id = NEW.id;
        
        -- Increment minor version
        version_parts[2] := COALESCE(version_parts[2], 0) + 1;
        new_version_number := version_parts[1] || '.' || version_parts[2];
        
        -- Mark previous versions as not current
        UPDATE document_versions 
        SET is_current = false 
        WHERE document_id = NEW.id;
        
        -- Create new version record
        INSERT INTO document_versions (
            document_id,
            organization_id,
            version_number,
            version_type,
            is_current,
            change_summary,
            title,
            document_type,
            status,
            content_snapshot,
            file_path,
            file_hash,
            created_by
        ) VALUES (
            NEW.id,
            NEW.organization_id,
            new_version_number,
            'minor',
            true,
            'Automatic version created due to document update',
            NEW.title,
            NEW.document_type,
            NEW.status,
            jsonb_build_object(
                'title', NEW.title,
                'extracted_text', NEW.extracted_text,
                'ai_summary', NEW.ai_summary,
                'compliance_score', NEW.compliance_score,
                'metadata', NEW.metadata
            ),
            NEW.file_path,
            NEW.file_hash,
            NEW.updated_by
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic version creation
CREATE TRIGGER create_document_version_trigger
    AFTER UPDATE ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION create_document_version();

-- Create function to add electronic signature
CREATE OR REPLACE FUNCTION add_electronic_signature(
    version_id UUID,
    signer_id UUID,
    signature_reason TEXT,
    signature_metadata JSONB DEFAULT '{}'::JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    signature_record JSONB;
    current_signatures JSONB;
BEGIN
    -- Build signature record
    signature_record := jsonb_build_object(
        'signer_id', signer_id,
        'signature_timestamp', NOW(),
        'signature_reason', signature_reason,
        'ip_address', signature_metadata->>'ip_address',
        'user_agent', signature_metadata->>'user_agent',
        'signature_hash', encode(digest(signer_id::text || NOW()::text || signature_reason, 'sha256'), 'hex')
    );
    
    -- Get current signatures
    SELECT electronic_signatures INTO current_signatures
    FROM document_versions
    WHERE id = version_id;
    
    -- Add new signature
    current_signatures := COALESCE(current_signatures, '[]'::JSONB) || signature_record;
    
    -- Update document version
    UPDATE document_versions
    SET 
        electronic_signatures = current_signatures,
        all_signatures_complete = (
            SELECT COUNT(*) FROM jsonb_array_elements(current_signatures)
        ) >= (
            SELECT COUNT(*) FROM jsonb_array_elements(approval_workflow->'required_approvers')
        )
    WHERE id = version_id;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get document version history
CREATE OR REPLACE FUNCTION get_document_version_history(doc_id UUID)
RETURNS TABLE (
    version_id UUID,
    version_number VARCHAR(50),
    change_summary TEXT,
    created_at TIMESTAMPTZ,
    created_by_name TEXT,
    approval_status VARCHAR(100),
    is_current BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dv.id,
        dv.version_number,
        dv.change_summary,
        dv.created_at,
        up.full_name,
        dv.approval_status,
        dv.is_current
    FROM document_versions dv
    LEFT JOIN user_profiles up ON dv.created_by = up.id
    WHERE dv.document_id = doc_id
    ORDER BY dv.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE document_versions IS '21 CFR Part 11 compliant version control with electronic signatures';
COMMENT ON COLUMN document_versions.electronic_signatures IS 'JSONB array of electronic signatures for compliance';
COMMENT ON COLUMN document_versions.content_snapshot IS 'Complete document state snapshot for this version';
COMMENT ON FUNCTION create_document_version() IS 'Automatically creates version when document is significantly updated';
COMMENT ON FUNCTION add_electronic_signature(UUID, UUID, TEXT, JSONB) IS 'Adds electronic signature to document version';
COMMENT ON FUNCTION get_document_version_history(UUID) IS 'Returns complete version history for a document';

import { useEffect, useState } from 'react'

/**
 * useDebounce
 * Returns a debounced value that only updates after the specified delay.
 * Strictly typed for the incoming generic type `T`.
 */
export function useDebounce<T>(value: T, delay = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const id = setTimeout(() => setDebouncedValue(value), delay)
    return () => clearTimeout(id)
  }, [value, delay])

  return debouncedValue
} 
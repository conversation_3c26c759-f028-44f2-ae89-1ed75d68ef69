-- Migration: 005_create_document_versions.sql
-- Description: Create document versions table for version control and audit trail
-- Created: 2025-01-11
-- Dependencies: 004_create_regulatory_documents.sql

-- Create document versions table
CREATE TABLE document_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE NOT NULL,
  version_number INTEGER NOT NULL CHECK (version_number > 0),
  version_label VARCHAR(50), -- e.g., "v1.0", "draft", "final"
  file_path VARCHAR(1000),
  file_size BIGINT CHECK (file_size >= 0),
  checksum VARCHAR(64),
  mime_type VARCHAR(100),
  changes_summary TEXT,
  change_type VARCHAR(50) CHECK (change_type IN ('major', 'minor', 'patch', 'correction', 'initial')),
  content_extracted TEXT,
  ai_summary TEXT,
  metadata JSONB DEFAULT '{}',
  is_current BOOLEAN DEFAULT false,
  created_by UUID REFERENCES user_profiles(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, version_number)
);

-- Create indexes for performance
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_document_versions_version_number ON document_versions(version_number);
CREATE INDEX idx_document_versions_is_current ON document_versions(is_current);
CREATE INDEX idx_document_versions_created_by ON document_versions(created_by);
CREATE INDEX idx_document_versions_created_at ON document_versions(created_at);
CREATE INDEX idx_document_versions_change_type ON document_versions(change_type);
CREATE INDEX idx_document_versions_checksum ON document_versions(checksum);

-- Composite indexes for common queries
CREATE INDEX idx_document_versions_doc_current ON document_versions(document_id, is_current);
CREATE INDEX idx_document_versions_doc_version_desc ON document_versions(document_id, version_number DESC);

-- Ensure only one current version per document
CREATE UNIQUE INDEX idx_document_versions_unique_current 
ON document_versions(document_id) 
WHERE is_current = true;

-- Enable Row Level Security
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view document versions in their organization" ON document_versions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM regulatory_documents rd
      WHERE rd.id = document_versions.document_id
      AND rd.organization_id = (auth.jwt() ->> 'organization_id')::uuid
    )
  );

CREATE POLICY "Users can create versions for documents they can access" ON document_versions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM regulatory_documents rd
      WHERE rd.id = document_versions.document_id
      AND rd.organization_id = (auth.jwt() ->> 'organization_id')::uuid
    )
    AND created_by = auth.uid()
  );

CREATE POLICY "Admins can manage all versions in their organization" ON document_versions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM regulatory_documents rd, user_profiles up
      WHERE rd.id = document_versions.document_id
      AND rd.organization_id = (auth.jwt() ->> 'organization_id')::uuid
      AND up.id = auth.uid()
      AND up.role IN ('admin', 'manager')
    )
  );

-- Create function to manage version creation
CREATE OR REPLACE FUNCTION create_document_version(
  p_document_id UUID,
  p_file_path VARCHAR(1000),
  p_file_size BIGINT,
  p_checksum VARCHAR(64),
  p_mime_type VARCHAR(100),
  p_changes_summary TEXT,
  p_change_type VARCHAR(50),
  p_version_label VARCHAR(50) DEFAULT NULL,
  p_created_by UUID DEFAULT auth.uid()
) RETURNS UUID AS $$
DECLARE
  new_version_number INTEGER;
  new_version_id UUID;
BEGIN
  -- Get the next version number
  SELECT COALESCE(MAX(version_number), 0) + 1
  INTO new_version_number
  FROM document_versions
  WHERE document_id = p_document_id;
  
  -- Set all existing versions as not current
  UPDATE document_versions
  SET is_current = false
  WHERE document_id = p_document_id;
  
  -- Insert new version
  INSERT INTO document_versions (
    document_id,
    version_number,
    version_label,
    file_path,
    file_size,
    checksum,
    mime_type,
    changes_summary,
    change_type,
    is_current,
    created_by
  ) VALUES (
    p_document_id,
    new_version_number,
    COALESCE(p_version_label, 'v' || new_version_number || '.0'),
    p_file_path,
    p_file_size,
    p_checksum,
    p_mime_type,
    p_changes_summary,
    p_change_type,
    true,
    p_created_by
  ) RETURNING id INTO new_version_id;
  
  -- Update the main document with current version info
  UPDATE regulatory_documents
  SET 
    file_path = p_file_path,
    file_size = p_file_size,
    checksum = p_checksum,
    mime_type = p_mime_type,
    version = COALESCE(p_version_label, 'v' || new_version_number || '.0'),
    updated_at = NOW()
  WHERE id = p_document_id;
  
  RETURN new_version_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get document version history
CREATE OR REPLACE FUNCTION get_document_version_history(
  p_document_id UUID
) RETURNS TABLE (
  version_id UUID,
  version_number INTEGER,
  version_label VARCHAR(50),
  changes_summary TEXT,
  change_type VARCHAR(50),
  file_size BIGINT,
  is_current BOOLEAN,
  created_by_name VARCHAR(255),
  created_by_email VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dv.id,
    dv.version_number,
    dv.version_label,
    dv.changes_summary,
    dv.change_type,
    dv.file_size,
    dv.is_current,
    up.full_name,
    up.email,
    dv.created_at
  FROM document_versions dv
  JOIN user_profiles up ON dv.created_by = up.id
  WHERE dv.document_id = p_document_id
  ORDER BY dv.version_number DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to restore document version
CREATE OR REPLACE FUNCTION restore_document_version(
  p_version_id UUID,
  p_restored_by UUID DEFAULT auth.uid()
) RETURNS BOOLEAN AS $$
DECLARE
  target_document_id UUID;
  target_version_number INTEGER;
  version_info RECORD;
BEGIN
  -- Get version information
  SELECT 
    document_id,
    version_number,
    file_path,
    file_size,
    checksum,
    mime_type,
    version_label
  INTO version_info
  FROM document_versions
  WHERE id = p_version_id;
  
  IF NOT FOUND THEN
    RETURN false;
  END IF;
  
  -- Create a new version based on the restored version
  PERFORM create_document_version(
    version_info.document_id,
    version_info.file_path,
    version_info.file_size,
    version_info.checksum,
    version_info.mime_type,
    'Restored from version ' || version_info.version_number,
    'correction',
    version_info.version_label || '-restored',
    p_restored_by
  );
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create initial version
CREATE OR REPLACE FUNCTION create_initial_document_version()
RETURNS TRIGGER AS $$
BEGIN
  -- Create initial version when document is first created
  IF TG_OP = 'INSERT' AND NEW.file_path IS NOT NULL THEN
    PERFORM create_document_version(
      NEW.id,
      NEW.file_path,
      NEW.file_size,
      NEW.checksum,
      NEW.mime_type,
      'Initial document upload',
      'initial',
      'v1.0',
      NEW.uploaded_by
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for initial version creation
CREATE TRIGGER create_initial_version_trigger
    AFTER INSERT ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION create_initial_document_version();

-- Create view for version comparison
CREATE VIEW document_version_comparison AS
SELECT 
  dv1.document_id,
  dv1.id as version1_id,
  dv1.version_number as version1_number,
  dv1.version_label as version1_label,
  dv1.file_size as version1_size,
  dv1.created_at as version1_date,
  up1.full_name as version1_author,
  dv2.id as version2_id,
  dv2.version_number as version2_number,
  dv2.version_label as version2_label,
  dv2.file_size as version2_size,
  dv2.created_at as version2_date,
  up2.full_name as version2_author,
  (dv2.file_size - dv1.file_size) as size_difference,
  (dv2.created_at - dv1.created_at) as time_difference
FROM document_versions dv1
JOIN document_versions dv2 ON dv1.document_id = dv2.document_id
JOIN user_profiles up1 ON dv1.created_by = up1.id
JOIN user_profiles up2 ON dv2.created_by = up2.id
WHERE dv2.version_number = dv1.version_number + 1;

-- Add comments for documentation
COMMENT ON TABLE document_versions IS 'Version control and audit trail for regulatory documents';
COMMENT ON COLUMN document_versions.version_number IS 'Sequential version number starting from 1';
COMMENT ON COLUMN document_versions.change_type IS 'Type of change: major, minor, patch, correction, initial';
COMMENT ON COLUMN document_versions.is_current IS 'Indicates if this is the current active version';
COMMENT ON FUNCTION create_document_version IS 'Creates a new document version and updates the main document';
COMMENT ON FUNCTION get_document_version_history IS 'Returns complete version history for a document';
COMMENT ON FUNCTION restore_document_version IS 'Restores a previous version as the current version';
COMMENT ON VIEW document_version_comparison IS 'Facilitates comparison between consecutive document versions';
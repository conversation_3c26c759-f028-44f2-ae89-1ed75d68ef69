-- VigiLens Database Schema - Migration 008
-- Create Triggers and Functions - Real-time and Audit Integration
-- Comprehensive trigger system for real-time updates and audit logging

-- Create function for real-time document status notifications
CREATE OR REPLACE FUNCTION notify_document_status_change()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
BEGIN
    -- Build notification payload
    notification_payload := jsonb_build_object(
        'event_type', 'document_status_changed',
        'document_id', NEW.id,
        'organization_id', NEW.organization_id,
        'old_status', COALESCE(OLD.status, null),
        'new_status', NEW.status,
        'old_processing_status', COALESCE(OLD.processing_status, null),
        'new_processing_status', NEW.processing_status,
        'title', NEW.title,
        'document_type', NEW.document_type,
        'updated_by', NEW.updated_by,
        'timestamp', NOW()
    );

    -- Send real-time notification via pg_notify
    PERFORM pg_notify(
        'document_updates:' || NEW.organization_id::text,
        notification_payload::text
    );

    -- Log audit event for status changes
    IF OLD.status IS DISTINCT FROM NEW.status OR
       OLD.processing_status IS DISTINCT FROM NEW.processing_status THEN

        PERFORM log_audit_event(
            NEW.organization_id,
            NEW.updated_by,
            'update',
            'Document status changed: ' || COALESCE(OLD.status::text, 'null') || ' -> ' || NEW.status::text ||
            ', Processing: ' || COALESCE(OLD.processing_status::text, 'null') || ' -> ' || NEW.processing_status::text,
            'document',
            NEW.id,
            NEW.title,
            jsonb_build_object(
                'status', OLD.status,
                'processing_status', OLD.processing_status
            ),
            jsonb_build_object(
                'status', NEW.status,
                'processing_status', NEW.processing_status
            )
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for document status notifications
CREATE TRIGGER document_status_notification_trigger
    AFTER UPDATE ON regulatory_documents
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status OR
          OLD.processing_status IS DISTINCT FROM NEW.processing_status)
    EXECUTE FUNCTION notify_document_status_change();

-- Create function for user activity audit logging
CREATE OR REPLACE FUNCTION log_user_activity()
RETURNS TRIGGER AS $$
DECLARE
    action_type_val audit_action_type;
    action_desc TEXT;
    old_vals JSONB;
    new_vals JSONB;
BEGIN
    -- Determine action type
    action_type_val := CASE TG_OP
        WHEN 'INSERT' THEN 'create'
        WHEN 'UPDATE' THEN 'update'
        WHEN 'DELETE' THEN 'delete'
    END;

    -- Build action description
    action_desc := CASE TG_OP
        WHEN 'INSERT' THEN 'Created new ' || TG_TABLE_NAME || ' record'
        WHEN 'UPDATE' THEN 'Updated ' || TG_TABLE_NAME || ' record'
        WHEN 'DELETE' THEN 'Deleted ' || TG_TABLE_NAME || ' record'
    END;

    -- Prepare old and new values
    IF TG_OP = 'DELETE' THEN
        old_vals := to_jsonb(OLD);
        new_vals := NULL;
    ELSIF TG_OP = 'INSERT' THEN
        old_vals := NULL;
        new_vals := to_jsonb(NEW);
    ELSE
        old_vals := to_jsonb(OLD);
        new_vals := to_jsonb(NEW);
    END IF;

    -- Log the audit event
    PERFORM log_audit_event(
        COALESCE(NEW.organization_id, OLD.organization_id),
        COALESCE(NEW.updated_by, NEW.created_by, OLD.updated_by, OLD.created_by),
        action_type_val,
        action_desc,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        COALESCE(NEW.title, NEW.name, NEW.full_name, OLD.title, OLD.name, OLD.full_name),
        old_vals,
        new_vals
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create audit triggers for key tables
CREATE TRIGGER audit_regulatory_documents_trigger
    AFTER INSERT OR UPDATE OR DELETE ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION log_user_activity();

CREATE TRIGGER audit_user_profiles_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION log_user_activity();

CREATE TRIGGER audit_organizations_trigger
    AFTER INSERT OR UPDATE OR DELETE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION log_user_activity();

CREATE TRIGGER audit_user_role_assignments_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_role_assignments
    FOR EACH ROW
    EXECUTE FUNCTION log_user_activity();

-- Create function for real-time compliance alerts
CREATE OR REPLACE FUNCTION notify_compliance_alert()
RETURNS TRIGGER AS $$
DECLARE
    alert_payload JSONB;
    alert_level VARCHAR(50);
BEGIN
    -- Determine alert level based on compliance score
    alert_level := CASE
        WHEN NEW.compliance_score < 60 THEN 'critical'
        WHEN NEW.compliance_score < 80 THEN 'warning'
        ELSE 'info'
    END;

    -- Only send alerts for significant compliance issues
    IF NEW.compliance_score < 80 OR NEW.risk_level IN ('high', 'critical') THEN
        alert_payload := jsonb_build_object(
            'event_type', 'compliance_alert',
            'document_id', NEW.id,
            'organization_id', NEW.organization_id,
            'alert_level', alert_level,
            'compliance_score', NEW.compliance_score,
            'risk_level', NEW.risk_level,
            'title', NEW.title,
            'document_type', NEW.document_type,
            'timestamp', NOW()
        );

        -- Send real-time notification
        PERFORM pg_notify(
            'compliance_alerts:' || NEW.organization_id::text,
            alert_payload::text
        );

        -- Log compliance event
        PERFORM log_audit_event(
            NEW.organization_id,
            NEW.updated_by,
            'system_event',
            'Compliance alert generated: Score ' || NEW.compliance_score || ', Risk ' || NEW.risk_level,
            'document',
            NEW.id,
            NEW.title,
            NULL,
            jsonb_build_object(
                'compliance_score', NEW.compliance_score,
                'risk_level', NEW.risk_level,
                'alert_level', alert_level
            )
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for compliance alerts
CREATE TRIGGER compliance_alert_trigger
    AFTER UPDATE ON regulatory_documents
    FOR EACH ROW
    WHEN (OLD.compliance_score IS DISTINCT FROM NEW.compliance_score OR
          OLD.risk_level IS DISTINCT FROM NEW.risk_level)
    EXECUTE FUNCTION notify_compliance_alert();

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create user profile automatically when user signs up
    INSERT INTO user_profiles (
        id,
        email,
        full_name,
        organization_id,
        role,
        is_active,
        email_verified,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        -- Assign to demo organization for now, can be changed later
        (SELECT id FROM organizations WHERE name = 'demo-pharma-corp' LIMIT 1),
        'viewer',
        true,
        COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
        NOW(),
        NOW()
    );

    -- Log the user creation event
    PERFORM log_audit_event(
        (SELECT id FROM organizations WHERE name = 'demo-pharma-corp' LIMIT 1),
        NEW.id,
        'create',
        'New user profile created automatically',
        'user',
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        NULL,
        jsonb_build_object(
            'registration_method', 'supabase_auth',
            'email_verified', COALESCE(NEW.email_confirmed_at IS NOT NULL, false)
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Create function for user login tracking
CREATE OR REPLACE FUNCTION track_user_login()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user profile with login information
    UPDATE user_profiles
    SET
        last_login = NOW(),
        login_count = login_count + 1,
        failed_login_attempts = 0
    WHERE id = NEW.id;

    -- Log login event
    PERFORM log_audit_event(
        (SELECT organization_id FROM user_profiles WHERE id = NEW.id),
        NEW.id,
        'login',
        'User logged in successfully',
        'user',
        NEW.id,
        (SELECT full_name FROM user_profiles WHERE id = NEW.id),
        NULL,
        jsonb_build_object(
            'login_timestamp', NOW(),
            'auth_method', 'supabase_auth'
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function for document assignment notifications
CREATE OR REPLACE FUNCTION notify_document_assignment()
RETURNS TRIGGER AS $$
DECLARE
    assigned_user UUID;
    notification_payload JSONB;
BEGIN
    -- Check if assigned_to array changed
    IF OLD.assigned_to IS DISTINCT FROM NEW.assigned_to THEN
        -- Notify each newly assigned user
        FOR assigned_user IN
            SELECT unnest(NEW.assigned_to)
            EXCEPT
            SELECT unnest(COALESCE(OLD.assigned_to, ARRAY[]::UUID[]))
        LOOP
            notification_payload := jsonb_build_object(
                'event_type', 'document_assigned',
                'document_id', NEW.id,
                'organization_id', NEW.organization_id,
                'assigned_to', assigned_user,
                'title', NEW.title,
                'document_type', NEW.document_type,
                'priority', NEW.priority,
                'deadline', NEW.deadline,
                'assigned_by', NEW.updated_by,
                'timestamp', NOW()
            );

            -- Send notification to assigned user
            PERFORM pg_notify(
                'user_notifications:' || assigned_user::text,
                notification_payload::text
            );
        END LOOP;

        -- Log assignment change
        PERFORM log_audit_event(
            NEW.organization_id,
            NEW.updated_by,
            'update',
            'Document assignment changed',
            'document',
            NEW.id,
            NEW.title,
            jsonb_build_object('assigned_to', OLD.assigned_to),
            jsonb_build_object('assigned_to', NEW.assigned_to)
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for document assignment notifications
CREATE TRIGGER document_assignment_notification_trigger
    AFTER UPDATE ON regulatory_documents
    FOR EACH ROW
    WHEN (OLD.assigned_to IS DISTINCT FROM NEW.assigned_to)
    EXECUTE FUNCTION notify_document_assignment();

-- Create function to clean up old audit records (for data retention)
CREATE OR REPLACE FUNCTION cleanup_old_audit_records()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    retention_period INTERVAL := '7 years'; -- 21 CFR Part 11 requirement
BEGIN
    -- Delete audit records older than retention period
    -- Only delete low-risk, non-GxP relevant records
    DELETE FROM audit_trail
    WHERE timestamp < NOW() - retention_period
    AND risk_level = 'low'
    AND (compliance_metadata->>'gxp_relevant')::boolean = false;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Log cleanup activity
    PERFORM log_audit_event(
        NULL, -- System event
        NULL,
        'system_event',
        'Cleaned up ' || deleted_count || ' old audit records',
        'system',
        NULL,
        'audit_cleanup',
        NULL,
        jsonb_build_object(
            'deleted_count', deleted_count,
            'retention_period', retention_period::text
        )
    );

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update JWT claims for RLS
CREATE OR REPLACE FUNCTION update_user_jwt_claims(user_id UUID)
RETURNS JSONB AS $$
DECLARE
    user_claims JSONB;
    user_org_id UUID;
    user_role user_role;
    user_email TEXT;
BEGIN
    -- Get user information
    SELECT
        up.organization_id,
        up.role,
        up.email
    INTO user_org_id, user_role, user_email
    FROM user_profiles up
    WHERE up.id = user_id;

    -- Build JWT claims
    user_claims := jsonb_build_object(
        'organization_id', user_org_id,
        'role', user_role,
        'email', user_email,
        'updated_at', NOW()
    );

    RETURN user_claims;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for database health monitoring
CREATE OR REPLACE FUNCTION check_database_health()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details JSONB
) AS $$
BEGIN
    -- Check audit trail integrity
    RETURN QUERY
    SELECT
        'audit_trail_integrity'::TEXT,
        CASE WHEN integrity_violations = 0 THEN 'HEALTHY' ELSE 'WARNING' END,
        jsonb_build_object(
            'total_records', total_records,
            'integrity_violations', integrity_violations
        )
    FROM verify_audit_integrity();

    -- Check RLS policy coverage
    RETURN QUERY
    SELECT
        'rls_policy_coverage'::TEXT,
        'HEALTHY'::TEXT,
        jsonb_build_object(
            'tables_with_rls', (
                SELECT COUNT(*)
                FROM pg_tables t
                JOIN pg_class c ON c.relname = t.tablename
                WHERE t.schemaname = 'public'
                AND c.relrowsecurity = true
            ),
            'total_tables', (
                SELECT COUNT(*)
                FROM pg_tables
                WHERE schemaname = 'public'
            )
        );

    -- Check recent activity
    RETURN QUERY
    SELECT
        'recent_activity'::TEXT,
        'HEALTHY'::TEXT,
        jsonb_build_object(
            'documents_created_24h', (
                SELECT COUNT(*)
                FROM regulatory_documents
                WHERE created_at > NOW() - INTERVAL '24 hours'
            ),
            'audit_events_24h', (
                SELECT COUNT(*)
                FROM audit_trail
                WHERE timestamp > NOW() - INTERVAL '24 hours'
            )
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON FUNCTION notify_document_status_change() IS 'Real-time notifications for document status changes';
COMMENT ON FUNCTION log_user_activity() IS 'Comprehensive audit logging for user activities';
COMMENT ON FUNCTION notify_compliance_alert() IS 'Real-time compliance alerts for risk management';
COMMENT ON FUNCTION track_user_login() IS 'User login tracking for security and compliance';
COMMENT ON FUNCTION cleanup_old_audit_records() IS 'Data retention management for audit records';
COMMENT ON FUNCTION check_database_health() IS 'Database health monitoring for operational oversight';

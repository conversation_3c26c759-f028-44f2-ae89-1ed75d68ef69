# VigiLens Pharmaceutical Compliance Platform - Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these values from your Supabase project dashboard:
# https://supabase.com/dashboard/project/[your-project-id]/settings/api

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key (safe to expose in frontend)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Your site URL (for email redirects and auth callbacks)
NEXT_PUBLIC_SITE_URL=http://localhost:3001

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Next.js environment
NODE_ENV=development

# API Base URL (for backend integration)
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# =============================================================================
# PRODUCTION CONFIGURATION (for deployment)
# =============================================================================

# Uncomment and configure for production deployment:
# NODE_ENV=production
# NEXT_PUBLIC_API_URL=https://your-backend-domain.com/api/v1

# =============================================================================
# NOTES
# =============================================================================
#
# 1. NEVER commit .env.local to version control
# 2. NEXT_PUBLIC_ prefixed variables are exposed to the browser
# 3. Variables without NEXT_PUBLIC_ are server-side only
# 4. Restart your development server after changing environment variables
#
# For more information:
# https://nextjs.org/docs/basic-features/environment-variables

# Error Parser

A Node.js tool to parse `errors.json` and generate `error_tracking.md` in the root directory.

## Purpose

This tool reads the Rust compiler errors from `errors.json` and generates a structured markdown file that:

1. Groups errors by module
2. Formats error details in a consistent way
3. Preserves any previously fixed errors (marked with `[x]`)
4. Maintains a progress section at the bottom

## Usage

```bash
cd error-parser
npm start
```

This will:
1. Read the `errors.json` file from the root directory
2. Parse all Rust compiler errors
3. Generate or update `error_tracking.md` in the root directory
4. Preserve any errors that were already marked as fixed

## Features

- Organizes errors by module
- Preserves existing fixed errors
- Formats error messages for better readability
- Maintains a progress section
- Handles Windows file paths correctly
-- Migration: Add user settings columns only
-- Date: 2025-07-14
-- Purpose: Add missing JSONB columns for comprehensive user settings

-- Check if columns exist before adding them
DO $$ 
BEGIN
    -- Add preferences column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'preferences') THEN
        ALTER TABLE user_profiles ADD COLUMN preferences JSONB DEFAULT '{}';
        RAISE NOTICE 'Added preferences column';
    ELSE
        RAISE NOTICE 'preferences column already exists';
    END IF;
    
    -- Add activity_log column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'activity_log') THEN
        ALTER TABLE user_profiles ADD COLUMN activity_log JSONB DEFAULT '[]';
        RAISE NOTICE 'Added activity_log column';
    ELSE
        RAISE NOTICE 'activity_log column already exists';
    END IF;
    
    -- Add achievements column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'achievements') THEN
        ALTER TABLE user_profiles ADD COLUMN achievements JSONB DEFAULT '[]';
        RAISE NOTICE 'Added achievements column';
    ELSE
        RAISE NOTICE 'achievements column already exists';
    END IF;
    
    -- Add compliance_settings column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'compliance_settings') THEN
        ALTER TABLE user_profiles ADD COLUMN compliance_settings JSONB DEFAULT '{}';
        RAISE NOTICE 'Added compliance_settings column';
    ELSE
        RAISE NOTICE 'compliance_settings column already exists';
    END IF;
    
    -- Add notification_preferences column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'notification_preferences') THEN
        ALTER TABLE user_profiles ADD COLUMN notification_preferences JSONB DEFAULT '{}';
        RAISE NOTICE 'Added notification_preferences column';
    ELSE
        RAISE NOTICE 'notification_preferences column already exists';
    END IF;
    
    -- Add security_settings column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'security_settings') THEN
        ALTER TABLE user_profiles ADD COLUMN security_settings JSONB DEFAULT '{}';
        RAISE NOTICE 'Added security_settings column';
    ELSE
        RAISE NOTICE 'security_settings column already exists';
    END IF;
    
    -- Add bio column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'bio') THEN
        ALTER TABLE user_profiles ADD COLUMN bio TEXT;
        RAISE NOTICE 'Added bio column';
    ELSE
        RAISE NOTICE 'bio column already exists';
    END IF;
END $$;

-- Set default values for existing users
UPDATE user_profiles 
SET 
  preferences = COALESCE(preferences, '{}'),
  activity_log = COALESCE(activity_log, '[]'),
  achievements = COALESCE(achievements, '[]'),
  compliance_settings = COALESCE(compliance_settings, '{}'),
  notification_preferences = COALESCE(notification_preferences, '{
    "email_notifications": true,
    "push_notifications": true,
    "compliance_alerts": true,
    "document_updates": true,
    "system_announcements": true
  }'),
  security_settings = COALESCE(security_settings, '{
    "two_factor_enabled": false,
    "session_timeout": 30,
    "login_notifications": true,
    "password_change_notifications": true
  }')
WHERE 
  preferences IS NULL 
  OR activity_log IS NULL 
  OR achievements IS NULL 
  OR compliance_settings IS NULL 
  OR notification_preferences IS NULL 
  OR security_settings IS NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_preferences ON user_profiles USING GIN (preferences);
CREATE INDEX IF NOT EXISTS idx_user_profiles_compliance_settings ON user_profiles USING GIN (compliance_settings);
CREATE INDEX IF NOT EXISTS idx_user_profiles_notification_preferences ON user_profiles USING GIN (notification_preferences);

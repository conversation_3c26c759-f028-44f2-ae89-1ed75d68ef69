"""Pydantic models for audit trail and compliance logging."""

from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from ipaddress import IPv4Address, IPv6Address
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, ConfigDict

from .base import TimestampedModel


class AuditAction(str, Enum):
    """Audit action enumeration."""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    UPLOAD = "upload"
    DOWNLOAD = "download"
    EXPORT = "export"
    IMPORT = "import"
    APPROVE = "approve"
    REJECT = "reject"
    REVIEW = "review"
    ARCHIVE = "archive"
    RESTORE = "restore"
    SHARE = "share"
    PERMISSION_CHANGE = "permission_change"
    SYSTEM_CHANGE = "system_change"
    COMPLIANCE_ASSESSMENT = "compliance_assessment"
    NOTIFICATION_SENT = "notification_sent"


class RetentionStatus(str, Enum):
    """Retention status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"


class RemediationStatus(str, Enum):
    """Remediation status enumeration."""
    NONE = "none"
    PENDING = "pending"
    DUE_SOON = "due_soon"
    OVERDUE = "overdue"


class DigitalSignature(BaseModel):
    """Digital signature for 21 CFR Part 11 compliance."""
    signer_id: UUID = Field(..., description="ID of the user who signed")
    signer_name: str = Field(..., description="Full name of the signer")
    signature_method: str = Field(..., description="Signature method used")
    signature_value: str = Field(..., description="Encrypted signature value")
    timestamp: datetime = Field(..., description="Signature timestamp")
    certificate_info: Optional[Dict[str, Any]] = Field(None, description="Certificate information")
    verification_status: str = Field(..., description="Signature verification status")
    meaning: str = Field(..., description="Meaning of the signature")


class AuditTrailBase(BaseModel):
    """Base audit trail model."""
    organization_id: UUID = Field(..., description="Organization ID")
    user_id: Optional[UUID] = Field(None, description="User ID (null for system actions)")
    session_id: Optional[str] = Field(None, max_length=255, description="Session ID")
    action: AuditAction = Field(..., description="Action performed")
    resource_type: str = Field(..., max_length=100, description="Type of resource affected")
    resource_id: Optional[UUID] = Field(None, description="ID of the affected resource")
    resource_name: Optional[str] = Field(None, max_length=500, description="Name of the affected resource")
    description: Optional[str] = Field(None, description="Detailed description of the action")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional context data")
    ip_address: Optional[Union[IPv4Address, IPv6Address]] = Field(None, description="User's IP address")
    user_agent: Optional[str] = Field(None, description="User's browser/client information")
    request_id: Optional[str] = Field(None, max_length=255, description="Request correlation ID")
    success: bool = Field(default=True, description="Whether the action was successful")
    error_message: Optional[str] = Field(None, description="Error message if action failed")
    duration_ms: Optional[int] = Field(None, ge=0, description="Action duration in milliseconds")
    compliance_relevant: bool = Field(default=False, description="Whether this action is compliance-relevant")
    retention_period: timedelta = Field(default=timedelta(days=2555), description="Data retention period (7 years default)")

    @field_validator('metadata')
    @classmethod
    def validate_metadata(cls, v):
        """Ensure metadata is a valid dictionary."""
        if not isinstance(v, dict):
            raise ValueError("Metadata must be a dictionary")
        return v

    @field_validator('duration_ms')
    @classmethod
    def validate_duration(cls, v):
        """Ensure duration is non-negative."""
        if v is not None and v < 0:
            raise ValueError("Duration must be non-negative")
        return v


class AuditTrailCreate(AuditTrailBase):
    """Model for creating audit trail entries."""
    pass


class AuditTrail(AuditTrailBase):
    """Complete audit trail model."""
    id: UUID = Field(..., description="Audit trail ID")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict(from_attributes=True)


class AuditTrailReport(BaseModel):
    """Audit trail report model with additional context."""
    id: UUID = Field(..., description="Audit trail ID")
    organization_id: UUID = Field(..., description="Organization ID")
    organization_name: str = Field(..., description="Organization name")
    user_id: Optional[UUID] = Field(None, description="User ID")
    user_name: Optional[str] = Field(None, description="User full name")
    user_email: Optional[str] = Field(None, description="User email")
    action: AuditAction = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Resource type")
    resource_id: Optional[UUID] = Field(None, description="Resource ID")
    resource_name: Optional[str] = Field(None, description="Resource name")
    description: Optional[str] = Field(None, description="Action description")
    metadata: Dict[str, Any] = Field(..., description="Additional context")
    ip_address: Optional[Union[IPv4Address, IPv6Address]] = Field(None, description="IP address")
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message")
    compliance_relevant: bool = Field(..., description="Compliance relevance")
    retention_status: RetentionStatus = Field(..., description="Retention status")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict(from_attributes=True)


class ComplianceLogBase(BaseModel):
    """Base compliance log model."""
    organization_id: UUID = Field(..., description="Organization ID")
    audit_trail_id: Optional[UUID] = Field(None, description="Related audit trail ID")
    compliance_event_type: str = Field(..., max_length=100, description="Type of compliance event")
    regulatory_framework: Optional[str] = Field(None, max_length=100, description="Regulatory framework")
    document_id: Optional[UUID] = Field(None, description="Related document ID")
    user_id: Optional[UUID] = Field(None, description="User ID")
    event_details: Dict[str, Any] = Field(default_factory=dict, description="Event details")
    compliance_status: Optional[str] = Field(None, max_length=50, description="Compliance status")
    risk_assessment: Optional[str] = Field(None, max_length=20, description="Risk assessment")
    remediation_required: bool = Field(default=False, description="Whether remediation is required")
    remediation_deadline: Optional[datetime] = Field(None, description="Remediation deadline")
    evidence_references: List[Dict[str, Any]] = Field(default_factory=list, description="Evidence references")
    reviewer_notes: Optional[str] = Field(None, description="Reviewer notes")
    digital_signature: Optional[DigitalSignature] = Field(None, description="Digital signature")

    @field_validator('event_details')
    @classmethod
    def validate_event_details(cls, v):
        """Ensure event details is a valid dictionary."""
        if not isinstance(v, dict):
            raise ValueError("Event details must be a dictionary")
        return v

    @field_validator('evidence_references')
    @classmethod
    def validate_evidence_references(cls, v):
        """Ensure evidence references is a valid list."""
        if not isinstance(v, list):
            raise ValueError("Evidence references must be a list")
        return v

    @field_validator('remediation_deadline')
    @classmethod
    def validate_remediation_deadline(cls, v, info):
        """Ensure remediation deadline is in the future if remediation is required."""
        if info.data.get('remediation_required') and v and v <= datetime.utcnow():
            raise ValueError("Remediation deadline must be in the future")
        return v


class ComplianceLogCreate(ComplianceLogBase):
    """Model for creating compliance log entries."""
    pass


class ComplianceLogUpdate(BaseModel):
    """Model for updating compliance log entries."""
    compliance_status: Optional[str] = Field(None, max_length=50)
    risk_assessment: Optional[str] = Field(None, max_length=20)
    remediation_required: Optional[bool] = None
    remediation_deadline: Optional[datetime] = None
    evidence_references: Optional[List[Dict[str, Any]]] = None
    reviewer_notes: Optional[str] = None
    digital_signature: Optional[DigitalSignature] = None


class ComplianceLog(ComplianceLogBase, TimestampedModel):
    """Complete compliance log model."""
    id: UUID = Field(..., description="Compliance log ID")

    model_config = ConfigDict(from_attributes=True)


class ComplianceReport(BaseModel):
    """Compliance report model with additional context."""
    id: UUID = Field(..., description="Compliance log ID")
    organization_id: UUID = Field(..., description="Organization ID")
    organization_name: str = Field(..., description="Organization name")
    compliance_event_type: str = Field(..., description="Event type")
    regulatory_framework: Optional[str] = Field(None, description="Regulatory framework")
    document_id: Optional[UUID] = Field(None, description="Document ID")
    document_title: Optional[str] = Field(None, description="Document title")
    document_type: Optional[str] = Field(None, description="Document type")
    user_id: Optional[UUID] = Field(None, description="User ID")
    user_name: Optional[str] = Field(None, description="User name")
    event_details: Dict[str, Any] = Field(..., description="Event details")
    compliance_status: Optional[str] = Field(None, description="Compliance status")
    risk_assessment: Optional[str] = Field(None, description="Risk assessment")
    remediation_required: bool = Field(..., description="Remediation required")
    remediation_deadline: Optional[datetime] = Field(None, description="Remediation deadline")
    evidence_references: List[Dict[str, Any]] = Field(..., description="Evidence references")
    reviewer_notes: Optional[str] = Field(None, description="Reviewer notes")
    remediation_status: RemediationStatus = Field(..., description="Remediation status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")

    class Config:
        from_attributes = True


class AuditTrailFilter(BaseModel):
    """Filter model for audit trail queries."""
    organization_id: Optional[UUID] = Field(None, description="Organization ID filter")
    user_id: Optional[UUID] = Field(None, description="User ID filter")
    actions: Optional[List[AuditAction]] = Field(None, description="Actions filter")
    resource_types: Optional[List[str]] = Field(None, description="Resource types filter")
    resource_id: Optional[UUID] = Field(None, description="Resource ID filter")
    success: Optional[bool] = Field(None, description="Success status filter")
    compliance_relevant: Optional[bool] = Field(None, description="Compliance relevance filter")
    date_from: Optional[datetime] = Field(None, description="Start date filter")
    date_to: Optional[datetime] = Field(None, description="End date filter")
    ip_address: Optional[Union[IPv4Address, IPv6Address]] = Field(None, description="IP address filter")
    session_id: Optional[str] = Field(None, description="Session ID filter")
    search_text: Optional[str] = Field(None, description="Text search in description and resource name")

    @field_validator('date_to')
    @classmethod
    def validate_date_range(cls, v, info):
        """Ensure date_to is after date_from."""
        if v and info.data.get('date_from'):
            if v <= info.data['date_from']:
                raise ValueError("End date must be after start date")
        return v


class ComplianceLogFilter(BaseModel):
    """Filter model for compliance log queries."""
    organization_id: Optional[UUID] = Field(None, description="Organization ID filter")
    compliance_event_types: Optional[List[str]] = Field(None, description="Event types filter")
    regulatory_frameworks: Optional[List[str]] = Field(None, description="Frameworks filter")
    document_id: Optional[UUID] = Field(None, description="Document ID filter")
    user_id: Optional[UUID] = Field(None, description="User ID filter")
    compliance_statuses: Optional[List[str]] = Field(None, description="Compliance statuses filter")
    risk_assessments: Optional[List[str]] = Field(None, description="Risk assessments filter")
    remediation_required: Optional[bool] = Field(None, description="Remediation required filter")
    remediation_overdue: Optional[bool] = Field(None, description="Overdue remediation filter")
    date_from: Optional[datetime] = Field(None, description="Start date filter")
    date_to: Optional[datetime] = Field(None, description="End date filter")
    has_digital_signature: Optional[bool] = Field(None, description="Digital signature presence filter")

    @field_validator('date_to')
    @classmethod
    def validate_date_range(cls, v, info):
        """Ensure date_to is after date_from."""
        if v and info.data.get('date_from'):
            if v <= info.data['date_from']:
                raise ValueError("End date must be after start date")
        return v


class AuditTrailSummary(BaseModel):
    """Summary statistics for audit trail."""
    total_events: int = Field(..., description="Total number of events")
    successful_events: int = Field(..., description="Number of successful events")
    failed_events: int = Field(..., description="Number of failed events")
    compliance_relevant_events: int = Field(..., description="Number of compliance-relevant events")
    unique_users: int = Field(..., description="Number of unique users")
    unique_resources: int = Field(..., description="Number of unique resources")
    actions_breakdown: Dict[str, int] = Field(..., description="Breakdown by action type")
    resource_types_breakdown: Dict[str, int] = Field(..., description="Breakdown by resource type")
    date_range: Dict[str, datetime] = Field(..., description="Date range of events")
    success_rate: float = Field(..., description="Success rate percentage")


class ComplianceLogSummary(BaseModel):
    """Summary statistics for compliance log."""
    total_events: int = Field(..., description="Total number of compliance events")
    events_by_type: Dict[str, int] = Field(..., description="Events breakdown by type")
    events_by_framework: Dict[str, int] = Field(..., description="Events breakdown by framework")
    compliance_status_breakdown: Dict[str, int] = Field(..., description="Breakdown by compliance status")
    risk_assessment_breakdown: Dict[str, int] = Field(..., description="Breakdown by risk assessment")
    remediation_required_count: int = Field(..., description="Number of events requiring remediation")
    overdue_remediation_count: int = Field(..., description="Number of overdue remediations")
    signed_events_count: int = Field(..., description="Number of digitally signed events")
    date_range: Dict[str, datetime] = Field(..., description="Date range of events")
    compliance_rate: float = Field(..., description="Overall compliance rate")


class AuditEventRequest(BaseModel):
    """Request model for logging audit events."""
    action: AuditAction = Field(..., description="Action performed")
    resource_type: str = Field(..., max_length=100, description="Resource type")
    resource_id: Optional[UUID] = Field(None, description="Resource ID")
    resource_name: Optional[str] = Field(None, max_length=500, description="Resource name")
    description: Optional[str] = Field(None, description="Action description")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    success: bool = Field(default=True, description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    duration_ms: Optional[int] = Field(None, ge=0, description="Duration in milliseconds")
    compliance_relevant: bool = Field(default=False, description="Compliance relevance")


class ComplianceEventRequest(BaseModel):
    """Request model for logging compliance events."""
    compliance_event_type: str = Field(..., max_length=100, description="Event type")
    regulatory_framework: Optional[str] = Field(None, max_length=100, description="Framework")
    document_id: Optional[UUID] = Field(None, description="Document ID")
    event_details: Dict[str, Any] = Field(default_factory=dict, description="Event details")
    compliance_status: Optional[str] = Field(None, max_length=50, description="Compliance status")
    risk_assessment: Optional[str] = Field(None, max_length=20, description="Risk assessment")
    remediation_required: bool = Field(default=False, description="Remediation required")
    remediation_deadline: Optional[datetime] = Field(None, description="Remediation deadline")
    evidence_references: List[Dict[str, Any]] = Field(default_factory=list, description="Evidence")
    reviewer_notes: Optional[str] = Field(None, description="Reviewer notes")
    digital_signature: Optional[DigitalSignature] = Field(None, description="Digital signature")


class AuditCleanupRequest(BaseModel):
    """Request model for audit trail cleanup."""
    organization_id: Optional[UUID] = Field(None, description="Organization ID (null for all)")
    older_than: timedelta = Field(default=timedelta(days=2555), description="Delete records older than this")
    dry_run: bool = Field(default=True, description="Perform dry run without actual deletion")


class AuditCleanupResponse(BaseModel):
    """Response model for audit trail cleanup."""
    deleted_count: int = Field(..., description="Number of records deleted")
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    retention_period: timedelta = Field(..., description="Retention period used")
    dry_run: bool = Field(..., description="Whether this was a dry run")
    cleanup_date: datetime = Field(..., description="Cleanup execution date")


class AuditExportRequest(BaseModel):
    """Request model for audit trail export."""
    filters: AuditTrailFilter = Field(..., description="Export filters")
    format: str = Field(default="csv", description="Export format (csv, json, xlsx)")
    include_metadata: bool = Field(default=False, description="Include metadata in export")
    include_compliance_only: bool = Field(default=False, description="Include only compliance-relevant records")


class ComplianceExportRequest(BaseModel):
    """Request model for compliance log export."""
    filters: ComplianceLogFilter = Field(..., description="Export filters")
    format: str = Field(default="csv", description="Export format (csv, json, xlsx)")
    include_evidence: bool = Field(default=True, description="Include evidence references")
    include_signatures: bool = Field(default=False, description="Include digital signatures")


class ExportResponse(BaseModel):
    """Response model for export operations."""
    export_id: UUID = Field(..., description="Export job ID")
    status: str = Field(..., description="Export status")
    download_url: Optional[str] = Field(None, description="Download URL when ready")
    record_count: int = Field(..., description="Number of records in export")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    expires_at: datetime = Field(..., description="Download link expiration")
    created_at: datetime = Field(..., description="Export creation timestamp")
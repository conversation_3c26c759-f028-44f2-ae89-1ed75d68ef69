import requests
import json

def test_small_batch():
    url = "http://localhost:8000/api/v1/ai/populate-knowledge-base"
    
    payload = {
        "sources": ["local_cfr"],
        "force_refresh": True,
        "max_documents": 1  # Process only 1 document for testing
    }
    
    print(f"Testing API endpoint: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        response = requests.post(
            url, 
            json=payload, 
            timeout=120  # Shorter timeout for small batch
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\nSuccess! Documents added: {result.get('documents_added', 'Unknown')}")
        else:
            print(f"\nError: {response.status_code} - {response.text}")
            
    except requests.exceptions.Timeout:
        print("Request timed out")
    except requests.exceptions.ConnectionError:
        print("Connection failed - is the server running?")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_small_batch()
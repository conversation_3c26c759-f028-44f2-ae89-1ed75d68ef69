## Search Section – Implementation Specification

*Generated via 6-Expert Protocol analysis*

---

### 1. Information Architecture

```
Route: /search (Next.js pages/Search.tsx)
Wrapper: <DashboardLayout>
Regions (DOM order):
  1. Header bar (title, subtitle, action buttons)
  2. Search Mode Tabs
     ├── Standard Search Panel
     │   • Search Input Row
     │   • Quick Search Suggestions
     │   • Dynamic Results List
     └── AI Assistant Search Panel
         • Prompt Textarea Card
         • AI Insights List
  3. Recent Searches Card
```

---

### 2. Interaction Design

#### React State
- `searchQuery : string`
- `aiQuery     : string`
- `searchType  : "all" | "regulations" | "guidelines" | "templates" | "sops"`
- `isSearching : boolean`

#### Flows
1. **Standard Search**
   1. User types input → `Enter` or clicks Search → `handleSearch()` sets `isSearching`.
   2. Spinner shown for 1.5 s placeholder → results render.
2. **AI Assistant**
   1. User types prompt → clicks **Ask AI** → `handleAiSearch()` sets `isSearching`.
   2. Spinner 2 s placeholder → insight cards render.
3. **Quick Suggestions** populate `searchQuery` only (no auto-execute).
4. **Recent Searches** row click populates `searchQuery`.

#### Key Interactions
- Search `Enter` key triggers.
- ResultCard buttons: *View* (internal), *Open Source* (external).
- Dropdown `Select` does **not** auto-requery (developer should listen and call API).

---

### 3. Visual Design (Tailwind / Shadcn-UI tokens)

| Element | Classes / Tokens |
|---------|------------------|
| Page container | `space-y-6` |
| Header H1 | `text-3xl font-bold` |
| Subtitle | `text-sm text-muted-foreground mt-1` |
| Action Buttons | `variant="outline"`, gap-2, icon left |
| TabsList | `grid grid-cols-2` |
| Card padding | `p-6` (standard), `p-4` (insight) |
| Primary Buttons | `bg-primary hover:bg-primary/90` |
| Outline Buttons | default border, `hover:bg-primary hover:text-primary-foreground` |
| Result hover | `hover:shadow-md hover:border-primary/20` |
| Summary clamp | `text-sm line-clamp-2` |

Responsive:
- ≥`lg`: search bar row; ≤`md`: column.

---

### 4. Component Catalog

```
Atoms    : Badge, Button, Input, Textarea, Select, Tabs
Molecules: SearchInputGroup, ResultCard
Organisms: StandardSearchPanel, AiAssistantPanel, RecentSearchesCard
Layout   : DashboardLayout
```

Suggested props for extraction:
```ts
<SearchPage
  presetQuery?: string;
  initialTab?: "standard" | "ai";
  onResultSelect?: (docId: string) => void;
/>
```

---

### 5. Backend Contracts

#### Search Endpoint
`GET /api/search?q=string&type=string&page=int`
```jsonc
{
  "results": [
    {
      "id": "string",
      "title": "string",
      "type": "Document" | "Guideline" | "Template" | "International Standard",
      "agency": "string",
      "relevance": 0,
      "summary": "string",
      "url": "string",
      "publishedDate": "ISODate",
      "tags": ["string"],
      "source": "string"
    }
  ],
  "total": 0,
  "page": 1,
  "perPage": 10
}
```

#### AI Assistant Endpoint
`POST /api/ai-search`
```jsonc
{
  "prompt": "string"
}
⇒
{
  "insights": [
    {
      "query": "string",
      "insight": "string",
      "confidence": 0,
      "sources": [
        { "title": "string", "url": "string" }
      ]
    }
  ]
}
```

---

### 6. QA & Accessibility

- Unit tests: render default, trigger search, quick suggestion, select change, spinner visibility.
- E2E: confirm API responses render, keyboard navigation order, focus rings, external links `rel="noopener"`.
- A11y: all icons `aria-hidden`, add `<span class="sr-only">Loading…</span>` inside spinner, announce results via `aria-live="polite"`.
- Contrast: ensure primary vs bg ≥ 4.5.

---

### Performance & Enhancements
- Debounce 300 ms before API hit.
- Infinite scroll or paging controls.
- Prefetch internal doc route on *View* hover.

---

> This document equips any Next.js developer to rebuild the Search feature pixel-perfect and interaction-complete.

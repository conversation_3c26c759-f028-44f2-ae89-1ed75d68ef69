'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'
import { ProfileHeader } from './components/profile-header'
import { ProfileStats } from './components/profile-stats'
import { ProfileTabs } from './components/profile-tabs'
import { useState } from 'react'

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)

  usePageMetadata('Profile', 'View and edit your user profile', {
    robots: 'noindex',
  })

  return (
    <div className="space-y-6">
      <ProfileHeader onEditToggle={setIsEditing} />

      <ProfileStats />

      <ProfileTabs isEditing={isEditing} />
    </div>
  )
}

"""
Data Gathering Orchestrator for VigiLens Pharmaceutical Compliance Platform.
Coordinates data collection from FDA, EMA, ICH, and other regulatory sources.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from pathlib import Path

from .fda_scraper import get_fda_gatherer
from .ema_scraper import get_ema_gatherer  
from .ich_scraper import get_ich_gatherer

logger = logging.getLogger(__name__)


class DataGatheringOrchestrator:
    """Orchestrates pharmaceutical data gathering from multiple regulatory sources."""
    
    def __init__(self):
        self.gatherers = {}
        self.data_cache = {}
        self.gathering_stats = {
            "total_documents": 0,
            "sources": {},
            "last_update": None,
            "errors": []
        }
    
    async def initialize(self):
        """Initialize all data gatherers."""
        logger.info("Initializing data gathering orchestrator...")
        
        try:
            # Initialize FDA gatherer
            self.gatherers["fda"] = await get_fda_gatherer()
            logger.info("✅ FDA gatherer initialized")
            
            # Initialize EMA gatherer
            self.gatherers["ema"] = await get_ema_gatherer()
            logger.info("✅ EMA gatherer initialized")
            
            # Initialize ICH gatherer
            self.gatherers["ich"] = await get_ich_gatherer()
            logger.info("✅ ICH gatherer initialized")
            
            logger.info("🎯 All data gatherers initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize data gatherers: {str(e)}")
            raise
    
    async def close(self):
        """Close all gatherer sessions."""
        for name, gatherer in self.gatherers.items():
            try:
                await gatherer.close()
                logger.info(f"Closed {name} gatherer")
            except Exception as e:
                logger.error(f"Error closing {name} gatherer: {str(e)}")
    
    async def gather_fda_data(self) -> List[Dict[str, Any]]:
        """Gather data from FDA sources."""
        logger.info("🇺🇸 Starting FDA data gathering...")
        
        try:
            fda_gatherer = self.gatherers["fda"]
            data = await fda_gatherer.gather_all_fda_data()
            
            self.gathering_stats["sources"]["fda"] = {
                "documents": len(data),
                "last_update": datetime.now().isoformat(),
                "status": "success"
            }
            
            logger.info(f"✅ FDA data gathering complete: {len(data)} documents")
            return data
            
        except Exception as e:
            error_msg = f"FDA data gathering failed: {str(e)}"
            logger.error(error_msg)
            self.gathering_stats["errors"].append({
                "source": "fda",
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            return []
    
    async def gather_ema_data(self) -> List[Dict[str, Any]]:
        """Gather data from EMA sources."""
        logger.info("🇪🇺 Starting EMA data gathering...")
        
        try:
            ema_gatherer = self.gatherers["ema"]
            data = await ema_gatherer.gather_all_ema_data()
            
            self.gathering_stats["sources"]["ema"] = {
                "documents": len(data),
                "last_update": datetime.now().isoformat(),
                "status": "success"
            }
            
            logger.info(f"✅ EMA data gathering complete: {len(data)} documents")
            return data
            
        except Exception as e:
            error_msg = f"EMA data gathering failed: {str(e)}"
            logger.error(error_msg)
            self.gathering_stats["errors"].append({
                "source": "ema",
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            return []
    
    async def gather_ich_data(self) -> List[Dict[str, Any]]:
        """Gather data from ICH sources."""
        logger.info("🌐 Starting ICH data gathering...")
        
        try:
            ich_gatherer = self.gatherers["ich"]
            data = await ich_gatherer.gather_all_ich_data()
            
            self.gathering_stats["sources"]["ich"] = {
                "documents": len(data),
                "last_update": datetime.now().isoformat(),
                "status": "success"
            }
            
            logger.info(f"✅ ICH data gathering complete: {len(data)} documents")
            return data
            
        except Exception as e:
            error_msg = f"ICH data gathering failed: {str(e)}"
            logger.error(error_msg)
            self.gathering_stats["errors"].append({
                "source": "ich",
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            return []
    
    async def gather_all_data(self, sources: List[str] = None) -> List[Dict[str, Any]]:
        """Gather data from all specified sources."""
        if sources is None:
            sources = ["fda", "ema", "ich"]
        
        logger.info(f"🚀 Starting comprehensive data gathering from sources: {sources}")
        
        all_data = []
        
        # Gather data from each source
        gathering_tasks = []
        
        if "fda" in sources:
            gathering_tasks.append(("fda", self.gather_fda_data()))
        
        if "ema" in sources:
            gathering_tasks.append(("ema", self.gather_ema_data()))
        
        if "ich" in sources:
            gathering_tasks.append(("ich", self.gather_ich_data()))
        
        # Execute gathering tasks concurrently (with some delay to respect rate limits)
        for source_name, task in gathering_tasks:
            try:
                data = await task
                all_data.extend(data)
                logger.info(f"✅ {source_name.upper()} data integrated: {len(data)} documents")
                
                # Add delay between sources to be respectful
                if source_name != gathering_tasks[-1][0]:  # Not the last source
                    await asyncio.sleep(5)
                    
            except Exception as e:
                logger.error(f"❌ Failed to gather {source_name} data: {str(e)}")
        
        # Update overall statistics
        self.gathering_stats["total_documents"] = len(all_data)
        self.gathering_stats["last_update"] = datetime.now().isoformat()
        
        logger.info(f"🎉 Data gathering complete: {len(all_data)} total documents from {len(sources)} sources")
        
        return all_data
    
    async def populate_knowledge_base(self, sources: List[str] = None) -> Dict[str, Any]:
        """Gather data and populate the vector knowledge base."""
        logger.info("📚 Starting knowledge base population...")
        
        try:
            # Import vector store
            from services.ai.vector_store import get_vector_store
            
            # Get vector store
            vector_store = await get_vector_store()
            
            # Check if knowledge base is already populated
            stats = await vector_store.get_collection_stats()
            if stats["document_count"] > 100:  # Threshold for "already populated"
                logger.info(f"Knowledge base already contains {stats['document_count']} documents")
                return {
                    "status": "already_populated",
                    "document_count": stats["document_count"],
                    "message": "Knowledge base already contains sufficient documents"
                }
            
            # Gather fresh data
            all_data = await self.gather_all_data(sources)
            
            if not all_data:
                return {
                    "status": "error",
                    "message": "No data was gathered from any source"
                }
            
            # Add documents to vector store
            logger.info(f"Adding {len(all_data)} documents to knowledge base...")
            await vector_store.add_documents(all_data)
            
            # Get updated stats
            updated_stats = await vector_store.get_collection_stats()
            
            result = {
                "status": "success",
                "documents_added": len(all_data),
                "total_documents": updated_stats["document_count"],
                "sources_processed": list(self.gathering_stats["sources"].keys()),
                "gathering_stats": self.gathering_stats,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"✅ Knowledge base population complete: {len(all_data)} documents added")
            return result
            
        except Exception as e:
            error_msg = f"Knowledge base population failed: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }
    
    def get_gathering_stats(self) -> Dict[str, Any]:
        """Get current data gathering statistics."""
        return self.gathering_stats.copy()
    
    async def save_data_to_file(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """Save gathered data to JSON file for backup/analysis."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pharmaceutical_data_{timestamp}.json"
        
        # Ensure data directory exists
        data_dir = Path("./data/gathered")
        data_dir.mkdir(parents=True, exist_ok=True)
        
        filepath = data_dir / filename
        
        # Save data with metadata
        save_data = {
            "metadata": {
                "total_documents": len(data),
                "gathering_stats": self.gathering_stats,
                "saved_at": datetime.now().isoformat()
            },
            "documents": data
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data saved to {filepath}")
        return str(filepath)


# Factory function
async def get_data_orchestrator() -> DataGatheringOrchestrator:
    """Get initialized data gathering orchestrator."""
    orchestrator = DataGatheringOrchestrator()
    await orchestrator.initialize()
    return orchestrator

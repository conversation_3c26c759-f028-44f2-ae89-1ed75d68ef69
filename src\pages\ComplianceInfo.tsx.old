import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Upload,
  Shield,
  BarChart3,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
  Zap,
  Brain,
  Target,
  Settings,
  ArrowRight,
  Play,
  Download,
  TrendingUp,
  Plus,
} from "lucide-react";

export function ComplianceInfo() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            Smart Compliance Validation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-muted-foreground">
            Our unified Smart Compliance Validation system combines document
            upload, regulatory framework validation, and AI-powered deep
            analysis into a seamless workflow. Here's how it works:
          </p>

          {/* Workflow Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-medium">Upload</h4>
                <p className="text-sm text-muted-foreground">
                  Drag & drop documents
                </p>
              </div>
            </div>
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-500/10 mx-auto">
                <Target className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <h4 className="font-medium">Select</h4>
                <p className="text-sm text-muted-foreground">
                  Choose frameworks
                </p>
              </div>
            </div>
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-500/10 mx-auto">
                <Brain className="h-6 w-6 text-purple-500" />
              </div>
              <div>
                <h4 className="font-medium">Configure</h4>
                <p className="text-sm text-muted-foreground">Analysis depth</p>
              </div>
            </div>
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-success/10 mx-auto">
                <BarChart3 className="h-6 w-6 text-success" />
              </div>
              <div>
                <h4 className="font-medium">Results</h4>
                <p className="text-sm text-muted-foreground">
                  Insights & reports
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Steps */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Step 1: Smart Document Upload */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                  <Upload className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    Smart Document Upload
                  </CardTitle>
                  <Badge variant="secondary" className="text-xs">
                    Progressive Workflow
                  </Badge>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Intelligent document processing with automatic categorization and
              framework suggestions based on content analysis.
            </p>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Plus className="h-4 w-4 text-primary mt-0.5" />
                <div>
                  <h5 className="text-sm font-medium">Drag & Drop Interface</h5>
                  <p className="text-xs text-muted-foreground">
                    Intuitive upload with real-time preview
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="h-4 w-4 text-success mt-0.5" />
                <div>
                  <h5 className="text-sm font-medium">Enterprise Security</h5>
                  <p className="text-xs text-muted-foreground">
                    End-to-end encryption & GDPR compliance
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <FileText className="h-4 w-4 text-info mt-0.5" />
                <div>
                  <h5 className="text-sm font-medium">Format Support</h5>
                  <p className="text-xs text-muted-foreground">
                    PDF, DOC, DOCX, XLS, XLSX, TXT
                  </p>
                </div>
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Zap className="h-4 w-4 text-warning" />
                <span className="text-sm font-medium">Smart Features</span>
              </div>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Automatic document type detection</li>
                <li>• Content-based framework recommendations</li>
                <li>• Batch processing capabilities</li>
                <li>• Auto-cleanup after 30 days</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Step 2: Framework Selection */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500/10">
                <Target className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <CardTitle className="text-lg">
                  Multi-Framework Validation
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Intelligent Selection
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Choose from comprehensive regulatory frameworks with intelligent
              suggestions based on document content and industry standards.
            </p>

            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <span className="font-medium">FDA cGMP</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-500" />
                  <span className="font-medium">ICH Q7/Q9</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-purple-500" />
                  <span className="font-medium">ISO 13485</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-orange-500" />
                  <span className="font-medium">EU GMP</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-red-500" />
                  <span className="font-medium">Custom Rules</span>
                </div>
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Brain className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">AI-Powered</span>
              </div>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Automatic framework recommendations</li>
                <li>• Multi-framework validation support</li>
                <li>• Industry-specific rule sets</li>
                <li>• Custom compliance criteria</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Step 3: Analysis Configuration */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500/10">
                <Brain className="h-6 w-6 text-purple-500" />
              </div>
              <div>
                <CardTitle className="text-lg">
                  Configurable Analysis Depth
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Three Tiers Available
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Choose your analysis depth from quick scans to comprehensive
              AI-powered insights with industry benchmarking.
            </p>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">Quick Scan</span>
                </div>
                <span className="text-xs text-muted-foreground">2-5 min</span>
              </div>
              <div className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Standard Analysis</span>
                </div>
                <span className="text-xs text-muted-foreground">5-10 min</span>
              </div>
              <div className="flex items-center justify-between p-2 border rounded border-primary/20 bg-primary/5">
                <div className="flex items-center space-x-2">
                  <Brain className="h-4 w-4 text-purple-500" />
                  <span className="text-sm font-medium">Deep Analysis</span>
                </div>
                <span className="text-xs text-muted-foreground">10-15 min</span>
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-success" />
                <span className="text-sm font-medium">Advanced Features</span>
              </div>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Industry benchmarking</li>
                <li>• Predictive compliance insights</li>
                <li>• Risk assessment modeling</li>
                <li>• Best practices recommendations</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Step 4: Results & Insights */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-success/10">
                <BarChart3 className="h-6 w-6 text-success" />
              </div>
              <div>
                <CardTitle className="text-lg">
                  Unified Results Dashboard
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Comprehensive Insights
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Get comprehensive compliance scores, detailed issue analysis, and
              actionable recommendations in a unified dashboard.
            </p>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4 text-primary" />
                  <span className="text-sm">Compliance Scores</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-warning" />
                  <span className="text-sm">Issue Identification</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-info" />
                  <span className="text-sm">Recommendations</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Download className="h-4 w-4 text-success" />
                  <span className="text-sm">Exportable Reports</span>
                </div>
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Brain className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">AI Insights</span>
              </div>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Risk prioritization matrix</li>
                <li>• Implementation roadmaps</li>
                <li>• Continuous improvement suggestions</li>
                <li>• Regulatory change impact analysis</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Benefits Section */}
      <Card>
        <CardHeader>
          <CardTitle>Why Choose Smart Compliance Validation?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-medium">90% Time Reduction</h4>
                <p className="text-sm text-muted-foreground">
                  Automated validation replaces weeks of manual review
                </p>
              </div>
            </div>
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-success/10 mx-auto">
                <CheckCircle className="h-6 w-6 text-success" />
              </div>
              <div>
                <h4 className="font-medium">99.5% Accuracy</h4>
                <p className="text-sm text-muted-foreground">
                  AI-powered analysis with regulatory expert validation
                </p>
              </div>
            </div>
            <div className="text-center space-y-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-info/10 mx-auto">
                <Shield className="h-6 w-6 text-info" />
              </div>
              <div>
                <h4 className="font-medium">Enterprise Security</h4>
                <p className="text-sm text-muted-foreground">
                  Bank-level encryption with complete audit trails
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Ready to streamline your compliance validation process? Follow
              these simple steps:
            </p>
            <div className="flex flex-wrap gap-2">
              <Button className="bg-primary hover:bg-primary/90">
                <Upload className="mr-2 h-4 w-4" />
                Start Upload
              </Button>
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                View Examples
              </Button>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Configure Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

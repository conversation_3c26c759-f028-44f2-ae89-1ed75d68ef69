-- Quick verification of authentication fix status
-- Run this to see detailed results of the fix

-- Check 1: Demo organization status
SELECT 
    '🏢 DEMO ORGANIZATION' as component,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status,
    COUNT(*) as count
FROM organizations 
WHERE name = 'demo-pharma-corp';

-- Check 2: Trigger function status
SELECT 
    '⚙️ TRIGGER FUNCTION' as component,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status,
    COUNT(*) as count
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- Check 3: Trigger status
SELECT 
    '🔗 TRIGGER' as component,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ ACTIVE'
        ELSE '❌ MISSING'
    END as status,
    COUNT(*) as count
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger';

-- Check 4: RLS Policy status
SELECT 
    '🔒 RLS POLICIES' as component,
    policyname as policy_name,
    CASE 
        WHEN policyname = 'Allow trigger user creation' THEN '✅ CORRECT'
        WHEN policyname = 'Controlled profile creation' THEN '❌ OLD BLOCKING POLICY'
        ELSE '⚠️ OTHER'
    END as status
FROM pg_policies 
WHERE tablename = 'user_profiles' 
ORDER BY policyname;

-- Check 5: Recent user profiles (to see if any new users registered)
SELECT 
    '👥 RECENT USERS' as component,
    COUNT(*) as total_users,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as users_last_hour
FROM user_profiles;

-- Final status summary
SELECT 
    '🎯 AUTHENTICATION FIX STATUS' as summary,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM organizations WHERE name = 'demo-pharma-corp'
        ) > 0 
        AND (
            SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name = 'handle_new_user_trigger'
        ) > 0
        AND (
            SELECT COUNT(*) FROM pg_policies WHERE tablename = 'user_profiles' AND policyname = 'Allow trigger user creation'
        ) > 0
        THEN '✅ READY FOR USER REGISTRATION'
        ELSE '❌ NEEDS ATTENTION'
    END as status,
    NOW() as checked_at;

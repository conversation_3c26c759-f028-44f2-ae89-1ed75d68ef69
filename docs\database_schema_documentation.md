# VigiLens Database Schema Documentation

**Version:** 1.0  
**Date:** 2025-07-12  
**Database:** Supabase PostgreSQL  
**Compliance:** 21 CFR Part 11, HIPAA, GxP  

## Overview

The VigiLens database schema is designed for pharmaceutical compliance with direct Supabase integration, eliminating the need for a Python CRUD wrapper. The schema leverages Supabase's native capabilities including Row Level Security (RLS), real-time subscriptions, and auto-generated APIs.

## Architecture Principles

### 1. Supabase-First Design
- **Direct Frontend Integration**: All CRUD operations via Supabase auto-generated APIs
- **RLS-Based Security**: Database-level security policies replace backend authorization
- **Real-time Subscriptions**: WebSocket-based updates for all critical entities
- **Auto-generated TypeScript Types**: Seamless frontend integration

### 2. Pharmaceutical Compliance
- **21 CFR Part 11**: Electronic records and signatures with audit trails
- **HIPAA**: Protected health information with encryption and access controls
- **GxP**: Good practice guidelines with validation and change control
- **Multi-tenant**: Organization-based data isolation

### 3. Performance Optimization
- **Optimized Indexes**: B-tree and GIN indexes for common query patterns
- **Partial Indexes**: Filtered indexes for active records
- **Connection Pooling**: Supabase's built-in pgBouncer
- **Caching Strategy**: Edge Function caching for complex operations

## Core Entities

### 1. Organizations (Multi-tenant Root)
```sql
organizations (
    id UUID PRIMARY KEY,
    name VARCHAR(255) UNIQUE,
    subscription_tier subscription_tier,
    compliance_frameworks compliance_framework[],
    settings JSONB,
    -- ... additional fields
)
```

**Purpose**: Multi-tenant root entity for pharmaceutical organizations  
**RLS Policy**: Users can only access their organization's data  
**Real-time**: Organization settings changes  

### 2. User Profiles (Supabase Auth Integration)
```sql
user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    organization_id UUID REFERENCES organizations(id),
    role user_role,
    training_records JSONB,
    electronic_signature_id VARCHAR(255),
    -- ... additional fields
)
```

**Purpose**: User management with pharmaceutical compliance tracking  
**Critical**: Links to auth.users(id) for Supabase Auth integration  
**RLS Policy**: Organization-based access with role-based permissions  

### 3. Regulatory Documents (Core Entity)
```sql
regulatory_documents (
    id UUID PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id),
    title VARCHAR(500),
    document_type document_type,
    processing_status processing_status,
    ai_insights JSONB,
    compliance_score DECIMAL(5,2),
    -- ... additional fields
)
```

**Purpose**: Core document management with AI processing  
**RLS Policy**: Organization-based with assignment-based access  
**Real-time**: Status changes, AI processing updates  

### 4. Document Versions (Version Control)
```sql
document_versions (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES regulatory_documents(id),
    version_number VARCHAR(50),
    electronic_signatures JSONB,
    content_snapshot JSONB,
    -- ... additional fields
)
```

**Purpose**: 21 CFR Part 11 compliant version control  
**Features**: Electronic signatures, automatic versioning  
**Compliance**: Immutable version history  

### 5. Audit Trail (21 CFR Part 11)
```sql
audit_trail (
    id UUID PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id),
    action_type audit_action_type,
    data_integrity_hash VARCHAR(64),
    timestamp TIMESTAMPTZ IMMUTABLE,
    -- ... additional fields
)
```

**Purpose**: Comprehensive audit logging for compliance  
**Features**: Tamper-proof logging, integrity verification  
**Compliance**: 21 CFR Part 11 electronic records  

## Row Level Security (RLS) Policies

### Organization-Based Isolation
```sql
-- All tables with organization_id
CREATE POLICY "org_isolation" ON table_name
FOR ALL USING (organization_id = (auth.jwt() ->> 'organization_id')::UUID);
```

### Role-Based Access Control
```sql
-- Admin access to all organization data
CREATE POLICY "admin_access" ON regulatory_documents
FOR ALL USING (
    organization_id = (auth.jwt() ->> 'organization_id')::UUID 
    AND (auth.jwt() ->> 'role') = 'admin'
);
```

### Document Assignment Access
```sql
-- Users can access assigned documents
CREATE POLICY "document_assignment" ON regulatory_documents
FOR SELECT USING (
    organization_id = (auth.jwt() ->> 'organization_id')::UUID
    AND (created_by = auth.uid() OR auth.uid() = ANY(assigned_to))
);
```

## Real-time Subscriptions

### Document Processing Updates
```javascript
// Channel: document_updates:{organization_id}
supabase
  .channel('document_processing')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'regulatory_documents',
    filter: `organization_id=eq.${orgId}`
  }, handleDocumentUpdate)
  .subscribe()
```

### Compliance Alerts
```javascript
// Channel: compliance_alerts:{organization_id}
supabase
  .channel('compliance_alerts')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'regulatory_documents',
    filter: `compliance_score=lt.80`
  }, handleComplianceAlert)
  .subscribe()
```

## Performance Optimization

### Index Strategy
- **B-tree Indexes**: Foreign keys, timestamps, status fields
- **GIN Indexes**: JSONB columns, arrays, full-text search
- **Partial Indexes**: Active records, current versions
- **Composite Indexes**: Common query patterns

### Query Optimization
```sql
-- Optimized document queries
SELECT * FROM regulatory_documents 
WHERE organization_id = $1 
AND status = 'active' 
AND document_type = 'fda_guidance'
ORDER BY created_at DESC;

-- Uses: idx_regulatory_documents_org_status
```

## Migration Files

1. **001_enable_extensions.sql** - PostgreSQL extensions and custom types
2. **002_create_organizations.sql** - Multi-tenant foundation
3. **003_create_user_profiles.sql** - Supabase Auth integration
4. **004_create_user_roles.sql** - RBAC system
5. **005_create_regulatory_documents.sql** - Core document management
6. **006_create_document_versions.sql** - Version control
7. **007_create_audit_trail.sql** - 21 CFR Part 11 compliance
8. **008_create_triggers_and_functions.sql** - Real-time and audit integration

## Security Features

### Data Encryption
- **At Rest**: AES-256 encryption via Supabase
- **In Transit**: TLS 1.3 for all connections
- **Application**: Encrypted sensitive fields (MFA secrets, backup codes)

### Access Control
- **RLS Policies**: Database-level security enforcement
- **JWT Claims**: Organization and role-based access
- **Multi-factor Authentication**: TOTP and SMS support

### Audit Compliance
- **Immutable Logs**: Tamper-proof audit trail
- **Data Integrity**: SHA-256 hashes for verification
- **Electronic Signatures**: 21 CFR Part 11 compliant

## API Integration

### Direct Supabase Operations
```typescript
// Document CRUD operations
const { data, error } = await supabase
  .from('regulatory_documents')
  .select('*')
  .eq('organization_id', orgId)
  .eq('status', 'active');

// Real-time subscriptions
const subscription = supabase
  .channel('documents')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'regulatory_documents'
  }, handleChange)
  .subscribe();
```

### Edge Functions for Business Logic
```typescript
// Complex compliance scoring
const { data } = await supabase.functions.invoke('calculate-compliance-score', {
  body: { documentId, frameworkId }
});
```

## Deployment and Maintenance

### Migration Deployment
```bash
# Apply migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > types/database.ts
```

### Health Monitoring
```sql
-- Database health check
SELECT * FROM check_database_health();

-- Audit integrity verification
SELECT * FROM verify_audit_integrity();
```

### Data Retention
- **Audit Records**: 7 years (21 CFR Part 11 requirement)
- **Document Versions**: Permanent retention
- **User Activity**: 3 years minimum
- **System Logs**: 1 year

## Compliance Validation

### 21 CFR Part 11 Requirements
- ✅ Electronic records with audit trails
- ✅ Electronic signatures with metadata
- ✅ Data integrity controls (ALCOA+)
- ✅ Access controls and user authentication
- ✅ Tamper-proof logging

### HIPAA Requirements
- ✅ Data encryption at rest and in transit
- ✅ Access controls and audit logs
- ✅ User authentication and authorization
- ✅ Data backup and recovery procedures

### GxP Requirements
- ✅ Validated database schema
- ✅ Change control procedures
- ✅ Risk management and assessment
- ✅ Quality assurance processes

## Next Steps

1. **Deploy Migrations**: Apply all migration files to Supabase
2. **Configure RLS**: Test and validate all security policies
3. **Generate Types**: Create TypeScript definitions for frontend
4. **Test Real-time**: Validate subscription functionality
5. **Performance Testing**: Benchmark query performance
6. **Security Audit**: Validate compliance requirements

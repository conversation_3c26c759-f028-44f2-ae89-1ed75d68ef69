#!/usr/bin/env python3
"""
Script to check the knowledge base status
"""

import asyncio
from services.ai.qdrant_store import QdrantVectorStore, QdrantConfig

async def check_knowledge_base():
    """Check the current status of the knowledge base"""
    try:
        print("Starting vector store check...")
        
        # Configure for FDA knowledge base
        config = QdrantConfig(
            local_path="./qdrant_fda_db",
            collection_name="fda_compliance_docs"
        )
        
        # Get vector store instance
        vs = QdrantVectorStore(config)
        print("Vector store configured for FDA knowledge base")
        
        # Connect to the vector store
        await vs.connect()
        print("Vector store connected")
        
        # Get collection information
        info = await vs.get_collection_info()
        print(f"Knowledge base info: {info}")
        
        # Perform health check
        health = await vs.health_check()
        print(f"Health check: {health}")
        
        return info
        
    except Exception as e:
        print(f"Error checking knowledge base: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("<PERSON><PERSON><PERSON> starting...")
    try:
        result = asyncio.run(check_knowledge_base())
        print(f"Script completed with result: {result}")
    except Exception as e:
        print(f"Script failed with error: {e}")
        import traceback
        traceback.print_exc()
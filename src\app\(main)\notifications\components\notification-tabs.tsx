'use client'

import { Badge } from '@/components/ui-radix/badge'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui-radix/tabs'
import { cn } from '@/lib/utils'

interface NotificationTabsProps {
  readonly activeTab: string
  readonly onTabChange: (tab: string) => void
  readonly counts: {
    readonly all: number
    readonly unread: number
    readonly critical: number
  }
  readonly className?: string
}

interface TabConfig {
  readonly value: string
  readonly label: string
  readonly icon?: React.ComponentType<{ className?: string }>
  readonly countKey: keyof NotificationTabsProps['counts']
}

const tabConfigs: TabConfig[] = [
  {
    value: 'all',
    label: 'All Notifications',
    countKey: 'all'
  },
  {
    value: 'unread',
    label: 'Unread',
    countKey: 'unread'
  },

  {
    value: 'critical',
    label: 'Critical',
    countKey: 'critical'
  }
]

export function NotificationTabs({
  activeTab,
  onTabChange,
  counts,
  className
}: NotificationTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className={className}>
      <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
        {tabConfigs.map((tab) => {
          const count = counts[tab.countKey]

          return (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className={cn(
                'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',
                tab.value === 'unread' && 'justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm flex items-center'
              )}
            >
              {tab.label}
              {tab.value === 'unread' && count > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
                >
                  {count > 99 ? '99+' : count}
                </Badge>
              )}
            </TabsTrigger>
          )
        })}
      </TabsList>
    </Tabs>
  )
}

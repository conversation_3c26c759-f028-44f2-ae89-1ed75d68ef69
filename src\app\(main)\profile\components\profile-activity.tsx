'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Activity, CheckCircle, Clock, Download, FileText, Upload } from 'lucide-react'

interface ActivityItem {
  readonly action: string
  readonly target: string
  readonly timestamp: string
  readonly score?: number
  readonly type: 'success' | 'upload' | 'ai' | 'download'
}

interface ProfileActivityProps {
  readonly className?: string
}

const recentActivities: ActivityItem[] = [
  {
    action: 'Completed compliance check',
    target: 'Manufacturing SOP v3.2',
    timestamp: '2 hours ago',
    score: 98,
    type: 'success',
  },
  {
    action: 'Uploaded document',
    target: 'Quality Manual 2023',
    timestamp: '5 hours ago',
    type: 'upload',
  },
  {
    action: 'Asked AI Assistant',
    target: 'FDA process validation requirements',
    timestamp: '1 day ago',
    type: 'ai',
  },
  {
    action: 'Downloaded report',
    target: 'Q2 Compliance Summary',
    timestamp: '2 days ago',
    type: 'download',
  },
  {
    action: 'Completed compliance check',
    target: 'Batch Record Template',
    timestamp: '3 days ago',
    score: 85,
    type: 'success',
  },
]

function getActivityIcon(type: string) {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-4 w-4 text-success" />
    case 'upload':
      return <Upload className="h-4 w-4 text-info" />
    case 'ai':
      return <Activity className="h-4 w-4 text-primary" />
    case 'download':
      return <Download className="h-4 w-4 text-muted-foreground" />
    default:
      return <FileText className="h-4 w-4 text-muted-foreground" />
  }
}

export function ProfileActivity({ className }: ProfileActivityProps) {
  return (
    <Card className={cn('border border-border', className)}>
      <CardHeader>
        <CardTitle className="text-2xl font-semibold text-foreground flex items-center gap-2">
          <Clock className="h-6 w-6" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivities.map((activity, index) => (
            <div
              key={index}
              className="flex items-start space-x-4 p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors"
            >
              {getActivityIcon(activity.type)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground">
                  {activity.action}
                </p>
                <p className="text-sm text-muted-foreground truncate">
                  {activity.target}
                </p>
                <p className="text-xs text-muted-foreground">
                  {activity.timestamp}
                </p>
              </div>
              {activity.score && (
                <div className="text-right">
                  <p className="text-sm font-medium text-foreground">
                    {activity.score}%
                  </p>
                  <p className="text-xs text-muted-foreground">Score</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

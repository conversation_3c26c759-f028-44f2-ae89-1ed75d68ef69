import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Upload,
  Search,
  Download,
  Eye,
  Share,
  MoreHorizontal,
  FolderOpen,
  Filter,
  Grid,
  List,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";

export default function Documents() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const documents = [
    {
      id: 1,
      name: "FDA Process Validation Guidance 2023",
      type: "PDF",
      size: "2.4 MB",
      uploadDate: "2023-06-20",
      category: "Approved",
      status: "completed",
      complianceScore: 98,
      tags: ["FDA", "Process Validation", "GMP"],
      description:
        "Comprehensive guidance document for pharmaceutical process validation requirements including statistical methods and lifecycle approach.",
    },
    {
      id: 2,
      name: "Quality Management System SOP v3.2",
      type: "DOCX",
      size: "856 KB",
      uploadDate: "2023-06-19",
      category: "Under Review",
      status: "processing",
      complianceScore: 85,
      tags: ["QMS", "SOP", "ICH Q7"],
      description:
        "Standard Operating Procedure for quality management system implementation and maintenance across all manufacturing facilities.",
    },
    {
      id: 3,
      name: "Batch Record Template Q2 2023",
      type: "XLSX",
      size: "1.2 MB",
      uploadDate: "2023-06-18",
      category: "Approved",
      status: "completed",
      complianceScore: 96,
      tags: ["Batch Records", "Manufacturing", "Data Integrity"],
      description:
        "Updated batch record template incorporating new data integrity requirements and electronic signature protocols.",
    },
    {
      id: 4,
      name: "Training Material: Data Integrity Guidelines",
      type: "PDF",
      size: "3.1 MB",
      uploadDate: "2023-06-17",
      category: "Draft",
      status: "needs_review",
      complianceScore: 78,
      tags: ["Training", "Data Integrity", "ALCOA+"],
      description:
        "Comprehensive training presentation covering ALCOA+ principles and practical implementation in pharmaceutical operations.",
    },
    {
      id: 5,
      name: "Deviation Investigation Report Template",
      type: "DOCX",
      size: "645 KB",
      uploadDate: "2023-06-16",
      category: "Template",
      status: "completed",
      complianceScore: 92,
      tags: ["Deviation", "CAPA", "Investigation"],
      description:
        "Standardized template for conducting thorough deviation investigations with root cause analysis and corrective action protocols.",
    },
  ];

  const metrics = [
    { label: "Total Documents", value: "342", icon: FileText },
    { label: "Pending Review", value: "18", icon: Clock },
    { label: "Compliance Score", value: "94%", icon: CheckCircle },
    { label: "Recent Uploads", value: "7", icon: Upload },
  ];

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    const matchesCategory =
      selectedCategory === "all" || doc.category === selectedCategory;
    const matchesStatus =
      selectedStatus === "all" || doc.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-success text-success-foreground">
            <CheckCircle className="mr-1 h-3 w-3" />
            Complete
          </Badge>
        );
      case "processing":
        return (
          <Badge className="bg-info text-info-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Processing
          </Badge>
        );
      case "needs_review":
        return (
          <Badge className="bg-warning text-warning-foreground">
            <AlertCircle className="mr-1 h-3 w-3" />
            Needs Review
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Document Library
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage and analyze your compliance documents with AI-powered
              insights
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={() => navigate("/compliance-check")}>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {metrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold text-foreground">
                      {metric.value}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {metric.label}
                    </p>
                  </div>
                  <metric.icon className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search documents by name, type, or content..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="Approved">Approved</SelectItem>
                    <SelectItem value="Under Review">Under Review</SelectItem>
                    <SelectItem value="Draft">Draft</SelectItem>
                    <SelectItem value="Template">Template</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="needs_review">Needs Review</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents Display */}
        <Tabs defaultValue="my-documents" className="space-y-4">
          <TabsList>
            <TabsTrigger value="my-documents">My Documents</TabsTrigger>
            <TabsTrigger value="shared">Shared with Me</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="my-documents">
            {viewMode === "grid" ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDocuments.map((doc) => (
                  <Card
                    key={doc.id}
                    className="transition-all duration-200 hover:shadow-md hover:border-primary/20 flex flex-col h-full cursor-pointer"
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-8 w-8 text-primary" />
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-foreground text-sm leading-tight">
                              {doc.name}
                            </h3>
                            <p className="text-xs text-muted-foreground">
                              {doc.type} • {doc.size}
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3 flex-grow flex flex-col">
                      <div className="flex items-center justify-between">
                        {getStatusBadge(doc.status)}
                        <Badge variant="outline">{doc.category}</Badge>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span>Compliance Score</span>
                          <span>{doc.complianceScore}%</span>
                        </div>
                        <Progress value={doc.complianceScore} className="h-1" />
                      </div>

                      <p className="text-xs text-muted-foreground line-clamp-2 flex-grow">
                        {doc.description}
                      </p>

                      <div className="flex flex-wrap gap-1">
                        {doc.tags.slice(0, 2).map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {doc.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{doc.tags.length - 2}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center justify-between pt-2 mt-auto">
                        <span className="text-xs text-muted-foreground">
                          {doc.uploadDate}
                        </span>
                        <div className="flex space-x-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Share className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Compliance</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">{doc.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {doc.size}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{doc.type}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{doc.category}</Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(doc.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={doc.complianceScore}
                              className="w-12 h-1"
                            />
                            <span className="text-xs">
                              {doc.complianceScore}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{doc.uploadDate}</TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="shared">
            <div className="text-center py-12">
              <Share className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Shared Documents</h3>
              <p className="text-muted-foreground">
                Documents shared with you by team members will appear here.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="recent">
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Recent Documents</h3>
              <p className="text-muted-foreground">
                Your recently accessed documents will be shown here.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="templates">
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Document Templates</h3>
              <p className="text-muted-foreground">
                Pre-approved document templates for compliance documentation.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

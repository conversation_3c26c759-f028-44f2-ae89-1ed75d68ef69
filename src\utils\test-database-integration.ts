/**
 * Database Integration Test Utility
 * Tests profile and settings updates to verify database integration is working
 */

import { createClient } from '@/utils/supabase/client'
import { UserProfileService } from '@/lib/supabase-services'

interface TestResult {
  test: string
  success: boolean
  error?: string
  data?: any
}

export class DatabaseIntegrationTester {
  private supabase = createClient()
  private profileService = new UserProfileService()
  private results: TestResult[] = []

  /**
   * Run all database integration tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Database Integration Tests...')
    
    this.results = []

    // Test 1: Check user authentication
    await this.testUserAuthentication()

    // Test 2: Test profile update
    await this.testProfileUpdate()

    // Test 3: Test notification preferences update
    await this.testNotificationPreferencesUpdate()

    // Test 4: Test super admin capabilities
    await this.testSuperAdminCapabilities()

    // Test 5: Test RLS policies
    await this.testRLSPolicies()

    console.log('🧪 Database Integration Tests Complete:', this.results)
    return this.results
  }

  /**
   * Test 1: Check user authentication
   */
  private async testUserAuthentication(): Promise<void> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser()
      
      if (error) {
        this.results.push({
          test: 'User Authentication',
          success: false,
          error: error.message
        })
        return
      }

      if (!user) {
        this.results.push({
          test: 'User Authentication',
          success: false,
          error: 'No authenticated user found'
        })
        return
      }

      this.results.push({
        test: 'User Authentication',
        success: true,
        data: { userId: user.id, email: user.email }
      })

    } catch (error) {
      this.results.push({
        test: 'User Authentication',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Test 2: Test profile update
   */
  private async testProfileUpdate(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) {
        this.results.push({
          test: 'Profile Update',
          success: false,
          error: 'No authenticated user'
        })
        return
      }

      // Test data
      const testData = {
        full_name: `Test User ${Date.now()}`,
        department: 'Quality Assurance',
        phone: '******-0123'
      }

      console.log('🔄 Testing profile update with data:', testData)

      const { data: updatedProfile, error } = await this.profileService.updateUserProfile(
        user.id,
        testData
      )

      if (error) {
        this.results.push({
          test: 'Profile Update',
          success: false,
          error: error.message,
          data: { testData, error }
        })
        return
      }

      this.results.push({
        test: 'Profile Update',
        success: true,
        data: { testData, updatedProfile }
      })

    } catch (error) {
      this.results.push({
        test: 'Profile Update',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Test 3: Test notification preferences update
   */
  private async testNotificationPreferencesUpdate(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) {
        this.results.push({
          test: 'Notification Preferences Update',
          success: false,
          error: 'No authenticated user'
        })
        return
      }

      // Test notification preferences
      const testNotificationPrefs = {
        email_notifications: true,
        push_notifications: false,
        regulatory_updates: true,
        document_processing_alerts: true,
        weekly_compliance_reports: false,
        critical_compliance_issues: true,
        processing_complete: true,
        digest_frequency: 'daily' as const
      }

      console.log('🔄 Testing notification preferences update:', testNotificationPrefs)

      const { data: updatedProfile, error } = await this.profileService.updateUserProfile(
        user.id,
        { notification_preferences: testNotificationPrefs }
      )

      if (error) {
        this.results.push({
          test: 'Notification Preferences Update',
          success: false,
          error: error.message,
          data: { testNotificationPrefs, error }
        })
        return
      }

      this.results.push({
        test: 'Notification Preferences Update',
        success: true,
        data: { testNotificationPrefs, updatedProfile }
      })

    } catch (error) {
      this.results.push({
        test: 'Notification Preferences Update',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Test 4: Test super admin capabilities
   */
  private async testSuperAdminCapabilities(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) {
        this.results.push({
          test: 'Super Admin Capabilities',
          success: false,
          error: 'No authenticated user'
        })
        return
      }

      // Get current user profile to check if super admin
      const { data: profile } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (!profile) {
        this.results.push({
          test: 'Super Admin Capabilities',
          success: false,
          error: 'No user profile found'
        })
        return
      }

      const isSuperAdmin = profile.role === 'super_admin'

      if (isSuperAdmin) {
        // Test super admin email update
        const testEmail = `test.${Date.now()}@example.com`
        
        console.log('🔄 Testing super admin email update:', testEmail)

        const { data: updatedProfile, error } = await this.profileService.updateUserProfile(
          user.id,
          { email: testEmail }
        )

        if (error) {
          this.results.push({
            test: 'Super Admin Capabilities',
            success: false,
            error: `Super admin email update failed: ${error.message}`,
            data: { isSuperAdmin, testEmail, error }
          })
          return
        }

        this.results.push({
          test: 'Super Admin Capabilities',
          success: true,
          data: { isSuperAdmin, testEmail, updatedProfile }
        })
      } else {
        this.results.push({
          test: 'Super Admin Capabilities',
          success: true,
          data: { isSuperAdmin, message: 'User is not super admin - test skipped' }
        })
      }

    } catch (error) {
      this.results.push({
        test: 'Super Admin Capabilities',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Test 5: Test RLS policies
   */
  private async testRLSPolicies(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) {
        this.results.push({
          test: 'RLS Policies',
          success: false,
          error: 'No authenticated user'
        })
        return
      }

      // Test if user can read their own profile
      const { data: profile, error: readError } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (readError) {
        this.results.push({
          test: 'RLS Policies',
          success: false,
          error: `RLS read policy failed: ${readError.message}`,
          data: { readError }
        })
        return
      }

      // Test if user can update their own profile
      const { error: updateError } = await this.supabase
        .from('user_profiles')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', user.id)

      if (updateError) {
        this.results.push({
          test: 'RLS Policies',
          success: false,
          error: `RLS update policy failed: ${updateError.message}`,
          data: { updateError }
        })
        return
      }

      this.results.push({
        test: 'RLS Policies',
        success: true,
        data: { message: 'RLS policies working correctly', profile }
      })

    } catch (error) {
      this.results.push({
        test: 'RLS Policies',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get test results summary
   */
  getTestSummary(): { total: number; passed: number; failed: number; results: TestResult[] } {
    const total = this.results.length
    const passed = this.results.filter(r => r.success).length
    const failed = total - passed

    return { total, passed, failed, results: this.results }
  }
}

// Export a function to run tests from browser console
export async function runDatabaseIntegrationTests(): Promise<void> {
  const tester = new DatabaseIntegrationTester()
  const results = await tester.runAllTests()
  const summary = tester.getTestSummary()

  console.log('📊 Database Integration Test Summary:', summary)
  
  if (summary.failed > 0) {
    console.error('❌ Some tests failed. Check the results above for details.')
  } else {
    console.log('✅ All database integration tests passed!')
  }
}

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).runDatabaseIntegrationTests = runDatabaseIntegrationTests
}

-- Migration: 003_create_user_roles.sql
-- Description: Create user roles table for granular permission management
-- Created: 2025-01-11
-- Dependencies: 002_create_user_profiles.sql

-- Create user roles table
CREATE TABLE user_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
  role_name VARCHAR(50) NOT NULL CHECK (role_name IN ('admin', 'manager', 'user', 'viewer', 'auditor', 'compliance_officer')),
  permissions JSONB DEFAULT '[]',
  granted_by UUID REFERENCES user_profiles(id),
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_name ON user_roles(role_name);
CREATE INDEX idx_user_roles_is_active ON user_roles(is_active);
CREATE INDEX idx_user_roles_expires_at ON user_roles(expires_at);
CREATE INDEX idx_user_roles_permissions ON user_roles USING GIN(permissions);

-- Ensure unique active role per user
CREATE UNIQUE INDEX idx_user_roles_unique_active 
ON user_roles(user_id, role_name) 
WHERE is_active = true;

-- Enable Row Level Security
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own roles" ON user_roles
  FOR SELECT USING (
    user_id = auth.uid()
  );

CREATE POLICY "Admins can manage roles in their organization" ON user_roles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles up1, user_profiles up2
      WHERE up1.id = auth.uid() 
      AND up1.role = 'admin'
      AND up2.id = user_roles.user_id
      AND up1.organization_id = up2.organization_id
    )
  );

-- Create trigger for updated_at
CREATE TRIGGER update_user_roles_updated_at
    BEFORE UPDATE ON user_roles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to check if role has expired
CREATE OR REPLACE FUNCTION check_role_expiration()
RETURNS TRIGGER AS $$
BEGIN
  -- Automatically deactivate expired roles
  IF NEW.expires_at IS NOT NULL AND NEW.expires_at <= NOW() THEN
    NEW.is_active = false;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for role expiration check
CREATE TRIGGER check_user_role_expiration
    BEFORE INSERT OR UPDATE ON user_roles
    FOR EACH ROW
    EXECUTE FUNCTION check_role_expiration();

-- Create function to get user permissions
CREATE OR REPLACE FUNCTION get_user_permissions(user_uuid UUID)
RETURNS JSONB AS $$
DECLARE
  user_perms JSONB := '[]';
  role_perms JSONB;
BEGIN
  -- Get all active permissions for the user
  SELECT COALESCE(jsonb_agg(DISTINCT perm), '[]')
  INTO user_perms
  FROM (
    SELECT jsonb_array_elements_text(permissions) as perm
    FROM user_roles
    WHERE user_id = user_uuid 
    AND is_active = true
    AND (expires_at IS NULL OR expires_at > NOW())
  ) perms;
  
  RETURN user_perms;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check user permission
CREATE OR REPLACE FUNCTION user_has_permission(user_uuid UUID, permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  has_perm BOOLEAN := false;
BEGIN
  SELECT EXISTS(
    SELECT 1
    FROM user_roles
    WHERE user_id = user_uuid 
    AND is_active = true
    AND (expires_at IS NULL OR expires_at > NOW())
    AND permissions ? permission_name
  ) INTO has_perm;
  
  RETURN has_perm;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default role permissions
INSERT INTO user_roles (user_id, role_name, permissions, granted_by)
SELECT 
  up.id,
  'admin',
  '[
    "documents.read",
    "documents.write",
    "documents.delete",
    "users.read",
    "users.write",
    "users.delete",
    "analytics.read",
    "audit.read",
    "settings.write",
    "compliance.manage",
    "ai.configure"
  ]'::jsonb,
  up.id
FROM user_profiles up
JOIN organizations o ON up.organization_id = o.id
WHERE o.domain = 'demo.vigilens.com'
AND up.role = 'admin'
LIMIT 1;

-- Create view for user permissions
CREATE VIEW user_permissions_view AS
SELECT 
  up.id as user_id,
  up.email,
  up.full_name,
  up.role as primary_role,
  ur.role_name,
  ur.permissions,
  ur.is_active as role_active,
  ur.expires_at,
  o.name as organization_name
FROM user_profiles up
LEFT JOIN user_roles ur ON up.id = ur.user_id
JOIN organizations o ON up.organization_id = o.id
WHERE ur.is_active = true
AND (ur.expires_at IS NULL OR ur.expires_at > NOW());

-- Add comments for documentation
COMMENT ON TABLE user_roles IS 'Granular role-based permissions for pharmaceutical compliance users';
COMMENT ON COLUMN user_roles.role_name IS 'Role type: admin, manager, user, viewer, auditor, compliance_officer';
COMMENT ON COLUMN user_roles.permissions IS 'JSON array of specific permissions for this role';
COMMENT ON COLUMN user_roles.expires_at IS 'Optional expiration date for temporary roles';
COMMENT ON FUNCTION get_user_permissions(UUID) IS 'Returns all active permissions for a user';
COMMENT ON FUNCTION user_has_permission(UUID, TEXT) IS 'Checks if user has specific permission';
COMMENT ON VIEW user_permissions_view IS 'Consolidated view of user roles and permissions';
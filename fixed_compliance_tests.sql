-- Fixed Pharmaceutical Compliance Tests
-- Run these to validate the compliance features properly

-- Test 1: Multi-tenant data isolation (FIXED)
SELECT 
    'MULTI-TENANT TEST' as test_name,
    up.email,
    o.display_name as organization,
    COUNT(rd.*) as visible_documents,
    MAX(up.created_at) as user_created_at
FROM user_profiles up
JOIN organizations o ON up.organization_id = o.id
LEFT JOIN regulatory_documents rd ON rd.organization_id = o.id
GROUP BY up.email, o.display_name
ORDER BY MAX(up.created_at) DESC;

-- Test 2: Check audit trail data integrity issue
SELECT 
    'AUDIT INTEGRITY ISSUE' as test_name,
    action_type,
    action_description,
    data_integrity_hash,
    CASE 
        WHEN data_integrity_hash IS NULL THEN 'MISSING HASH'
        ELSE 'HAS HASH'
    END as hash_status
FROM audit_trail
ORDER BY timestamp DESC;

-- Test 3: Create test documents with proper user assignment
INSERT INTO regulatory_documents (
    organization_id,
    title,
    document_type,
    description,
    status,
    regulatory_agencies,
    compliance_frameworks,
    gxp_relevant,
    compliance_score,
    risk_level,
    created_by
) 
SELECT 
    up.organization_id,
    'SOP-001: Manufacturing Process - ' || o.display_name,
    'sop'::document_type,
    'Standard Operating Procedure for pharmaceutical manufacturing compliance',
    'draft'::document_status,
    ARRAY['fda']::regulatory_agency[],
    ARRAY['fda_21_cfr_part_11', 'gxp']::compliance_framework[],
    true,
    85.5,
    'medium'::risk_level,
    up.id
FROM user_profiles up
JOIN organizations o ON o.id = up.organization_id
WHERE up.email LIKE '%@%'  -- All real users
LIMIT 3;

-- Test 4: Create more test documents
INSERT INTO regulatory_documents (
    organization_id,
    title,
    document_type,
    description,
    status,
    regulatory_agencies,
    compliance_frameworks,
    gxp_relevant,
    compliance_score,
    risk_level,
    created_by
) 
SELECT 
    up.organization_id,
    'CAPA-001: Corrective Action - ' || o.display_name,
    'capa'::document_type,
    'Corrective and Preventive Action for quality improvement',
    'under_review'::document_status,
    ARRAY['fda', 'ema']::regulatory_agency[],
    ARRAY['fda_21_cfr_part_11', 'eu_gmp']::compliance_framework[],
    true,
    92.0,
    'high'::risk_level,
    up.id
FROM user_profiles up
JOIN organizations o ON o.id = up.organization_id
WHERE up.email LIKE '%@%'
LIMIT 2;

-- Test 5: Verify documents were created
SELECT 
    'DOCUMENTS CREATED' as test_name,
    rd.title,
    rd.document_type,
    rd.status,
    rd.compliance_score,
    rd.risk_level,
    o.display_name as organization,
    up.email as created_by_email
FROM regulatory_documents rd
JOIN organizations o ON o.id = rd.organization_id
JOIN user_profiles up ON up.id = rd.created_by
ORDER BY rd.created_at DESC;

-- Test 6: Create electronic signatures for the documents
INSERT INTO electronic_signatures (
    organization_id,
    document_id,
    signer_id,
    signer_name,
    signer_title,
    signature_type,
    signature_meaning,
    signature_reason,
    authentication_method,
    authentication_timestamp,
    signature_hash,
    document_hash_at_signing
)
SELECT 
    rd.organization_id,
    rd.id,
    rd.created_by,
    up.full_name,
    'Quality Manager',
    'approval'::signature_type,
    'I hereby approve this document for use in pharmaceutical manufacturing operations in accordance with 21 CFR Part 11',
    'Document review completed and approved for implementation',
    'password',
    NOW(),
    encode(digest(rd.id::text || up.id::text || NOW()::text || 'approval_signature', 'sha256'), 'hex'),
    encode(digest(rd.title || rd.description || rd.status::text, 'sha256'), 'hex')
FROM regulatory_documents rd
JOIN user_profiles up ON up.id = rd.created_by
WHERE rd.status IN ('draft', 'under_review')
LIMIT 3;

-- Test 7: Verify electronic signatures
SELECT 
    'ELECTRONIC SIGNATURES' as test_name,
    es.signature_type,
    es.signer_name,
    es.signature_meaning,
    es.authentication_method,
    rd.title as document_title,
    o.display_name as organization,
    es.signed_at
FROM electronic_signatures es
JOIN regulatory_documents rd ON rd.id = es.document_id
JOIN organizations o ON o.id = es.organization_id
ORDER BY es.signed_at DESC;

-- Test 8: Test audit trail with proper logging
SELECT 
    'AUDIT TRAIL COMPREHENSIVE' as test_name,
    at.action_type,
    at.action_description,
    at.resource_type,
    at.gxp_relevant,
    at.risk_level,
    CASE 
        WHEN at.data_integrity_hash IS NOT NULL THEN 'HAS HASH'
        ELSE 'MISSING HASH'
    END as integrity_status,
    o.display_name as organization,
    at.timestamp
FROM audit_trail at
JOIN organizations o ON o.id = at.organization_id
ORDER BY at.timestamp DESC
LIMIT 10;

-- Test 9: 21 CFR Part 11 Compliance Summary
SELECT 
    'CFR PART 11 COMPLIANCE SUMMARY' as validation_type,
    'Electronic Signatures' as feature,
    COUNT(*) as total_signatures,
    COUNT(CASE WHEN signature_hash IS NOT NULL THEN 1 END) as valid_signatures,
    COUNT(CASE WHEN signature_meaning IS NOT NULL THEN 1 END) as signatures_with_meaning,
    COUNT(CASE WHEN authentication_method IS NOT NULL THEN 1 END) as authenticated_signatures
FROM electronic_signatures;

-- Test 10: Document compliance metrics
SELECT 
    'DOCUMENT COMPLIANCE METRICS' as test_name,
    document_type,
    COUNT(*) as total_documents,
    AVG(compliance_score) as avg_compliance_score,
    COUNT(CASE WHEN gxp_relevant = true THEN 1 END) as gxp_relevant_docs,
    COUNT(CASE WHEN risk_level = 'high' THEN 1 END) as high_risk_docs
FROM regulatory_documents
GROUP BY document_type
ORDER BY document_type;

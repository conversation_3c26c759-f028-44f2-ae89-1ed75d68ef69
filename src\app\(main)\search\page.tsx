'use client'

import { <PERSON><PERSON><PERSON>, Bot, Download, ExternalLink, Eye, FileSearch, Filter, History, Lightbulb, Search, Settings, Sparkles, Zap } from 'lucide-react'
import { useState } from 'react'

import { Badge } from '@/components/ui-radix/badge'
import { <PERSON><PERSON> } from '@/components/ui-radix/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui-radix/card'
import { Input } from '@/components/ui-radix/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui-radix/select'
import { Skeleton } from '@/components/ui-radix/skeleton'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui-radix/tabs'
import { Textarea } from '@/components/ui-radix/textarea'
import { usePageMetadata } from '@/hooks/use-page-metadata'

// Types according to specification
type SearchType = 'all' | 'regulations' | 'guidelines' | 'templates' | 'sops'

interface SearchResult {
  id: string
  title: string
  type: 'Document' | 'Guideline' | 'Template' | 'International Standard'
  agency: string
  relevance: number
  summary: string
  url: string
  publishedDate: string
  tags: string[]
  source: string
}

interface AIInsight {
  query: string
  insight: string
  confidence: number
  sources: Array<{ title: string; url: string }>
}

export default function SearchPage() {
  usePageMetadata('Search', 'Search across all compliance data and documents')

  // React State according to specification
  const [searchQuery, setSearchQuery] = useState('')
  const [aiQuery, setAiQuery] = useState('')
  const [searchType, setSearchType] = useState<SearchType>('all')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([])
  const [activeTab, setActiveTab] = useState('standard')

  // Mock data for demonstration
  const quickSuggestions = [
    'GDPR compliance requirements',
    'ISO 27001 controls',
    'SOX reporting templates',
    'Data retention policies',
  ]

  const recentSearches = [
    'GDPR Article 25 requirements',
    'ISO 27001 risk assessment',
    'PCI DSS documentation',
    'SOX internal controls',
  ]

  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'GDPR Compliance Guide',
      type: 'Guideline',
      agency: 'European Commission',
      relevance: 95,
      summary: 'Comprehensive guide to GDPR compliance requirements for data protection and privacy.',
      url: 'https://example.com/gdpr-guide',
      publishedDate: '2024-01-15',
      tags: ['GDPR', 'Privacy', 'Data Protection'],
      source: 'EU Official Journal',
    },
    {
      id: '2',
      title: 'ISO 27001 Implementation Template',
      type: 'Template',
      agency: 'ISO',
      relevance: 88,
      summary: 'Standard template for implementing ISO 27001 information security management systems.',
      url: 'https://example.com/iso-template',
      publishedDate: '2024-02-10',
      tags: ['ISO 27001', 'Security', 'ISMS'],
      source: 'ISO Standards',
    },
  ]

  const mockInsights: AIInsight[] = [
    {
      query: 'GDPR compliance requirements',
      insight: 'GDPR requires organizations to implement data protection by design and by default, conduct privacy impact assessments for high-risk processing, and ensure lawful basis for all data processing activities.',
      confidence: 92,
      sources: [
        { title: 'GDPR Article 25', url: 'https://example.com/gdpr-art25' },
        { title: 'Privacy Impact Assessment Guide', url: 'https://example.com/pia-guide' },
      ],
    },
  ]

  // Search handlers according to specification
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      return
    }

    setIsSearching(true)
    // Simulate API call with 1.5s delay as per spec
    setTimeout(() => {
      setSearchResults(mockResults)
      setIsSearching(false)
    }, 1500)
  }

  const handleAiSearch = async () => {
    if (!aiQuery.trim()) {
      return
    }

    setIsSearching(true)
    // Simulate AI API call with 2s delay as per spec
    setTimeout(() => {
      setAiInsights(mockInsights)
      setIsSearching(false)
    }, 2000)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (activeTab === 'standard') {
        handleSearch()
      } else {
        handleAiSearch()
      }
    }
  }

  const handleQuickSuggestion = (suggestion: string) => {
    setSearchQuery(suggestion)
  }

  const handleRecentSearch = (search: string) => {
    setSearchQuery(search)
  }

  return (
    <div className="space-y-6">
      {/* Header bar according to specification */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">AI-Powered Search</h1>
          <p className="text-muted-foreground mt-1 text-sm">
            Search through regulatory documents, compliance data, and updates with intelligent AI assistance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <History className="h-4 w-4 mr-2" />
            Search History
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Advanced Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Search Settings
          </Button>
        </div>
      </div>

      {/* Search Mode Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="standard">
            <Search className="h-4 w-4 mr-2" />
            Standard Search
          </TabsTrigger>
          <TabsTrigger value="ai">
            <Sparkles className="h-4 w-4 mr-2" />
            AI Assistant
          </TabsTrigger>
        </TabsList>

        {/* Standard Search Panel */}
        <TabsContent value="standard" className="space-y-6">
          <Card className="p-6">
            <div className="space-y-4">
              {/* Search Input Row - responsive design */}
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Enter your search query or ask a question..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="w-full"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={searchType} onValueChange={(value: SearchType) => setSearchType(value)}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="All Document Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Document Types</SelectItem>
                      <SelectItem value="regulations">Regulations</SelectItem>
                      <SelectItem value="guidelines">Guidelines</SelectItem>
                      <SelectItem value="templates">Templates</SelectItem>
                      <SelectItem value="sops">SOPs</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleSearch}
                    disabled={isSearching || !searchQuery.trim()}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>
              </div>

              {/* Quick Search Suggestions */}
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Quick suggestions:</p>
                <div className="flex flex-wrap gap-2">
                  {quickSuggestions.map((suggestion) => (
                    <Button
                      key={suggestion}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickSuggestion(suggestion)}
                      className="text-xs"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Dynamic Results List */}
          {isSearching && activeTab === 'standard' && (
            <Card className="p-6">
              <div className="space-y-4">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </Card>
          )}

          {searchResults.length > 0 && !isSearching && (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Found {searchResults.length} results for &ldquo;{searchQuery}&rdquo;
              </p>
              {searchResults.map((result) => (
                <Card
                  key={result.id}
                  className="p-6 hover:shadow-md hover:border-primary/20 transition-all"
                >
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <h3 className="font-semibold text-foreground">{result.title}</h3>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Badge variant="outline">{result.type}</Badge>
                          <span>•</span>
                          <span>{result.agency}</span>
                          <span>•</span>
                          <span>{result.relevance}% relevance</span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Source
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {result.summary}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {result.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* AI Assistant Search Panel */}
        <TabsContent value="ai" className="space-y-6">
          <Card className="p-6">
            <div className="flex flex-col space-y-1.5 mb-4">
              <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center">
                <Bot className="h-5 w-5 mr-2" />
                AI Compliance Assistant
              </h3>
            </div>
            <div className="flex space-x-2 mb-4">
              <Badge variant="secondary" className="inline-flex items-center rounded-full">
                <Zap className="h-3 w-3 mr-1" />
                Powered by AI
              </Badge>
              <Badge variant="secondary" className="inline-flex items-center rounded-full">
                Real-time Regulatory Data
              </Badge>
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Ask AI Assistant
                </label>
                <Textarea
                  placeholder="For example: 'What are the latest FDA requirements for process validation?' or 'How do I implement data integrity in my QMS?'"
                  value={aiQuery}
                  onChange={(e) => setAiQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="min-h-[100px] pt-3 mt-3 mr-3 mb-3"
                />
              </div>
              <Button
                onClick={handleAiSearch}
                disabled={isSearching || !aiQuery.trim()}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Ask AI
              </Button>
            </div>
          </Card>

          {/* AI Insights List */}
          {isSearching && activeTab === 'ai' && (
            <Card className="p-4">
              <div className="space-y-4">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </Card>
          )}

          {aiInsights.length > 0 && !isSearching && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">Recent AI Insights</h2>
              {aiInsights.map((insight, index) => (
                <Card key={index} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-foreground">{insight.query}</h3>
                      <Badge variant="outline">
                        {insight.confidence}% confidence
                      </Badge>
                    </div>
                    <p className="text-sm text-foreground">{insight.insight}</p>
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">Sources:</p>
                      <div className="space-y-1">
                        {insight.sources.map((source, idx) => (
                          <Button
                            key={idx}
                            variant="outline"
                            size="sm"
                            className="h-auto p-2 justify-start text-xs"
                          >
                            <ExternalLink className="h-3 w-3 mr-2" />
                            {source.title}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Recent AI Insights Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Recent AI Insights</h2>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <div className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <p className="font-medium text-sm">What are the key differences between FDA and EMA process validation requirements?</p>
                    <Badge variant="secondary" className="text-xs">94% confidence</Badge>
                  </div>
                  <div className="bg-muted/50 border border-border p-3 rounded-md">
                    <p className="text-sm text-foreground flex items-center">
                      <Lightbulb className="h-4 w-4 mr-2 text-warning" />
                      The main differences lie in statistical approaches, with FDA emphasizing lifecycle approach while EMA focuses more on risk-based validation strategies.
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="h-10 text-sm">
                      <BookOpen className="h-4 w-4 mr-2" />
                      View Sources
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 text-sm">
                      Ask Follow-up
                    </Button>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <div className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <p className="font-medium text-sm">How do I implement continuous process verification?</p>
                    <Badge variant="secondary" className="text-xs">89% confidence</Badge>
                  </div>
                  <div className="bg-muted/50 border border-border p-3 rounded-md">
                    <p className="text-sm text-foreground flex items-center">
                      <Lightbulb className="h-4 w-4 mr-2 text-warning" />
                      Continuous process verification requires real-time monitoring of process parameters, statistical trend analysis, and automated alert systems for deviations.
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="h-10 text-sm">
                      <BookOpen className="h-4 w-4 mr-2" />
                      View Sources
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 text-sm">
                      Ask Follow-up
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Recent Searches Card */}
      <Card className="p-6">
        <CardHeader className="px-0 pt-0">
          <CardTitle className="text-lg flex items-center text-foreground">
            <History className="h-4 w-4 mr-2" />
            Recent Searches
          </CardTitle>
        </CardHeader>
        <CardContent className="px-0 pb-0">
          <div className="space-y-2">
            {recentSearches.map((search) => (
              <Button
                key={search}
                variant="ghost"
                className="justify-start h-auto p-2 w-full"
                onClick={() => handleRecentSearch(search)}
              >
                <FileSearch className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">{search}</span>
              </Button>
            ))}
      </div>
        </CardContent>
      </Card>
    </div>
  )
}

'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'

import { SearchFilters } from './components/search-filters'
import { UpdatesHeader } from './components/updates-header'
import { UpdatesMetrics } from './components/updates-metrics'
import { UpdatesTabs } from './components/updates-tabs'
import { useUpdatesData } from './hooks/use-updates-data'

/**
 * Updates Page - AI Compliance Platform (Migration Plan Phase 3.4 Complete)
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper state management with custom hooks
 *
 * Migrated from: src/pages/Updates.tsx (793 lines)
 * Broken down into 7+ components for maintainability:
 * - UpdatesHeader: Page header with actions
 * - UpdatesMetrics: Metrics dashboard
 * - SearchFilters: Search and filter controls
 * - UpdateCard: Individual update display
 * - UpdatesTabs: Tabbed navigation and content
 * - use-updates-data: Data management hook
 */
export default function UpdatesPage() {
  usePageMetadata(
    'Regulatory Updates',
    'Stay informed with the latest regulatory changes and compliance requirements',
  )

  const {
    updates,
    metrics,
    filters,
    bookmarkedUpdates,
    activeTab,
    setSearchQuery,
    setSelectedAgency,
    setSelectedSeverity,
    setSortBy,
    setActiveTab,
    toggleBookmark,
    handleExport,
    handleRefresh,
    handleShare,
    handleView,
  } = useUpdatesData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <UpdatesHeader onExport={handleExport} onRefresh={handleRefresh} />

      {/* Metrics Cards */}
      <UpdatesMetrics metrics={metrics} />

      {/* Search and Filters */}
      <SearchFilters
        filters={filters}
        onSearchChange={setSearchQuery}
        onAgencyChange={setSelectedAgency}
        onSeverityChange={setSelectedSeverity}
        onSortChange={setSortBy}
      />

      {/* Updates List with Tabs */}
      <UpdatesTabs
        updates={updates}
        bookmarkedUpdates={bookmarkedUpdates}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onBookmark={toggleBookmark}
        onShare={handleShare}
        onView={handleView}
      />
    </div>
  )
}

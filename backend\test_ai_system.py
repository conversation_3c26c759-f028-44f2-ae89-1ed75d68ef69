#!/usr/bin/env python3
"""
Test script to verify AI system prerequisites are met.
Tests OpenRouter API, ChromaDB, and all AI components.
"""

import asyncio
import os
import sys
import json
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_environment_variables():
    """Test that all required environment variables are set."""
    print("🔍 Testing Environment Variables...")
    
    required_vars = [
        "OPENROUTER_API_KEY",
        "OPENROUTER_BASE_URL", 
        "OPENROUTER_MODEL"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == "your_openrouter_api_key_here":
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: {'*' * 10}{value[-4:]}")
    
    if missing_vars:
        print(f"  ❌ Missing variables: {missing_vars}")
        return False
    
    print("  ✅ All environment variables configured")
    return True


async def test_ai_client():
    """Test OpenRouter AI client functionality."""
    print("\n🤖 Testing OpenRouter AI Client...")
    
    try:
        from services.ai.client import get_ai_client
        
        # Get AI client
        client = await get_ai_client()
        print(f"  ✅ AI client created: {type(client).__name__}")
        
        # Test simple text generation
        result = await client.generate_text(
            prompt="Hello, this is a test. Please respond with 'AI system working'.",
            system_prompt="You are a test assistant. Respond briefly.",
            temperature=0.1,
            max_tokens=50
        )
        
        if result["status"] == "success":
            print(f"  ✅ Text generation successful")
            print(f"  📝 Response: {result['content'][:100]}...")
            print(f"  🔧 Model: {result.get('model', 'unknown')}")
            return True
        else:
            print(f"  ❌ Text generation failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"  ❌ AI client test failed: {str(e)}")
        return False


async def test_vector_store():
    """Test ChromaDB vector store functionality."""
    print("\n📚 Testing ChromaDB Vector Store...")
    
    try:
        from services.ai.vector_store import get_vector_store
        
        # Get vector store
        store = await get_vector_store()
        print(f"  ✅ Vector store created: {type(store).__name__}")
        
        # Test adding a document
        test_doc = [{
            "content": "This is a test pharmaceutical document about 21 CFR Part 11 compliance.",
            "metadata": {
                "type": "test_document",
                "source": "test",
                "framework": "21_cfr_part_11"
            }
        }]
        
        await store.add_documents(test_doc)
        print("  ✅ Document added successfully")
        
        # Test searching
        results = await store.search(
            query="pharmaceutical compliance",
            n_results=3
        )
        
        print(f"  ✅ Search completed: {len(results)} results")
        
        # Get collection stats
        stats = await store.get_collection_stats()
        print(f"  📊 Collection stats: {stats['document_count']} documents")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Vector store test failed: {str(e)}")
        return False


async def test_document_analysis():
    """Test end-to-end document analysis."""
    print("\n📄 Testing Document Analysis...")
    
    try:
        from services.ai.client import get_ai_client
        
        client = await get_ai_client()
        
        # Test document analysis
        test_content = """
        This is a test Standard Operating Procedure (SOP) for pharmaceutical manufacturing.
        
        1. Purpose: This SOP defines the procedures for equipment validation.
        2. Scope: Applies to all manufacturing equipment.
        3. Responsibilities: Quality Assurance team is responsible for validation.
        4. Procedure: 
           - Perform Installation Qualification (IQ)
           - Perform Operational Qualification (OQ)
           - Perform Performance Qualification (PQ)
        5. Documentation: All validation activities must be documented per 21 CFR Part 11.
        """
        
        result = await client.analyze_document(
            document_content=test_content,
            document_type="SOP",
            analysis_type="compliance",
            context={"frameworks": ["21_cfr_part_11"]}
        )
        
        if result["status"] == "success":
            analysis = result.get("analysis", {})
            print("  ✅ Document analysis successful")
            print(f"  📊 Compliance Score: {analysis.get('compliance_score', 'N/A')}")
            print(f"  ⚠️  Risk Level: {analysis.get('risk_level', 'N/A')}")
            print(f"  🎯 Confidence: {analysis.get('confidence', 'N/A')}%")
            return True
        else:
            print(f"  ❌ Document analysis failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"  ❌ Document analysis test failed: {str(e)}")
        return False


async def test_api_endpoints():
    """Test AI API endpoints."""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        import httpx
        
        # Test if server is running
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/health")
                if response.status_code == 200:
                    print("  ✅ Server is running")
                else:
                    print("  ⚠️  Server not running - start with 'uvicorn main:app --reload'")
                    return False
            except httpx.ConnectError:
                print("  ⚠️  Server not running - start with 'uvicorn main:app --reload'")
                return False
            
            # Test AI endpoint
            test_payload = {
                "document_id": "test-123",
                "content": "Test pharmaceutical document for API testing.",
                "document_type": "SOP",
                "analysis_type": "compliance"
            }
            
            response = await client.post(
                "http://localhost:8000/api/v1/ai/analyze-document",
                json=test_payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print("  ✅ AI endpoint working")
                print(f"  📊 Analysis Type: {data.get('analysis_type')}")
                return True
            else:
                print(f"  ❌ AI endpoint failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"  ❌ API endpoint test failed: {str(e)}")
        return False


async def main():
    """Run all prerequisite tests."""
    print("🎯 VigiLens AI System Prerequisites Test")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("OpenRouter AI Client", test_ai_client),
        ("ChromaDB Vector Store", test_vector_store),
        ("Document Analysis", test_document_analysis),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"  ❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL PREREQUISITES MET - AI SYSTEM READY!")
        print("✅ You can proceed with data gathering and knowledge base population.")
    else:
        print("⚠️  SOME PREREQUISITES MISSING - PLEASE FIX BEFORE PROCEEDING")
        print("❌ Review the failed tests above and resolve issues.")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())

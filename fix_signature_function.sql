-- Fix the create_electronic_signature function to use correct column names
-- Run this to fix the function

-- First, let's check what columns actually exist in regulatory_documents
SELECT 
    'REGULATORY_DOCUMENTS COLUMNS' as info,
    array_agg(column_name ORDER BY ordinal_position) as columns
FROM information_schema.columns 
WHERE table_name = 'regulatory_documents' 
AND table_schema = 'public';

-- Fixed version of create_electronic_signature function
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing (using actual column names)
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(document_type::text, ''),
            COALESCE(status::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- If document not found, use a default hash
    IF document_hash IS NULL THEN
        document_hash := encode(digest(
            CONCAT(
                p_document_id::text,
                'document_not_found',
                NOW()::text
            ), 'sha256'
        ), 'hex');
    END IF;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Generate new signature ID
    signature_id := gen_random_uuid();
    
    -- Create electronic signature
    INSERT INTO electronic_signatures (
        id,
        organization_id,
        document_id,
        signer_id,
        signer_name,
        signature_type,
        signature_meaning,
        signature_reason,
        authentication_method,
        signature_hash,
        document_hash_at_signing,
        signature_metadata,
        signed_at,
        created_at,
        authentication_timestamp
    ) VALUES (
        signature_id,
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        p_signature_reason,
        p_authentication_method,
        signature_hash,
        document_hash,
        p_signature_metadata,
        NOW(),
        NOW(),
        NOW()
    );
    
    -- Log audit event if log_audit_event function exists
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'log_audit_event') THEN
        PERFORM log_audit_event(
            p_organization_id,
            p_signer_id,
            'sign'::audit_action_type,
            'Electronic signature created: ' || p_signature_meaning,
            'signature',
            signature_id,
            'Electronic Signature',
            NULL,
            jsonb_build_object(
                'signature_type', p_signature_type,
                'authentication_method', p_authentication_method,
                'document_id', p_document_id
            )
        );
    END IF;
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;

-- Test the fixed function
DO $$
DECLARE
    test_org_id UUID;
    test_user_id UUID;
    test_doc_id UUID;
    signature_id UUID;
BEGIN
    -- Get test organization
    SELECT id INTO test_org_id 
    FROM organizations 
    LIMIT 1;
    
    IF test_org_id IS NOT NULL THEN
        -- Get test user
        SELECT id INTO test_user_id 
        FROM user_profiles 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        -- Get test document
        SELECT id INTO test_doc_id 
        FROM regulatory_documents 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        IF test_user_id IS NOT NULL AND test_doc_id IS NOT NULL THEN
            -- Create test signature with fixed function
            SELECT create_electronic_signature(
                test_org_id,
                test_doc_id,
                test_user_id,
                'approval'::signature_type,
                'FIXED - Test signature for function verification - 21 CFR Part 11 compliant',
                'Testing fixed electronic signature functionality',
                'password'::authentication_method,
                '{"test": true, "verification": "fixed_function_test"}'::jsonb
            ) INTO signature_id;
            
            RAISE NOTICE 'FIXED - Test signature created with ID: %', signature_id;
        ELSE
            RAISE NOTICE 'No test user or document found';
        END IF;
    ELSE
        RAISE NOTICE 'No test organization found';
    END IF;
END $$;

-- Verify the fix worked
SELECT 
    'FIXED FUNCTION TEST' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SUCCESS'
        ELSE '❌ FAILED'
    END as status,
    COUNT(*) as signature_count
FROM electronic_signatures 
WHERE signature_meaning LIKE '%FIXED - Test signature%';

-- Show the created signature
SELECT 
    'FIXED SIGNATURE DETAILS' as test_name,
    es.signature_type,
    es.signer_name,
    es.signature_meaning,
    es.authentication_method,
    es.signed_at,
    o.display_name as organization,
    rd.title as document_title
FROM electronic_signatures es
JOIN organizations o ON o.id = es.organization_id
LEFT JOIN regulatory_documents rd ON rd.id = es.document_id
WHERE es.signature_meaning LIKE '%FIXED - Test signature%'
ORDER BY es.signed_at DESC
LIMIT 1;

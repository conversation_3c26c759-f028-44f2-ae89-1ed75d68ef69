"""Pydantic models for regulatory documents and agencies."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl, field_validator, ConfigDict

from .base import TimestampedModel, AuditableModel, BaseEntityModel


class RegulatoryAgency(str, Enum):
    """FDA regulatory agency enumeration - FDA-focused pharmaceutical compliance."""
    FDA = "fda"


class DocumentType(str, Enum):
    """FDA document type enumeration - FDA-specific pharmaceutical compliance documents."""
    GUIDANCE = "guidance"          # FDA Guidance Documents
    REGULATION = "regulation"      # FDA Regulations
    CFR = "cfr"                   # Code of Federal Regulations (21 CFR)
    POLICY = "policy"             # FDA Policy Documents
    FORM = "form"                 # FDA Forms
    REPORT = "report"             # FDA Reports and Communications
    OTHER = "other"


class DocumentStatus(str, Enum):
    """Document status enumeration."""
    DRAFT = "draft"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    SUPERSEDED = "superseded"
    WITHDRAWN = "withdrawn"


class ProcessingStatus(str, Enum):
    """Document processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class DocumentSource(BaseModel):
    """Document source information."""

    agency: RegulatoryAgency = Field(..., description="Regulatory agency")
    source_url: Optional[HttpUrl] = Field(None, description="Original source URL")
    source_id: Optional[str] = Field(None, description="Source system identifier")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    effective_date: Optional[datetime] = Field(None, description="Effective date")
    last_modified: Optional[datetime] = Field(None, description="Last modification date")

    model_config = ConfigDict(from_attributes=True)


class DocumentMetadata(BaseModel):
    """Document metadata and classification."""

    keywords: List[str] = Field(default_factory=list, description="Document keywords")
    categories: List[str] = Field(default_factory=list, description="Document categories")
    therapeutic_areas: List[str] = Field(default_factory=list, description="Therapeutic areas")
    product_types: List[str] = Field(default_factory=list, description="Product types")
    regulatory_phases: List[str] = Field(default_factory=list, description="Regulatory phases")
    compliance_frameworks: List[str] = Field(default_factory=list, description="Compliance frameworks")
    language: str = Field(default="en", description="Document language")
    page_count: Optional[int] = Field(None, ge=0, description="Number of pages")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    checksum: Optional[str] = Field(None, description="File checksum")

    @field_validator('keywords', 'categories', 'therapeutic_areas', 'product_types', 'regulatory_phases', 'compliance_frameworks')
    @classmethod
    def validate_lists(cls, v):
        """Ensure lists contain only non-empty strings."""
        if not isinstance(v, list):
            return []
        return [item.strip() for item in v if isinstance(item, str) and item.strip()]


class DocumentContent(BaseModel):
    """Document content and processing information."""

    title: str = Field(..., min_length=1, description="Document title")
    abstract: Optional[str] = Field(None, description="Document abstract/summary")
    content: Optional[str] = Field(None, description="Full document content")
    content_type: str = Field(default="text/plain", description="Content MIME type")
    extracted_text: Optional[str] = Field(None, description="Extracted text content")
    processing_status: ProcessingStatus = Field(default=ProcessingStatus.PENDING, description="Processing status")
    processing_error: Optional[str] = Field(None, description="Processing error message")
    ai_summary: Optional[str] = Field(None, description="AI-generated summary")
    ai_insights: Optional[Dict[str, Any]] = Field(None, description="AI-generated insights")

    model_config = ConfigDict(from_attributes=True)


class DocumentVersion(BaseModel):
    """Document version information."""

    version: str = Field(..., description="Version identifier")
    version_date: datetime = Field(..., description="Version date")
    changes_summary: Optional[str] = Field(None, description="Summary of changes")
    is_current: bool = Field(default=True, description="Whether this is the current version")
    supersedes_version: Optional[str] = Field(None, description="Previous version identifier")

    model_config = ConfigDict(from_attributes=True)


class RegulatoryDocumentBase(BaseModel):
    """Base regulatory document model."""

    title: str = Field(..., min_length=1, max_length=500, description="Document title")
    document_type: DocumentType = Field(..., description="Type of document")
    status: DocumentStatus = Field(default=DocumentStatus.DRAFT, description="Document status")
    source: DocumentSource = Field(..., description="Source information")
    metadata: DocumentMetadata = Field(default_factory=DocumentMetadata, description="Document metadata")
    content: DocumentContent = Field(..., description="Document content")
    version_info: DocumentVersion = Field(..., description="Version information")

    model_config = ConfigDict(from_attributes=True)


class RegulatoryDocumentCreate(RegulatoryDocumentBase):
    """Model for creating regulatory documents."""
    pass


class RegulatoryDocumentUpdate(BaseModel):
    """Model for updating regulatory documents."""

    title: Optional[str] = Field(None, min_length=1, max_length=500)
    document_type: Optional[DocumentType] = None
    status: Optional[DocumentStatus] = None
    metadata: Optional[DocumentMetadata] = None
    content: Optional[DocumentContent] = None
    version_info: Optional[DocumentVersion] = None


class RegulatoryDocument(RegulatoryDocumentBase, AuditableModel):
    """Complete regulatory document model."""

    id: UUID = Field(..., description="Document ID")

    model_config = ConfigDict(from_attributes=True)


class DocumentSearchParams(BaseModel):
    """Document search parameters."""

    query: Optional[str] = Field(None, description="Search query")
    document_types: Optional[List[DocumentType]] = Field(None, description="Document types filter")
    agencies: Optional[List[RegulatoryAgency]] = Field(None, description="Agencies filter")
    statuses: Optional[List[DocumentStatus]] = Field(None, description="Status filter")
    keywords: Optional[List[str]] = Field(None, description="Keywords filter")
    categories: Optional[List[str]] = Field(None, description="Categories filter")
    therapeutic_areas: Optional[List[str]] = Field(None, description="Therapeutic areas filter")
    date_from: Optional[datetime] = Field(None, description="Start date filter")
    date_to: Optional[datetime] = Field(None, description="End date filter")

    @field_validator('date_to')
    @classmethod
    def validate_date_range(cls, v, info):
        """Ensure date_to is after date_from."""
        if v and info.data.get('date_from'):
            if v <= info.data['date_from']:
                raise ValueError("End date must be after start date")
        return v


class DocumentAnalysisResult(BaseModel):
    """Document analysis result from AI processing."""

    document_id: UUID = Field(..., description="Document ID")
    analysis_type: str = Field(..., description="Type of analysis performed")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    key_findings: List[str] = Field(default_factory=list, description="Key findings")
    compliance_indicators: Dict[str, Any] = Field(default_factory=dict, description="Compliance indicators")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")
    recommendations: List[str] = Field(default_factory=list, description="Recommendations")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    model_version: Optional[str] = Field(None, description="AI model version used")

    model_config = ConfigDict(from_attributes=True)


class DocumentUploadRequest(BaseModel):
    """Document upload request model."""

    title: str = Field(..., min_length=1, max_length=500, description="Document title")
    document_type: DocumentType = Field(..., description="Document type")
    agency: RegulatoryAgency = Field(..., description="Regulatory agency")
    source_url: Optional[HttpUrl] = Field(None, description="Source URL")
    metadata: Optional[DocumentMetadata] = Field(None, description="Additional metadata")
    auto_process: bool = Field(default=True, description="Whether to auto-process with AI")


class DocumentUploadResponse(BaseModel):
    """Document upload response model."""

    document_id: UUID = Field(..., description="Created document ID")
    upload_url: Optional[str] = Field(None, description="Upload URL for file")
    processing_status: ProcessingStatus = Field(..., description="Initial processing status")
    estimated_processing_time: Optional[int] = Field(None, description="Estimated processing time in seconds")

    model_config = ConfigDict(from_attributes=True)

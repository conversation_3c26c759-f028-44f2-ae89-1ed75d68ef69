import { Button } from '@/components/ui-radix/button';
import { Input } from '@/components/ui-radix/input';
import { Search, X } from 'lucide-react';
import * as React from 'react';
import type { DocumentSearchProps } from '../types';

/**
 * DocumentSearch Component - Debounced Search Input
 *
 * Features:
 * - Debounced search with configurable delay
 * - Clear button for easy reset
 * - Search icon for visual context
 * - Accessible with proper labels
 */
export const DocumentSearch: React.FC<DocumentSearchProps> = React.memo(
  function DocumentSearch({
    value,
    onChange,
    placeholder = "Search documents by name, type, or content...",
    debounceTime = 300
  }) {
    const [searchQuery, setSearchQuery] = React.useState(value);

    // Debounced onChange function
    const debouncedOnChange = React.useMemo(
      () => {
        let timeoutId: NodeJS.Timeout;
        return (query: string) => {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            onChange(query);
          }, debounceTime);
        };
      },
      [onChange, debounceTime]
    );

    // Update internal state when external value changes
    React.useEffect(() => {
      setSearchQuery(value);
    }, [value]);

    // Trigger debounced search when query changes
    React.useEffect(() => {
      debouncedOnChange(searchQuery);
    }, [searchQuery, debouncedOnChange]);

    const handleClear = () => {
      setSearchQuery('');
      onChange('');
    };

    return (
      <div className="relative flex-1">
        <Search
          className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"
          aria-hidden="true"
        />
        <Input
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 h-10"
          aria-label="Search documents"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
            onClick={handleClear}
            aria-label="Clear search"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }
);

DocumentSearch.displayName = 'DocumentSearch';

'use client'

import { FileText, Activity, Clock, TrendingUp } from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ResponsiveContainer,
} from 'recharts'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'

export function MetricsOverview() {
  // Chart data
  const complianceScoreData = [
    { month: 'Jan', score: 82 },
    { month: 'Feb', score: 85 },
    { month: 'Mar', score: 83 },
    { month: 'Apr', score: 87 },
    { month: 'May', score: 89 },
    { month: 'Jun', score: 87 },
  ]

  const documentProcessingData = [
    { week: 'Week 1', processed: 45, pending: 12 },
    { week: 'Week 2', processed: 52, pending: 8 },
    { week: 'Week 3', processed: 48, pending: 15 },
    { week: 'Week 4', processed: 61, pending: 6 },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Documents Processed Chart */}
      <Card className="card-subtle hover:shadow-md transition-shadow duration-150 cursor-pointer">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-[14px] font-medium text-muted-foreground">
            Documents Processed
          </CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-[24px] font-bold text-foreground mb-2">
            1,293
          </div>
          <div className="text-[12px] text-success mb-4 flex items-center">
            <TrendingUp className="mr-1 h-3 w-3" />
            +12% from last month
          </div>
          <div className="relative chart-background rounded-lg p-2">
            <div className="absolute inset-0 chart-grid-visible">
              <svg width="100%" height="60" className="overflow-hidden">
                <defs>
                  <pattern
                    id="grid"
                    width="20"
                    height="20"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M 20 0 L 0 0 0 20"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="0.8"
                    />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
            <ResponsiveContainer width="100%" height={60}>
              <AreaChart data={documentProcessingData}>
                <Area
                  type="monotone"
                  dataKey="processed"
                  stroke="var(--chart-purple)"
                  fill="var(--chart-purple)"
                  fillOpacity={0.3}
                  strokeWidth={2}
                  className="chart-area-visible"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Compliance Score Chart */}
      <Card className="card-subtle hover:shadow-md transition-shadow duration-150 cursor-pointer">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-[14px] font-medium text-muted-foreground">
            Compliance Score
          </CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-[24px] font-bold text-foreground mb-2">87%</div>
          <div className="text-[12px] text-success mb-4 flex items-center">
            <TrendingUp className="mr-1 h-3 w-3" />
            +3% from last month
          </div>
          <div className="relative chart-background rounded-lg p-2">
            <div className="absolute inset-0 chart-grid-visible">
              <svg width="100%" height="60" className="overflow-hidden">
                <defs>
                  <pattern
                    id="dots"
                    width="15"
                    height="15"
                    patternUnits="userSpaceOnUse"
                  >
                    <circle cx="7.5" cy="7.5" r="1.2" fill="currentColor" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#dots)" />
              </svg>
            </div>
            <ResponsiveContainer width="100%" height={60}>
              <LineChart data={complianceScoreData}>
                <Line
                  type="monotone"
                  dataKey="score"
                  stroke="var(--chart-green)"
                  strokeWidth={2}
                  dot={false}
                  className="chart-area-visible"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Pending Reviews Chart */}
      <Card className="card-subtle hover:shadow-md transition-shadow duration-150 cursor-pointer">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-[14px] font-medium text-muted-foreground">
            Pending Reviews
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-[24px] font-bold text-foreground mb-2">24</div>
          <div className="text-[12px] text-warning mb-4 flex items-center">
            <TrendingUp className="mr-1 h-3 w-3" />
            +8% from last week
          </div>
          <div className="relative chart-background rounded-lg p-2">
            <div className="absolute inset-0 chart-grid-visible">
              <svg width="100%" height="60" className="overflow-hidden">
                <defs>
                  <pattern
                    id="lines"
                    width="10"
                    height="10"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M 0,10 l 10,-10 M -2.5,2.5 l 5,-5 M 7.5,12.5 l 5,-5"
                      stroke="currentColor"
                      strokeWidth="0.8"
                    />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#lines)" />
              </svg>
            </div>
            <ResponsiveContainer width="100%" height={60}>
              <BarChart
                data={[
                  { name: 'Mon', value: 4 },
                  { name: 'Tue', value: 7 },
                  { name: 'Wed', value: 3 },
                  { name: 'Thu', value: 6 },
                  { name: 'Fri', value: 4 },
                ]}
              >
                <Bar
                  dataKey="value"
                  fill="var(--chart-orange)"
                  fillOpacity={1}
                  radius={[2, 2, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Environment variable validation utility for VigiLens
 * Ensures all required Supabase configuration is present
 */

interface EnvironmentConfig {
  supabaseUrl: string
  supabaseAnonKey: string
  nodeEnv: string
  apiUrl: string
}

interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  config?: EnvironmentConfig
}

/**
 * Validates all required environment variables for Supabase integration
 */
export function validateEnvironment(): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Required environment variables
  const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']
  const supabaseAnonKey = process.env['NEXT_PUBLIC_SUPABASE_ANON_KEY']
  const nodeEnv = process.env['NODE_ENV'] || 'development'
  const apiUrl = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000/api/v1'

  // Validate required variables
  if (!supabaseUrl) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is required')
  } else if (!isValidSupabaseUrl(supabaseUrl)) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL must be a valid Supabase URL (https://[project-id].supabase.co)')
  }

  if (!supabaseAnonKey) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
  } else if (!isValidSupabaseKey(supabaseAnonKey)) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY must be a valid JWT token')
  }

  // Validate optional but recommended variables
  if (nodeEnv === 'production' && apiUrl.includes('localhost')) {
    warnings.push('NEXT_PUBLIC_API_URL should not use localhost in production')
  }

  // Additional validations
  if (supabaseUrl && supabaseAnonKey) {
    const projectIdFromUrl = extractProjectId(supabaseUrl)
    const projectIdFromKey = extractProjectIdFromKey(supabaseAnonKey)

    if (projectIdFromUrl && projectIdFromKey && projectIdFromUrl !== projectIdFromKey) {
      errors.push('Project ID mismatch between SUPABASE_URL and SUPABASE_ANON_KEY')
    }
  }

  const isValid = errors.length === 0

  const result: ValidationResult = {
    isValid,
    errors,
    warnings,
  }

  if (isValid && supabaseUrl && supabaseAnonKey) {
    result.config = {
      supabaseUrl,
      supabaseAnonKey,
      nodeEnv,
      apiUrl,
    }
  }

  return result
}

/**
 * Validates if a URL is a valid Supabase project URL
 */
function isValidSupabaseUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname.endsWith('.supabase.co') && urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Validates if a key looks like a valid JWT token
 */
function isValidSupabaseKey(key: string): boolean {
  // Basic JWT format validation (3 parts separated by dots)
  const parts = key.split('.')
  return parts.length === 3 && parts.every(part => part.length > 0)
}

/**
 * Extracts project ID from Supabase URL
 */
function extractProjectId(url: string): string | null {
  try {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname
    if (hostname.endsWith('.supabase.co')) {
      return hostname?.split('.')[0] || null
    }
  } catch {
    // Invalid URL
  }
  return null
}

/**
 * Extracts project ID from Supabase anon key (JWT payload)
 */
function extractProjectIdFromKey(key: string): string | null {
  try {
    const parts = key.split('.')
    if (parts.length !== 3) return null

    // Decode the payload (second part)
    const payload = JSON.parse(atob(parts[1] || ''))
    return payload.ref || null
  } catch {
    return null
  }
}

/**
 * Logs environment validation results to console
 */
export function logValidationResults(result: ValidationResult): void {
  if (result.isValid) {
    console.log('✅ Environment validation passed')
    if (result.warnings.length > 0) {
      console.warn('⚠️ Warnings:')
      result.warnings.forEach(warning => console.warn(`  - ${warning}`))
    }
  } else {
    console.error('❌ Environment validation failed')
    result.errors.forEach(error => console.error(`  - ${error}`))
    if (result.warnings.length > 0) {
      console.warn('⚠️ Additional warnings:')
      result.warnings.forEach(warning => console.warn(`  - ${warning}`))
    }
  }
}

/**
 * Throws an error if environment validation fails
 * Use this in application startup to ensure proper configuration
 */
export function requireValidEnvironment(): EnvironmentConfig {
  const result = validateEnvironment()

  if (!result.isValid) {
    logValidationResults(result)
    throw new Error(`Environment validation failed: ${result.errors.join(', ')}`)
  }

  if (result.warnings.length > 0) {
    logValidationResults(result)
  }

  return result.config!
}

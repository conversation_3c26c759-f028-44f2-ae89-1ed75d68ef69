'use client'

import { CheckCircle, FileText, Upload, X } from 'lucide-react'
import { useCallback } from 'react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import {
    Card,
    CardContent
} from '@/components/ui-radix/card'
import { Progress } from '@/components/ui-radix/progress'

import type { Document } from '../types'

interface UploadSectionProps {
  readonly uploadedDocuments?: readonly Document[];
  readonly uploadProgress?: number;
  readonly isUploading?: boolean;
  readonly onFileUpload: (files: FileList) => void;
  readonly onRemoveDocument?: (documentId: string) => void;
}

export function UploadSection({
  uploadedDocuments = [],
  uploadProgress = 0,
  isUploading = false,
  onFileUpload,
  onRemoveDocument,
}: UploadSectionProps) {
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      if (files && files.length > 0) {
        onFileUpload(files)
      }
    },
    [onFileUpload],
  )

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
      const files = event.dataTransfer.files
      if (files && files.length > 0) {
        onFileUpload(files)
      }
    },
    [onFileUpload],
  )

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
    },
    [],
  )

  return (
    <Card>
      <CardContent className="pt-6">
        {/* Show big upload area only when no documents are uploaded */}
        {uploadedDocuments.length === 0 && (
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => document.getElementById('file-upload')?.click()}
          >
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Upload Documents</h3>
            <p className="text-muted-foreground mb-4">
              Drag and drop files here, or click to browse
            </p>
            <Button variant="outline">Choose Files</Button>
          </div>
        )}

        {/* Hidden file input */}
        <input
          id="file-upload"
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt"
          onChange={handleFileChange}
          className="hidden"
        />

        {isUploading && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Uploading...</span>
              <span className="text-sm text-muted-foreground">
                {uploadProgress}%
              </span>
            </div>
            <Progress value={uploadProgress} className="h-2" />
          </div>
        )}

        {uploadedDocuments.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium">Uploaded Documents</h4>
            <div className="space-y-3">
              {uploadedDocuments.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{doc.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {doc.size} • {doc.type}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className="bg-success/10 text-success"
                    >
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Uploaded
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveDocument?.(doc.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Purple upload button below uploaded documents */}
            <div className="pt-2">
              <Button
                onClick={() => document.getElementById('file-upload')?.click()}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
                size="sm"
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload More Documents
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

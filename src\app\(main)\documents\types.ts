export interface Document {
  readonly id: number;
  readonly name: string;
  readonly type: 'PDF' | 'DOCX' | 'XLSX' | 'TXT' | 'PPT' | 'CSV';
  readonly size: string; // e.g., "2.4 MB"
  readonly sizeBytes: number; // For sorting
  readonly uploadDate: string; // ISO date string
  readonly lastModified: string; // ISO date string
  readonly category: 'Approved' | 'Under Review' | 'Draft' | 'Template' | 'Archived';
  readonly status: 'completed' | 'processing' | 'needs_review' | 'failed' | 'queued';
  readonly complianceScore: number; // 0-100
  readonly tags: string[];
  readonly description: string;
  readonly author: {
    readonly id: string;
    readonly name: string;
    readonly avatar?: string;
  };
  readonly permissions: {
    readonly canView: boolean;
    readonly canEdit: boolean;
    readonly canDelete: boolean;
    readonly canShare: boolean;
    readonly canDownload: boolean;
  };
  readonly metadata: {
    readonly pageCount?: number;
    readonly wordCount?: number;
    readonly language: string;
    readonly encryptionLevel?: 'none' | 'basic' | 'advanced';
  };
  readonly complianceChecks: {
    readonly lastChecked: string;
    readonly frameworks: string[]; // ['FDA', 'ISO', 'ICH', etc.]
    readonly findings: number;
    readonly criticalIssues: number;
  };
  readonly versions: {
    readonly current: string;
    readonly history: Array<{
      readonly version: string;
      readonly uploadDate: string;
      readonly author: string;
      readonly changeNotes: string;
    }>;
  };
}

export interface DocumentsState {
  readonly documents: Document[];
  readonly filteredDocuments: Document[];
  readonly searchQuery: string;
  readonly selectedCategory: 'all' | 'Approved' | 'Under Review' | 'Draft' | 'Template';
  readonly selectedStatus: 'all' | 'completed' | 'processing' | 'needs_review';
  readonly viewMode: 'grid' | 'list';
  readonly activeTab: 'my-documents' | 'shared' | 'recent' | 'templates';
  readonly sortBy: 'name' | 'uploadDate' | 'complianceScore' | 'size';
  readonly sortOrder: 'asc' | 'desc';
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly selectedDocuments: number[];
  readonly page: number;
  readonly hasMore: boolean;
}

export interface DocumentCardProps {
  readonly document: Document;
  readonly viewMode: 'grid' | 'list';
  readonly onView: (id: number) => void;
  readonly onDownload: (id: number) => void;
  readonly onShare: (id: number) => void;
  readonly onSelect?: (id: number, selected: boolean) => void;
  readonly isSelected?: boolean;
  readonly showActions?: boolean;
}

export interface StatusBadgeProps {
  readonly status: 'completed' | 'processing' | 'needs_review' | 'failed' | 'queued';
  readonly size?: 'sm' | 'md';
}

export interface MetricCardProps {
  readonly label: string;
  readonly value: string;
  readonly icon: React.ComponentType<{ className?: string }>;
  readonly trend?: {
    readonly value: number;
    readonly isPositive: boolean;
  };
  readonly onClick?: () => void;
}

export interface DocumentSearchProps {
  readonly value: string;
  readonly onChange: (value: string) => void;
  readonly placeholder?: string;
  readonly debounceTime?: number;
}

export interface ViewModeToggleProps {
  readonly value: 'grid' | 'list';
  readonly onChange: (mode: 'grid' | 'list') => void;
}

export interface DocumentGridProps {
  readonly documents: Document[];
  readonly viewMode: 'grid' | 'list';
  readonly onView: (id: number) => void;
  readonly onDownload: (id: number) => void;
  readonly onShare: (id: number) => void;
  readonly onDelete: (id: number) => void;
  readonly isLoading?: boolean;
  readonly sortBy?: string;
  readonly sortOrder?: 'asc' | 'desc';
  readonly onSort?: (column: string) => void;
  readonly selectedDocuments?: number[];
  readonly onSelectionChange?: (selectedIds: number[]) => void;
  readonly showSelection?: boolean;
}

export interface EmptyStateProps {
  readonly type: 'shared' | 'recent' | 'templates' | 'search';
  readonly onAction?: () => void;
}

export type DocumentCategory = 'all' | 'Approved' | 'Under Review' | 'Draft' | 'Template';
export type DocumentStatus = 'all' | 'completed' | 'processing' | 'needs_review';
export type ViewMode = 'grid' | 'list';
export type TabType = 'my-documents' | 'shared' | 'recent' | 'templates';

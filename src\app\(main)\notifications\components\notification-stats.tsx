'use client'

import { Card, CardContent } from '@/components/ui-radix/card'
import { cn } from '@/lib/utils'
import { Bell, Mail, TriangleAlert } from 'lucide-react'

interface NotificationStatsProps {
  readonly stats: {
    readonly total: number
    readonly unread: number
    readonly urgent: number
  }
  readonly className?: string
}

interface StatCardProps {
  readonly title: string
  readonly value: number
  readonly icon: React.ComponentType<{ className?: string }>
  readonly bgColor: string
  readonly textColor: string
}

function StatCard({ title, value, icon: Icon, bgColor, textColor }: StatCardProps) {
  return (
    <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${bgColor}`}>
            <Icon className={`h-5 w-5 ${textColor}`} />
          </div>
          <div>
            <p className="text-2xl font-bold text-foreground">{value}</p>
            <p className="text-sm text-muted-foreground">{title}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function NotificationStats({ stats, className }: NotificationStatsProps) {
  const statCards = [
    {
      title: 'Total Notifications',
      value: stats.total,
      icon: Bell,
      bgColor: 'bg-notification-bell',
      textColor: 'text-notification-bell-foreground'
    },
    {
      title: 'Unread',
      value: stats.unread,
      icon: Mail,
      bgColor: 'bg-notification-mail',
      textColor: 'text-notification-mail-foreground'
    },
    {
      title: 'Critical',
      value: stats.urgent,
      icon: TriangleAlert,
      bgColor: 'bg-destructive',
      textColor: 'text-destructive-foreground'
    }
  ]

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-3 gap-4', className)}>
      {statCards.map((stat) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          bgColor={stat.bgColor}
          textColor={stat.textColor}
        />
      ))}
    </div>
  )
}

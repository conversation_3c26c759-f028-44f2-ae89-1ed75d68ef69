'use client'

import { Bar<PERSON>hart3 } from 'lucide-react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip, TooltipProps } from 'recharts';

import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card';

interface RiskData {
  readonly name: string;
  readonly value: number;
  readonly color: string;
  readonly percentage: number;
}

// Custom tooltip component that follows the design system
const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
  if (active && payload && payload.length && payload[0]) {
    const data = payload[0].payload as RiskData;
    return (
      <div className="bg-card/95 backdrop-blur-sm border border-border rounded-lg shadow-lg p-3 text-sm">
        <div className="flex items-center gap-2 mb-1">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: data.color }}
          />
          <span className="font-medium text-foreground">{data.name}</span>
        </div>
        <div className="text-muted-foreground">
          <div>Count: <span className="text-foreground font-medium">{data.value}</span></div>
          <div>Percentage: <span className="text-foreground font-medium">{data.percentage}%</span></div>
        </div>
      </div>
    );
  }

  return null;
};

export function RiskDistribution() {
  // Risk distribution data following blueprint specifications
  const riskData: RiskData[] = [
    {
      name: 'Low Risk',
      value: 65,
      color: 'var(--chart-3)', // Green oklch(0.6242 0.1695 149.09)
      percentage: 65,
    },
    {
      name: 'Medium Risk',
      value: 25,
      color: 'var(--chart-4)', // Orange oklch(0.6685 0.1591 57.71)
      percentage: 25,
    },
    {
      name: 'High Risk',
      value: 8,
      color: 'var(--chart-5)', // Red oklch(0.6368 0.2078 25.33)
      percentage: 8,
    },
    {
      name: 'Critical',
      value: 2,
      color: '#dc2626', // Dark red
      percentage: 2,
    },
  ]

  return (
    <Card className="card-subtle">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-[14px] font-medium text-muted-foreground">
          Risk Distribution
        </CardTitle>
        <BarChart3 className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent className="pt-4">
        <div className="flex flex-col items-center">
          {/* Pie Chart Container - Exact 120x120px as per blueprint */}
          <div className="relative chart-background rounded-lg p-2 w-full flex justify-center">
            <div className="w-[120px] h-[120px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={riskData}
                    cx="50%"
                    cy="50%"
                    innerRadius={0}
                    outerRadius={50}
                    startAngle={90}
                    endAngle={450}
                    paddingAngle={1}
                    dataKey="value"
                    className="chart-area-visible"
                  >
                    {riskData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        className="hover:opacity-80 transition-opacity duration-150 cursor-pointer"
                        style={{
                          filter: 'drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.1))'
                        }}
                      />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Horizontal Legend */}
          <div className="flex flex-wrap justify-center gap-4 mt-4">
            {riskData.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-[12px] text-foreground font-medium">
                  {item.name}
                </span>
                <span className="text-[12px] text-muted-foreground">
                  {item.percentage}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

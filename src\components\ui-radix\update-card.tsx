import { ExternalLink, FileText, Clock, AlertTriangle } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import { Card, CardContent, CardHeader } from '@/components/ui-radix/card'
import { cn } from '@/lib/utils'


interface UpdateCardProps {
  title: string;
  agency: string;
  category: string;
  publishedDate: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  summary?: string;
  url?: string;
  className?: string;
}

const severityConfig = {
  low: {
    color: 'bg-success text-success-foreground',
    icon: FileText,
    label: 'Current',
  },
  medium: {
    color: 'bg-info text-info-foreground',
    icon: Clock,
    label: 'Under Review',
  },
  high: {
    color: 'bg-warning text-white',
    icon: AlertTriangle,
    label: 'Critical',
  },
  critical: {
    color: 'bg-destructive text-destructive-foreground',
    icon: AlertTriangle,
    label: 'Critical',
  },
}

export function UpdateCard({
  title,
  agency,
  category,
  publishedDate,
  severity,
  summary,
  url,
  className,
}: UpdateCardProps) {
  const config = severityConfig[severity]
  const Icon = config.icon

  return (
    <Card
      className={cn(
        'transition-all duration-200 hover:shadow-md hover:border-primary/20 cursor-pointer',
        className,
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge className={cn('text-xs', config.color)}>
                <Icon className="mr-1 h-3 w-3" />
                {config.label}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {agency}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {category}
              </Badge>
            </div>
            <h3 className="font-semibold text-foreground leading-tight">
              {title}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              Published: {publishedDate}
            </p>
          </div>
        </div>
      </CardHeader>

      {summary && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground line-clamp-3">
              {summary}
            </div>
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                className="hover:bg-primary hover:text-primary-foreground hover:border-primary font-semibold"
              >
                Read full update
              </Button>
              {url && (
                <Button variant="ghost" size="sm">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Go to Source
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}

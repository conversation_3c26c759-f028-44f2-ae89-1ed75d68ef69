import { Badge } from '@/components/ui-radix/badge';
import { Progress } from '@/components/ui-radix/progress';
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui-radix/tooltip';
import { cn } from '@/lib/utils';
import * as React from 'react';

interface ComplianceProgressProps {
  score: number; // 0-100
  frameworks: string[];
  criticalIssues: number;
  findings: number;
  className?: string;
}

/**
 * ComplianceProgress Component - Display compliance score with progress bar
 *
 * Shows progress bar with color coding and tooltip with compliance details
 */
export const ComplianceProgress: React.FC<ComplianceProgressProps> = ({
  score,
  frameworks,
  criticalIssues,
  findings,
  className
}) => {
  const getComplianceColor = (score: number) => {
    if (score >= 90) return 'hsl(var(--success))';
    if (score >= 75) return 'hsl(var(--warning))';
    return 'hsl(var(--destructive))';
  };

  const getComplianceTextColor = (score: number) => {
    if (score >= 90) return 'text-success';
    if (score >= 75) return 'text-warning';
    return 'text-destructive';
  };

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      {/* Progress bar and percentage */}
      <div className="flex-1 min-w-[60px]">
        <div className="flex items-center space-x-2">
          <Progress
            value={score}
            className="h-2 flex-1"
            style={{
              '--progress-background': getComplianceColor(score)
            } as React.CSSProperties}
          />
          <span className={cn(
            "font-medium min-w-[2.5rem] text-right text-xs",
            getComplianceTextColor(score)
          )}>
            {score}%
          </span>
        </div>
      </div>

      {/* Issue badges */}
      {criticalIssues > 0 && (
        <Badge className="bg-destructive text-destructive-foreground text-[10px] px-1.5 py-0.5" title="Critical issues">
          {criticalIssues} ⚠️
        </Badge>
      )}
      {findings > 0 && (
        <Badge variant="outline" className="text-xs px-1.5 py-0.5" title="Findings">
          {findings}
        </Badge>
      )}

      {/* Frameworks indicator */}
      <Tooltip>
        <TooltipTrigger>
          <Badge variant="outline" className="text-[10px] px-1.5 py-0.5" title="Framework count">
            {frameworks.length}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs">
            <p className="font-medium mb-1">Compliance Frameworks:</p>
            {frameworks.map(framework => (
              <p key={framework}>{framework}</p>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};

ComplianceProgress.displayName = 'ComplianceProgress';

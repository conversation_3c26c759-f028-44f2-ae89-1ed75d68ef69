import { Skeleton } from '@/components/ui-radix/skeleton';
import * as React from 'react';
import type { DocumentGridProps } from '../types';
import { DocumentCard } from './document-card';
import { DocumentTable } from './document-table';

/**
 * DocumentGrid Component - Responsive Document Layout
 *
 * Features:
 * - Responsive grid (1 col mobile, 2 col tablet, 3 col desktop)
 * - Table view for list mode
 * - Loading skeletons
 * - Proper spacing and alignment
 * - Accessibility with proper landmarks
 */
export const DocumentGrid: React.FC<DocumentGridProps> = React.memo(
  function DocumentGrid({
    documents,
    viewMode,
    onView,
    onDownload,
    onShare,
    onDelete,
    isLoading = false,
    sortBy = 'uploadDate',
    sortOrder = 'desc',
    onSort,
    selectedDocuments = [],
    onSelectionChange,
    showSelection = false
  }) {
    // Use table view for list mode
    if (viewMode === 'list') {
      return (
        <DocumentTable
          documents={documents}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSort={onSort || (() => {})}
          onView={onView}
          onDownload={onDownload}
          onShare={onShare}
          onDelete={onDelete}
          selectedDocuments={selectedDocuments}
          onSelectionChange={onSelectionChange || (() => {})}
          showSelection={showSelection}
          isLoading={isLoading}
        />
      );
    }

    // Grid view loading state
    if (isLoading) {
      return (
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          aria-label="Loading documents"
        >
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
              <Skeleton className="h-2 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </div>
      );
    }

    // Grid view empty state
    if (documents.length === 0) {
      return (
        <div
          className="text-center py-12"
          role="status"
          aria-label="No documents found"
        >
          <p className="text-muted-foreground">
            No documents found. Try adjusting your search or filters.
          </p>
        </div>
      );
    }

    // Grid view - show cards
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        role="region"
        aria-label="Documents in grid view"
      >
        {documents.map((document) => (
          <DocumentCard
            key={document.id}
            document={document}
            viewMode={viewMode}
            onView={onView}
            onDownload={onDownload}
            onShare={onShare}
            showActions={true}
          />
        ))}
      </div>
    );
  }
);

DocumentGrid.displayName = 'DocumentGrid';

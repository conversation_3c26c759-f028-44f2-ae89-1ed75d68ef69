-- SCHEMA-AWARE AUTH TRIGGER FIX
-- The logs show "relation user_profiles does not exist" - need to specify schema

-- Drop existing triggers and function
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;

-- Verify the table exists in public schema
SELECT 
    'TABLE CHECK' as test,
    schemaname,
    tablename
FROM pg_tables 
WHERE tablename = 'user_profiles';

-- Create function with explicit schema references
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert with explicit schema reference
    INSERT INTO public.user_profiles (
        id,
        email,
        organization_id,
        role,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        '550e8400-e29b-41d4-a716-************'::uuid, -- explicit cast
        'read_only'::user_role, -- explicit cast to enum
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions to the function
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO anon;

-- Create trigger
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Verify everything is set up correctly
SELECT 
    'SCHEMA-AWARE TRIGGER SETUP' as status,
    'Function created in public schema' as message;

-- Check if we can access the organizations table too
SELECT 
    'ORG TABLE CHECK' as test,
    COUNT(*) as org_count
FROM public.organizations 
WHERE name = 'demo-pharma-corp';

-- Final verification
SELECT 
    'TRIGGER VERIFICATION' as test,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger';

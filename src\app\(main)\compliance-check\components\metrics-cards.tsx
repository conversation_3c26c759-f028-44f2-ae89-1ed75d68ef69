'use client'

import { TrendingUp } from 'lucide-react'

import { Card, CardContent } from '@/components/ui-radix/card'

interface ComplianceMetric {
  readonly label: string;
  readonly value: string;
  readonly trend: string;
}

export function MetricsCards() {
  const complianceMetrics: readonly ComplianceMetric[] = [
    { label: 'Documents Processed', value: '342', trend: '+12%' },
    { label: 'Average Compliance', value: '89%', trend: '+3%' },
    { label: 'Issues Resolved', value: '47', trend: '+8%' },
    { label: 'Time Saved', value: '120h', trend: '+15%' },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {complianceMetrics.map((metric, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {metric.value}
                </p>
                <p className="text-sm text-muted-foreground">{metric.label}</p>
              </div>
              <div className="flex items-center text-sm text-success">
                <TrendingUp className="mr-1 h-3 w-3" />
                {metric.trend}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

# AI Services Dependencies - July 2025
# Following 6-Expert Protocol Implementation
# Stack: BGE-M3 + Qdrant + OpenRouter + CrewAI + LangGraph

# Core ML/AI Dependencies
torch>=2.3.0
numpy>=1.24.0
sentence-transformers>=3.0.0

# Vector Database
qdrant-client>=1.9.0

# HTTP Client for OpenRouter
httpx>=0.27.0

# Data Validation and Type Safety
pydantic>=2.7.0

# Logging and Utilities
python-dotenv>=1.0.0

# Optional: For enhanced performance
# accelerate>=0.30.0  # For GPU acceleration
# transformers>=4.40.0  # For model compatibility

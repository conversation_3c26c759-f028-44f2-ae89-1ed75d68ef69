"""
FDA Data Gathering Service for VigiLens Pharmaceutical Compliance Platform.
Implements enterprise-grade data collection from FDA sources with rate limiting.
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import time
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)


class FDADataGatherer:
    """Enterprise FDA data gathering service with compliance and rate limiting."""
    
    def __init__(self):
        self.base_urls = {
            "openfda": "https://api.fda.gov",
            "ecfr": "https://www.ecfr.gov/api/versioner/v1",
            "guidance": "https://www.fda.gov/regulatory-information/search-fda-guidance-documents"
        }
        
        # Rate limiting configuration (FDA-compliant)
        self.rate_limits = {
            "openfda": {"requests_per_minute": 240, "requests_per_day": 120000},
            "ecfr": {"requests_per_minute": 60, "requests_per_day": 10000},
            "guidance": {"requests_per_minute": 30, "requests_per_day": 5000}
        }
        
        self.session = None
        self.request_counts = {}
        self.last_request_times = {}
    
    async def initialize(self):
        """Initialize HTTP session with proper headers."""
        headers = {
            "User-Agent": "VigiLens-Pharmaceutical-Compliance/1.0 (<EMAIL>)",
            "Accept": "application/json",
            "Accept-Encoding": "gzip, deflate"
        }
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=aiohttp.TCPConnector(limit=10, limit_per_host=5)
        )
        
        logger.info("FDA data gatherer initialized")
    
    async def close(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
    
    async def _rate_limit_check(self, source: str):
        """Check and enforce rate limits."""
        current_time = time.time()
        
        if source not in self.request_counts:
            self.request_counts[source] = {"minute": 0, "day": 0, "minute_start": current_time, "day_start": current_time}
        
        counts = self.request_counts[source]
        limits = self.rate_limits[source]
        
        # Reset minute counter if needed
        if current_time - counts["minute_start"] >= 60:
            counts["minute"] = 0
            counts["minute_start"] = current_time
        
        # Reset day counter if needed
        if current_time - counts["day_start"] >= 86400:
            counts["day"] = 0
            counts["day_start"] = current_time
        
        # Check limits
        if counts["minute"] >= limits["requests_per_minute"]:
            wait_time = 60 - (current_time - counts["minute_start"])
            logger.warning(f"Rate limit reached for {source}, waiting {wait_time:.1f} seconds")
            await asyncio.sleep(wait_time)
            return await self._rate_limit_check(source)
        
        if counts["day"] >= limits["requests_per_day"]:
            wait_time = 86400 - (current_time - counts["day_start"])
            logger.error(f"Daily rate limit reached for {source}, waiting {wait_time/3600:.1f} hours")
            raise Exception(f"Daily rate limit exceeded for {source}")
        
        # Increment counters
        counts["minute"] += 1
        counts["day"] += 1
    
    async def fetch_openfda_drug_labels(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch drug labeling data from openFDA API."""
        await self._rate_limit_check("openfda")
        
        url = f"{self.base_urls['openfda']}/drug/label.json"
        params = {
            "limit": min(limit, 1000),  # FDA max limit
            "search": "effective_time:[20200101 TO 20251231]"  # Recent labels
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = data.get("results", [])
                    
                    # Process and clean data
                    processed_results = []
                    for item in results:
                        processed_item = {
                            "content": self._extract_label_content(item),
                            "metadata": {
                                "source": "FDA_openFDA",
                                "type": "drug_label",
                                "product_name": item.get("openfda", {}).get("brand_name", ["Unknown"])[0],
                                "manufacturer": item.get("openfda", {}).get("manufacturer_name", ["Unknown"])[0],
                                "effective_time": item.get("effective_time"),
                                "collected_at": datetime.now().isoformat(),
                                "framework": "21_cfr_part_201"
                            }
                        }
                        processed_results.append(processed_item)
                    
                    logger.info(f"Fetched {len(processed_results)} drug labels from openFDA")
                    return processed_results
                else:
                    logger.error(f"openFDA API error: {response.status}")
                    return []
        
        except Exception as e:
            logger.error(f"Error fetching openFDA drug labels: {str(e)}")
            return []
    
    def _extract_label_content(self, label_data: Dict[str, Any]) -> str:
        """Extract meaningful content from FDA drug label."""
        content_parts = []
        
        # Key sections to extract
        sections = [
            "purpose", "indications_and_usage", "dosage_and_administration",
            "contraindications", "warnings", "precautions", "adverse_reactions",
            "drug_interactions", "clinical_pharmacology", "description"
        ]
        
        for section in sections:
            if section in label_data:
                section_content = label_data[section]
                if isinstance(section_content, list):
                    section_content = " ".join(section_content)
                content_parts.append(f"{section.upper()}: {section_content}")
        
        return "\n\n".join(content_parts)
    
    async def fetch_cfr_regulations(self, parts: List[str] = None) -> List[Dict[str, Any]]:
        """Fetch CFR regulations from eCFR API."""
        if parts is None:
            parts = ["11", "211", "820"]  # Key pharmaceutical CFR parts
        
        results = []
        
        for part in parts:
            await self._rate_limit_check("ecfr")
            
            url = f"{self.base_urls['ecfr']}/structure/2025-07-17/title-21/chapter-I/part-{part}"
            
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Process CFR data
                        processed_item = {
                            "content": self._extract_cfr_content(data, part),
                            "metadata": {
                                "source": "FDA_eCFR",
                                "type": "regulation",
                                "cfr_part": f"21_cfr_part_{part}",
                                "title": f"21 CFR Part {part}",
                                "effective_date": "2025-07-17",
                                "collected_at": datetime.now().isoformat(),
                                "framework": f"21_cfr_part_{part}"
                            }
                        }
                        results.append(processed_item)
                        
                        logger.info(f"Fetched 21 CFR Part {part} from eCFR")
                    else:
                        logger.error(f"eCFR API error for part {part}: {response.status}")
            
            except Exception as e:
                logger.error(f"Error fetching CFR part {part}: {str(e)}")
        
        return results
    
    def _extract_cfr_content(self, cfr_data: Dict[str, Any], part: str) -> str:
        """Extract meaningful content from CFR regulation data."""
        content_parts = []
        
        # Add title and purpose
        title = cfr_data.get("title", f"21 CFR Part {part}")
        content_parts.append(f"TITLE: {title}")
        
        # Extract sections
        if "children" in cfr_data:
            for section in cfr_data["children"]:
                section_title = section.get("title", "")
                section_content = section.get("content", "")
                if section_content:
                    content_parts.append(f"SECTION {section_title}: {section_content}")
        
        return "\n\n".join(content_parts)
    
    async def fetch_guidance_documents(self, search_terms: List[str] = None) -> List[Dict[str, Any]]:
        """Fetch FDA guidance documents (requires web scraping)."""
        if search_terms is None:
            search_terms = ["21 CFR Part 11", "electronic records", "validation", "GMP"]
        
        # This would require web scraping implementation
        # For now, return placeholder structure
        logger.info("Guidance document fetching requires web scraping implementation")
        
        return [{
            "content": "FDA Guidance document content would be extracted here",
            "metadata": {
                "source": "FDA_Guidance",
                "type": "guidance",
                "title": "Placeholder Guidance Document",
                "collected_at": datetime.now().isoformat(),
                "framework": "21_cfr_part_11"
            }
        }]
    
    async def gather_all_fda_data(self) -> List[Dict[str, Any]]:
        """Gather all FDA data sources."""
        logger.info("Starting comprehensive FDA data gathering...")
        
        all_data = []
        
        # Fetch drug labels
        drug_labels = await self.fetch_openfda_drug_labels(limit=50)
        all_data.extend(drug_labels)
        
        # Fetch CFR regulations
        cfr_data = await self.fetch_cfr_regulations()
        all_data.extend(cfr_data)
        
        # Fetch guidance documents
        guidance_data = await self.fetch_guidance_documents()
        all_data.extend(guidance_data)
        
        logger.info(f"FDA data gathering complete: {len(all_data)} documents collected")
        return all_data


# Factory function
async def get_fda_gatherer() -> FDADataGatherer:
    """Get initialized FDA data gatherer."""
    gatherer = FDADataGatherer()
    await gatherer.initialize()
    return gatherer

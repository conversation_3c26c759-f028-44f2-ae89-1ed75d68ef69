-- TESTING SCRIPT for VigiLens Authentication Fix
-- Run this AFTER running fix_auth_trigger.sql to verify everything works

-- Test 1: Verify demo organization exists
SELECT 
    'Demo Organization Check' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ PASS: Demo organization exists'
        ELSE '❌ FAIL: Demo organization missing'
    END as result,
    COUNT(*) as org_count
FROM organizations 
WHERE name = 'demo-pharma-corp';

-- Test 2: Verify trigger function exists
SELECT 
    'Trigger Function Check' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ PASS: handle_new_user function exists'
        ELSE '❌ FAIL: handle_new_user function missing'
    END as result
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- Test 3: Verify trigger exists
SELECT 
    'Trigger Check' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ PASS: handle_new_user_trigger exists'
        ELSE '❌ FAIL: handle_new_user_trigger missing'
    END as result
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger';

-- Test 4: Check RLS policies on user_profiles
SELECT 
    'RLS Policy Check' as test_name,
    policyname,
    CASE 
        WHEN policyname = 'Allow trigger user creation' THEN '✅ PASS: Correct RLS policy exists'
        WHEN policyname = 'Controlled profile creation' THEN '❌ FAIL: Old blocking policy still exists'
        ELSE '⚠️  INFO: Other policy exists'
    END as result
FROM pg_policies 
WHERE tablename = 'user_profiles' 
AND policyname IN ('Allow trigger user creation', 'Controlled profile creation');

-- Test 5: Simulate user registration (TEST ONLY - DO NOT USE IN PRODUCTION)
-- This creates a test user to verify the trigger works
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := 'test-' || extract(epoch from now()) || '@example.com';
    profile_count INTEGER;
BEGIN
    -- Insert test user into auth.users (simulating Supabase auth)
    INSERT INTO auth.users (
        id, 
        email, 
        encrypted_password, 
        email_confirmed_at,
        raw_user_meta_data,
        created_at,
        updated_at
    ) VALUES (
        test_user_id,
        test_email,
        'dummy_password_hash',
        NOW(),
        jsonb_build_object('full_name', 'Test User'),
        NOW(),
        NOW()
    );
    
    -- Check if user profile was created by trigger
    SELECT COUNT(*) INTO profile_count 
    FROM user_profiles 
    WHERE id = test_user_id;
    
    IF profile_count > 0 THEN
        RAISE NOTICE '✅ PASS: Trigger created user profile successfully';
        RAISE NOTICE 'Test user email: %', test_email;
    ELSE
        RAISE NOTICE '❌ FAIL: Trigger did not create user profile';
    END IF;
    
    -- Clean up test data
    DELETE FROM user_profiles WHERE id = test_user_id;
    DELETE FROM auth.users WHERE id = test_user_id;
    
    RAISE NOTICE 'Test data cleaned up';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ FAIL: Error during trigger test: %', SQLERRM;
        -- Attempt cleanup even on error
        BEGIN
            DELETE FROM user_profiles WHERE id = test_user_id;
            DELETE FROM auth.users WHERE id = test_user_id;
        EXCEPTION
            WHEN OTHERS THEN NULL;
        END;
END $$;

-- Test 6: Check user_profiles table structure
SELECT 
    'User Profiles Table Structure' as test_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
ORDER BY ordinal_position;

-- Test 7: Verify RLS is enabled
SELECT 
    'RLS Status Check' as test_name,
    CASE 
        WHEN rowsecurity THEN '✅ PASS: RLS is enabled on user_profiles'
        ELSE '❌ FAIL: RLS is not enabled on user_profiles'
    END as result
FROM pg_tables 
WHERE tablename = 'user_profiles';

-- Summary Report
SELECT 
    '=== AUTHENTICATION FIX VERIFICATION SUMMARY ===' as summary,
    NOW() as test_timestamp;

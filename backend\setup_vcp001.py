#!/usr/bin/env python3
"""VCP_001 Complete Setup Script.

This script automates the complete setup of VCP_001: Database Schema Design & Implementation
for the VigiLens Pharmaceutical Compliance Platform.

Usage:
    python setup_vcp001.py [--reset] [--skip-deps] [--validate-only] [--verbose]
    
Options:
    --reset: Reset database before setup (DESTRUCTIVE)
    --skip-deps: Skip dependency installation
    --validate-only: Only run validation, skip setup
    --verbose: Enable verbose logging
"""

import argparse
import asyncio
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, List

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VCP001Setup:
    """VCP_001 complete setup manager."""
    
    def __init__(self, verbose: bool = False, skip_deps: bool = False):
        self.verbose = verbose
        self.skip_deps = skip_deps
        self.backend_dir = Path(__file__).parent
        self.setup_results = {
            "steps_completed": [],
            "steps_failed": [],
            "warnings": [],
            "recommendations": []
        }
        
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
    
    def log_step(self, step: str, success: bool, message: str = ""):
        """Log setup step result."""
        if success:
            self.setup_results["steps_completed"].append(step)
            logger.info(f"✅ {step}: SUCCESS {message}")
        else:
            self.setup_results["steps_failed"].append(step)
            logger.error(f"❌ {step}: FAILED {message}")
    
    def log_warning(self, message: str):
        """Log warning message."""
        self.setup_results["warnings"].append(message)
        logger.warning(f"⚠️  {message}")
    
    def add_recommendation(self, message: str):
        """Add recommendation."""
        self.setup_results["recommendations"].append(message)
    
    def check_python_version(self) -> bool:
        """Check Python version compatibility."""
        try:
            version = sys.version_info
            if version.major == 3 and version.minor >= 11:
                self.log_step(
                    "Python Version Check", 
                    True, 
                    f"Python {version.major}.{version.minor}.{version.micro}"
                )
                return True
            else:
                self.log_step(
                    "Python Version Check", 
                    False, 
                    f"Python {version.major}.{version.minor}.{version.micro} < 3.11"
                )
                return False
        except Exception as e:
            self.log_step("Python Version Check", False, str(e))
            return False
    
    def check_environment_file(self) -> bool:
        """Check if .env file exists and has required variables."""
        env_file = self.backend_dir / '.env'
        env_example = self.backend_dir / '.env.example'
        
        if not env_file.exists():
            if env_example.exists():
                self.log_warning("No .env file found. Please copy .env.example to .env and configure it.")
                self.add_recommendation("Copy .env.example to .env: cp .env.example .env")
                self.add_recommendation("Edit .env file with your actual configuration values")
            else:
                self.log_step("Environment File Check", False, "No .env or .env.example found")
                return False
        
        # Check required environment variables
        required_vars = ['DATABASE_URL', 'SUPABASE_URL', 'SUPABASE_ANON_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.log_step(
                "Environment Variables", 
                False, 
                f"Missing: {', '.join(missing_vars)}"
            )
            self.add_recommendation(f"Set missing environment variables: {', '.join(missing_vars)}")
            return False
        else:
            self.log_step("Environment Variables", True, "All required variables found")
            return True
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies."""
        if self.skip_deps:
            self.log_step("Dependency Installation", True, "Skipped as requested")
            return True
        
        requirements_file = self.backend_dir / 'requirements.txt'
        
        if not requirements_file.exists():
            self.log_step("Dependency Installation", False, "requirements.txt not found")
            return False
        
        try:
            # Check if we're in a virtual environment
            in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
            
            if not in_venv:
                self.log_warning("Not running in a virtual environment")
                self.add_recommendation("Consider using a virtual environment: python -m venv venv")
            
            # Install dependencies
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
            
            logger.info("Installing dependencies...")
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                self.log_step("Dependency Installation", True, "All dependencies installed")
                return True
            else:
                self.log_step(
                    "Dependency Installation", 
                    False, 
                    f"pip install failed: {result.stderr}"
                )
                return False
                
        except subprocess.TimeoutExpired:
            self.log_step("Dependency Installation", False, "Installation timed out")
            return False
        except Exception as e:
            self.log_step("Dependency Installation", False, str(e))
            return False
    
    def check_database_connectivity(self) -> bool:
        """Check database connectivity."""
        database_url = os.getenv('DATABASE_URL')
        
        if not database_url:
            self.log_step("Database Connectivity", False, "No DATABASE_URL configured")
            return False
        
        try:
            import asyncpg
            
            async def test_connection():
                try:
                    conn = await asyncpg.connect(database_url)
                    result = await conn.fetchval("SELECT 1")
                    await conn.close()
                    return result == 1
                except Exception as e:
                    logger.debug(f"Database connection test failed: {e}")
                    return False
            
            # Run async test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            connected = loop.run_until_complete(test_connection())
            loop.close()
            
            if connected:
                self.log_step("Database Connectivity", True, "Connection successful")
                return True
            else:
                self.log_step("Database Connectivity", False, "Connection failed")
                self.add_recommendation("Check DATABASE_URL and ensure Supabase project is active")
                return False
                
        except ImportError:
            self.log_step("Database Connectivity", False, "asyncpg not installed")
            return False
        except Exception as e:
            self.log_step("Database Connectivity", False, str(e))
            return False
    
    def run_database_initialization(self, reset: bool = False) -> bool:
        """Run database initialization script."""
        init_script = self.backend_dir / 'init_database.py'
        
        if not init_script.exists():
            self.log_step("Database Initialization", False, "init_database.py not found")
            return False
        
        try:
            cmd = [sys.executable, str(init_script)]
            
            if reset:
                cmd.append('--reset')
                logger.warning("DESTRUCTIVE: Resetting database")
            
            if self.verbose:
                cmd.append('--verbose')
            
            logger.info("Initializing database schema...")
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=120  # 2 minutes timeout
            )
            
            if result.returncode == 0:
                self.log_step("Database Initialization", True, "Schema created successfully")
                if self.verbose:
                    logger.debug(f"Init output: {result.stdout}")
                return True
            else:
                self.log_step(
                    "Database Initialization", 
                    False, 
                    f"Script failed: {result.stderr}"
                )
                return False
                
        except subprocess.TimeoutExpired:
            self.log_step("Database Initialization", False, "Initialization timed out")
            return False
        except Exception as e:
            self.log_step("Database Initialization", False, str(e))
            return False
    
    def run_validation(self) -> bool:
        """Run VCP_001 validation script."""
        validation_script = self.backend_dir / 'validate_vcp001.py'
        
        if not validation_script.exists():
            self.log_step("Validation", False, "validate_vcp001.py not found")
            return False
        
        try:
            cmd = [sys.executable, str(validation_script)]
            
            if self.verbose:
                cmd.append('--verbose')
            
            logger.info("Running VCP_001 validation...")
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=60  # 1 minute timeout
            )
            
            # Validation script returns 0 for PASSED, 1 for MOSTLY_PASSED, 2+ for FAILED
            if result.returncode == 0:
                self.log_step("Validation", True, "All tests passed")
                return True
            elif result.returncode == 1:
                self.log_step("Validation", True, "Most tests passed (minor issues)")
                self.log_warning("Some validation tests failed, but core functionality is working")
                return True
            else:
                self.log_step(
                    "Validation", 
                    False, 
                    f"Validation failed: {result.stderr}"
                )
                if self.verbose:
                    logger.debug(f"Validation output: {result.stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            self.log_step("Validation", False, "Validation timed out")
            return False
        except Exception as e:
            self.log_step("Validation", False, str(e))
            return False
    
    def test_fastapi_startup(self) -> bool:
        """Test FastAPI application startup."""
        main_file = self.backend_dir / 'main.py'
        
        if not main_file.exists():
            self.log_step("FastAPI Startup Test", False, "main.py not found")
            return False
        
        try:
            # Import and test basic app creation
            import sys
            sys.path.insert(0, str(self.backend_dir))
            
            # Try to import the app
            from main import app, settings
            
            # Basic checks
            if app and hasattr(app, 'title'):
                self.log_step(
                    "FastAPI Startup Test", 
                    True, 
                    f"App '{app.title}' loaded successfully"
                )
                return True
            else:
                self.log_step("FastAPI Startup Test", False, "App object invalid")
                return False
                
        except ImportError as e:
            self.log_step("FastAPI Startup Test", False, f"Import failed: {e}")
            self.add_recommendation("Check that all dependencies are installed")
            return False
        except Exception as e:
            self.log_step("FastAPI Startup Test", False, str(e))
            return False
    
    def generate_summary_report(self) -> Dict:
        """Generate final setup summary report."""
        total_steps = len(self.setup_results["steps_completed"]) + len(self.setup_results["steps_failed"])
        success_rate = len(self.setup_results["steps_completed"]) / total_steps if total_steps > 0 else 0
        
        if success_rate == 1.0:
            status = "COMPLETE"
        elif success_rate >= 0.8:
            status = "MOSTLY_COMPLETE"
        else:
            status = "INCOMPLETE"
        
        report = {
            "vcp_001_status": status,
            "success_rate": f"{success_rate:.1%}",
            "steps_completed": len(self.setup_results["steps_completed"]),
            "steps_failed": len(self.setup_results["steps_failed"]),
            "warnings": len(self.setup_results["warnings"]),
            "recommendations": len(self.setup_results["recommendations"]),
            "details": self.setup_results
        }
        
        return report
    
    async def run_complete_setup(self, reset: bool = False, validate_only: bool = False) -> Dict:
        """Run complete VCP_001 setup process."""
        logger.info("Starting VCP_001: Database Schema Design & Implementation Setup")
        logger.info("=" * 70)
        
        if validate_only:
            logger.info("Running validation only...")
            self.run_validation()
        else:
            # Step 1: Check Python version
            self.check_python_version()
            
            # Step 2: Check environment configuration
            self.check_environment_file()
            
            # Step 3: Install dependencies
            self.install_dependencies()
            
            # Step 4: Check database connectivity
            self.check_database_connectivity()
            
            # Step 5: Initialize database
            self.run_database_initialization(reset)
            
            # Step 6: Test FastAPI startup
            self.test_fastapi_startup()
            
            # Step 7: Run validation
            self.run_validation()
        
        # Generate final report
        report = self.generate_summary_report()
        
        logger.info("=" * 70)
        logger.info(f"VCP_001 Setup Complete: {report['vcp_001_status']}")
        logger.info(f"Success Rate: {report['success_rate']}")
        logger.info(f"Steps Completed: {report['steps_completed']}")
        logger.info(f"Steps Failed: {report['steps_failed']}")
        
        if self.setup_results["warnings"]:
            logger.info(f"Warnings: {len(self.setup_results['warnings'])}")
            for warning in self.setup_results["warnings"]:
                logger.warning(f"  - {warning}")
        
        if self.setup_results["recommendations"]:
            logger.info("Recommendations:")
            for rec in self.setup_results["recommendations"]:
                logger.info(f"  - {rec}")
        
        return report


async def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description='Setup VCP_001: Database Schema Design & Implementation')
    parser.add_argument('--reset', action='store_true', help='Reset database before setup (DESTRUCTIVE)')
    parser.add_argument('--skip-deps', action='store_true', help='Skip dependency installation')
    parser.add_argument('--validate-only', action='store_true', help='Only run validation, skip setup')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--output', help='Output report to JSON file')
    
    args = parser.parse_args()
    
    # Confirmation for destructive operations
    if args.reset and not args.validate_only:
        confirmation = input("\nWARNING: --reset will delete all data. Type 'RESET' to confirm: ")
        if confirmation != 'RESET':
            logger.info("Reset cancelled")
            return 0
    
    # Initialize setup manager
    setup = VCP001Setup(args.verbose, args.skip_deps)
    
    try:
        # Run setup process
        report = await setup.run_complete_setup(args.reset, args.validate_only)
        
        # Output report to file if requested
        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Setup report written to {args.output}")
        
        # Return appropriate exit code
        if report["vcp_001_status"] == "COMPLETE":
            logger.info("\n🎉 VCP_001 setup completed successfully!")
            logger.info("You can now start the FastAPI server with: uvicorn main:app --reload")
            return 0
        elif report["vcp_001_status"] == "MOSTLY_COMPLETE":
            logger.info("\n⚠️  VCP_001 setup mostly completed with minor issues")
            return 1
        else:
            logger.error("\n❌ VCP_001 setup failed")
            return 2
        
    except Exception as e:
        logger.error(f"Setup process failed: {e}")
        return 3


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
-- SIMPLE STEP-BY-STEP DEBUGGING
-- Run each query separately to see what fails

-- Query 1: Check if organizations table exists and has data
SELECT 'ORGANIZATIONS TABLE' as test, COUNT(*) as total_orgs FROM organizations;

-- Query 2: Check if demo org exists
SELECT 'DEMO ORG' as test, name, id FROM organizations WHERE name = 'demo-pharma-corp';

-- Query 3: Check if user_profiles table exists
SELECT 'USER_PROFILES TABLE' as test, COUNT(*) as total_profiles FROM user_profiles;

-- Query 4: Check user_profiles table structure
SELECT 'COLUMNS' as test, column_name FROM information_schema.columns WHERE table_name = 'user_profiles';

-- Query 5: Check if user_role enum exists
SELECT 'USER_ROLE ENUM' as test, typname FROM pg_type WHERE typname = 'user_role';

-- Query 6: Check current trigger
SELECT 'TRIGGER' as test, trigger_name FROM information_schema.triggers WHER<PERSON> trigger_name = 'handle_new_user_trigger';

-- Query 7: Check current function
SELECT 'FUNCTION' as test, routine_name FROM information_schema.routines WHERE routine_name = 'handle_new_user';

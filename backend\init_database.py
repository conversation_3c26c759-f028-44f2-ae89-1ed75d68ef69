#!/usr/bin/env python3
"""Database initialization script for VigiLens Pharmaceutical Compliance Platform.

This script initializes the complete database schema for VCP_001: Database Schema Design & Implementation.
It runs all migrations in the correct order and sets up the initial data.

Usage:
    python init_database.py [--reset] [--verbose]

Options:
    --reset: Drop existing tables before creating new ones (DESTRUCTIVE)
    --verbose: Enable verbose logging
"""

import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional
from typing import List

import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL: str = os.getenv('DATABASE_URL') or ""
if not DATABASE_URL:
    logger.error("DATABASE_URL environment variable is required")
    sys.exit(1)

# Migration files in execution order
MIGRATION_FILES = [
    "001_create_organizations.sql",
    "002_create_user_profiles.sql",
    "003_create_user_roles.sql",
    "004_create_regulatory_documents.sql",
    "005_create_document_versions.sql",
    "006_create_compliance_frameworks.sql",
    "007_create_audit_trail.sql"
]

# Get the migrations directory
MIGRATIONS_DIR = Path(__file__).parent / "migrations"


class DatabaseInitializer:
    """Database initialization and migration manager."""

    def __init__(self, database_url: str, verbose: bool = False):
        self.database_url = database_url
        self.verbose = verbose
        self.connection: Optional[asyncpg.Connection] = None

        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)

    async def connect(self) -> None:
        """Establish database connection."""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logger.info("Connected to database successfully")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    async def disconnect(self) -> None:
        """Close database connection."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from database")

    async def check_connection(self) -> bool:
        """Check if database connection is working."""
        if not self.connection:
            logger.error("No database connection available")
            return False
        try:
            result = await self.connection.fetchval("SELECT 1")
            return result == 1
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False

    async def create_migration_table(self) -> None:
        """Create migrations tracking table if it doesn't exist."""
        if not self.connection:
            raise RuntimeError("No database connection available")

        query = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            id SERIAL PRIMARY KEY,
            filename VARCHAR(255) NOT NULL UNIQUE,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            checksum VARCHAR(64),
            success BOOLEAN DEFAULT true
        );
        """
        await self.connection.execute(query)
        logger.info("Migration tracking table ready")

    async def is_migration_applied(self, filename: str) -> bool:
        """Check if a migration has already been applied."""
        if not self.connection:
            return False

        result = await self.connection.fetchval(
            "SELECT COUNT(*) FROM schema_migrations WHERE filename = $1 AND success = true",
            filename
        )
        return result > 0

    async def record_migration(self, filename: str, checksum: str, success: bool = True) -> None:
        """Record migration application in tracking table."""
        if not self.connection:
            raise RuntimeError("No database connection available")

        await self.connection.execute(
            """
            INSERT INTO schema_migrations (filename, checksum, success)
            VALUES ($1, $2, $3)
            ON CONFLICT (filename) DO UPDATE SET
                applied_at = NOW(),
                checksum = EXCLUDED.checksum,
                success = EXCLUDED.success
            """,
            filename, checksum, success
        )

    def calculate_checksum(self, content: str) -> str:
        """Calculate MD5 checksum of migration content."""
        import hashlib
        return hashlib.md5(content.encode()).hexdigest()

    async def run_migration(self, filename: str) -> bool:
        """Run a single migration file."""
        migration_path = MIGRATIONS_DIR / filename

        if not migration_path.exists():
            logger.error(f"Migration file not found: {migration_path}")
            return False

        # Check if already applied
        if await self.is_migration_applied(filename):
            logger.info(f"Migration {filename} already applied, skipping")
            return True

        try:
            # Read migration content
            with open(migration_path, 'r', encoding='utf-8') as f:
                content = f.read()

            checksum = self.calculate_checksum(content)

            logger.info(f"Applying migration: {filename}")

            # Execute migration in a transaction
            if not self.connection:
                raise RuntimeError("No database connection available")

            async with self.connection.transaction():
                await self.connection.execute(content)
                await self.record_migration(filename, checksum, True)

            logger.info(f"Migration {filename} applied successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to apply migration {filename}: {e}")
            # Record failed migration
            try:
                await self.record_migration(filename, "", False)
            except:
                pass
            return False

    async def run_all_migrations(self) -> bool:
        """Run all migrations in order."""
        logger.info("Starting database migration process")

        success_count = 0
        total_count = len(MIGRATION_FILES)

        for filename in MIGRATION_FILES:
            if await self.run_migration(filename):
                success_count += 1
            else:
                logger.error(f"Migration process stopped due to failure in {filename}")
                break

        if success_count == total_count:
            logger.info(f"All {total_count} migrations applied successfully")
            return True
        else:
            logger.error(f"Migration process failed. Applied {success_count}/{total_count} migrations")
            return False

    async def reset_database(self) -> bool:
        """Reset database by dropping all tables (DESTRUCTIVE)."""
        if not self.connection:
            raise RuntimeError("No database connection available")

        logger.warning("DESTRUCTIVE OPERATION: Resetting database")

        try:
            # Drop all tables in reverse dependency order
            drop_queries = [
                "DROP TABLE IF EXISTS compliance_log CASCADE;",
                "DROP TABLE IF EXISTS audit_trail CASCADE;",
                "DROP TABLE IF EXISTS document_compliance_assessments CASCADE;",
                "DROP TABLE IF EXISTS compliance_frameworks CASCADE;",
                "DROP TABLE IF EXISTS document_versions CASCADE;",
                "DROP TABLE IF EXISTS regulatory_documents CASCADE;",
                "DROP TABLE IF EXISTS user_roles CASCADE;",
                "DROP TABLE IF EXISTS user_profiles CASCADE;",
                "DROP TABLE IF EXISTS organizations CASCADE;",
                "DROP TABLE IF EXISTS schema_migrations CASCADE;",

                # Drop custom types
                "DROP TYPE IF EXISTS audit_action CASCADE;",
                "DROP TYPE IF EXISTS regulatory_agency CASCADE;",
                "DROP TYPE IF EXISTS document_status CASCADE;",
                "DROP TYPE IF EXISTS document_type CASCADE;",
                "DROP TYPE IF EXISTS subscription_tier CASCADE;",
                "DROP TYPE IF EXISTS user_role CASCADE;",

                # Drop functions
                "DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;",
                "DROP FUNCTION IF EXISTS handle_new_user() CASCADE;",
                "DROP FUNCTION IF EXISTS update_last_login(UUID) CASCADE;",
                "DROP FUNCTION IF EXISTS check_role_expiration() CASCADE;",
                "DROP FUNCTION IF EXISTS get_user_permissions(UUID) CASCADE;",
                "DROP FUNCTION IF EXISTS verify_user_permission(UUID, TEXT) CASCADE;",
                "DROP FUNCTION IF EXISTS calculate_compliance_score(TEXT, JSONB) CASCADE;",
                "DROP FUNCTION IF EXISTS auto_calculate_risk_level(DECIMAL) CASCADE;",
                "DROP FUNCTION IF EXISTS update_content_vector() CASCADE;",
                "DROP FUNCTION IF EXISTS create_document_version(UUID, UUID, TEXT) CASCADE;",
                "DROP FUNCTION IF EXISTS get_document_version_history(UUID) CASCADE;",
                "DROP FUNCTION IF EXISTS restore_document_version(UUID, INTEGER) CASCADE;",
                "DROP FUNCTION IF EXISTS assess_document_compliance(UUID, UUID, VARCHAR, UUID) CASCADE;",
                "DROP FUNCTION IF EXISTS log_audit_event(UUID, UUID, VARCHAR, audit_action, VARCHAR, UUID, VARCHAR, TEXT, JSONB, INET, TEXT, VARCHAR, BOOLEAN, TEXT, INTEGER, BOOLEAN) CASCADE;",
                "DROP FUNCTION IF EXISTS log_compliance_event(UUID, VARCHAR, VARCHAR, UUID, UUID, JSONB, VARCHAR, VARCHAR, BOOLEAN, TIMESTAMP WITH TIME ZONE, JSONB, TEXT, JSONB) CASCADE;",
                "DROP FUNCTION IF EXISTS cleanup_audit_trail(UUID, INTERVAL) CASCADE;",
                "DROP FUNCTION IF EXISTS trigger_audit_document_changes() CASCADE;",

                # Drop views
                "DROP VIEW IF EXISTS user_permissions_view CASCADE;",
                "DROP VIEW IF EXISTS document_analytics_view CASCADE;",
                "DROP VIEW IF EXISTS document_version_comparison_view CASCADE;",
                "DROP VIEW IF EXISTS compliance_dashboard_view CASCADE;",
                "DROP VIEW IF EXISTS audit_trail_report_view CASCADE;",
                "DROP VIEW IF EXISTS compliance_report_view CASCADE;"
            ]

            for query in drop_queries:
                try:
                    await self.connection.execute(query)
                except Exception as e:
                    if self.verbose:
                        logger.debug(f"Drop query failed (may be expected): {query} - {e}")

            logger.info("Database reset completed")
            return True

        except Exception as e:
            logger.error(f"Database reset failed: {e}")
            return False

    async def verify_schema(self) -> bool:
        """Verify that all expected tables and functions exist."""
        if not self.connection:
            logger.error("No database connection available")
            return False

        logger.info("Verifying database schema")

        expected_tables = [
            'organizations',
            'user_profiles',
            'user_roles',
            'regulatory_documents',
            'document_versions',
            'compliance_frameworks',
            'document_compliance_assessments',
            'audit_trail',
            'compliance_log',
            'schema_migrations'
        ]

        expected_functions = [
            'update_updated_at_column',
            'handle_new_user',
            'update_last_login',
            'check_role_expiration',
            'get_user_permissions',
            'verify_user_permission',
            'calculate_compliance_score',
            'auto_calculate_risk_level',
            'update_content_vector',
            'create_document_version',
            'get_document_version_history',
            'restore_document_version',
            'assess_document_compliance',
            'log_audit_event',
            'log_compliance_event',
            'cleanup_audit_trail',
            'trigger_audit_document_changes'
        ]

        expected_views = [
            'user_permissions_view',
            'document_analytics_view',
            'document_version_comparison_view',
            'compliance_dashboard_view',
            'audit_trail_report_view',
            'compliance_report_view'
        ]

        try:
            # Check tables
            for table in expected_tables:
                result = await self.connection.fetchval(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1",
                    table
                )
                if result == 0:
                    logger.error(f"Missing table: {table}")
                    return False
                elif self.verbose:
                    logger.debug(f"Table verified: {table}")

            # Check functions
            for function in expected_functions:
                result = await self.connection.fetchval(
                    "SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = $1",
                    function
                )
                if result == 0:
                    logger.error(f"Missing function: {function}")
                    return False
                elif self.verbose:
                    logger.debug(f"Function verified: {function}")

            # Check views
            for view in expected_views:
                result = await self.connection.fetchval(
                    "SELECT COUNT(*) FROM information_schema.views WHERE table_name = $1",
                    view
                )
                if result == 0:
                    logger.error(f"Missing view: {view}")
                    return False
                elif self.verbose:
                    logger.debug(f"View verified: {view}")

            logger.info("Schema verification completed successfully")
            return True

        except Exception as e:
            logger.error(f"Schema verification failed: {e}")
            return False

    async def get_migration_status(self) -> List[dict]:
        """Get status of all migrations."""
        if not self.connection:
            return []

        try:
            await self.create_migration_table()

            results = await self.connection.fetch(
                "SELECT filename, applied_at, success FROM schema_migrations ORDER BY applied_at"
            )

            status = []
            applied_files = {row['filename'] for row in results if row['success']}

            for filename in MIGRATION_FILES:
                if filename in applied_files:
                    row = next(r for r in results if r['filename'] == filename and r['success'])
                    status.append({
                        'filename': filename,
                        'status': 'applied',
                        'applied_at': row['applied_at']
                    })
                else:
                    status.append({
                        'filename': filename,
                        'status': 'pending',
                        'applied_at': None
                    })

            return status

        except Exception as e:
            logger.error(f"Failed to get migration status: {e}")
            return []


async def main():
    """Main initialization function."""
    parser = argparse.ArgumentParser(description='Initialize VigiLens database schema')
    parser.add_argument('--reset', action='store_true', help='Reset database (DESTRUCTIVE)')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--status', action='store_true', help='Show migration status only')
    parser.add_argument('--verify', action='store_true', help='Verify schema only')

    args = parser.parse_args()

    # Initialize database manager
    db_init = DatabaseInitializer(DATABASE_URL, args.verbose)

    try:
        # Connect to database
        await db_init.connect()

        # Check connection
        if not await db_init.check_connection():
            logger.error("Database connection check failed")
            return 1

        # Show migration status if requested
        if args.status:
            status = await db_init.get_migration_status()
            print("\nMigration Status:")
            print("-" * 50)
            for item in status:
                applied_at = item['applied_at'].strftime('%Y-%m-%d %H:%M:%S') if item['applied_at'] else 'N/A'
                print(f"{item['filename']:<35} {item['status']:<10} {applied_at}")
            return 0

        # Verify schema if requested
        if args.verify:
            success = await db_init.verify_schema()
            return 0 if success else 1

        # Reset database if requested
        if args.reset:
            confirmation = input("\nWARNING: This will delete all data. Type 'RESET' to confirm: ")
            if confirmation != 'RESET':
                logger.info("Reset cancelled")
                return 0

            if not await db_init.reset_database():
                return 1

        # Create migration tracking table
        await db_init.create_migration_table()

        # Run migrations
        if not await db_init.run_all_migrations():
            return 1

        # Verify schema
        if not await db_init.verify_schema():
            logger.warning("Schema verification failed, but migrations completed")
            return 1

        logger.info("\n" + "="*60)
        logger.info("DATABASE INITIALIZATION COMPLETED SUCCESSFULLY")
        logger.info("VCP_001: Database Schema Design & Implementation - DONE")
        logger.info("="*60)

        return 0

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return 1

    finally:
        await db_init.disconnect()


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))

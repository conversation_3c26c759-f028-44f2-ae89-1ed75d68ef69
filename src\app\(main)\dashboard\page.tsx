'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'

import { DashboardHeader } from './components/dashboard-header'
import { MetricsOverview } from './components/metrics-overview'
import { RecentActivity } from './components/recent-activity'
import { RegulatoryUpdates } from './components/regulatory-updates'
import { RiskDistribution } from './components/risk-distribution'
import { useDashboardData } from './hooks/use-dashboard-data'

/**
 * Dashboard Page - AI Compliance Platform
 *
 * Main dashboard showing compliance metrics, recent activity, and regulatory updates
 */
export default function DashboardPage() {
  usePageMetadata(
    'Dashboard',
    'Overview of your compliance status and activities',
  )

  const { loading } = useDashboardData()

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <DashboardHeader />

      <MetricsOverview />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RegulatoryUpdates />
        </div>
        <div className="space-y-6">
          <RiskDistribution />
          <RecentActivity />
        </div>
      </div>
    </div>
  )
}

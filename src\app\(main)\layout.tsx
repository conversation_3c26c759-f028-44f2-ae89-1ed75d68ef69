import { DashboardLayout } from '@/components/layout/dashboard-layout'
import '@/utils/comprehensive-test-suite' // Load test functions globally
import '@/utils/quick-test' // Load quick test functions

import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: {
    template: '%s | AI Compliance',
    default: 'Dashboard | AI Compliance',
  },
  description: 'AI Compliance management dashboard',
  robots: 'noindex',
}

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <DashboardLayout>{children}</DashboardLayout>
}

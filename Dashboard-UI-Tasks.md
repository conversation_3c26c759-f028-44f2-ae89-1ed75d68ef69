# Dashboard-UI-Tasks.md

## 0. Prerequisites & Environment Setup (only follow if necessary, if not ignore)

- [ ] **Verify stack:** Ensure Next.js 15.1.5, React 19.1.0, Tailwind CSS 4.1.11, TypeScript 5.8.3, shadcn/ui, and all required UI libraries are installed (see `package.json`).
- [ ] **Enforce rules:** Enable strict TypeScript, ESLint naming conventions, and 200-line file limit (see `MY_DEVELOPMENT_RULES.md`).
- [ ] **Directory structure:** Use kebab-case for all files and folders. Place dashboard route in `app/(main)/dashboard/` with components in `app/(main)/dashboard/components/`.
- [ ] **Do not edit `/components/ui/`** (shadcn/ui) components directly—compose or wrap as needed.
- [ ] **Ensure Next.js, Tailwind CSS, and all required UI libraries are installed**
  - [ ] Next.js (next, react, react-dom)
  - [ ] Tailwind CSS (tailwindcss, postcss, autoprefixer)
  - [ ] Radix UI primitives (for popover, dropdown, etc.)
  - [ ] Lucide React (for icons)
  - [ ] Recharts (for charts)
  - [ ] Class variance authority (class-variance-authority)
  - [ ] Utility libraries: clsx, zod, etc.
- [ ] **Verify Tailwind and PostCSS config matches design tokens and custom utilities**
- [ ] **Ensure all color tokens, shadows, radii, and fonts are defined in index.css as per the blueprint**

## 1. Layout & Container Structure

- [x] **Dashboard Layout**
  - [x] Implement a flex row layout: sidebar (fixed 256px) + main content (flex-1, vertical stack).
  - [x] Sidebar: left, always visible, full height, background `oklch(0.1943 0.0434 287.91)`.
  - [x] Main: right, contains header, then scrollable main area with container padding (`p-6`, `mx-auto`).
  - [x] Use semantic HTML: `<aside>`, `<header>`, `<main>`, `<nav>`, `<footer>`.
  - [x] Enforce max 6 DOM nesting levels.
  - [x] Responsive: Sidebar collapses to drawer on mobile (<768px), main content fills width.

## 2. Sidebar (Navigation)

- [x] **Visuals**
  - [x] Background: `oklch(0.1943 0.0434 287.91)` (light), `oklch(0.1943 0.0434 287.91)` (dark).
  - [x] Border: right, `oklch(0.2500 0.0400 287.91)`.
  - [x] Logo: 32x32px, left, with brand color `oklch(0.5413 0.2466 293.01)`.
  - [x] Title: "AI Compliance", font-bold, 18px, color `--sidebar-foreground`.
- [x] **Navigation Links**
  - [x] Main and Account sections, each with heading (uppercase, 12px, 70% opacity).
  - [x] Each link: icon (20px), label, 12px vertical padding, 16px horizontal, rounded-lg.
  - [x] Active: `bg-sidebar-accent`, `text-sidebar-accent-foreground`, shadow-sm.
  - [x] Inactive: `text-sidebar-foreground`, hover `bg-sidebar-accent`.
  - [x] Focus: `focus:ring-2 focus:ring-primary`.
  - [x] Keyboard navigation: tab/arrow keys, aria-current, role="menuitem".
- [x] **User Profile**
  - [x] Avatar: 32x32px, initials, `bg-primary`, `text-primary-foreground`.
  - [x] Name: 14px bold, role, 12px muted.
  - [x] Link to profile, hover `bg-sidebar-accent`.

## 3. Header (Top Bar)

- [x] **Visuals**
  - [x] Height: 64px, border-bottom, `bg-card`.
  - [x] Title: "Regulatory Autopilot", 20px bold, `text-foreground`.
- [x] **Search Bar**
  - [x] Centered, max-width 400px, left icon (16px), input with `pl-10`, `bg-background`, border.
  - [x] Placeholder: "Quick search for regulations..."
  - [x] Accessible: label, focus ring, keyboard nav.
- [x] **Actions**
  - [x] Theme toggle: sun/moon icon, 36x36px, ghost button, toggles `.dark` class.
  - [x] Notifications: bell icon, badge (red, 20px), popover with 3 notifications (success, warning, info), each with dot, message, time.
  - [x] User menu: avatar, dropdown with profile/settings/logout.
  - [x] All actions keyboard accessible, aria-labels, focus rings.

## 4. Main Content Area

- [x] **Page Header**
  - [x] Title: 28px bold, `text-foreground`.
  - [x] Subtitle: 14px, `text-muted-foreground`, margin-top 4px.
  - [x] "View all updates" button: right, `bg-primary`, hover `bg-primary/90`, icon left.
- [x] **Metrics Section**
  - [x] 3 cards in grid (1col mobile, 3col md+), gap-6.
  - [x] Each card: subtle shadow (light), thin border (dark), radius 8px.
  - [x] Metric: value (32px bold), label (14px), icon (16px), change indicator (success/warning color, icon left, 12px).
  - [x] Chart: area/line/bar, 60px tall, grid/dots/lines SVG bg, chart color matches metric.
  - [x] Responsive: stack on mobile, grid on desktop.
- [x] **Updates & Activities**
  - [x] 2/3 width: "Latest Regulatory Updates" card, list of update cards (title, agency, category, date, severity badge, summary), button top-right.
  - [x] 1/3 width: "Recent Activities" card (list, dot color by type, message, time), "Compliance Calendar" card (month, 3 events, flex rows).
  - [x] All cards: consistent padding, radius, shadow/border, spacing.

## 5. Visual Design & Theming

- [x] **Colors**
  - [x] Use only OKLCH variables from `index.css` and Tailwind theme (see `tailwind.config.ts`).
  - [x] Status: success (`oklch(0.6242 0.1695 149.09)`), warning (`oklch(0.6685 0.1591 57.71)`), info (`oklch(0.5461 0.2152 262.88)`), destructive (`oklch(0.6368 0.2078 25.33)`).
  - [x] Sidebar, card, chart, and accent colors as defined.
- [x] **Typography**
  - [x] Font: Inter, fallback sans-serif.
  - [x] Sizes: 12px, 14px, 16px, 18px, 20px, 28px, 32px as specified above.
  - [x] Weights: 400, 500, 600, 700 as per design.
  - [x] Letter spacing: -0.01em for headers.
- [x] **Spacing & Radius**
  - [x] Use Tailwind spacing scale, `p-6` for main, `gap-6` for grids, `rounded-lg` (8px), `rounded-xl` (12px) for cards.
  - [x] Card padding: 24px, header/content split.
- [x] **Shadows & Borders**
  - [x] Card shadow: `shadow-sm` (light), border only (dark).
  - [x] Chart backgrounds: subtle grid/dots/lines SVG overlays, opacity 0.2 (light), 0.1 (dark).

## 6. Interactivity & Accessibility

- [x] **Keyboard Navigation**
  - [x] All nav links, buttons, dropdowns, popovers, and inputs must be tab-accessible.
  - [x] Use `aria-label`, `aria-current`, `role` attributes as appropriate.
  - [x] Focus rings: `focus:ring-2 focus:ring-primary` everywhere.
- [x] **Screen Reader Support**
  - [x] All icons: `aria-hidden` or `aria-label`.
  - [x] Nav/section landmarks: `role="navigation"`, `role="main"`, `role="banner"`, etc.
  - [x] Announce page title/updates on navigation.
- [x] **Color Contrast**
  - [x] All text and UI elements must meet WCAG 2.1 AA contrast.
- [x] **Responsiveness**
  - [x] Mobile-first: sidebar collapses, grid stacks, padding reduces (`p-3`), font sizes scale down.
  - [x] Test at 320px, 375px, 768px, 1024px, 1440px.

## 7. Metadata & Quality Gates

- [x] **Metadata**
  - [x] Use `usePageMetadata` hook for dashboard page, set title and description.
  - [x] No server metadata for app pages.
- [x] **Quality Gates**
  - [x] All files <200 lines, kebab-case, strict TypeScript, zero `any` types.
  - [x] Lint, type-check, and accessibility test scripts must pass (`npm run verify:all`).
  - [x] No code in `/components/ui/` may be edited.
  - [x] All new code must be modular, readable, and follow single responsibility.

---

> **Reference:** All tasks must strictly follow the general rules in `MY_DEVELOPMENT_RULES.md` (naming, file size, accessibility, architecture, performance, metadata, and quality gates). Ignore CRM-specific instructions. For any ambiguity, default to 2025 best practices and WCAG 2.1 AA compliance.

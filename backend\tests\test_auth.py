"""
Test Suite for VigiLens Authentication System

VCP_002: Authentication & Authorization System
Tests for Supabase Auth integration, JWT validation, RBAC, and MFA.

Following DEVELOPMENT_RULES.md for comprehensive testing.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, AsyncMock
from uuid import uuid4, UUID

from fastapi import HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from auth.dependencies import <PERSON>pabaseJWTBearer, get_current_user, get_current_active_user
from auth.models import AuthUser, PharmaceuticalRole, MFAMethod
from auth.rbac import (
    get_role_permissions,
    has_permission,
    PharmaceuticalPermissions,
    <PERSON><PERSON><PERSON><PERSON>,
    PermissionChe<PERSON>
)
from auth.mfa import TOTPGenerator, MFAManager


class TestSupabaseJWTBearer:
    """Test Supabase JWT Bearer authentication."""

    @pytest.fixture
    def jwt_bearer(self):
        """Create JWT bearer instance for testing."""
        return SupabaseJWTBearer()

    @pytest.fixture
    def mock_supabase_user(self):
        """Mock Supabase user response."""
        return Mock(
            id="123e4567-e89b-12d3-a456-************",
            email="<EMAIL>",
            email_confirmed_at="2025-07-15T10:00:00Z",
            phone="+1234567890",
            phone_confirmed_at="2025-07-15T10:00:00Z",
            created_at="2025-07-15T09:00:00Z",
            updated_at="2025-07-15T10:00:00Z",
            last_sign_in_at="2025-07-15T10:00:00Z",
            user_metadata={},
            app_metadata={}
        )

    @pytest.fixture
    def mock_user_profile(self):
        """Mock user profile from database."""
        return {
            "id": "123e4567-e89b-12d3-a456-************",
            "role": "quality_manager",
            "organization_id": "456e7890-e89b-12d3-a456-************",
            "full_name": "Test User",
            "is_active": True,
            "is_mfa_enabled": True,
            "mfa_methods": ["totp"],
            "organizations": {
                "name": "Test Pharmaceutical Company"
            }
        }

    @pytest.mark.asyncio
    async def test_valid_jwt_authentication(self, jwt_bearer, mock_supabase_user, mock_user_profile):
        """Test successful JWT authentication."""
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid_jwt_token"
        )

        with patch.object(jwt_bearer.supabase.auth, 'get_user') as mock_get_user, \
             patch.object(jwt_bearer.supabase.table, 'select') as mock_select:

            # Mock Supabase responses
            mock_get_user.return_value = Mock(user=mock_supabase_user)
            mock_select.return_value.eq.return_value.single.return_value.execute.return_value = Mock(
                data=mock_user_profile
            )

            # Mock audit logging
            with patch.object(jwt_bearer.supabase.table, 'insert') as mock_insert:
                mock_insert.return_value.execute.return_value = Mock()

                result = await jwt_bearer(credentials)

                assert isinstance(result, AuthUser)
                assert result.email == "<EMAIL>"
                assert result.role == PharmaceuticalRole.QUALITY_MANAGER
                assert result.organization_name == "Test Pharmaceutical Company"
                assert result.is_active is True
                assert result.is_mfa_enabled is True

    @pytest.mark.asyncio
    async def test_invalid_jwt_token(self, jwt_bearer):
        """Test authentication with invalid JWT token."""
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="invalid_jwt_token"
        )

        with patch.object(jwt_bearer.supabase.auth, 'get_user') as mock_get_user:
            mock_get_user.return_value = Mock(user=None)

            with pytest.raises(HTTPException) as exc_info:
                await jwt_bearer(credentials)

            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired token" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_missing_user_profile(self, jwt_bearer, mock_supabase_user):
        """Test authentication when user profile is missing."""
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid_jwt_token"
        )

        with patch.object(jwt_bearer.supabase.auth, 'get_user') as mock_get_user, \
             patch.object(jwt_bearer.supabase.table, 'select') as mock_select:

            mock_get_user.return_value = Mock(user=mock_supabase_user)
            mock_select.return_value.eq.return_value.single.return_value.execute.return_value = Mock(
                data=None
            )

            with pytest.raises(HTTPException) as exc_info:
                await jwt_bearer(credentials)

            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "User profile not found" in exc_info.value.detail


class TestRBAC:
    """Test Role-Based Access Control system."""

    def test_role_permissions_hierarchy(self):
        """Test that role permissions follow hierarchy correctly."""
        # Super admin should have all permissions
        super_admin_perms = get_role_permissions(PharmaceuticalRole.SUPER_ADMIN)
        assert len(super_admin_perms) > 0

        # Viewer should have minimal permissions
        viewer_perms = get_role_permissions(PharmaceuticalRole.VIEWER)
        assert PharmaceuticalPermissions.DOCUMENT_READ in viewer_perms
        assert PharmaceuticalPermissions.DOCUMENT_DELETE not in viewer_perms

        # Quality manager should have quality permissions
        quality_perms = get_role_permissions(PharmaceuticalRole.QUALITY_MANAGER)
        assert PharmaceuticalPermissions.QUALITY_CREATE_BATCH in quality_perms
        assert PharmaceuticalPermissions.QUALITY_APPROVE_BATCH in quality_perms

    def test_permission_checking(self):
        """Test individual permission checking."""
        # Super admin should have all permissions
        assert has_permission(PharmaceuticalRole.SUPER_ADMIN, PharmaceuticalPermissions.DOCUMENT_DELETE)
        assert has_permission(PharmaceuticalRole.SUPER_ADMIN, PharmaceuticalPermissions.USER_DELETE)

        # Viewer should not have admin permissions
        assert not has_permission(PharmaceuticalRole.VIEWER, PharmaceuticalPermissions.USER_DELETE)
        assert not has_permission(PharmaceuticalRole.VIEWER, PharmaceuticalPermissions.DOCUMENT_DELETE)

        # Quality manager should have quality permissions but not user management
        assert has_permission(PharmaceuticalRole.QUALITY_MANAGER, PharmaceuticalPermissions.QUALITY_CREATE_BATCH)
        assert not has_permission(PharmaceuticalRole.QUALITY_MANAGER, PharmaceuticalPermissions.USER_DELETE)

    def test_role_checker_utilities(self):
        """Test role checker utility functions."""
        # Test admin role checking
        assert RoleChecker.is_admin_role(PharmaceuticalRole.SUPER_ADMIN)
        assert RoleChecker.is_admin_role(PharmaceuticalRole.ORG_ADMIN)
        assert not RoleChecker.is_admin_role(PharmaceuticalRole.VIEWER)

        # Test quality role checking
        assert RoleChecker.is_quality_role(PharmaceuticalRole.QUALITY_MANAGER)
        assert RoleChecker.is_quality_role(PharmaceuticalRole.SUPER_ADMIN)
        assert not RoleChecker.is_quality_role(PharmaceuticalRole.VIEWER)

        # Test document signing capability
        assert RoleChecker.can_sign_documents(PharmaceuticalRole.REGULATORY_LEAD)
        assert RoleChecker.can_sign_documents(PharmaceuticalRole.QUALITY_MANAGER)
        assert not RoleChecker.can_sign_documents(PharmaceuticalRole.VIEWER)

    def test_permission_checker_utilities(self):
        """Test permission checker utility functions."""
        # Test document access permissions
        assert PermissionChecker.check_document_access(PharmaceuticalRole.QUALITY_MANAGER, "create")
        assert PermissionChecker.check_document_access(PharmaceuticalRole.VIEWER, "read")
        assert not PermissionChecker.check_document_access(PharmaceuticalRole.VIEWER, "delete")

        # Test quality access permissions
        assert PermissionChecker.check_quality_access(PharmaceuticalRole.QUALITY_MANAGER, "create_batch")
        assert not PermissionChecker.check_quality_access(PharmaceuticalRole.VIEWER, "create_batch")

        # Test audit access permissions
        assert PermissionChecker.check_audit_access(PharmaceuticalRole.AUDITOR, "view_logs")
        assert not PermissionChecker.check_audit_access(PharmaceuticalRole.VIEWER, "export_logs")


class TestTOTP:
    """Test TOTP (Time-based One-Time Password) functionality."""

    def test_totp_generation(self):
        """Test TOTP code generation."""
        secret = "JBSWY3DPEHPK3PXP"  # Test secret
        totp = TOTPGenerator(secret)

        # Generate TOTP for specific timestamp
        timestamp = 1642680000  # Fixed timestamp for testing
        code = totp.generate_totp(timestamp)

        assert len(code) == 6
        assert code.isdigit()

    def test_totp_verification(self):
        """Test TOTP code verification."""
        secret = "JBSWY3DPEHPK3PXP"
        totp = TOTPGenerator(secret)

        timestamp = 1642680000
        code = totp.generate_totp(timestamp)

        # Verify the same code
        assert totp.verify_totp(code, timestamp)

        # Verify with time window
        assert totp.verify_totp(code, timestamp + 15)  # Within window
        assert not totp.verify_totp(code, timestamp + 100)  # Outside window

    def test_secret_generation(self):
        """Test TOTP secret generation."""
        totp = TOTPGenerator("")
        secret = totp.generate_secret()

        assert len(secret) == 32  # Base32 encoded 20 bytes = 32 characters
        assert secret.isalnum()
        assert secret.isupper()

    def test_qr_code_generation(self):
        """Test QR code generation for TOTP setup."""
        secret = "JBSWY3DPEHPK3PXP"
        totp = TOTPGenerator(secret)

        qr_code = totp.generate_qr_code("<EMAIL>")

        assert qr_code.startswith("data:image/png;base64,")
        assert len(qr_code) > 100  # QR code should be substantial


class TestMFAManager:
    """Test Multi-Factor Authentication manager."""

    @pytest.fixture
    def mock_supabase_client(self):
        """Mock Supabase client for testing."""
        return Mock()

    @pytest.fixture
    def mfa_manager(self, mock_supabase_client):
        """Create MFA manager for testing."""
        return MFAManager(mock_supabase_client)

    @pytest.fixture
    def test_user(self):
        """Create test user for MFA testing."""
        return AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.QUALITY_MANAGER,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )

    @pytest.mark.asyncio
    async def test_setup_totp(self, mfa_manager, test_user):
        """Test TOTP setup for user."""
        with patch.object(mfa_manager.supabase.table, 'update') as mock_update:
            mock_update.return_value.eq.return_value.execute.return_value = Mock()

            result = await mfa_manager.setup_totp(test_user)

            assert "secret" in result
            assert "qr_code" in result
            assert "backup_codes" in result
            assert len(result["backup_codes"]) == 10

            # Verify database update was called
            mock_update.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_sms(self, mfa_manager, test_user):
        """Test SMS MFA setup for user."""
        phone = "+1234567890"

        with patch.object(mfa_manager.supabase.table, 'update') as mock_update:
            mock_update.return_value.eq.return_value.execute.return_value = Mock()

            result = await mfa_manager.setup_sms(test_user, phone)

            assert result["phone"] == phone
            assert "message" in result

            # Verify database update was called
            mock_update.assert_called_once()

    @pytest.mark.asyncio
    async def test_verify_totp(self, mfa_manager, test_user):
        """Test TOTP verification."""
        # Mock user profile with TOTP secret
        mock_profile = {"mfa_secret": "JBSWY3DPEHPK3PXP"}

        with patch.object(mfa_manager.supabase.table, 'select') as mock_select:
            mock_select.return_value.eq.return_value.single.return_value.execute.return_value = Mock(
                data=mock_profile
            )

            # Generate valid TOTP code
            totp = TOTPGenerator(mock_profile["mfa_secret"])
            valid_code = totp.generate_totp()

            # Test verification
            result = await mfa_manager.verify_totp(test_user, valid_code)
            assert result is True

            # Test invalid code
            result = await mfa_manager.verify_totp(test_user, "000000")
            assert result is False

    @pytest.mark.asyncio
    async def test_mfa_requirements_by_role(self, mfa_manager):
        """Test MFA requirements based on pharmaceutical roles."""
        # Test super admin - MFA required
        super_admin = AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.SUPER_ADMIN,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        assert mfa_manager.is_mfa_required(super_admin) is True

        # Test viewer - MFA not required
        viewer = AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.VIEWER,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        assert mfa_manager.is_mfa_required(viewer) is False

        # Test sensitive operation - MFA required regardless of role
        assert mfa_manager.is_mfa_required(viewer, "document_sign") is True

    @pytest.mark.asyncio
    async def test_supported_mfa_methods(self, mfa_manager):
        """Test supported MFA methods by role."""
        # Test super admin - supports TOTP and SMS
        super_admin = AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.SUPER_ADMIN,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        methods = mfa_manager.get_supported_mfa_methods(super_admin)
        assert "totp" in methods
        assert "sms" in methods

        # Test viewer - no MFA methods
        viewer = AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.VIEWER,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        methods = mfa_manager.get_supported_mfa_methods(viewer)
        assert methods == []


class TestAuthDependencies:
    """Test authentication dependencies."""

    @pytest.fixture
    def test_user(self):
        """Create test user for dependency testing."""
        return AuthUser(
            id=UUID("123e4567-e89b-12d3-a456-************"),
            email="<EMAIL>",
            email_confirmed_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.QUALITY_MANAGER,
            organization_id=UUID("456e7890-e89b-12d3-a456-************"),
            organization_name="Test Company",
            is_active=True,
            is_mfa_enabled=True,
            mfa_methods=[MFAMethod.TOTP]
        )

    @pytest.mark.asyncio
    async def test_get_current_user(self, test_user):
        """Test get_current_user dependency."""
        result = await get_current_user(test_user)
        assert result == test_user

    @pytest.mark.asyncio
    async def test_get_current_active_user_success(self, test_user):
        """Test get_current_active_user with active user."""
        result = await get_current_active_user(test_user)
        assert result == test_user

    @pytest.mark.asyncio
    async def test_get_current_active_user_inactive(self, test_user):
        """Test get_current_active_user with inactive user."""
        test_user.is_active = False

        with pytest.raises(HTTPException) as exc_info:
            await get_current_active_user(test_user)

        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "inactive" in exc_info.value.detail.lower()

    @pytest.mark.asyncio
    async def test_get_current_active_user_unconfirmed_email(self, test_user):
        """Test get_current_active_user with unconfirmed email."""
        test_user.email_confirmed_at = None

        with pytest.raises(HTTPException) as exc_info:
            await get_current_active_user(test_user)

        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "not confirmed" in exc_info.value.detail.lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""Supabase client configuration for <PERSON>igi<PERSON>ens backend."""

import os
from typing import Optional

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class SupabaseConfig:
    """Supabase configuration and client management."""
    
    def __init__(self):
        self.url: str = os.getenv("SUPABASE_URL", "")
        self.anon_key: str = os.getenv("SUPABASE_ANON_KEY", "")
        self.service_role_key: str = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
        
        if not self.url or not self.service_role_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        
        self._client: Optional[Client] = None
        self._anon_client: Optional[Client] = None
    
    @property
    def client(self) -> Client:
        """Get Supabase client with service role key (full access)."""
        if self._client is None:
            self._client = create_client(self.url, self.service_role_key)
        return self._client
    
    @property
    def anon_client(self) -> Client:
        """Get Supabase client with anon key (RLS enforced)."""
        if self._anon_client is None:
            if not self.anon_key:
                raise ValueError("SUPABASE_ANON_KEY must be set for anon client")
            self._anon_client = create_client(self.url, self.anon_key)
        return self._anon_client


# Global Supabase configuration instance
supabase_config = SupabaseConfig()

# Convenience exports
supabase = supabase_config.client
supabase_anon = supabase_config.anon_client

aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
APScheduler==3.11.0
asttokens==3.0.0
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
black==25.1.0
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
chromadb==1.0.15
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
coverage==7.9.2
crewai==0.148.0
cryptography==45.0.5
dataclasses-json==0.6.7
decorator==5.2.1
deprecation==2.1.0
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docstring_parser==0.16
durationpy==0.10
ecdsa==0.19.1
email_validator==2.2.0
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.116.1
filelock==3.18.0
flake8==7.3.0
flatbuffers==25.2.10
frozenlist==1.7.0
fsspec==2025.7.0
google-auth==2.40.3
googleapis-common-protos==1.70.0
gotrue==2.12.3
greenlet==3.2.3
grpcio==1.73.1
gunicorn==23.0.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.4
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
iniconfig==2.1.0
instructor==1.10.0
ipython==9.4.0
ipython_pygments_lexers==1.1.1
isort==6.0.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
json5==0.12.0
json_repair==0.25.2
jsonpatch==1.33
jsonpickle==4.1.1
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kubernetes==33.1.0
langchain==0.3.26
langchain-community==0.3.27
langchain-core==0.3.68
langchain-text-splitters==0.3.8
langgraph==0.5.3
langgraph-checkpoint==2.1.1
langgraph-prebuilt==0.5.2
langgraph-sdk==0.1.73
langsmith==0.4.5
litellm==1.72.6
lxml==6.0.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
multidict==6.6.3
mypy==1.17.0
mypy_extensions==1.1.0
networkx==3.5
numpy==2.3.1
oauthlib==3.3.1
onnxruntime==1.22.0
openai==1.97.0
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp-proto-common==1.35.0
opentelemetry-exporter-otlp-proto-grpc==1.35.0
opentelemetry-exporter-otlp-proto-http==1.35.0
opentelemetry-proto==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
orjson==3.11.0
ormsgpack==1.10.0
overrides==7.7.0
packaging==24.2
pandas==2.3.1
parso==0.8.4
pathspec==0.12.1
pdfminer.six==20250506
pdfplumber==0.11.7
pillow==11.3.0
platformdirs==4.3.8
pluggy==1.6.0
portalocker==3.2.0
postgrest==1.1.1
posthog==5.4.0
prompt_toolkit==3.0.51
propcache==0.3.2
protobuf==6.31.1
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.1
pycodestyle==2.14.0
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyflakes==3.4.0
Pygments==2.19.2
PyJWT==2.10.1
PyMuPDF==1.26.3
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pytest==8.4.1
pytest-asyncio==1.0.0
pytest-cov==6.2.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-jose==3.5.0
python-magic==0.4.27
python-multipart==0.0.20
pytz==2025.2
pyvis==0.3.2
pywin32==311
PyYAML==6.0.2
qdrant-client==1.15.0
qrcode==8.2
realtime==2.5.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.26.0
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.7.1
scipy==1.16.0
sentence-transformers==5.0.0
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.47.1
storage3==0.12.0
StrEnum==0.4.15
structlog==25.4.0
supabase==2.16.0
supafunc==0.10.1
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.2
tomli==2.2.1
tomli_w==1.2.0
torch==2.7.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.53.2
typer==0.16.0
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.5.0
uv==0.8.0
uvicorn==0.35.0
watchfiles==1.1.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0

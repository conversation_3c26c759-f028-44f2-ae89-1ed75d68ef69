import { useState, useRef, useEffect } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Bot,
  Send,
  Plus,
  MessageSquare,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Lightbulb,
  BookOpen,
  Download,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Upload,
  Paperclip,
  X,
  Search,
} from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  type?: "text" | "suggestion" | "document";
  metadata?: any;
  attachments?: AttachedDocument[];
}

interface AttachedDocument {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadedDate: string;
  category: string;
}

interface UploadedDocument {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadedDate: string;
  category: string;
  description: string;
}

export default function AIAssistant() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content:
        "Hello! I'm your AI Compliance Assistant. I can help you with regulatory compliance questions and document analysis. What would you like to know?",
      sender: "ai",
      timestamp: new Date(),
      type: "text",
    },
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [attachedDocuments, setAttachedDocuments] = useState<
    AttachedDocument[]
  >([]);
  const [isDocumentPopoverOpen, setIsDocumentPopoverOpen] = useState(false);
  const [documentSearchQuery, setDocumentSearchQuery] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mock uploaded documents from document library
  const uploadedDocuments: UploadedDocument[] = [
    {
      id: "doc1",
      name: "Process Validation Protocol v2.1",
      type: "PDF",
      size: "2.4 MB",
      uploadedDate: "2023-06-15",
      category: "Validation",
      description: "Process validation protocol for sterile manufacturing",
    },
    {
      id: "doc2",
      name: "GMP Audit Checklist 2023",
      type: "DOCX",
      size: "875 KB",
      uploadedDate: "2023-06-10",
      category: "Audit",
      description: "Comprehensive GMP audit checklist for Q2 2023",
    },
    {
      id: "doc3",
      name: "Data Integrity SOP",
      type: "PDF",
      size: "1.8 MB",
      uploadedDate: "2023-06-08",
      category: "SOP",
      description: "Standard operating procedure for data integrity compliance",
    },
    {
      id: "doc4",
      name: "FDA Warning Letter Response",
      type: "PDF",
      size: "945 KB",
      uploadedDate: "2023-06-05",
      category: "Regulatory",
      description: "Response to FDA 483 observations and warning letter",
    },
    {
      id: "doc5",
      name: "Annual Product Review Template",
      type: "XLSX",
      size: "1.2 MB",
      uploadedDate: "2023-06-01",
      category: "Template",
      description: "Annual product review template for pharmaceutical products",
    },
  ];

  const quickActions = [
    {
      icon: FileText,
      label: "Analyze Document",
      description: "Upload a document for compliance analysis",
      action: "document_analysis",
    },
    {
      icon: AlertTriangle,
      label: "Check Compliance",
      description: "Verify compliance against regulations",
      action: "compliance_check",
    },
    {
      icon: BookOpen,
      label: "Regulatory Updates",
      description: "Get latest regulatory changes",
      action: "regulatory_updates",
    },
    {
      icon: Lightbulb,
      label: "Best Practices",
      description: "Learn industry best practices",
      action: "best_practices",
    },
  ];

  const conversationHistory = [
    {
      id: "conv1",
      title: "FDA Process Validation Guidance",
      lastMessage: "What are the key requirements...",
      timestamp: "2 hours ago",
      messageCount: 12,
    },
    {
      id: "conv2",
      title: "Data Integrity Requirements",
      lastMessage: "How to implement ALCOA+...",
      timestamp: "Yesterday",
      messageCount: 8,
    },
    {
      id: "conv3",
      title: "GMP Compliance Questions",
      lastMessage: "Annual product review process...",
      timestamp: "3 days ago",
      messageCount: 15,
    },
  ];

  const suggestedQuestions = [
    "What are the latest FDA guidance documents for pharmaceutical manufacturing?",
    "How do I implement a risk-based approach to process validation?",
    "What are the key requirements for data integrity in pharmaceutical manufacturing?",
    "Can you explain the differences between ICH Q8, Q9, and Q10?",
    "What documentation is required for computer system validation?",
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && attachedDocuments.length === 0) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage || "Shared documents for analysis",
      sender: "user",
      timestamp: new Date(),
      type: "text",
      attachments:
        attachedDocuments.length > 0 ? [...attachedDocuments] : undefined,
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setAttachedDocuments([]);
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const hasDocuments =
        userMessage.attachments && userMessage.attachments.length > 0;
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: hasDocuments
          ? generateDocumentAnalysisResponse(userMessage.attachments!)
          : generateAIResponse(inputMessage),
        sender: "ai",
        timestamp: new Date(),
        type: "text",
      };
      setMessages((prev) => [...prev, aiResponse]);
      setIsTyping(false);
    }, 2000);
  };

  const handleAttachDocument = (document: UploadedDocument) => {
    const attachedDoc: AttachedDocument = {
      id: document.id,
      name: document.name,
      type: document.type,
      size: document.size,
      uploadedDate: document.uploadedDate,
      category: document.category,
    };

    if (!attachedDocuments.find((doc) => doc.id === attachedDoc.id)) {
      setAttachedDocuments((prev) => [...prev, attachedDoc]);
    }
    setIsDocumentPopoverOpen(false);
  };

  const handleRemoveAttachment = (docId: string) => {
    setAttachedDocuments((prev) => prev.filter((doc) => doc.id !== docId));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      Array.from(files).forEach((file) => {
        const newDoc: AttachedDocument = {
          id: `upload_${Date.now()}_${Math.random()}`,
          name: file.name,
          type: file.name.split(".").pop()?.toUpperCase() || "FILE",
          size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
          uploadedDate: new Date().toISOString().split("T")[0],
          category: "Uploaded",
        };
        setAttachedDocuments((prev) => [...prev, newDoc]);
      });
    }
    setIsDocumentPopoverOpen(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const generateDocumentAnalysisResponse = (
    docs: AttachedDocument[],
  ): string => {
    const docNames = docs.map((doc) => doc.name).join(", ");
    return `I've analyzed the ${docs.length} document(s) you've shared: ${docNames}.\n\n**Document Analysis Summary:**\n\n${docs
      .map(
        (doc) =>
          `📄 **${doc.name}** (${doc.type})\n- Category: ${doc.category}\n- Size: ${doc.size}\n- Analysis: This document appears to be related to ${doc.category.toLowerCase()} compliance. I can help identify key compliance requirements, gaps, and recommendations.`,
      )
      .join(
        "\n\n",
      )}\n\n**Key Compliance Insights:**\n- All documents have been scanned for regulatory compliance\n- No critical compliance gaps identified\n- Recommendations available for process improvements\n\nWould you like me to provide specific analysis for any particular document or identify compliance requirements across all documents?`;
  };

  const generateAIResponse = (userInput: string): string => {
    if (userInput.toLowerCase().includes("process validation")) {
      return "Process validation is a critical component of pharmaceutical manufacturing that ensures consistent product quality. According to FDA guidance, it consists of three stages:\n\n1. **Process Design (Stage 1)**: Define commercial manufacturing process based on development studies\n2. **Process Qualification (Stage 2)**: Evaluate process design to determine if capable of reproducible commercial manufacturing\n3. **Continued Process Verification (Stage 3)**: Ongoing assurance that process remains in control\n\nKey requirements include:\n- Risk assessment and control strategy\n- Statistical analysis of process data\n- Validation protocols and reports\n- Change control procedures\n\nWould you like me to elaborate on any specific stage or requirement?";
    } else if (userInput.toLowerCase().includes("data integrity")) {
      return "Data integrity is fundamental to pharmaceutical quality systems and regulatory compliance. The ALCOA+ principles provide the framework:\n\n**ALCOA:**\n- **Attributable**: Data must be attributable to the person generating it\n- **Legible**: Data must be readable and permanent\n- **Contemporaneous**: Data recorded at time of activity\n- **Original**: First recording or certified copy\n- **Accurate**: Data must be correct and complete\n\n**Plus (+):**\n- **Complete**: All data generated must be available\n- **Consistent**: Data format and procedures uniform\n- **Enduring**: Data preserved throughout retention period\n- **Available**: Data readily retrievable for review\n\nImplementation requires robust procedures, training, and technical controls. Do you need specific guidance on any aspect?";
    } else {
      return "I understand you're asking about compliance matters. Based on current regulatory guidance and industry best practices, I can provide detailed information on this topic. Let me analyze the specific requirements and provide you with actionable recommendations.\n\nFor the most accurate and up-to-date information, I recommend consulting the latest regulatory guidance documents and considering your specific operational context.\n\nWould you like me to elaborate on any particular aspect or provide more specific guidance?";
    }
  };

  const handleQuickAction = (action: string) => {
    const actionMessages: { [key: string]: string } = {
      document_analysis:
        "I'd like to analyze a document for compliance requirements.",
      compliance_check:
        "Can you help me check compliance against current regulations?",
      regulatory_updates:
        "What are the latest regulatory updates I should be aware of?",
      best_practices:
        "Can you share industry best practices for pharmaceutical compliance?",
    };

    setInputMessage(actionMessages[action] || "");
  };

  const filteredDocuments = uploadedDocuments.filter(
    (doc) =>
      doc.name.toLowerCase().includes(documentSearchQuery.toLowerCase()) ||
      doc.category.toLowerCase().includes(documentSearchQuery.toLowerCase()) ||
      doc.description.toLowerCase().includes(documentSearchQuery.toLowerCase()),
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              AI Compliance Assistant
            </h1>
            <p className="text-muted-foreground mt-1">
              Get help with regulatory compliance questions and document
              analysis
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-success text-success-foreground">
              <CheckCircle className="mr-1 h-3 w-3" />
              Online
            </Badge>
            <Button variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              New Chat
            </Button>
          </div>
        </div>

        <div className="flex gap-6 h-[calc(100vh-12rem)]">
          {/* Conversation History */}
          <div className="w-80">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-base">Conversations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {conversationHistory.map((conv) => (
                  <div
                    key={conv.id}
                    className="p-3 rounded-lg border hover:bg-muted/30 cursor-pointer transition-all duration-200 hover:border-primary/20 hover:shadow-sm"
                  >
                    <h4 className="font-medium text-sm mb-1 line-clamp-1">
                      {conv.title}
                    </h4>
                    <p className="text-xs text-muted-foreground mb-1 line-clamp-1">
                      {conv.lastMessage}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{conv.timestamp}</span>
                      <span>{conv.messageCount} msgs</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            <Card className="flex-1 flex flex-col">
              <CardHeader className="border-b py-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-base">
                        Compliance AI Assistant
                      </CardTitle>
                      <p className="text-xs text-muted-foreground">
                        Ready to help with regulatory compliance questions
                      </p>
                    </div>
                  </div>
                  {/* Quick Actions in Header */}
                  <div className="flex space-x-1">
                    {quickActions.slice(0, 2).map((action, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="h-8 px-2 hover:bg-muted hover:text-foreground"
                        onClick={() => handleQuickAction(action.action)}
                      >
                        <action.icon className="h-3 w-3 mr-1" />
                        <span className="text-xs">{action.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 overflow-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.sender === "user"
                        ? "justify-end"
                        : "justify-start"
                    }`}
                  >
                    {message.sender === "ai" && (
                      <Avatar className="w-7 h-7 flex-shrink-0">
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          <Bot className="h-3 w-3" />
                        </AvatarFallback>
                      </Avatar>
                    )}

                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.sender === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      {/* Show attachments if present */}
                      {message.attachments &&
                        message.attachments.length > 0 && (
                          <div className="mb-3 space-y-2">
                            {message.attachments.map((doc) => (
                              <div
                                key={doc.id}
                                className={`flex items-center space-x-2 p-2 rounded border ${
                                  message.sender === "user"
                                    ? "border-primary-foreground/20 bg-primary-foreground/10"
                                    : "border-border bg-background/50"
                                }`}
                              >
                                <FileText className="h-4 w-4" />
                                <div className="flex-1 min-w-0">
                                  <p className="text-xs font-medium truncate">
                                    {doc.name}
                                  </p>
                                  <p className="text-xs opacity-70">
                                    {doc.type} • {doc.size}
                                  </p>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {doc.category}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        )}

                      <div className="whitespace-pre-wrap text-sm">
                        {message.content}
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-70">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        {message.sender === "ai" && (
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-5 w-5 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-5 w-5 p-0"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-5 w-5 p-0"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    {message.sender === "user" && (
                      <Avatar className="w-7 h-7 flex-shrink-0">
                        <AvatarFallback className="bg-secondary">
                          <User className="h-3 w-3" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                ))}

                {isTyping && (
                  <div className="flex gap-3">
                    <Avatar className="w-7 h-7">
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        <Bot className="h-3 w-3" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100" />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200" />
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </CardContent>

              {/* Input Area */}
              <div className="border-t p-4">
                {/* Attached Documents Preview */}
                {attachedDocuments.length > 0 && (
                  <div className="mb-3 p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">
                        Attached Documents ({attachedDocuments.length})
                      </span>
                    </div>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {attachedDocuments.map((doc) => (
                        <div
                          key={doc.id}
                          className="flex items-center space-x-2 p-2 bg-background rounded border"
                        >
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium truncate">
                              {doc.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {doc.type} • {doc.size}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {doc.category}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => handleRemoveAttachment(doc.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 mb-3">
                  <Input
                    placeholder="Ask me anything about compliance..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                    className="flex-1"
                  />

                  {/* Document Attachment Popover */}
                  <Popover
                    open={isDocumentPopoverOpen}
                    onOpenChange={setIsDocumentPopoverOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="hover:bg-primary hover:text-primary-foreground hover:border-primary"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-96 p-0" align="end" side="top">
                      <div className="p-4">
                        <h4 className="font-medium mb-3">Add Documents</h4>

                        {/* Upload New Document */}
                        <div className="space-y-3">
                          <Button
                            variant="outline"
                            className="w-full justify-start hover:bg-primary hover:text-primary-foreground hover:border-primary"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            Upload New Document
                          </Button>
                          <input
                            ref={fileInputRef}
                            type="file"
                            multiple
                            accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                            onChange={handleFileUpload}
                            className="hidden"
                          />

                          <Separator />

                          {/* Search Existing Documents */}
                          <div className="space-y-2">
                            <div className="relative">
                              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                              <Input
                                placeholder="Search uploaded documents..."
                                value={documentSearchQuery}
                                onChange={(e) =>
                                  setDocumentSearchQuery(e.target.value)
                                }
                                className="pl-10"
                              />
                            </div>

                            <ScrollArea className="h-60 w-full">
                              <div className="space-y-2">
                                {filteredDocuments.map((doc) => (
                                  <div
                                    key={doc.id}
                                    className="flex items-start space-x-3 p-2 rounded hover:bg-muted/30 cursor-pointer transition-colors"
                                    onClick={() => handleAttachDocument(doc)}
                                  >
                                    <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div className="flex-1 min-w-0">
                                      <p className="text-sm font-medium truncate">
                                        {doc.name}
                                      </p>
                                      <p className="text-xs text-muted-foreground line-clamp-2">
                                        {doc.description}
                                      </p>
                                      <div className="flex items-center gap-2 mt-1">
                                        <Badge
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          {doc.category}
                                        </Badge>
                                        <span className="text-xs text-muted-foreground">
                                          {doc.type} • {doc.size}
                                        </span>
                                      </div>
                                    </div>
                                    {attachedDocuments.find(
                                      (attached) => attached.id === doc.id,
                                    ) && (
                                      <CheckCircle className="h-4 w-4 text-success" />
                                    )}
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          </div>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>

                  <Button
                    onClick={handleSendMessage}
                    disabled={
                      (!inputMessage.trim() &&
                        attachedDocuments.length === 0) ||
                      isTyping
                    }
                    className="bg-primary hover:bg-primary/90"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                {/* Suggested Questions Below Chat */}
                <div className="flex flex-wrap gap-2">
                  {suggestedQuestions.slice(0, 3).map((question, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      className="text-xs h-7 px-2 hover:bg-muted hover:text-foreground"
                      onClick={() => setInputMessage(question)}
                    >
                      {question.slice(0, 30)}...
                    </Button>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

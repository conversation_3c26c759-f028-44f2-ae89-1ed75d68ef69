-- Migration: 002_create_user_profiles.sql
-- Description: Create user profiles table integrated with Supabase Auth
-- Created: 2025-01-11
-- Dependencies: 001_create_organizations.sql

-- Create user profiles table
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  email VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user', 'viewer')),
  department VARCHAR(100),
  phone VARCHAR(20),
  last_login TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_profiles_organization_id ON user_profiles(organization_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_is_active ON user_profiles(is_active);
CREATE INDEX idx_user_profiles_department ON user_profiles(department);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Organization admins can view all users in their org" ON user_profiles
  FOR SELECT USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('admin', 'manager')
    )
  );

CREATE POLICY "Organization admins can manage users in their org" ON user_profiles
  FOR ALL USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role = 'admin'
    )
  );

-- Create trigger for updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  default_org_id UUID;
BEGIN
  -- Get the default organization ID (for demo purposes)
  SELECT id INTO default_org_id FROM organizations WHERE domain = 'demo.vigilens.com' LIMIT 1;
  
  -- Insert user profile
  INSERT INTO user_profiles (id, organization_id, email, full_name, role)
  VALUES (
    NEW.id,
    COALESCE((NEW.raw_user_meta_data ->> 'organization_id')::uuid, default_org_id),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data ->> 'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data ->> 'role', 'user')
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create function to update last_login
CREATE OR REPLACE FUNCTION update_last_login(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_profiles 
  SET last_login = NOW() 
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE user_profiles IS 'User profiles integrated with Supabase Auth for pharmaceutical compliance platform';
COMMENT ON COLUMN user_profiles.id IS 'References auth.users(id) from Supabase Auth';
COMMENT ON COLUMN user_profiles.organization_id IS 'Organization the user belongs to';
COMMENT ON COLUMN user_profiles.role IS 'User role: admin, manager, user, viewer';
COMMENT ON COLUMN user_profiles.preferences IS 'JSON user preferences for notifications, dashboard, etc.';
COMMENT ON FUNCTION handle_new_user() IS 'Automatically creates user profile when new user registers via Supabase Auth';
COMMENT ON FUNCTION update_last_login(UUID) IS 'Updates user last login timestamp';
-- Check actual electronic_signatures table structure and fix function
-- Run this to see what columns exist and create a working function

-- Check what columns actually exist in electronic_signatures table
SELECT 
    'ELECTRONIC_SIGNATURES COLUMNS' as info,
    array_agg(column_name ORDER BY ordinal_position) as columns
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND table_schema = 'public';

-- Check what columns actually exist in regulatory_documents table
SELECT 
    'REGULATORY_DOCUMENTS COLUMNS' as info,
    array_agg(column_name ORDER BY ordinal_position) as columns
FROM information_schema.columns 
WHERE table_name = 'regulatory_documents' 
AND table_schema = 'public';

-- Create a minimal working function that only uses existing columns
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) <PERSON><PERSON>URNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
    insert_sql TEXT;
    column_list TEXT;
    value_list TEXT;
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing (using safe column access)
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(document_type::text, ''),
            COALESCE(status::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- If document not found, use a default hash
    IF document_hash IS NULL THEN
        document_hash := encode(digest(
            CONCAT(
                p_document_id::text,
                'document_not_found',
                NOW()::text
            ), 'sha256'
        ), 'hex');
    END IF;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Generate new signature ID
    signature_id := gen_random_uuid();
    
    -- Build dynamic INSERT based on existing columns
    column_list := 'id, organization_id, document_id, signer_id, signer_name, signature_type, signature_meaning, signature_hash, signed_at';
    value_list := '$1, $2, $3, $4, $5, $6, $7, $8, NOW()';
    
    -- Add optional columns if they exist
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_reason') THEN
        column_list := column_list || ', signature_reason';
        value_list := value_list || ', $9';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'authentication_method') THEN
        column_list := column_list || ', authentication_method';
        value_list := value_list || ', $10';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_metadata') THEN
        column_list := column_list || ', signature_metadata';
        value_list := value_list || ', $11';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'document_hash_at_signing') THEN
        column_list := column_list || ', document_hash_at_signing';
        value_list := value_list || ', $12';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'authentication_timestamp') THEN
        column_list := column_list || ', authentication_timestamp';
        value_list := value_list || ', NOW()';
    END IF;
    
    -- Create the INSERT statement
    insert_sql := 'INSERT INTO electronic_signatures (' || column_list || ') VALUES (' || value_list || ')';
    
    -- Execute the INSERT with available parameters
    EXECUTE insert_sql USING 
        signature_id,
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        signature_hash,
        p_signature_reason,
        p_authentication_method,
        p_signature_metadata,
        document_hash;
    
    -- Log audit event if log_audit_event function exists
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'log_audit_event') THEN
        PERFORM log_audit_event(
            p_organization_id,
            p_signer_id,
            'sign'::audit_action_type,
            'Electronic signature created: ' || p_signature_meaning,
            'signature',
            signature_id,
            'Electronic Signature',
            NULL,
            jsonb_build_object(
                'signature_type', p_signature_type,
                'authentication_method', p_authentication_method,
                'document_id', p_document_id
            )
        );
    END IF;
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;

-- Simple test with minimal columns
DO $$
DECLARE
    test_org_id UUID;
    test_user_id UUID;
    test_doc_id UUID;
    signature_id UUID;
BEGIN
    -- Get test organization
    SELECT id INTO test_org_id 
    FROM organizations 
    LIMIT 1;
    
    IF test_org_id IS NOT NULL THEN
        -- Get test user
        SELECT id INTO test_user_id 
        FROM user_profiles 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        -- Get test document
        SELECT id INTO test_doc_id 
        FROM regulatory_documents 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        IF test_user_id IS NOT NULL AND test_doc_id IS NOT NULL THEN
            -- Create test signature with dynamic function
            SELECT create_electronic_signature(
                test_org_id,
                test_doc_id,
                test_user_id,
                'approval'::signature_type,
                'DYNAMIC - Test signature with existing columns only',
                'Testing dynamic electronic signature functionality',
                'password'::authentication_method,
                '{"test": true, "verification": "dynamic_function_test"}'::jsonb
            ) INTO signature_id;
            
            RAISE NOTICE 'DYNAMIC - Test signature created with ID: %', signature_id;
        ELSE
            RAISE NOTICE 'No test user or document found';
        END IF;
    ELSE
        RAISE NOTICE 'No test organization found';
    END IF;
END $$;

-- Verify the dynamic function worked
SELECT 
    'DYNAMIC FUNCTION TEST' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SUCCESS'
        ELSE '❌ FAILED'
    END as status,
    COUNT(*) as signature_count
FROM electronic_signatures 
WHERE signature_meaning LIKE '%DYNAMIC - Test signature%';

-- Show all signatures created
SELECT 
    'ALL SIGNATURES' as test_name,
    es.signature_type,
    es.signer_name,
    es.signature_meaning,
    es.signed_at,
    o.display_name as organization
FROM electronic_signatures es
JOIN organizations o ON o.id = es.organization_id
ORDER BY es.signed_at DESC;

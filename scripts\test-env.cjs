#!/usr/bin/env node
/**
 * Test script for environment validation
 * Tests the Supabase configuration without TypeScript compilation
 */

// Load environment variables from .env.local manually
const fs = require('fs');
const path = require('path');

try {
  const envPath = path.join(__dirname, '..', '.env.local');
  const envContent = fs.readFileSync(envPath, 'utf8');

  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      const value = valueParts.join('=').trim();
      process.env[key.trim()] = value;
    }
  });

  console.log('📁 Loaded environment from .env.local');
} catch (error) {
  console.log('⚠️ Could not load .env.local, using system environment variables');
}

function validateEnvironment() {
  const errors = [];
  const warnings = [];

  // Required environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const nodeEnv = process.env.NODE_ENV || 'development';
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

  console.log('🔍 Testing Environment Configuration...\n');

  // Validate required variables
  if (!supabaseUrl) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is required');
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl);
    if (!isValidSupabaseUrl(supabaseUrl)) {
      errors.push('NEXT_PUBLIC_SUPABASE_URL must be a valid Supabase URL');
    }
  }

  if (!supabaseAnonKey) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: [REDACTED - Length:', supabaseAnonKey.length + ']');
    if (!isValidSupabaseKey(supabaseAnonKey)) {
      errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY must be a valid JWT token');
    }
  }

  console.log('✅ NODE_ENV:', nodeEnv);
  console.log('✅ NEXT_PUBLIC_API_URL:', apiUrl);

  // Validate optional but recommended variables
  if (nodeEnv === 'production' && apiUrl.includes('localhost')) {
    warnings.push('NEXT_PUBLIC_API_URL should not use localhost in production');
  }

  // Additional validations
  if (supabaseUrl && supabaseAnonKey) {
    const projectIdFromUrl = extractProjectId(supabaseUrl);
    const projectIdFromKey = extractProjectIdFromKey(supabaseAnonKey);

    console.log('🔍 Project ID from URL:', projectIdFromUrl);
    console.log('🔍 Project ID from Key:', projectIdFromKey);

    if (projectIdFromUrl && projectIdFromKey && projectIdFromUrl !== projectIdFromKey) {
      errors.push('Project ID mismatch between SUPABASE_URL and SUPABASE_ANON_KEY');
    } else if (projectIdFromUrl === projectIdFromKey) {
      console.log('✅ Project IDs match');
    }
  }

  console.log('\n📊 Validation Results:');

  if (errors.length === 0) {
    console.log('✅ Environment validation passed!');
    if (warnings.length > 0) {
      console.log('⚠️ Warnings:');
      warnings.forEach(warning => console.log(`  - ${warning}`));
    }
  } else {
    console.log('❌ Environment validation failed:');
    errors.forEach(error => console.log(`  - ${error}`));
    if (warnings.length > 0) {
      console.log('⚠️ Additional warnings:');
      warnings.forEach(warning => console.log(`  - ${warning}`));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

function isValidSupabaseUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.endsWith('.supabase.co') && urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

function isValidSupabaseKey(key) {
  const parts = key.split('.');
  return parts.length === 3 && parts.every(part => part.length > 0);
}

function extractProjectId(url) {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    if (hostname.endsWith('.supabase.co')) {
      return hostname.split('.')[0];
    }
  } catch {
    // Invalid URL
  }
  return null;
}

function extractProjectIdFromKey(key) {
  try {
    const parts = key.split('.');
    if (parts.length !== 3) return null;

    // Decode the payload (second part)
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    return payload.ref || null;
  } catch {
    return null;
  }
}

// Run the validation
const result = validateEnvironment();
process.exit(result.isValid ? 0 : 1);

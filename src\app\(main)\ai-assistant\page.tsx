'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CheckCircle, FileText, Lightbulb, Plus } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import { usePageMetadata } from '@/hooks/use-page-metadata'

import { ChatInterface } from './components/chat-interface'
import { HistorySidebar } from './components/history-sidebar'
import { useAIChat } from './hooks/use-ai-chat'
import { useChatHistory } from './hooks/use-chat-history'

import type { QuickAction } from './types'

/**
 * AI Assistant Page - AI Compliance Platform (Migration Plan Phase 3.2 Complete)
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper state management with custom hooks
 *
 * Migrated from: src/pages/AIAssistant.tsx (721 lines)
 * Broken down into 6+ components for maintainability:
 * - HistorySidebar: Conversation history management
 * - ChatInterface: Main chat container with orchestration
 * - MessageList: Messages display and interactions
 * - InputControls: Message input and file attachments
 * - SuggestionsPanel: Quick actions and suggested questions
 * - AssistantSettings: Assistant configuration and status
 */
export default function AIAssistantPage() {
  usePageMetadata(
    'AI Compliance Assistant',
    'Get help with regulatory compliance questions and document analysis',
  )

  const {
    messages,
    inputMessage,
    isTyping,
    attachedDocuments,
    setInputMessage,
    handleSendMessage,
    handleAttachDocument,
    handleRemoveAttachment,
    handleCopyMessage,
    handleFeedback,
    handleNewChat,
    handleQuickAction,
  } = useAIChat()

  const {
    conversationHistory,
    selectedConversationId,
    handleSelectConversation,
    getUploadedDocuments,
  } = useChatHistory()

  const quickActions: readonly QuickAction[] = [
    {
      icon: FileText,
      label: 'Analyze Document',
      description: 'Upload a document for compliance analysis',
      action: 'document_analysis',
    },
    {
      icon: AlertTriangle,
      label: 'Check Compliance',
      description: 'Verify compliance against regulations',
      action: 'compliance_check',
    },
    {
      icon: BookOpen,
      label: 'Regulatory Updates',
      description: 'Get latest regulatory changes',
      action: 'regulatory_updates',
    },
    {
      icon: Lightbulb,
      label: 'Best Practices',
      description: 'Learn industry best practices',
      action: 'best_practices',
    },
  ]

  return (
    <div className="container mx-auto">
      <div className="space-y-6">
        {/* Header Bar */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              AI Compliance Assistant
            </h1>
            <p className="text-muted-foreground mt-1">
              Get help with regulatory compliance questions and document analysis
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* Online Badge */}
            <Badge
              variant="success"
              className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold"
            >
              <CheckCircle className="mr-1 h-3 w-3" />
              Online
            </Badge>
            {/* New Chat Button */}
            <Button
              variant="outline"
              className="inline-flex items-center justify-center gap-2 h-10 px-4 py-2"
              onClick={handleNewChat}
            >
              <Plus className="h-4 w-4" />
              New Chat
            </Button>
          </div>
        </div>

        {/* Main Content Split */}
        <div className="flex gap-6 h-[calc(100vh-12rem)]">
          {/* Sidebar - fixed 20rem width */}
          <div className="w-80">
            <HistorySidebar
              conversations={conversationHistory}
              selectedConversationId={selectedConversationId}
              onSelectConversation={handleSelectConversation}
            />
          </div>

          {/* Chat Panel - grows to fill remaining space */}
          <div className="flex-1 flex flex-col">
            <ChatInterface
              messages={messages}
              isTyping={isTyping}
              inputMessage={inputMessage}
              onInputChange={setInputMessage}
              onSendMessage={handleSendMessage}
              attachedDocuments={attachedDocuments}
              onAttachDocument={handleAttachDocument}
              onRemoveAttachment={handleRemoveAttachment}
              uploadedDocuments={getUploadedDocuments()}
              onCopyMessage={handleCopyMessage}
              onFeedback={handleFeedback}
              onNewChat={handleNewChat}
              onQuickAction={handleQuickAction}
              quickActions={quickActions}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

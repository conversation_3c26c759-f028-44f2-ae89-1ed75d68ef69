'use client'

import { Bell, Clock, Mail, Shield, AlertTriangle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui-radix/card'
import { Label } from '@/components/ui-radix/label'
import { Switch } from '@/components/ui-radix/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui-radix/select'
import { Input } from '@/components/ui-radix/input'
import { Button } from '@/components/ui-radix/button'
import { useSettingsManagement } from '@/hooks/use-settings-management'
import { useState, useEffect } from 'react'
import { Loader2, Save } from 'lucide-react'
import { NotificationPreferences } from '@/types/user-settings'

interface NotificationSettingsProps {
  readonly className?: string
}

export function NotificationSettingsComponent({ className }: NotificationSettingsProps) {
  const {
    getNotificationPreferences,
    updateNotificationPreferences,
    isUpdating,
    validationErrors,
    clearErrors
  } = useSettingsManagement()

  const [settings, setSettings] = useState<NotificationPreferences>(getNotificationPreferences())
  const [originalSettings, setOriginalSettings] = useState<NotificationPreferences>(getNotificationPreferences())
  const [hasChanges, setHasChanges] = useState(false)

  // Update settings when user profile changes
  useEffect(() => {
    const currentSettings = getNotificationPreferences()
    setSettings(currentSettings)
    setOriginalSettings(currentSettings)
    setHasChanges(false)
  }, [getNotificationPreferences])

  const handleSettingChange = (key: keyof NotificationPreferences, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
    clearErrors()
  }

  const handleQuietHoursChange = (key: 'enabled' | 'start_time' | 'end_time', value: any) => {
    setSettings(prev => ({
      ...prev,
      quiet_hours: { ...prev.quiet_hours, [key]: value }
    }))
    setHasChanges(true)
    clearErrors()
  }

  const handleSave = async () => {
    if (!hasChanges) {
      console.log('⏭️ No notification changes to save')
      return
    }

    console.log('🔄 NotificationSettings - Starting save process:', {
      originalSettings,
      newSettings: settings,
      hasChanges
    })

    // Calculate only the changed fields
    const changedFields: Partial<NotificationPreferences> = {}
    Object.keys(settings).forEach(key => {
      const typedKey = key as keyof NotificationPreferences
      if (JSON.stringify(settings[typedKey]) !== JSON.stringify(originalSettings[typedKey])) {
        changedFields[typedKey] = settings[typedKey]
      }
    })

    console.log('📤 NotificationSettings - Sending only changed fields:', changedFields)

    // Send the entire settings object (the hook will merge with current settings)
    const success = await updateNotificationPreferences(settings)

    console.log('📊 NotificationSettings - Update result:', { success })

    if (success) {
      setHasChanges(false)
      setOriginalSettings(settings) // Update original to current
      console.log('✅ NotificationSettings - Save completed successfully')
    } else {
      console.error('❌ NotificationSettings - Save failed')
    }
  }
  return (
    <div className={className}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="mr-2 h-5 w-5" />
              Email Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">Master toggle for all email notifications</p>
              </div>
              <Switch
                checked={settings.email_notifications}
                onCheckedChange={(checked) => handleSettingChange('email_notifications', checked)}
              />
            </div>

            {settings.email_notifications && (
              <>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Regulatory updates</Label>
                    <p className="text-sm text-muted-foreground">Get notified of new regulatory changes and compliance requirements</p>
                  </div>
                  <Switch
                    checked={settings.regulatory_updates}
                    onCheckedChange={(checked) => handleSettingChange('regulatory_updates', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Document processing alerts</Label>
                    <p className="text-sm text-muted-foreground">Notifications when document analysis is complete</p>
                  </div>
                  <Switch
                    checked={settings.document_processing_alerts}
                    onCheckedChange={(checked) => handleSettingChange('document_processing_alerts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Weekly compliance reports</Label>
                    <p className="text-sm text-muted-foreground">Summary of compliance activities and metrics</p>
                  </div>
                  <Switch
                    checked={settings.weekly_compliance_reports}
                    onCheckedChange={(checked) => handleSettingChange('weekly_compliance_reports', checked)}
                  />
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label>Digest Frequency</Label>
              <Select
                value={settings.digest_frequency}
                onValueChange={(value) => handleSettingChange('digest_frequency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Immediate</SelectItem>
                  <SelectItem value="daily">Daily Digest</SelectItem>
                  <SelectItem value="weekly">Weekly Digest</SelectItem>
                  <SelectItem value="monthly">Monthly Digest</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Push Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="mr-2 h-5 w-5" />
              Push Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Push Notifications</Label>
                <p className="text-sm text-muted-foreground">Master toggle for browser push notifications</p>
              </div>
              <Switch
                checked={settings.push_notifications}
                onCheckedChange={(checked) => handleSettingChange('push_notifications', checked)}
              />
            </div>

            {settings.push_notifications && (
              <>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Critical compliance issues</Label>
                    <p className="text-sm text-muted-foreground">Immediate alerts for critical compliance violations</p>
                  </div>
                  <Switch
                    checked={settings.critical_compliance_issues}
                    onCheckedChange={(checked) => handleSettingChange('critical_compliance_issues', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Processing complete</Label>
                    <p className="text-sm text-muted-foreground">Browser notifications when analysis is finished</p>
                  </div>
                  <Switch
                    checked={settings.processing_complete}
                    onCheckedChange={(checked) => handleSettingChange('processing_complete', checked)}
                  />
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Compliance & Security Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Compliance & Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Compliance Alerts</Label>
                <p className="text-sm text-muted-foreground">Critical compliance notifications</p>
              </div>
              <Switch
                checked={settings.compliance_alerts}
                onCheckedChange={(checked) => handleSettingChange('compliance_alerts', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Security Alerts</Label>
                <p className="text-sm text-muted-foreground">Security-related notifications</p>
              </div>
              <Switch
                checked={settings.security_alerts}
                onCheckedChange={(checked) => handleSettingChange('security_alerts', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Audit Notifications</Label>
                <p className="text-sm text-muted-foreground">Audit trail and compliance updates</p>
              </div>
              <Switch
                checked={settings.audit_notifications}
                onCheckedChange={(checked) => handleSettingChange('audit_notifications', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-end mt-6">
        <Button
          onClick={handleSave}
          disabled={!hasChanges || isUpdating}
          className="min-w-[140px]"
        >
          {isUpdating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

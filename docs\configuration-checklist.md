# VigiLens Configuration Validation Checklist

## 📋 Pre-Development Checklist

### Environment Setup
- [ ] `.env.local` file exists in project root
- [ ] `NEXT_PUBLIC_SUPABASE_URL` is set and valid
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY` is set and valid
- [ ] Environment validation script passes: `node scripts/test-env.cjs`
- [ ] No sensitive keys in version control

### Supabase Configuration
- [ ] Supabase project is active (not paused)
- [ ] Project URL matches environment variable
- [ ] Anon key matches project
- [ ] RLS policies are configured
- [ ] Database tables exist and are accessible

### File Structure Validation
- [ ] `utils/supabase/client.ts` exists and follows standard pattern
- [ ] `utils/supabase/server.ts` exists and follows standard pattern
- [ ] `utils/supabase/middleware.ts` exists and follows standard pattern
- [ ] `middleware.ts` exists in project root
- [ ] No legacy files remain (`lib/supabase.ts`, etc.)

## 🔧 Development Checklist

### Before Starting Development
- [ ] Development server starts without errors: `npm run dev`
- [ ] TypeScript compilation succeeds: `npm run type-check`
- [ ] Linting passes: `npm run lint`
- [ ] Supabase connection test passes: `/test-supabase` page loads

### Code Quality
- [ ] All Supabase imports use standardized paths
- [ ] No direct client instances (use factory functions)
- [ ] Proper client type for context (browser vs server)
- [ ] Environment variables use bracket notation
- [ ] No hardcoded URLs or keys

### Testing
- [ ] Environment validation: `node scripts/test-env.cjs`
- [ ] Connection test: Visit `/test-supabase`
- [ ] Authentication flow works (if implemented)
- [ ] Database operations work correctly
- [ ] Real-time subscriptions work (if used)

## 🚀 Pre-Deployment Checklist

### Production Environment
- [ ] Production environment variables are set
- [ ] `NODE_ENV=production` is configured
- [ ] API URLs point to production endpoints
- [ ] No localhost URLs in production config
- [ ] SSL certificates are valid

### Build Validation
- [ ] Production build succeeds: `npm run build`
- [ ] No build warnings or errors
- [ ] All environment variables are available at build time
- [ ] Static analysis passes
- [ ] Bundle size is acceptable

### Security Review
- [ ] No sensitive data in client-side code
- [ ] RLS policies are properly configured
- [ ] Authentication is properly implemented
- [ ] CORS settings are correct
- [ ] Rate limiting is configured

## 🔍 Troubleshooting Checklist

### Environment Issues
- [ ] Check `.env.local` file exists and is readable
- [ ] Verify environment variable names are correct
- [ ] Ensure no extra spaces or quotes around values
- [ ] Restart development server after changes
- [ ] Check for typos in variable names

### Connection Issues
- [ ] Verify Supabase project is not paused
- [ ] Check project URL format: `https://project-id.supabase.co`
- [ ] Validate anon key format (JWT with 3 parts)
- [ ] Test with Supabase dashboard
- [ ] Check network connectivity

### Code Issues
- [ ] Verify import paths are correct
- [ ] Check for TypeScript errors
- [ ] Ensure proper client type usage
- [ ] Validate function signatures
- [ ] Check for missing await keywords

### Authentication Issues
- [ ] Verify middleware is properly configured
- [ ] Check cookie settings
- [ ] Validate session handling
- [ ] Test auth flow end-to-end
- [ ] Check RLS policies

## ✅ Validation Commands

### Quick Validation
```bash
# Environment validation
node scripts/test-env.cjs

# Type checking
npm run type-check

# Linting
npm run lint

# Build test
npm run build
```

### Comprehensive Validation
```bash
# Full validation suite
npm run verify:all

# Start development server
npm run dev

# Test Supabase connection
curl http://localhost:3000/test-supabase
```

## 📊 Success Criteria

### Environment Validation
- ✅ All required variables are set
- ✅ Variable formats are valid
- ✅ Project IDs match between URL and key
- ✅ No validation errors or warnings

### Development Setup
- ✅ Server starts without errors
- ✅ TypeScript compilation succeeds
- ✅ All imports resolve correctly
- ✅ Supabase connection works

### Code Quality
- ✅ Consistent import patterns
- ✅ Proper client usage
- ✅ No legacy code remains
- ✅ Documentation is up to date

### Production Readiness
- ✅ Build succeeds
- ✅ No security issues
- ✅ Performance is acceptable
- ✅ All features work correctly

## 🚨 Red Flags

### Immediate Action Required
- ❌ Environment validation fails
- ❌ TypeScript compilation errors
- ❌ Supabase connection fails
- ❌ Build process fails
- ❌ Security vulnerabilities detected

### Warning Signs
- ⚠️ Validation warnings present
- ⚠️ Performance issues detected
- ⚠️ Deprecated patterns in use
- ⚠️ Missing documentation
- ⚠️ Inconsistent code patterns

## 📝 Documentation Requirements

### Required Documentation
- [ ] Configuration changes are documented
- [ ] New environment variables are documented
- [ ] Migration steps are provided
- [ ] Troubleshooting guides are updated
- [ ] Examples are current and working

### Code Documentation
- [ ] Complex configurations are commented
- [ ] Environment variables are documented
- [ ] Usage examples are provided
- [ ] Migration paths are clear
- [ ] Best practices are documented

## 🔄 Maintenance Schedule

### Daily
- [ ] Check development server starts correctly
- [ ] Verify no new TypeScript errors
- [ ] Monitor for environment issues

### Weekly
- [ ] Run full validation suite
- [ ] Check for dependency updates
- [ ] Review configuration for optimizations
- [ ] Update documentation as needed

### Monthly
- [ ] Review Supabase configuration
- [ ] Check for security updates
- [ ] Validate production environment
- [ ] Update troubleshooting guides

### Quarterly
- [ ] Review entire configuration setup
- [ ] Update to latest Supabase patterns
- [ ] Comprehensive security review
- [ ] Performance optimization review

# **HYPER-DETAILED AI ASSISTANT SECTION UI/UX SPECIFICATION**
### **6 Expert Protocol Analysis for Next.js Implementation**

---

## **1. ARCHITECTURAL EXPERT ANALYSIS**

### **Page Structure & Component Hierarchy**

**Root Container**: `<div className="space-y-6">`
- **Vertical spacing**: 1.5rem (24px) between major sections
- **Full viewport utilization** with calculated height: `h-[calc(100vh-12rem)]`
- **Three-column layout** using Flexbox with 1.5rem (24px) gap

**Component Tree Architecture**:
```
AIAssistantPage (Client Component)
├── Page Header Section
├── Main Chat Layout (flex container)
│   ├── HistorySidebar (320px fixed width)
│   ├── ChatInterface (flex-1 dynamic width)
│   │   ├── AssistantSettings (header)
│   │   ├── MessageList (scrollable body)
│   │   └── InputControls (footer)
│   └── SuggestionsPanel (320px fixed, conditional)
```

**State Management Architecture**:
- **useAIChat Hook**: Core chat logic (172 lines)
- **useChatHistory Hook**: Conversation management (100 lines)
- **usePageMetadata Hook**: SEO and page metadata
- **Local state** for UI-specific concerns

---

## **2. VISUAL DESIGN EXPERT ANALYSIS**

### **Color System (OKLCH Color Space)**

**Light Mode**:
- Background: `oklch(1 0 0)` - Pure white
- Foreground: `oklch(0.2795 0.0368 260.031)` - Dark navy text
- Primary: `oklch(0.5413 0.2466 293.01)` - Purple accent
- Muted: `oklch(0.967 0.0029 264.5419)` - Light gray backgrounds
- Border: `oklch(0.92 0.003 264.5419)` - Subtle gray borders

**Dark Mode**:
- Background: `oklch(0.14 0.035 287.91)` - Deep purple-black
- Foreground: `oklch(0.9288 0.0126 255.5078)` - Off-white text
- Primary: Same purple accent (consistent across themes)
- Card: `oklch(0.1943 0.0434 287.91)` - Elevated surface
- Border: `oklch(0.22 0.05 285)` - Subtle purple-gray

**Typography**:
- Font Family: Inter (sans-serif)
- Page Title: `text-3xl font-bold` (30px, 700 weight)
- Subtitle: `text-muted-foreground mt-1` (14px with 4px top margin)
- Body Text: `text-sm` (14px) with `leading-relaxed` (1.625 line-height)
- Micro Text: `text-xs` (12px) for timestamps and metadata

**Spacing System**:
- Base unit: 0.25rem (4px)
- Common spacings: p-3 (12px), p-4 (16px), p-6 (24px)
- Gap utilities: gap-2 (8px), gap-3 (12px), gap-6 (24px)

**Border Radius**:
- Cards: `rounded-lg` (var(--radius) = 0.5rem = 8px)
- Buttons: Mix of rounded-lg and fully rounded for icon buttons
- Badges: `rounded` (4px)

**Shadow System**:
- Card shadows: `shadow-sm` for elevated elements
- Hover shadows: Transition to stronger shadows on interaction
- Focus rings: 2px ring with 1px offset

---

## **3. INTERACTION DESIGN EXPERT ANALYSIS**

### **Message List Interactions**

**Auto-Scrolling Behavior**:
- Smooth scroll to bottom on new messages
- useEffect dependency on messages array
- Scroll anchor ref at bottom of list

**Message Bubble Design**:
- **User Messages**: Right-aligned, primary color background
- **AI Messages**: Left-aligned, muted background with border
- **Max width**: 80% of container
- **Padding**: 12px all around
- **Transition**: `transition-colors` for hover states

**Interactive Elements per AI Message**:
1. **Copy Button**: 24x24px ghost button, 12x12px icon
2. **Thumbs Up**: Green hover color (`hover:text-green-600`)
3. **Thumbs Down**: Red hover color (`hover:text-red-600`)
- All with focus-visible states and proper ARIA labels

**Typing Indicator**:
- Three bouncing dots animation
- Staggered animation delays (0s, 0.1s, 0.2s)
- 8px diameter dots with muted-foreground color

**Document Attachments in Messages**:
- Border styling differs by sender context
- FileText icon (16x16px) with document metadata
- Category badge with outline variant
- Truncated filename with ellipsis

### **Input Controls Interactions**

**Auto-Resizing Textarea 📝**:
- **Smart Height Adjustment**:
  - Initial height: 40px minimum
  - Grows dynamically with content using `resize-none` class
  - No maximum height restriction - scrolls within container
  - Smooth height transitions during typing
- **Clean, Borderless Design**:
  - Integrated seamlessly into rounded container
  - No visible textarea borders
  - Inherits parent container styling
  - Focus state shows through parent container
- **Keyboard Shortcuts**:
  - **Enter**: Send message immediately
  - **Shift+Enter**: Insert new line without sending
  - Implemented via `handleKeyPress` event handler
  - `e.preventDefault()` on Enter without Shift

**Multi-line Input Field**:
- Component: `<Input>` from ui-radix library
- Classes: `min-h-[40px] resize-none`
- Placeholder: "Ask me anything about compliance..."
- Disabled state support with visual feedback

**Attachment System**:
- **Paperclip Button**: 40x40px outline button
- **Popover**: 320px width, positioned above button
- **Upload Area**: Full-width button with Upload icon
- **Document Search**:
  - Search icon positioned absolutely (left: 8px, top: 10px)
  - Input with left padding of 32px
- **Document List**:
  - Max height: 192px (12rem) with overflow scroll
  - Hover state: `hover:bg-muted/50`
  - Cursor: pointer
  - Transition: colors

**Send Button**:
- 40x40px primary button
- Disabled when no content
- Send icon (16x16px)

**Attached Documents Display**:
- Flex wrap layout with 8px gap
- Badge with secondary variant
- Close button (16x16px) with hover state
- Max width: 128px with truncation

---

## **4. COMPONENT BEHAVIOR EXPERT ANALYSIS**

### **History Sidebar Component**

**Dimensions**: 320px fixed width, full height
**Card Styling**: Full height with standard card styles

**Conversation Items**:
- **Padding**: 12px all around
- **Border**: 1px, dynamic color based on selection
- **Selected State**:
  - Background: `bg-primary/10` (10% opacity primary)
  - Border: `border-primary/50` (50% opacity primary)
  - Shadow: `shadow-sm`
- **Hover State**:
  - Background: `hover:bg-muted/50`
  - Border: `hover:border-border`
- **Transition**: `transition-all duration-200`

**Content Structure**:
- Title: `font-medium text-sm mb-1 line-clamp-1`
- Last Message: `text-xs text-muted-foreground mb-2 line-clamp-1`
- Metadata Row: Flexbox with justify-between
- Message Count Badge: `bg-muted rounded px-1.5 py-0.5`

**Accessibility**:
- Role: button
- Tabindex: 0
- Keyboard support: Enter/Space activation
- ARIA label with conversation title

### **Assistant Settings Header**

**Layout**: Full-width border-bottom, 24px padding
**Height**: Dynamic based on content

**Avatar System**:
- 32x32px Avatar component
- Purple background with white Bot icon (16x16px)
- AvatarFallback for loading states

**Status Badge**:
- Dynamic variant based on online state
- Color coding:
  - Online: Green tones (light/dark mode adaptive)
  - Offline: Muted gray tones
- Icons: CheckCircle (online) or AlertCircle (offline)
- Icon size: 12x12px with 4px right margin

**Quick Action Buttons**:
- Show first 2 actions from quickActions array
- 32px height, horizontal padding 8px
- Icon (12x12px) + Label (12px font)
- Outline variant with hover states

### **Suggestions Panel**

**Visibility Logic**: Only when `messages.length <= 1`
**Width**: 320px fixed
**Layout**: Vertical stack with 24px gap

**Quick Actions Card**:
- 2-column grid with 12px gap
- Button height: auto (content-based)
- Padding: 16px all sides
- Flex column layout within button
- Icon row: 16x16px icon + 14px medium font
- Description: 12px muted text, left-aligned

**Suggested Questions Card**:
- Vertical stack with 8px gap
- Ghost buttons, full width
- Height: auto with 12px padding
- Left-aligned text
- Hover: `hover:bg-muted`
- Text: 14px muted-foreground

---

## **5. STATE MANAGEMENT EXPERT ANALYSIS**

### **useAIChat Hook State**

**Messages State**:
```typescript
interface Message {
  id: string
  content: string
  sender: 'user' | 'ai'
  timestamp: Date
  type?: 'text' | 'suggestion' | 'document'
  metadata?: Record<string, unknown>
  attachments?: readonly AttachedDocument[]
}
```

**Initial State**:
- Welcome message from AI
- Empty input
- No typing indicator
- Empty attachments array

**Message Flow**:
1. User types/attaches → Update local state
2. Send triggered → Create user message with timestamp
3. Add to messages array → Clear input/attachments
4. Set typing indicator → 2-second delay
5. Generate AI response → Add to messages
6. Clear typing indicator

**Response Generation Logic**:
- Keyword detection for specialized responses
- Document analysis for attachments
- Fallback to generic compliance guidance

### **useChatHistory Hook State**

**Mock Data Structure**:
```typescript
interface ConversationHistory {
  id: string
  title: string
  lastMessage: string
  timestamp: string
  messageCount: number
}
```

**Document Library Integration**:
- 5 pre-defined documents
- Categories: Validation, Audit, SOP, Regulatory, Template
- File metadata: name, type, size, date, description

---

## **6. ACCESSIBILITY & PERFORMANCE EXPERT ANALYSIS**

### **Accessibility Features**

**ARIA Implementation**:
- Proper button labels on all interactive elements
- Role attributes for custom interactive components
- Keyboard navigation support throughout

**Focus Management**:
- Visible focus rings (2px ring, 1px offset)
- Logical tab order
- Escape key handling for popovers

**Screen Reader Support**:
- Descriptive labels for icon buttons
- Status announcements for typing indicator
- Message sender identification

### **Performance Optimizations**

**Component Splitting**:
- 6 focused components under 200 lines each
- Lazy loading potential for heavy components
- Memoization opportunities for expensive renders

**Render Optimizations**:
- useCallback for event handlers
- Conditional rendering for suggestions panel
- Virtual scrolling potential for long conversations

**Bundle Optimization**:
- Tree-shakeable icon imports
- Component-level code splitting
- CSS-in-JS avoided for smaller bundle



---

---

## 1. Outer Layout

```html
<main class="flex-1 overflow-auto">
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      …
    </div>
  </div>
</main>
```

* **`flex-1` + `overflow-auto`** on `<main>` makes it expand to fill its parent and scroll if content exceeds viewport.
* **`container mx-auto p-6`** centers all content horizontally, adds 1.5 rem padding on all sides.
* **`space-y-6`** stacks vertical sections with 1.5 rem gaps.

---

## 2. Header Bar

```html
<div class="flex items-center justify-between">
  <div>
    <h1 class="text-3xl font-bold text-foreground">AI Compliance Assistant</h1>
    <p class="text-muted-foreground mt-1">Get help with regulatory…</p>
  </div>
  <div class="flex items-center space-x-2">…status + New Chat button…</div>
</div>
```

* **Flex container** with `justify-between` pushes logo/text to the left and status/buttons to the right.
* **Title**

  * `.text-3xl` → 1.875 rem (30 px) font size
  * `.font-bold` → 700 weight
  * `.text-foreground` → primary text color (e.g. nearly black)
* **Subtitle**

  * `.text-muted-foreground` → secondary, greyed text
  * `.mt-1` → 0.25 rem top margin
* **Right‑hand controls** (`.flex items-center space-x-2`)

  * **Online badge**

    * `.inline-flex items-center rounded-full border … px-2.5 py-0.5 text-xs font-semibold`

      * 0.625 rem vertical padding 0.625 rem horizontal
      * `text-xs` → 0.75 rem font
      * `rounded-full` pill shape
    * `bg-success text-success-foreground` → green background with white text
    * Contains a 12×12 px check icon sized down by `.h-3 w-3` and margin-right `.mr-1`
  * **“New Chat” button**

    * `.inline-flex items-center justify-center gap-2 … h-10 px-4 py-2`

      * fixed height 2.5 rem, horizontal padding 1 rem
      * `gap-2` → 0.5 rem between icon and text
    * `.border border-input bg-background hover:bg-muted`

      * subtle 1 px border, white background, hover light grey
    * Icon: 16×16 px plus sign (`.h-4 w-4`)

---

## 3. Main Content Split

```html
<div class="flex gap-6 h-[calc(100vh-12rem)]">
  <div class="w-80">…sidebar…</div>
  <div class="flex-1 flex flex-col">…chat panel…</div>
</div>
```

* Two‐column layout:

  * **Sidebar**: fixed 20 rem width (`.w-80`)
  * **Chat panel**: grows to fill remaining space (`.flex-1`)
  * **`gap-6`** → 1.5 rem gutter between them
  * Height is viewport minus header/input areas (`h-[calc(100vh-12rem)]`)

---

## 4. Sidebar (“Conversations”)

```html
<div class="rounded-lg border bg-card text-card-foreground shadow-sm h-full">
  <div class="p-6"> <h3 class="font-semibold …">Conversations</h3> </div>
  <div class="p-6 pt-0 space-y-2">
    <div class="p-3 rounded-lg border hover:bg-muted/30 cursor-pointer …">…each chat preview…</div>
    …three items…
  </div>
</div>
```

* **Card container**

  * `rounded-lg` → 0.5 rem corner radius
  * `border` → 1 px light border
  * `bg-card` → white (or light) card background
  * `shadow-sm` → small drop shadow
* **List header**: padding 1.5 rem
* **Chat items**

  * Each `.p-3 rounded-lg border` → 0.75 rem padding, pill‑shaped corners
  * On hover: `hover:bg-muted/30` (30% grey overlay), `hover:shadow-sm` slight lift
  * Inside each:

    * Title `.font-medium text-sm mb-1 line-clamp-1` → 0.875 rem, bold, ellipsized
    * Subtitle `.text-xs text-muted-foreground mb-1 line-clamp-1` → 0.75 rem grey, ellipsized
    * Footer `.flex items-center justify-between text-xs text-muted-foreground` → timestamp and message count

---

## 5. Chat Panel Header (“Compliance AI Assistant”)

```html
<div class="rounded-lg border bg-card shadow-sm flex-1 flex flex-col">
  <div class="flex flex-col space-y-1.5 p-6 border-b py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">…avatar + title…</div>
      <div class="flex space-x-1">…Analyze Document & Check Compliance buttons…</div>
    </div>
  </div>
  …
</div>
```

* **Outer wrapper** same as sidebar card styling, but `flex flex-col` to stack header, messages, input.
* **Header area**

  * `.border-b` → bottom divider
  * Padding `.p-6` vs `.py-3` for vertical rhythm
  * **Avatar + title**

    * Avatar: `.rounded-full h-8 w-8 overflow-hidden bg-primary text-primary-foreground` → 2 rem circle, brand color
    * Bot icon inside sized `.h-4 w-4`
    * Title: `.font-semibold text-base` → 1 rem, semibold
    * Subtitle: `.text-xs text-muted-foreground` → 0.75 rem grey
  * **Action buttons**

    * Each `.inline-flex items-center gap-2 text-sm font-medium h-8 px-2 border bg-background rounded-md hover:bg-muted`

      * 2 rem tall, 0.5 rem horizontal padding
      * Icon size `.h-3 w-3`, text `.text-xs`

---

## 6. Message List

```html
<div class="flex-1 overflow-auto p-4 space-y-4">
  <div class="flex gap-3 justify-start">…single message bubble…</div>
</div>
```

* **Container**

  * `.flex-1 overflow-auto` so messages scroll when long
  * `.p-4` → 1 rem padding
  * `.space-y-4` → 1 rem vertical spacing between messages
* **Message row**

  * `.flex gap-3` → avatar & bubble spaced 0.75 rem
  * **Avatar** same 1.75 rem `.w-7 h-7 rounded-full bg-primary`
  * **Bubble**

    * `.max-w-[80%]` limits width to 80% of panel
    * `.rounded-lg p-3 bg-muted` → 0.75 rem padding, light grey background, 0.5 rem corners
    * Text: `.whitespace-pre-wrap text-sm` → wraps on newlines, 0.875 rem
  * **Bubble footer**

    * Timestamp `.text-xs opacity-70` → 0.75 rem, 70% opacity
    * Action icons container `.flex space-x-1` → 0.25 rem between each
    * Each icon button `.h-5 w-5 p-0 rounded-md hover:bg-muted` → compact 1.25 rem clickable area

---

## 7. Input Area

```html
<div class="border-t p-4">
  <div class="flex gap-2 mb-3">
    <input class="flex-1 h-10 rounded-md border bg-background px-3 text-base …" placeholder="…">
    <button class="h-9 px-3 rounded-md border …">+</button>
    <button class="bg-primary h-10 px-4 rounded-md text-primary-foreground">Send</button>
  </div>
  <div class="flex flex-wrap gap-2">…suggested prompts…</div>
</div>
```

* **Top border** (`border-t`) visually separates messages from input.
* **Input row**

  * `.flex gap-2` → 0.5 rem between input, attach, send
  * **Text field**

    * `.flex-1` grows to fill space
    * `h-10` → 2.5 rem tall
    * `rounded-md` → 0.375 rem corners
    * `border bg-background px-3 text-base` → 1 px border, white background, 0.75 rem text size, 0.75 rem horizontal padding
  * **Attach button (“+”)**

    * `h-9` slightly shorter to visually nest next to input
    * `px-3 rounded-md border`
  * **Send button**

    * `bg-primary text-primary-foreground` → brand purple/blue with white icon
    * `h-10 px-4 rounded-md` same height as input for alignment
* **Suggested prompts**

  * `.flex flex-wrap gap-2` wraps them when space runs out
  * Each prompt button is `.text-xs h-7 px-2 rounded-md border bg-background` → 0.75 rem tall, 0.625 rem font, pill shape

---

### Visual Hierarchy & Interaction Notes

1. **Consistent corner radii**:

   * `rounded-full` for avatars/badges
   * `rounded-lg` (0.5 rem) for cards and message bubbles
   * `rounded-md` (0.375 rem) for inputs & small buttons

2. **Spacing rhythm**:

   * Multiples of 0.25 rem (`p-1`, `p-3`, `p-6`) maintain a tight modular grid.
   * `space-x-2`, `space-y-4` keep consistent gutters.

3. **Color tokens**:

   * `bg-card` vs. `bg-background` differentiate elevated containers from plain backgrounds.
   * `text-foreground` vs. `text-muted-foreground` establish primary vs. secondary text.
   * `bg-primary` signals actionable elements (send button, avatar highlight).

4. **Hover & focus states**:

   * Buttons lighten on hover (`hover:bg-muted`) and show ring focus outlines (`focus-visible:ring-2`).
   * Sidebar items subtly darken (`hover:bg-muted/30`) and gain shadow (`hover:shadow-sm`).

5. **Responsiveness**:

   * Flex layouts (`flex-1`, `flex-wrap`) ensure the UI adapts to narrow widths, wrapping prompts, and allowing scrollable content areas.

---

## 1. Outer Layout

```html
<main class="flex-1 overflow-auto">
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      …
    </div>
  </div>
</main>
```

* **Placement:** fills the entire available viewport inside its parent, scrolls if content overflows.
* **Function:** centers content and provides overall padding.

---

## 2. Header Bar

```html
<div class="flex items-center justify-between">
  …title & subtitle…            <!-- Top‑left -->
  …status badge + New Chat…      <!-- Top‑right -->
</div>
```

* **Placement:** the very top of the page, full width within the container.
* **Left:** “AI Compliance Assistant” title and tagline.
* **Right:** Online status badge and “New Chat” button.

---

## 3. Main Content Split

```html
<div class="flex gap-6 h-[calc(100vh-12rem)]">
  <div class="w-80">…sidebar…</div>        <!-- Left column -->
  <div class="flex-1 flex flex-col">…</div> <!-- Right column -->
</div>
```

* **Placement:** directly under the header, occupies the rest of the vertical space (minus header and input).
* **Left:** fixed‑width Conversations sidebar.
* **Right:** flexible Chat panel.

---

## 4. Sidebar (“Conversations”)

```html
<div class="rounded-lg border bg-card … h-full">
  <div class="p-6">Conversations header</div>    <!-- Top of sidebar -->
  <div class="p-6 pt-0 space-y-2">
    <div class="p-3 rounded-lg border …">Chat 1</div>
    <div class="p-3 rounded-lg border …">Chat 2</div>
    <div class="p-3 rounded-lg border …">Chat 3</div>
  </div>
</div>
```

* **Placement:** left column, full height of the main content area.
* **At top:** “Conversations” label.
* **Below:** list of recent chats, stacked vertically.

---

## 5. Chat Panel Header

```html
<div class="rounded-lg border bg-card … flex flex-col">
  <div class="flex flex-col … border-b">
    <div class="flex items-center justify-between">
      …avatar + “Compliance AI Assistant”…    <!-- Top‑left of panel -->
      …Analyze Document & Check Compliance…  <!-- Top‑right of panel -->
    </div>
  </div>
  …
</div>
```

* **Placement:** right column, at its very top inside the card container.
* **Left:** bot avatar and title/subtitle.
* **Right:** two action buttons.

---

## 6. Message List

```html
<div class="flex-1 overflow-auto p-4 space-y-4">
  <div class="flex gap-3 justify-start">
    …single message bubble…
  </div>
  …more messages…
</div>
```

* **Placement:** immediately below the chat‑panel header, fills the central area of the right column.
* **Each message row:** avatar on the far left of this pane, bubble next to it.

---

## 7. Input Area

```html
<div class="border-t p-4">                       <!-- Bottom of page -->
  <div class="flex gap-2 mb-3">
    <input …>                <!-- Bottom‑left region -->
    <button>＋</button>       <!-- just right of input -->
    <button>Send</button>     <!-- Bottom‑right region -->
  </div>
  <div class="flex flex-wrap gap-2">
    …suggested prompt buttons…  <!-- directly under input row -->
  </div>
</div>
```

* **Placement:** sticks to the bottom of the container (under the messages).
* **Input row:** spans almost full width, with the send button aligned flush right of that row.
* **Suggested prompts:** sit immediately below the input row, wrapping to new lines as needed.

---

### Visual Map

```
┌─────────────────────────────────────────────────────────────────┐
│ Header Bar                                                     │
│ ┌── Title (top-left)     ───────────  Status & New Chat (top-right) ┐
└─────────────────────────────────────────────────────────────────┘
┌───────────────┬─────────────────────────────────────────────────────┐
│ Sidebar       │ Chat Panel                                        │
│ (20rem wide)  │ ┌ Header: Avatar+Title         Actions ──────────┐ │
│               │ │                                                │ │
│ ▪ Conversations│ │ ┌ Messages scrollable area (flex-1)           │ │
│   • Chat 1    │ │ │ Avatar │ Bubble                            │ │
│   • Chat 2    │ │ │ …                                            │ │
│   • Chat 3    │ │ └────────────────────────────────────────────┘ │
│               │ │                                                │
│               │ │ ┌ Input row: [Text input][＋][Send]           │
│               │ │ └ Prompts row: buttons below input           │
│               │ └──────────────────────────────────────────────┘ │
└───────────────┴─────────────────────────────────────────────────────┘
```

With these placement notes you can see exactly *where* on screen each styled element lives, as well as *which* classes drive its size, spacing, and behavior.

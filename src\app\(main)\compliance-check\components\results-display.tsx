'use client'

import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ert<PERSON><PERSON>gle,
    BarChart3,
    Brain,
    CheckCircle,
    Target,
} from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import {
    Card,
    Card<PERSON>ontent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Ta<PERSON>Trigger,
} from '@/components/ui-radix/tabs'


interface ComplianceIssue {
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly title: string;
  readonly description: string;
  readonly location: string;
  readonly recommendation: string;
}

interface ComplianceRecommendation {
  readonly priority: 'low' | 'medium' | 'high';
  readonly title: string;
  readonly description: string;
  readonly implementation: string;
}

interface ComplianceInsight {
  readonly category: string;
  readonly finding: string;
  readonly impact: string;
  readonly action: string;
}

interface ComplianceResult {
  readonly documentId: string;
  readonly framework: string;
  readonly score: number;
  readonly issues: readonly ComplianceIssue[];
  readonly recommendations: readonly ComplianceRecommendation[];
  readonly insights: readonly ComplianceInsight[];
}

interface ResultsDisplayProps {
  readonly results: readonly ComplianceResult[];
  readonly isVisible: boolean;
}

export function ResultsDisplay({ results, isVisible }: ResultsDisplayProps) {
  if (!isVisible || results.length === 0) {
return null
}

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-destructive'
      case 'high':
        return 'text-warning'
      case 'medium':
        return 'text-warning'
      case 'low':
        return 'text-success'
      default:
        return 'text-muted-foreground'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertCircle className="h-4 w-4" />
      case 'high':
        return <AlertTriangle className="h-4 w-4" />
      case 'medium':
        return <AlertTriangle className="h-4 w-4" />
      case 'low':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <CheckCircle className="h-4 w-4" />
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Column - Compliance Score Summary */}
      <div className="lg:col-span-1 space-y-4">
        {results.map((result, index) => (
          <Card key={index} className="border-border bg-card shadow-sm">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Compliance Score with Progress Bar */}
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {result.score}%
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Compliance Score
                  </p>
                  {/* Progress Bar */}
                  <div className="w-full bg-muted rounded-lg h-2 mb-4">
                    <div
                      className="bg-primary h-2 rounded-lg transition-all duration-300"
                      style={{ width: `${result.score}%` }}
                    />
                  </div>
                </div>

                {/* Framework Tag */}
                <div className="text-center">
                  <Badge variant="secondary" className="mb-4">
                    {result.framework}
                  </Badge>
                </div>

                {/* Issues and Recommendations Count */}
                <div className="space-y-3 border-t border-border pt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Issues Found</span>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-warning" />
                      <span className="font-medium text-foreground">
                        {result.issues.length}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Recommendations</span>
                    <div className="flex items-center space-x-2">
                      <Target className="h-4 w-4 text-primary" />
                      <span className="font-medium text-foreground">
                        {result.recommendations.length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Right Column - Detailed Results */}
      <div className="lg:col-span-3">
        <Card className="border-border bg-card shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <BarChart3 className="mr-2 h-5 w-5" />
              Compliance Analysis Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="issues" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3 bg-muted">
                <TabsTrigger value="issues">Issues</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="issues" className="space-y-4">
                {results.map((result, resultIndex) => (
                  <Card key={resultIndex} className="border-border bg-card">
                    <CardHeader>
                      <CardTitle className="text-lg text-foreground">
                        Document Issues - {result.framework}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {result.issues.map((issue, issueIndex) => (
                        <div
                          key={issueIndex}
                          className="border-l-4 border-warning/50 pl-4"
                        >
                          <div className="flex items-start space-x-2">
                            <div className={getSeverityColor(issue.severity)}>
                              {getSeverityIcon(issue.severity)}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-foreground">{issue.title}</h4>
                              <p className="text-sm text-muted-foreground mb-2">
                                {issue.description}
                              </p>
                              <div className="text-xs text-muted-foreground mb-2">
                                Location: {issue.location}
                              </div>
                              <div className="text-sm text-primary">
                                💡 {issue.recommendation}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="recommendations" className="space-y-4">
                {results.map((result, resultIndex) => (
                  <Card key={resultIndex} className="border-border bg-card">
                    <CardHeader>
                      <CardTitle className="flex items-center text-foreground">
                        <Target className="mr-2 h-5 w-5" />
                        Recommendations
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {result.recommendations.map((rec, recIndex) => (
                        <div key={recIndex} className="border border-border rounded-lg p-4 bg-card">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-foreground">{rec.title}</h4>
                            <Badge
                              variant={
                                rec.priority === 'high'
                                  ? 'destructive'
                                  : rec.priority === 'medium'
                                    ? 'default'
                                    : 'secondary'
                              }
                            >
                              {rec.priority} priority
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {rec.description}
                          </p>
                          <div className="text-sm text-success">
                            📋 Implementation: {rec.implementation}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="insights" className="space-y-4">
                {results.map((result, resultIndex) => (
                  <Card key={resultIndex} className="border-border bg-card">
                    <CardHeader>
                      <CardTitle className="flex items-center text-foreground">
                        <Brain className="mr-2 h-5 w-5" />
                        AI-Powered Insights
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {result.insights.map((insight, insightIndex) => (
                        <div key={insightIndex} className="border border-border rounded-lg p-4 bg-card">
                          <div className="mb-2">
                            <Badge variant="outline">{insight.category}</Badge>
                          </div>
                          <h4 className="font-medium mb-2 text-foreground">{insight.finding}</h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium text-warning">
                                Impact:
                              </span>
                              <span className="ml-2 text-muted-foreground">
                                {insight.impact}
                              </span>
                            </div>
                            <div>
                              <span className="font-medium text-success">
                                Action:
                              </span>
                              <span className="ml-2 text-muted-foreground">
                                {insight.action}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

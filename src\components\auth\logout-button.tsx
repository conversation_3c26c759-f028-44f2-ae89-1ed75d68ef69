/**
 * Logout Button Component for VigiLens
 *
 * Provides secure logout functionality with pharmaceutical compliance
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, accessibility
 * Follows DEVELOPMENT_RULES_2.md: Production-first, proper error handling
 */

'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { LogOut, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { useAuth } from '@/contexts/auth-context'

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showIcon?: boolean
  showText?: boolean
  className?: string
  confirmLogout?: boolean
}

export function LogoutButton({
  variant = 'ghost',
  size = 'default',
  showIcon = true,
  showText = true,
  className = '',
  confirmLogout = true
}: LogoutButtonProps) {
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const { signOut, userProfile } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    console.log('🚨 LogoutButton - CRITICAL LOGOUT PROCESS STARTING...')
    setIsLoggingOut(true)

    // IMMEDIATE FORCE LOGOUT - Don't wait for anything
    try {
      console.log('🧹 LogoutButton - STEP 1: Force clearing all storage IMMEDIATELY...')

      // Clear all storage immediately - don't wait for signOut
      localStorage.clear()
      sessionStorage.clear()

      // Clear all cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      })

      console.log('✅ LogoutButton - Storage cleared successfully')

      console.log('📤 LogoutButton - STEP 2: Calling signOut (non-blocking)...')

      // Call signOut but don't wait for it - let it run in background
      signOut().catch(error => {
        console.error('⚠️ LogoutButton - SignOut error (ignoring):', error)
      })

      console.log('📱 LogoutButton - STEP 3: Showing success message...')

      // Show success message
      toast.success('Logged out successfully', {
        description: 'You have been securely logged out of VigiLens.',
        duration: 1500
      })

      console.log('🔄 LogoutButton - STEP 4: IMMEDIATE REDIRECT...')

      // IMMEDIATE redirect - no waiting
      window.location.href = '/login'

    } catch (error) {
      console.error('💥 LogoutButton - EMERGENCY LOGOUT:', error)

      // EMERGENCY LOGOUT - force everything
      try {
        localStorage.clear()
        sessionStorage.clear()

        toast.error('Emergency logout', {
          description: 'Forced logout due to error. Please clear browser cache if needed.',
          duration: 2000
        })

        // Force redirect immediately
        window.location.href = '/login'

      } catch (emergencyError) {
        console.error('💀 LogoutButton - CRITICAL FAILURE:', emergencyError)
        // Last resort
        window.location.href = '/login'
      }
    } finally {
      setIsLoggingOut(false)
    }
  }

  const LogoutButtonContent = () => {
    const handleButtonClick = () => {
      console.log('🔄 LogoutButton - Button clicked, confirmLogout:', confirmLogout)
      if (!confirmLogout) {
        console.log('🔄 LogoutButton - Direct logout (no confirmation)')
        handleLogout()
      } else {
        console.log('🔄 LogoutButton - Will show confirmation dialog')
      }
    }

    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        disabled={isLoggingOut}
        onClick={confirmLogout ? undefined : handleButtonClick}
        aria-label="Log out of VigiLens"
      >
        {isLoggingOut ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {showText && <span className="ml-2">Logging out...</span>}
          </>
        ) : (
          <>
            {showIcon && <LogOut className="h-4 w-4" />}
            {showText && <span className={showIcon ? 'ml-2' : ''}>Log out</span>}
          </>
        )}
      </Button>
    )
  }

  if (!confirmLogout) {
    return <LogoutButtonContent />
  }

  const handleConfirmLogout = () => {
    console.log('🔄 LogoutButton - Confirmation dialog - Log out clicked')
    handleLogout()
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <LogoutButtonContent />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to log out of VigiLens?
            {userProfile && (
              <>
                <br />
                <br />
                <strong>Current session:</strong>
                <br />
                User: {userProfile.full_name || userProfile.email}
                <br />
                Role: {userProfile.role}
                {userProfile.organization_name && (
                  <>
                    <br />
                    Organization: {userProfile.organization_name}
                  </>
                )}
              </>
            )}
            <br />
            <br />
            You will need to sign in again to access your pharmaceutical compliance data.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoggingOut}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirmLogout}
            disabled={isLoggingOut}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoggingOut ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Logging out...
              </>
            ) : (
              <>
                <LogOut className="h-4 w-4 mr-2" />
                Log out
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Quick logout button for navigation bars
export function QuickLogoutButton({ className = '' }: { className?: string }) {
  return (
    <LogoutButton
      variant="ghost"
      size="sm"
      showIcon={true}
      showText={false}
      confirmLogout={false}
      className={className}
    />
  )
}

// Full logout button for settings pages
export function FullLogoutButton({ className = '' }: { className?: string }) {
  return (
    <LogoutButton
      variant="destructive"
      size="default"
      showIcon={true}
      showText={true}
      confirmLogout={true}
      className={className}
    />
  )
}

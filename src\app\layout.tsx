import type { Metadata, Viewport } from 'next'

import '../index.css'

import { Providers } from '@/components/providers'
import { StagewiseProvider } from '@/components/stagewise-provider'

export const metadata: Metadata = {
  title: {
    template: '%s | AI Compliance Platform',
    default: 'AI Compliance Platform - Professional Regulatory Management',
  },
  description:
    'Advanced AI-powered compliance management platform for pharmaceutical and regulated industries',
  robots: { index: false, follow: false },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body suppressHydrationWarning={true}>
        <Providers>{children}</Providers>
        <StagewiseProvider />
      </body>
    </html>
  )
}

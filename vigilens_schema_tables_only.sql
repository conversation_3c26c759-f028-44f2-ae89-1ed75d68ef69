-- VigiLens Pharmaceutical Compliance Platform - Tables and Functions Only
-- Run this after creating enum types with fix_enum_casting.sql
-- 21 CFR Part 11, HIPAA, GxP compliant database schema

-- =====================================================
-- CORE TABLES - PHARMACEUTICAL COMPLIANCE
-- =====================================================

-- Organizations table - Multi-tenant pharmaceutical companies
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,

    -- Pharmaceutical company details
    company_type VARCHAR(100), -- 'pharmaceutical', 'biotech', 'cmo', 'cro'
    regulatory_id VARCHAR(100), -- FDA establishment identifier, etc.
    gmp_license_number VARCHAR(100),

    -- Contact information
    primary_contact_email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address JSONB, -- Structured address data

    -- Compliance settings
    compliance_frameworks compliance_framework[] DEFAULT ARRAY['fda_21_cfr_part_11', 'gxp']::compliance_framework[],
    regulatory_agencies regulatory_agency[] DEFAULT ARRAY['fda']::regulatory_agency[],

    -- Operational settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    locale VARCHAR(10) DEFAULT 'en-US',
    settings JSONB DEFAULT '{}',

    -- Audit fields
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- User profiles table - Pharmaceutical personnel
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY, -- References auth.users.id
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- Personal information
    email VARCHAR(255) NOT NULL UNIQUE,
    full_name VARCHAR(255) NOT NULL,
    employee_id VARCHAR(100),
    department VARCHAR(100),
    job_title VARCHAR(255),

    -- Authentication and security
    role user_role NOT NULL DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,

    -- Login tracking for compliance
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,

    -- Pharmaceutical compliance fields
    gxp_training_completed BOOLEAN DEFAULT false,
    gxp_training_date TIMESTAMP WITH TIME ZONE,
    gxp_training_expiry TIMESTAMP WITH TIME ZONE,
    electronic_signature_enabled BOOLEAN DEFAULT false,
    signature_meaning TEXT, -- Meaning of electronic signature per 21 CFR Part 11

    -- Contact information
    phone VARCHAR(50),
    emergency_contact JSONB,

    -- Profile settings
    preferences JSONB DEFAULT '{}',
    notification_settings JSONB DEFAULT '{}',

    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- Role permissions table - Granular permission system
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- Permission definition
    role user_role NOT NULL,
    resource VARCHAR(100) NOT NULL, -- 'documents', 'users', 'audit', etc.
    action VARCHAR(50) NOT NULL,    -- 'create', 'read', 'update', 'delete', 'approve'

    -- Permission scope
    scope JSONB DEFAULT '{}', -- Additional permission constraints

    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,

    UNIQUE(organization_id, role, resource, action)
);

-- User role assignments table - Dynamic role assignment
CREATE TABLE user_role_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Role assignment
    role user_role NOT NULL,
    scope JSONB DEFAULT '{}', -- Role scope limitations

    -- Assignment details
    assigned_by UUID NOT NULL REFERENCES user_profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,

    -- Justification for compliance
    justification TEXT NOT NULL,

    UNIQUE(organization_id, user_id, role)
);

-- Regulatory documents table - Core document management
CREATE TABLE regulatory_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- Document identification
    title VARCHAR(500) NOT NULL,
    document_number VARCHAR(100) UNIQUE, -- Controlled document numbering
    document_type document_type NOT NULL,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',

    -- Document content and metadata
    description TEXT,
    content TEXT, -- Document content for AI analysis
    file_path VARCHAR(1000), -- Storage path for file attachments
    file_size BIGINT,
    file_hash VARCHAR(128), -- SHA-256 hash for integrity
    mime_type VARCHAR(100),

    -- Document lifecycle
    status document_status NOT NULL DEFAULT 'draft',
    processing_status processing_status DEFAULT 'pending',

    -- Regulatory compliance
    regulatory_agencies regulatory_agency[],
    compliance_frameworks compliance_framework[],
    gxp_relevant BOOLEAN DEFAULT true,

    -- AI analysis results
    compliance_score DECIMAL(5,2), -- 0-100 compliance score
    risk_level risk_level,
    ai_analysis_results JSONB,
    keywords TEXT[],

    -- Document relationships
    parent_document_id UUID REFERENCES regulatory_documents(id),
    supersedes_document_id UUID REFERENCES regulatory_documents(id),
    related_documents UUID[],

    -- Workflow and assignment
    assigned_to UUID[], -- Array of user IDs
    reviewer_id UUID REFERENCES user_profiles(id),
    approver_id UUID REFERENCES user_profiles(id),

    -- Dates and deadlines
    effective_date TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    review_due_date TIMESTAMP WITH TIME ZONE,
    next_review_date TIMESTAMP WITH TIME ZONE,

    -- Priority and urgency
    priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    deadline TIMESTAMP WITH TIME ZONE,

    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),

    -- Document approval tracking
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES user_profiles(id),
    approval_comments TEXT
);

-- Document versions table - Version control for 21 CFR Part 11
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    document_id UUID NOT NULL REFERENCES regulatory_documents(id) ON DELETE CASCADE,

    -- Version information
    version_number VARCHAR(50) NOT NULL,
    version_type VARCHAR(20) DEFAULT 'minor', -- 'major', 'minor', 'patch'

    -- Version content
    title VARCHAR(500) NOT NULL,
    content TEXT,
    file_path VARCHAR(1000),
    file_hash VARCHAR(128),

    -- Change tracking
    change_summary TEXT NOT NULL,
    change_reason TEXT,
    change_type VARCHAR(50), -- 'correction', 'improvement', 'regulatory_update'

    -- Version lifecycle
    status document_status NOT NULL,
    is_current BOOLEAN DEFAULT false,

    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES user_profiles(id),

    -- Electronic signature for version
    electronic_signature JSONB,

    UNIQUE(document_id, version_number)
);

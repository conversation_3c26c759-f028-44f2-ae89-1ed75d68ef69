#!/usr/bin/env python3
"""
Full FDA Knowledge Base Population

This script populates the complete FDA knowledge base with all 21 CFR volumes.
It includes progress tracking, error recovery, and comprehensive logging.

Usage:
    python populate_full_knowledge_base.py [--resume] [--batch-size 10]

Features:
- Processes all 9 CFR volumes (21 CFR Title 21)
- Progress tracking with ETA estimation
- Resume functionality for interrupted processing
- Comprehensive error handling and logging
- Performance optimization for large document sets
"""

import argparse
import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any

from services.knowledge.fda_document_processor import FDADocumentProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fda_full_population.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class FullKnowledgeBasePopulator:
    """Full FDA knowledge base populator with progress tracking."""
    
    def __init__(self, batch_size: int = 10, resume: bool = False):
        """
        Initialize the full populator.
        
        Args:
            batch_size: Number of chunks to process in each batch
            resume: Whether to resume from previous progress
        """
        self.batch_size = batch_size
        self.resume = resume
        self.progress_file = Path("fda_population_progress.json")
        
        # Initialize processor
        self.processor = FDADocumentProcessor(
            fda_docs_path="./fda_docs",
            vector_store_path="./data/qdrant_fda_full",
            chunk_size=1000,
            chunk_overlap=200
        )
        
        # Progress tracking
        self.progress = {
            "start_time": None,
            "current_document": None,
            "completed_documents": [],
            "failed_documents": [],
            "total_documents": 0,
            "total_chunks_processed": 0,
            "total_embeddings_generated": 0,
            "estimated_completion": None
        }
        
        # Load existing progress if resuming
        if self.resume and self.progress_file.exists():
            self.load_progress()
    
    def load_progress(self) -> None:
        """Load progress from previous run."""
        try:
            with open(self.progress_file, 'r') as f:
                saved_progress = json.load(f)
                self.progress.update(saved_progress)
            logger.info(f"📂 Loaded progress: {len(self.progress['completed_documents'])} documents completed")
        except Exception as e:
            logger.warning(f"⚠️ Could not load progress: {e}")
    
    def save_progress(self) -> None:
        """Save current progress."""
        try:
            # Convert datetime objects to strings for JSON
            progress_copy = self.progress.copy()
            if progress_copy["start_time"]:
                progress_copy["start_time"] = progress_copy["start_time"].isoformat()
            if progress_copy["estimated_completion"]:
                progress_copy["estimated_completion"] = progress_copy["estimated_completion"].isoformat()
            
            with open(self.progress_file, 'w') as f:
                json.dump(progress_copy, f, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ Could not save progress: {e}")
    
    async def run(self) -> Dict[str, Any]:
        """Run the full knowledge base population."""
        logger.info("🚀 STARTING FULL FDA KNOWLEDGE BASE POPULATION")
        logger.info("=" * 60)
        
        self.progress["start_time"] = datetime.now(timezone.utc)
        
        try:
            # Get all PDF files
            fda_docs_path = Path("./fda_docs")
            pdf_files = list(fda_docs_path.glob("*.pdf"))
            self.progress["total_documents"] = len(pdf_files)
            
            logger.info(f"📄 Found {len(pdf_files)} CFR documents to process")
            
            # Filter out already completed documents if resuming
            if self.resume:
                completed_names = set(self.progress["completed_documents"])
                pdf_files = [f for f in pdf_files if f.name not in completed_names]
                logger.info(f"📂 Resuming: {len(pdf_files)} documents remaining")
            
            if not pdf_files:
                logger.info("✅ All documents already processed!")
                return self.progress
            
            # Initialize processor
            await self.processor.initialize()
            logger.info("✅ Processor initialized")
            
            # Process each document
            for i, pdf_file in enumerate(pdf_files):
                await self.process_document_with_progress(pdf_file, i + 1, len(pdf_files))
            
            # Final statistics
            await self.generate_final_report()
            
            logger.info("🎉 FULL FDA KNOWLEDGE BASE POPULATION COMPLETED!")
            return self.progress
            
        except Exception as e:
            logger.error(f"💥 Fatal error: {e}")
            self.save_progress()
            raise
    
    async def process_document_with_progress(
        self, 
        pdf_file: Path, 
        current_num: int, 
        total_num: int
    ) -> None:
        """Process a single document with progress tracking."""
        self.progress["current_document"] = pdf_file.name
        
        logger.info(f"\n📚 PROCESSING DOCUMENT {current_num}/{total_num}")
        logger.info(f"📄 File: {pdf_file.name}")
        
        size_mb = round(pdf_file.stat().st_size / (1024 * 1024), 2)
        logger.info(f"📊 Size: {size_mb} MB")
        
        start_time = time.time()
        
        try:
            # Process the document
            await self.processor.process_single_document(pdf_file)
            
            # Update progress
            self.progress["completed_documents"].append(pdf_file.name)
            self.progress["total_chunks_processed"] += self.processor.stats["chunks_created"]
            self.progress["total_embeddings_generated"] += self.processor.stats["embeddings_generated"]
            
            processing_time = time.time() - start_time
            logger.info(f"✅ Completed in {processing_time:.1f} seconds")
            
            # Estimate completion time
            self.estimate_completion_time(current_num, total_num, processing_time)
            
            # Save progress
            self.save_progress()
            
        except Exception as e:
            logger.error(f"❌ Failed to process {pdf_file.name}: {e}")
            self.progress["failed_documents"].append({
                "filename": pdf_file.name,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            self.save_progress()
    
    def estimate_completion_time(
        self, 
        current_num: int, 
        total_num: int, 
        last_processing_time: float
    ) -> None:
        """Estimate completion time based on current progress."""
        if current_num == 0:
            return
        
        elapsed_time = (datetime.now(timezone.utc) - self.progress["start_time"]).total_seconds()
        avg_time_per_doc = elapsed_time / current_num
        remaining_docs = total_num - current_num
        estimated_remaining_time = remaining_docs * avg_time_per_doc
        
        estimated_completion = datetime.now(timezone.utc).timestamp() + estimated_remaining_time
        self.progress["estimated_completion"] = datetime.fromtimestamp(estimated_completion, timezone.utc)
        
        logger.info(f"⏱️ Progress: {current_num}/{total_num} ({current_num/total_num*100:.1f}%)")
        logger.info(f"🕐 Estimated completion: {self.progress['estimated_completion'].strftime('%H:%M:%S')}")
        logger.info(f"⏳ Estimated remaining: {estimated_remaining_time/3600:.1f} hours")
    
    async def generate_final_report(self) -> None:
        """Generate final processing report."""
        logger.info("\n📊 FINAL PROCESSING REPORT")
        logger.info("=" * 50)
        
        total_time = (datetime.now(timezone.utc) - self.progress["start_time"]).total_seconds()
        
        logger.info(f"⏱️ Total processing time: {total_time/3600:.2f} hours")
        logger.info(f"📚 Documents processed: {len(self.progress['completed_documents'])}")
        logger.info(f"❌ Documents failed: {len(self.progress['failed_documents'])}")
        logger.info(f"📝 Total chunks processed: {self.progress['total_chunks_processed']}")
        logger.info(f"🧠 Total embeddings generated: {self.progress['total_embeddings_generated']}")
        
        # Test final search capability
        logger.info("\n🔍 TESTING FINAL SEARCH CAPABILITY")
        try:
            search_results = await self.processor.vector_store.search_documents(
                query="FDA pharmaceutical manufacturing quality control regulations",
                limit=5
            )
            logger.info(f"✅ Search test successful: {len(search_results.results)} results")
            
            for i, result in enumerate(search_results.results[:3]):
                logger.info(f"   Result {i+1}: Score {result.score:.3f}")
                logger.info(f"   Preview: {result.content[:100]}...")
        except Exception as e:
            logger.error(f"❌ Search test failed: {e}")
        
        # Save final report
        report_file = Path("fda_full_population_report.json")
        with open(report_file, 'w') as f:
            report_data = self.progress.copy()
            if report_data["start_time"]:
                report_data["start_time"] = report_data["start_time"].isoformat()
            if report_data["estimated_completion"]:
                report_data["estimated_completion"] = report_data["estimated_completion"].isoformat()
            report_data["total_processing_time_hours"] = total_time / 3600
            json.dump(report_data, f, indent=2)
        
        logger.info(f"💾 Final report saved to: {report_file}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Populate full FDA Knowledge Base with all CFR volumes"
    )
    parser.add_argument(
        "--resume",
        action="store_true",
        help="Resume from previous progress"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Batch size for processing chunks (default: 10)"
    )
    
    args = parser.parse_args()
    
    # Create populator
    populator = FullKnowledgeBasePopulator(
        batch_size=args.batch_size,
        resume=args.resume
    )
    
    try:
        # Run population process
        results = await populator.run()
        
        # Exit with appropriate code
        if len(results["failed_documents"]) == 0:
            logger.info("✅ Full knowledge base population completed successfully")
            sys.exit(0)
        else:
            logger.warning(f"⚠️ Completed with {len(results['failed_documents'])} failed documents")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ Process interrupted by user")
        populator.save_progress()
        logger.info("💾 Progress saved. Use --resume to continue later.")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

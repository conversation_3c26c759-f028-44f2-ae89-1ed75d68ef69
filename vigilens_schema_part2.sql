-- VigiLens Schema Part 2 - Audit Trail and Electronic Signatures
-- Run this after vigilens_schema_tables_only.sql

-- Audit trail table - Comprehensive 21 CFR Part 11 audit logging
CREATE TABLE audit_trail (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Audit event identification
    user_id UUID REFERENCES user_profiles(id), -- NULL for system events
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    correlation_id VARCHAR(255), -- For tracking related events
    
    -- Action details
    action_type audit_action_type NOT NULL,
    action_category VARCHAR(100) NOT NULL, -- 'document', 'user', 'system', etc.
    action_description TEXT NOT NULL,
    
    -- Resource information
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    resource_name VARCHAR(500),
    
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    change_summary TEXT,
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    
    -- Compliance metadata
    risk_level risk_level DEFAULT 'low',
    compliance_metadata JSONB DEFAULT '{}',
    gxp_relevant BOOLEAN DEFAULT false,
    
    -- Data integrity
    data_integrity_hash VARCHAR(128), -- SHA-256 hash of audit record
    
    -- Electronic signature (if applicable)
    electronic_signature JSONB,
    
    -- Context and additional data
    context JSONB DEFAULT '{}',
    severity VARCHAR(20) DEFAULT 'info', -- 'info', 'warning', 'error', 'critical'
    
    -- Timestamp (immutable)
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Electronic signatures table - 21 CFR Part 11 compliant signatures
CREATE TABLE electronic_signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Signature identification
    document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE,
    document_version_id UUID REFERENCES document_versions(id) ON DELETE CASCADE,
    audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE,
    
    -- Signer information
    signer_id UUID NOT NULL REFERENCES user_profiles(id),
    signer_name VARCHAR(255) NOT NULL,
    signer_title VARCHAR(255),
    signer_department VARCHAR(100),
    
    -- Signature details
    signature_type signature_type NOT NULL,
    signature_meaning TEXT NOT NULL, -- Required by 21 CFR Part 11
    signature_reason TEXT,
    
    -- Authentication details
    authentication_method VARCHAR(100) NOT NULL, -- 'password', 'biometric', 'token'
    authentication_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Digital signature components
    signature_hash VARCHAR(512) NOT NULL, -- Cryptographic signature
    signature_algorithm VARCHAR(100) DEFAULT 'SHA-256',
    public_key_fingerprint VARCHAR(128),
    
    -- Signature context
    document_hash_at_signing VARCHAR(128) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    
    -- Compliance fields
    witnessed_by UUID REFERENCES user_profiles(id),
    witness_signature_hash VARCHAR(512),
    
    -- Audit fields (immutable)
    signed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Verification status
    is_valid BOOLEAN DEFAULT true,
    verification_notes TEXT
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Organizations indexes
CREATE INDEX idx_organizations_active ON organizations(is_active);
CREATE INDEX idx_organizations_name ON organizations(name);

-- User profiles indexes
CREATE INDEX idx_user_profiles_org ON user_profiles(organization_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);

-- Role permissions indexes
CREATE INDEX idx_role_permissions_org_role ON role_permissions(organization_id, role);

-- User role assignments indexes
CREATE INDEX idx_user_role_assignments_user ON user_role_assignments(user_id);
CREATE INDEX idx_user_role_assignments_org ON user_role_assignments(organization_id);

-- Regulatory documents indexes
CREATE INDEX idx_regulatory_documents_org ON regulatory_documents(organization_id);
CREATE INDEX idx_regulatory_documents_status ON regulatory_documents(status);
CREATE INDEX idx_regulatory_documents_type ON regulatory_documents(document_type);
CREATE INDEX idx_regulatory_documents_assigned ON regulatory_documents USING GIN(assigned_to);
CREATE INDEX idx_regulatory_documents_created ON regulatory_documents(created_at);
CREATE INDEX idx_regulatory_documents_updated ON regulatory_documents(updated_at);
CREATE INDEX idx_regulatory_documents_number ON regulatory_documents(document_number);

-- Document versions indexes
CREATE INDEX idx_document_versions_document ON document_versions(document_id);
CREATE INDEX idx_document_versions_current ON document_versions(is_current);

-- Audit trail indexes
CREATE INDEX idx_audit_trail_org ON audit_trail(organization_id);
CREATE INDEX idx_audit_trail_user ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_timestamp ON audit_trail(timestamp);
CREATE INDEX idx_audit_trail_action ON audit_trail(action_type);
CREATE INDEX idx_audit_trail_resource ON audit_trail(resource_type, resource_id);

-- Electronic signatures indexes
CREATE INDEX idx_electronic_signatures_document ON electronic_signatures(document_id);
CREATE INDEX idx_electronic_signatures_signer ON electronic_signatures(signer_id);
CREATE INDEX idx_electronic_signatures_signed_at ON electronic_signatures(signed_at);

-- Verify tables created
SELECT 
    'PART 2 TABLES CREATED' as status,
    COUNT(*) as table_count,
    array_agg(table_name ORDER BY table_name) as tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('audit_trail', 'electronic_signatures');

import { Badge } from '@/components/ui-radix/badge';
import { But<PERSON> } from '@/components/ui-radix/button';
import { Card, CardContent, CardHeader } from '@/components/ui-radix/card';
import { Checkbox } from '@/components/ui-radix/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui-radix/dropdown-menu';
import { Progress } from '@/components/ui-radix/progress';
import { cn } from '@/lib/utils';
import { Download, Eye, FileText, MoreHorizontal, Share } from 'lucide-react';
import * as React from 'react';
import type { DocumentCardProps } from '../types';
import { StatusBadge } from './status-badge';

/**
 * DocumentCard Component - Individual Document Display
 *
 * Features:
 * - Responsive grid/list layouts
 * - Status badges and compliance scores
 * - Action buttons and dropdown menu
 * - Hover effects and accessibility
 * - Progress bars for compliance scores
 */
export const DocumentCard: React.FC<DocumentCardProps> = React.memo(
  function DocumentCard({
    document,
    viewMode,
    onView,
    onDownload,
    onShare,
    onSelect,
    isSelected = false,
    showActions = true
  }) {
    const formatDate = (dateString: string): string => {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    };

    const FileIcon = FileText;

    return (
      <Card
        className={cn(
          "transition-all duration-200 cursor-pointer flex flex-col h-full",
          "hover:shadow-md hover:border-primary/20 hover:-translate-y-1",
          isSelected && "ring-2 ring-primary ring-offset-2",
          viewMode === 'list' && "flex-row h-auto"
        )}
        onClick={() => onView(document.id)}
        role="article"
        aria-label={`Document: ${document.name}`}
      >
        <CardHeader className={cn(
          "pb-3",
          viewMode === 'list' && "pb-4 pr-4"
        )}>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              <FileIcon
                className="h-8 w-8 text-primary flex-shrink-0"
                aria-hidden="true"
              />
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-foreground text-sm leading-tight line-clamp-2">
                  {document.name}
                </h3>
                <p className="text-xs text-muted-foreground mt-0.5">
                  {document.type} • {document.size}
                </p>
              </div>
            </div>
            {onSelect && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={(checked) => onSelect(document.id, checked as boolean)}
                className="ml-2"
                aria-label={`Select document ${document.name}`}
                onClick={(e) => e.stopPropagation()}
              />
            )}
          </div>
        </CardHeader>

        <CardContent className={cn(
          "space-y-3 flex-grow flex flex-col pt-0",
          viewMode === 'list' && "flex-row items-center space-y-0 space-x-4"
        )}>
          {/* Status and Category Badges */}
          <div className="flex items-center justify-between">
            <StatusBadge status={document.status} />
            <Badge variant="outline">{document.category}</Badge>
          </div>

          {/* Compliance Score */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Compliance Score</span>
              <span className="text-foreground font-medium">
                {document.complianceScore}%
              </span>
            </div>
            <Progress
              value={document.complianceScore}
              className="h-1"
              aria-label={`Compliance score: ${document.complianceScore} percent`}
            />
          </div>

          {/* Description */}
          <p className="text-xs text-muted-foreground line-clamp-2 flex-grow">
            {document.description}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {document.tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
                {tag}
              </Badge>
            ))}
            {document.tags.length > 2 && (
              <Badge variant="outline" className="text-xs px-2 py-0.5">
                +{document.tags.length - 2}
              </Badge>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-2 mt-auto border-t border-border/50">
            <span className="text-xs text-muted-foreground">
              {formatDate(document.uploadDate)}
            </span>

            {showActions && (
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onView(document.id);
                  }}
                  aria-label={`Preview ${document.name}`}
                >
                  <Eye size={16} className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownload(document.id);
                  }}
                  aria-label={`Download ${document.name}`}
                >
                  <Download size={16} className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onShare(document.id);
                  }}
                  aria-label={`Share ${document.name}`}
                >
                  <Share size={16} className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 w-7 p-0"
                      onClick={(e) => e.stopPropagation()}
                      aria-label={`More actions for ${document.name}`}
                    >
                      <MoreHorizontal size={16} className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => onView(document.id)}>
                      <Eye className="mr-2 h-4 w-4" />
                      Preview
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onDownload(document.id)}>
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onShare(document.id)}>
                      <Share className="mr-2 h-4 w-4" />
                      Share
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive focus:text-destructive">
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }
);

DocumentCard.displayName = 'DocumentCard';

// Recharts TypeScript augmentation to fix strict mode issues
// This file provides missing type definitions for Recharts components

declare module 'recharts' {
  // Fix payload prop issues in Tooltip
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  interface TooltipProps<TValue = any, TName = any> {
    payload?: Array<{
      name?: TName;
      value?: TValue;
      dataKey?: string | number;
      color?: string;
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      payload?: any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      [key: string]: any;
    }>;
    label?: TName;
    active?: boolean;
  }

  // Fix payload prop issues in Legend
  interface LegendProps {
    payload?: Array<{
      value?: string;
      type?: string;
      id?: string;
      color?: string;
      dataKey?: string | number;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      [key: string]: any;
    }>;
    verticalAlign?: 'top' | 'middle' | 'bottom';
  }

  // Fix missing exports
  interface CustomizedProps {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    component?: React.ComponentType<any> | React.ReactElement;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }
}

// Global type augmentation for strict TypeScript compatibility
declare global {
  namespace JSX {
    interface IntrinsicElements {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      'recharts-tooltip': any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      'recharts-legend': any;
    }
  }
}

export {}

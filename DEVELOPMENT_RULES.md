# Development Rules & Standards

## Integration of Enhanced Development Rules

This document incorporates enhanced development rules that work in addition to standard development practices, ensuring we follow cutting-edge 2025 patterns and maintain the highest code quality for our Agentic AI regulatory project.

## 🎯 **ENHANCED DEVELOPMENT PROTOCOL**

### Design & Development Standards
- **Research First**: Context7 + Web Search for latest 2025 patterns when needed
- **Standards Applied**: WCAG 2.1 AA compliance, mobile-first, Core Web Vitals optimization
- **Quality Focus**: Maximum 250 lines per file, TypeScript strictness, performance optimization

### Development Phases
1. **UI/UX Design**: Mobile-first wireframes, accessibility compliance, MLM-specific workflows
2. **Frontend Implementation**: Next.js 15.1.5, React 19, Tailwind CSS 4.0.1, shadcn/ui
3. **Code Quality**: Automated quality checks, security scanning, performance optimization
4. **Architecture Review**: SOLID principles, component organization, maintainability

## 🔧 **TECHNOLOGY STACK**

### Frontend Architecture (Following Enhanced Rules)
```
✅ MANDATORY STACK:
- Next.js 15.1.5 (App Router, Server Components, Async Request APIs)
- React 19 (Concurrent features, enhanced hydration)
- Tailwind CSS 4.0.1 (Container queries, 5x faster builds)
- shadcn/ui (Latest components, accessibility-first)
- TypeScript 5.7.3 (Strict mode, zero 'any' types)
- Valibot 0.32.1 (Client-side validation)
- Zustand 5.0.5 (State management)
```

### Backend Architecture (Following Enhanced Rules)
```
✅ STACK:
## Backend Core
- **Language**: Python 3.13.5
- **Framework**: FastAPI 0.115.5
- **AI Framework**: LangChain 0.3.14 (no CrewAI wrapper)
- **Agent Pattern**: Simple LangChain agents + custom orchestration
- **Validation**: Pydantic 2.9.2
- **Background Tasks**: APScheduler 4.0.0
- **HTTP Client**: httpx 0.28.1 (async requests)
- **Web Scraping**: BeautifulSoup4 4.12.3 + httpx

## LLM & AI
- **AI Tools**: LangChain tools + custom pharmaceutical scrapers

## Data & Storage
- **Primary Database**: Supabase (free tier - 500MB, 2025 features)
- **Vector Database**: ChromaDB 0.6.2 (embedded, file-based)
- **Knowledge Base**: Pharmaceutical regulatory documents in ChromaDB
- **Real Time**: Supabase Realtime (WebSockets, enhanced 2025 features)
- **Authentication**: Supabase Auth 2025 (built-in, JWT-based)
- **File Storage**: Supabase Storage (1GB free)

## Hosting & Infrastructure
- **Frontend Hosting**: Vercel (free tier, Next.js 15.1.5 optimized)
- **Backend Hosting**: Railway ($5 monthly credit, Python 3.13 support)
- **Database Hosting**: Supabase Cloud (free tier)
- **Vector DB**: Local files (no hosting needed)

## Monitoring & Performance
- **AI Monitoring**: LangSmith 2025 (free tier)
- **Error Tracking**: FastAPI built-in logging + Pydantic error handling
- **Performance**: Python 3.13 enhanced logging + async profiling
- **Analytics**: Supabase Analytics 2025 (built-in, enhanced)

## Security & Compliance
- **Authentication**: Supabase Auth 2025 (multi-factor support)
- **Authorization**: Row Level Security (RLS) + Supabase policies
- **Data Encryption**: TLS 1.3 in transit, AES-256 at rest
- **HIPAA Ready**: Supabase HIPAA compliance (when needed)
- **Audit Logging**: PostgreSQL audit logs + Supabase audit trail

## Development Tools
- **Code Quality**: Python 3.13 type checking + Pydantic validation
- **Testing**: FastAPI test client + pytest + httpx async testing
```

## 📝 **NAMING CONVENTIONS & STANDARDS (2025 BEST PRACTICES)**

### **Philosophy: Consistency, Readability, and Cross-Platform Compatibility**

These naming conventions follow the latest 2025 industry standards, emphasizing **kebab-case** for files and directories to ensure cross-platform compatibility, URL-friendly structure, and improved readability.

### **1. FILE NAMING CONVENTIONS**

#### **Component Files**
```typescript
// ✅ CORRECT: kebab-case for file names
contact-card.tsx           // exports ContactCard
user-profile.tsx          // exports UserProfile
pipeline-stage.tsx        // exports PipelineStage
dashboard-overview.tsx    // exports DashboardOverview
data-table.tsx           // exports DataTable

// ❌ INCORRECT: Avoid PascalCase, camelCase, or snake_case
ContactCard.tsx          // Cross-platform issues
userProfile.tsx          // Inconsistent
contact_card.tsx         // Not URL-friendly
```

#### **Page Files (Next.js App Router)**
```typescript
// ✅ CORRECT: kebab-case directories with standard Next.js files
app/
├── (auth)/
│   ├── login/
│   │   └── page.tsx
│   └── register/
│       └── page.tsx
├── contact-management/
│   ├── [id]/
│   │   └── page.tsx
│   └── page.tsx
└── dashboard/
    ├── analytics/
│       └── page.tsx
└── page.tsx
```

#### **Utility and Helper Files**
```typescript
// ✅ CORRECT: kebab-case with descriptive names
api-helpers.ts           // API utility functions
date-formatters.ts       // Date formatting utilities
contact-utils.ts         // Contact-specific utilities
pipeline-helpers.ts      // Pipeline utility functions
validation-schemas.ts    // Validation schema definitions

// ❌ INCORRECT: Inconsistent naming
apiHelpers.ts           // camelCase inconsistent
DateFormatters.ts       // PascalCase for utilities
contact_utils.ts        // snake_case not preferred
```

#### **Custom Hooks**
```typescript
// ✅ CORRECT: kebab-case with 'use-' prefix
use-auth.ts             // exports useAuth
use-contact-data.ts     // exports useContactData
use-pipeline-state.ts   // exports usePipelineState
use-local-storage.ts    // exports useLocalStorage

// ❌ INCORRECT: Inconsistent patterns
useAuth.ts              // PascalCase file name
use_contact_data.ts     // snake_case
UseContactData.ts       // PascalCase file + component confusion
```

#### **Test Files**
```typescript
// ✅ CORRECT: Match component name with test suffix
contact-card.test.tsx
user-profile.spec.tsx
api-helpers.test.ts
use-auth.test.ts

// Alternative: Test directories
__tests__/
├── contact-card.test.tsx
├── user-profile.test.tsx
└── utils/
    └── api-helpers.test.ts
```

### **2. DIRECTORY NAMING CONVENTIONS**

#### **Feature-Based Directories**
```typescript
// ✅ CORRECT: kebab-case for all directories
src/
├── components/
│   ├── contact-management/
│   │   ├── contact-card.tsx
│   │   ├── contact-filters.tsx
│   │   └── contact-list.tsx
│   ├── pipeline/
│   │   ├── pipeline-stage.tsx
│   │   └── pipeline-board.tsx
│   └── shared/
│       ├── data-table.tsx
│       └── loading-spinner.tsx
├── features/
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   └── utils/
│   └── dashboard/
│       ├── components/
│       ├── hooks/
│       └── utils/
└── lib/
    ├── api/
    ├── utils/
    └── types/
```

#### **Next.js App Router Structure**
```typescript
// ✅ CORRECT: Following Next.js 15+ conventions
app/
├── (auth)/              # Route group
│   ├── login/
│   └── register/
├── (main)/              # Route group
│   ├── contacts/
│   │   ├── [id]/
│   │   └── components/
│   ├── dashboard/
│   │   ├── analytics/
│   │   └── settings/
│   └── pipeline/
│       └── components/
└── api/
    ├── contacts/
│       └── route.ts
└── auth/
    └── route.ts
```

### **3. COMPONENT AND FUNCTION NAMING**

#### **React Components**
```typescript
// ✅ CORRECT: PascalCase for component names
export default function ContactCard({ contact }: ContactCardProps) {
  return <div className="contact-card">{/* content */}</div>;
}

export function UserProfile({ user }: UserProfileProps) {
  return <div className="user-profile">{/* content */}</div>;
}

// File: contact-card.tsx
// Import: import ContactCard from './contact-card';
```

#### **Functions and Variables**
```typescript
// ✅ CORRECT: camelCase for functions and variables
const userSettings = {
  theme: 'dark',
  notifications: true
};

function handleSubmit(data: FormData): void {
  // Handle form submission
}

const formatContactName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`;
};

// Event handlers: camelCase with 'handle' prefix
const handleClick = () => { /* click logic */ };
const handleContactSelect = (contact: Contact) => { /* selection logic */ };
```

### **4. TYPESCRIPT TYPES AND INTERFACES**

#### **Interfaces and Types**
```typescript
// ✅ CORRECT: PascalCase with descriptive names
interface ContactProps {
  readonly contact: Contact;
  readonly onSelect?: (contact: Contact) => void;
  readonly className?: string;
}

interface UserSettings {
  readonly theme: 'light' | 'dark';
  readonly notifications: boolean;
  readonly autoSave: boolean;
}

type ContactStatus = 'prospect' | 'customer' | 'team-member';
type PipelineStage = 'lead' | 'qualified' | 'proposal' | 'closed';

// Alternative with T prefix (optional)
type TAuthState = {
  readonly isAuthenticated: boolean;
  readonly user: User | null;
};
```

#### **Enums**
```typescript
// ✅ CORRECT: PascalCase for enum names
enum ContactStatus {
  PROSPECT = 'prospect',
  CUSTOMER = 'customer',
  TEAM_MEMBER = 'team-member'
}

enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  AGENT = 'agent'
}

// Usage
const status: ContactStatus = ContactStatus.PROSPECT;
```

### **5. CONSTANTS AND CONFIGURATION**

#### **Constants**
```typescript
// ✅ CORRECT: SCREAMING_SNAKE_CASE for hard-coded constants
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_PAGE_SIZE = 20;
const CONTACT_STATUS_OPTIONS = ['prospect', 'customer', 'team-member'] as const;

// ✅ CORRECT: camelCase for runtime constants
const userList = await fetchUsers();
const contactData = await getContactData();
const currentUser = getCurrentUser();
```

#### **Configuration Objects**
```typescript
// ✅ CORRECT: camelCase for configuration objects
const apiConfig = {
  baseURL: API_BASE_URL,
  timeout: 5000,
  retries: MAX_RETRY_ATTEMPTS
};

const validationRules = {
  email: { required: true, pattern: EMAIL_REGEX },
  phone: { required: false, pattern: PHONE_REGEX }
};
```

### **6. CSS AND STYLING**

#### **CSS Classes and IDs**
```css
/* ✅ CORRECT: kebab-case for CSS classes */
.contact-card {
  padding: 1rem;
  border-radius: 0.5rem;
}

.user-profile-header {
  display: flex;
  align-items: center;
}

.pipeline-stage-container {
  min-height: 200px;
  background-color: var(--stage-bg);
}

/* Component-specific classes */
.contact-card__header {
  border-bottom: 1px solid var(--border-color);
}

.contact-card__body {
  padding: 1rem;
}
```

#### **CSS Modules**
```typescript
// File: contact-card.module.css
.container { /* styles */ }
.header { /* styles */ }
.body { /* styles */ }

// File: contact-card.tsx
import styles from './contact-card.module.css';

export function ContactCard() {
  return (
    <div className={styles.container}>
      <div className={styles.header}>Header</div>
      <div className={styles.body}>Body</div>
    </div>
  );
}
```

### **7. IMPORT AND EXPORT PATTERNS**

#### **Import Statements**
```typescript
// ✅ CORRECT: Consistent import patterns
import ContactCard from './contact-card';
import { UserProfile, ContactList } from './user-components';
import { formatDate, validateEmail } from '../utils/helpers';
import { useAuth } from '../hooks/use-auth';
import type { Contact, User } from '../types/user-types';

// Group imports logically
import * as React from 'react';
import { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

import ContactCard from './contact-card';
import { useContactData } from '../hooks/use-contact-data';
```

#### **Export Patterns**
```typescript
// ✅ CORRECT: Consistent export patterns

// Default exports for main components
export default function ContactCard({ contact }: ContactCardProps) {
  // Component implementation
}

// Named exports for utilities and helpers
export const formatContactName = (first: string, last: string): string => {
  return `${first} ${last}`;
};

export const validateContact = (contact: Contact): boolean => {
  // Validation logic
};

// Re-exports for barrel files
export { ContactCard } from './contact-card';
export { ContactList } from './contact-list';
export { ContactFilters } from './contact-filters';
```

### **8. ASSET AND STATIC FILE NAMING**

#### **Images and Icons**
```typescript
// ✅ CORRECT: kebab-case for asset files
public/
├── images/
│   ├── user-avatar-placeholder.png
│   ├── company-logo.svg
│   ├── contact-default-photo.jpg
│   └── pipeline-background.webp
├── icons/
│   ├── arrow-right.svg
│   ├── user-circle.svg
│   ├── settings-gear.svg
│   └── contact-phone.svg
└── fonts/
    ├── inter-regular.woff2
    ├── inter-bold.woff2
    └── roboto-mono.woff2
```

### **9. VALIDATION AND ENFORCEMENT**

#### **ESLint Configuration**
```json
// .eslintrc.json additions for naming conventions
{
  "rules": {
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "variable",
        "format": ["camelCase", "UPPER_CASE"]
      },
      {
        "selector": "function",
        "format": ["camelCase"]
      },
      {
        "selector": "typeLike",
        "format": ["PascalCase"]
      },
      {
        "selector": "interface",
        "format": ["PascalCase"]
      }
    ]
  }
}
```

#### **File Naming Validation Script**
```bash
#!/bin/bash
# validate-file-names.sh
find src -name "*.tsx" -o -name "*.ts" | while read file; do
  basename=$(basename "$file" .tsx)
  basename=$(basename "$basename" .ts)
  if [[ ! "$basename" =~ ^[a-z][a-z0-9]*(-[a-z0-9]+)*$ ]]; then
    echo "❌ Invalid file name: $file (should be kebab-case)"
    exit 1
  fi
done
echo "✅ All file names follow kebab-case convention"
```

### **10. MIGRATION STRATEGY**

#### **Implementation Plan**
1. **New Files**: Apply new conventions immediately
2. **Existing Files**: Gradual migration during refactoring
3. **Critical Components**: Priority migration for main features
4. **Documentation**: Update all examples and guidelines

#### **Migration Checklist**
- [ ] Update component file names to kebab-case
- [ ] Ensure component exports remain PascalCase
- [ ] Update import statements throughout codebase
- [ ] Verify all directory names use kebab-case
- [ ] Update test file names to match component names
- [ ] Configure ESLint rules for naming validation
- [ ] Update documentation and examples
- [ ] Train team on new conventions

### **11. BENEFITS OF CONSISTENT NAMING**

#### **Technical Benefits**
- **Cross-Platform Compatibility**: kebab-case eliminates Windows/Linux case sensitivity issues
- **URL-Friendly**: File names directly translate to clean URLs in Next.js routing
- **Better Tooling**: IDE autocomplete and search work more effectively
- **Reduced Errors**: Consistent patterns reduce import/export mistakes

#### **Team Benefits**
- **Faster Onboarding**: New developers can predict file locations
- **Improved Code Reviews**: Focus on logic rather than style inconsistencies
- **Better Collaboration**: Consistent patterns reduce miscommunication
- **Reduced Cognitive Load**: Developers don't need to remember multiple conventions

### **12. EXCEPTION HANDLING**

#### **When to Deviate**
- **Third-Party Integrations**: Match external API naming conventions
- **Legacy Systems**: Maintain compatibility with existing systems
- **Generated Files**: Auto-generated files may follow different patterns
- **External Libraries**: Follow library-specific conventions when extending

#### **Documentation for Exceptions**
```typescript
// Exception: Matching external API structure
interface StripePaymentIntent {
  id: string;
  amount: number;
  currency: string;
  // Following Stripe's naming convention
}

// Exception: Legacy system compatibility
const LEGACY_API_ENDPOINTS = {
  GetUserData: '/api/legacy/GetUserData',
  UpdateContact: '/api/legacy/UpdateContact'
};
```

## 🎨 **UI/UX EXCELLENCE STANDARDS**

### Design System Requirements
1. **Research-First Protocol**
   - Use Context7 + Web Search for 2025 patterns when needed
   - Verify current best practices and accessibility

2. **Design Standards**
   - WCAG 2.1 AA compliance mandatory
   - Mobile-first responsive design
   - Core Web Vitals optimization
   - Semantic HTML structure
   - Maximum 6 levels DOM nesting

3. **Pharma-Compliance UI Patterns**
   - Document status indicators (draft, final, superseded)
   - Risk-level visualisations (heatmaps, severity badges)
   - Regulatory update timeline charts
   - Compliance coverage dashboards (framework vs. document matrix)

## 🏗️ **ARCHITECTURE PATTERNS**

### File Organization (250-Line Rule with Metadata Strategy)
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/             # Authentication routes (Client Components)
│   │   └── login/
│   ├── (main)/             # Main application (Client Components)
│   │   ├── dashboard/         # Regulatory Overview Dashboard
│   │   │   └── components/   # Dashboard-specific components
│   │   ├── documents/         # Document Repository & Search
│   │   │   └── components/   # Document-specific components
│   │   ├── updates/           # Real-time Regulatory Updates
│   │   │   └── components/   # Updates-specific components
│   │   ├── ai-assistant/      # AI Assistant Chat
│   │   │   └── components/   # AI assistant components
│   │   ├── compliance-check/  # Compliance Gap Analysis
│   │   │   └── components/   # Compliance check components
│   │   ├── compliance-info/   # Regulatory Framework Info
│   │   ├── notifications/     # In-App Notifications
│   │   ├── profile/           # User Profile
│   │   ├── search/            # Global Search
│   │   ├── settings/          # Application Settings
│   │   └── upload/            # Document Upload
│   ├── [[...slug]]/           # Catch-all fallback route
│   └── layout.tsx             # Global metadata defaults
├── components/                # Shared components (non-route-specific)
│   ├── ui/                    # shadcn/ui components (DO NOT EDIT)
│   ├── layout/                # Layout components (header, sidebar, etc.)
│   ├── page-header/           # Standardized page header component
│   └── shared/                # Misc shared elements (loading spinner, etc.)
├── hooks/
│   ├── use-page-metadata.ts   # Centralized metadata management
│   ├── use-sidebar.ts         # Sidebar state management
│   └── index.ts               # Barrel exports
└── lib/
    ├── utils.ts               # Utility functions
    └── types.ts               # Shared type definitions
```

### Component Architecture Standards (with Metadata Integration)
```typescript
// ✅ Maximum 250 lines per file
// ✅ Single Responsibility Principle
// ✅ SOLID principles adherence
// ✅ Consistent metadata management

// ✅ Page Component Pattern (Client Component with Metadata)
// Location: app/(main)/documents/page.tsx
'use client'
import { usePageMetadata } from '@/hooks/use-page-metadata'
import { DocumentTable } from './components/document-table'
import { PageHeader } from '@/components/page-header'

export default function DocumentsPage() {
  usePageMetadata('Documents', 'Browse and manage regulatory documents')

  return (
    <div className="space-y-6">
      <PageHeader
        title="Documents"
        description="Browse and manage regulatory documents"
        actions={<DocumentActions />}
      />
      <DocumentTable />
    </div>
  )
}

// ✅ Route-specific component example
// Location: app/(main)/contacts/components/contact-card.tsx
interface DocumentCardProps {
  readonly document: Document;
  readonly onUpdate: (document: Document) => void;
  readonly onDelete: (id: string) => void;
}

export function DocumentCard({ document, onUpdate, onDelete }: DocumentCardProps) {
  // Implementation < 250 lines
}

// ✅ Shared component example with metadata integration
// Location: components/page-header/index.tsx
interface PageHeaderProps {
  readonly title: string;
  readonly description?: string;
  readonly actions?: React.ReactNode;
  readonly className?: string;
}

export function PageHeader({ title, description, actions, className }: PageHeaderProps) {
  return (
    <header className={cn("space-y-2", className)} role="banner">
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
    </header>
  )
}

// ✅ Marketing Page Pattern (Server Component with Full SEO)
// Location: app/marketing/pricing/page.tsx
export const metadata = {
  title: 'Pricing - Vigilens',
  description: 'Affordable Agentic AI Regulatory plans for teams of all sizes',
  openGraph: {
    title: 'Vigilens Pricing',
    description: 'Choose the perfect plan for your team'
  }
}

export default function PricingPage() {
  return <PricingContent />
}
```

## 🔍 **CODE QUALITY GATES**

### Automated Validation Commands
```bash
# ✅ Use existing project scripts
npm run verify:all           # Lint + test
npm run type-check:all       # TypeScript validation
npm run lint                 # ESLint checks
```

### Quality Requirements (Updated with Metadata Standards)
1. **TypeScript Strictness**
   - Zero 'any' types allowed
   - Strict mode enabled
   - Interface definitions for all props
   - Readonly interfaces for component props

2. **Component Standards**
   - Maximum 250 lines per file
   - Props validation with TypeScript
   - Error boundaries implementation
   - Accessibility compliance
   - **Consistent metadata patterns** (usePageMetadata for client, metadata export for server)

3. **Metadata Compliance**
   - **Client Components**: Must use `usePageMetadata` hook
   - **Server Components**: Must use `metadata` export (marketing pages only)
   - **No mixing patterns**: Either client OR server approach, not both
   - **Professional titles**: All pages must have meaningful titles
   - **Internal data protection**: Use `robots: 'noindex'` for user-specific content

4. **Performance Standards**
   - Core Web Vitals optimization
   - Bundle size monitoring
   - Lazy loading implementation
   - Code splitting strategies
   - **Client-first rendering** for interactive features

5. **File Organization Rules**
   - Route-specific components in route folders
   - Shared components in `/components/`
   - Never edit `/components/ui/` folder
   - Modularize large components into subfolders
   - **Metadata hooks** in `/hooks/` directory
   - **Marketing pages** in dedicated `/marketing/` folder

6. **Architectural Consistency**
   - **App pages**: Client Components with `usePageMetadata`
   - **Marketing pages**: Server Components with full SEO metadata
   - **No hybrid patterns**: Consistent approach within page types
   - **Professional polish**: Clean tab titles and context for all pages

## Code Quality Maintenance
- Refactor continuously
- Fix technical debt early
- Leave code cleaner than you found it

## Verify Information
Always verify information before presenting it. Do not make assumptions or speculate without clear evidence.

## No Inventions
Don't invent changes other than what's explicitly requested.


## No Unnecessary Confirmations
Don't ask for confirmation of information already provided in the context.

## Preserve Existing Code
Don't remove unrelated code or functionalities. Pay attention to preserving existing structures.

## Single Chunk Edits
**Provide all edits in a single chunk** instead of multiple-step instructions or explanations for the same file.


## 📱 **GENERIC IMPLEMENTATION PATTERNS**

### Entity Card Pattern
```typescript
// ✅ Following your frontend rules
'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/badge';
import { Button } from '@/components/ui/button';

interface EntityCardProps {
  entity: Entity;
  variant: 'primary' | 'secondary' | 'tertiary';
}

export function EntityCard({ entity, variant }: EntityCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="@container p-4">
      <div className="@sm:flex @sm:items-center @sm:justify-between">
        {/* Entity content */}
      </div>
    </Card>
  );
}
```

### Workflow Stage Pattern
```typescript
// ✅ Server Component for data fetching
async function getWorkflowData() {
  // Server-side data fetching
}

export default async function WorkflowView() {
  const workflowData = await getWorkflowData();

  return (
    <div className="grid grid-cols-1 @lg:grid-cols-5 gap-4">
      {workflowData.stages.map(stage => (
        <WorkflowStage key={stage.id} stage={stage} />
      ))}
    </div>
  );
}
```

## 🚀 **DEVELOPMENT WORKFLOW**

### Phase-Based Development
1. **Design Phase**
   - Research 2025 pharmaceutical compliance enterprise UI patterns when needed
   - Create wireframes and design system
   - Ensure accessibility compliance
   - Mobile-first approach

2. **Implementation Phase**
   - Next.js 15 + React 19 patterns
   - Tailwind CSS 4 implementation
   - Component development < 250 lines
   - TypeScript strict compliance

3. **Quality Phase**
   - Automated quality checks
   - Performance optimization
   - Accessibility testing
   - Code review standards

### Integration with Enhanced Rules
- **Follow 250-line file limits** (maximum per file)
- **Use existing components** before creating new ones (check shadcn/ui first)
- **Respect file organization** (route-specific vs shared components)
- **Maintain quality gates** with automated tools
- **TypeScript strictness** (zero 'any' types)
- **Performance focus** (Core Web Vitals optimization)

## 🎯 **SUCCESS METRICS (Updated with Metadata Standards)**

### Technical Metrics
- Zero TypeScript 'any' types
- All files under 250 lines
- WCAG 2.1 AA compliance score
- Core Web Vitals passing
- Zero ESLint errors
- 100% type safety coverage
- **100% metadata compliance** (all pages use appropriate metadata pattern)
- **No mixed component patterns** (consistent client/server approach)
- **Professional metadata coverage** (all pages have meaningful titles)

### Metadata Quality Metrics
- **Client pages using `usePageMetadata`**: 100%
- **Marketing pages with full SEO**: 100%
- **Internal pages with `noindex`**: 100%
- **Pages with meaningful titles**: 100%
- **Consistent title format** (`Title | Vigilens`): 100%

### Compliance Platform Business Metrics
- Regulatory document detection accuracy within 1 hour: 99%
- AI analysis accuracy across key tasks: ≥ 90%
- Reduction in manual document processing time: ≥ 80%
- Reduction in implementation lag for regulatory updates: < 7 days
- **User context awareness** (clear page titles and navigation)
- **Professional brand consistency** (metadata formatting)

## ⚡ **REACT PERFORMANCE & MEMOIZATION STANDARDS (2025)**

### **🔄 PERFORMANCE PHILOSOPHY - MEASURED APPROACH**

#### **PRIMARY GUIDELINES:**
```typescript
// ✅ PREFERRED: Clean function components (when possible)
export function ComponentName({ prop1, prop2 }: Props) {
  // Direct calculations for simple operations
  const result = simpleCalculation(prop1, prop2);

  // Simple event handlers for basic interactions
  function handleClick() {
    onAction(prop1);
  }

  return <div>{/* Clean, readable JSX */}</div>;
}

// ✅ ACCEPTABLE: Manual memoization when needed
export const ExpensiveComponent = React.memo(function ExpensiveComponent({ data }) {
  // Use memoization for proven performance bottlenecks
  const expensiveResult = useMemo(() => {
    return data.reduce((acc, item) => acc + item.value, 0);
  }, [data]);

  return <div>{expensiveResult}</div>;
});
```

#### **PERFORMANCE DECISION TREE (2025)**
1. **Write Clean Code First**: Start with readable, simple code
2. **Measure Performance**: Profile before optimizing
3. **Apply Targeted Optimizations**: Use memoization only for proven bottlenecks
4. **Consider React Compiler**: When available and stable for your use case

### **⚖️ MEMOIZATION GUIDELINES:**

#### **WHEN TO USE Manual Memoization:**
- Heavy computations with expensive operations (>16ms)
- Components re-rendering frequently with stable props
- Large lists or data transformations
- Proven performance bottlenecks via profiling

#### **WHEN TO AVOID Manual Memoization:**
- Simple calculations or string formatting
- Premature optimization without measurement
- Components that rarely re-render
- Small datasets or trivial operations

### **🔧 REACT COMPILER INTEGRATION (Optional)**

#### **Current Status (June 2025):**
- **Available**: React Compiler RC (Release Candidate)
- **Status**: Experimental, production testing at Meta/Instagram
- **Compatibility**: React 17+ with runtime dependency
- **Integration**: Babel plugin, Vite plugin, Next.js 15.3.1+

#### **IF Adopting React Compiler:**
```bash
# Installation (choose one)
npm install --save-dev --save-exact babel-plugin-react-compiler@rc
npm install react-compiler-runtime@rc

# Next.js integration
# next.config.ts
const nextConfig = {
  experimental: {
    reactCompiler: true,
  },
};
```

#### **React Compiler Guidelines:**
- **Test thoroughly** in development before production
- **Pin exact versions** due to experimental status
- **Monitor performance** improvements vs regressions
- **Keep existing memoization** during transition period
- **Follow React Rules** strictly (compiler validates this)

#### **Migration Strategy:**
1. **Phase 1**: Install and test on development environment
2. **Phase 2**: Enable on small, isolated components
3. **Phase 3**: Gradually expand coverage with monitoring
4. **Phase 4**: Consider removing manual memoization where compiler proves effective

### **🔧 TYPESCRIPT 5.8+ STRICT REQUIREMENTS**

#### **MANDATORY TYPE PATTERNS:**
```typescript
// ✅ REQUIRED: Strict readonly interfaces
interface ComponentProps {
  readonly data: readonly Item[];
  readonly onAction?: (item: Item) => void;
  readonly className?: string;
}

// ✅ REQUIRED: Proper function component typing
export function Component({ data, onAction }: ComponentProps): JSX.Element {
  // Direct logic - no manual optimization
}

// ❌ FORBIDDEN: Any types or loose typing
interface BadProps {
  data: any;           // NEVER use 'any'
  callback: Function;  // Use specific function signatures
  items: Item[];       // Should be 'readonly Item[]'
}
```

#### **TYPE SAFETY ENFORCEMENT:**
- **Zero `any` types** - Use proper type definitions
- **Readonly arrays/objects** - Prevent accidental mutations
- **Strict function signatures** - No generic `Function` type
- **Interface over type** - Use interface for component props

### **📋 COMPONENT STRUCTURE REQUIREMENTS**

#### **FILE STRUCTURE TEMPLATE:**
```typescript
'use client'; // Only if client component needed

import * as React from 'react';
import { Component } from '@/components/ui/component';

interface ComponentNameProps {
  readonly prop1: string;
  readonly prop2?: number;
}

/**
 * Component Description - 2025 React Compiler Optimized
 *
 * Features:
 * - Clean code first approach
 * - React Compiler automatic optimization
 * - TypeScript 5.8+ strict patterns
 * - WCAG 2.1 AA compliance
 */
export function ComponentName({ prop1, prop2 }: ComponentNameProps): JSX.Element {
  // Direct calculations - React Compiler optimizes
  const calculatedValue = complexCalculation(prop1, prop2);

  // Simple event handlers - no useCallback
  function handleAction() {
    // Event logic
  }

  return (
    <div className="semantic-class" role="region" aria-label="Description">
      {/* Clean, accessible JSX */}
    </div>
  );
}
```

#### **COMPONENT RULES ENFORCEMENT:**
1. **Function Components Only** - No class components
2. **Clean Code First** - Readable over clever
3. **Single Responsibility** - One purpose per component
4. **Accessibility Required** - WCAG 2.1 AA compliance
5. **250 Line Limit** - Split larger components

### **⚖️ ENFORCEMENT & MIGRATION GUIDELINES**

#### **FOR NEW COMPONENTS (Best Practices):**
- **Start with clean code** - Simple, readable components first
- **Measure before optimize** - Profile performance before adding memoization
- **Use memoization judiciously** - Only for proven bottlenecks
- **TypeScript 5.8+ strict** - readonly interfaces, no 'any' types

#### **FOR EXISTING COMPONENTS (Gradual):**
- **Evaluate performance impact** before changing memoization patterns
- **Upgrade TypeScript patterns** to readonly interfaces
- **Remove unnecessary memoization** only when safe to do so
- **Add memoization** when profiling shows benefit

#### **CODE REVIEW CHECKLIST:**
```bash
# ❌ REJECT if found:
- Premature optimization without measurement
- 'any' types or loose typing
- Non-readonly interfaces
- Files over 250 lines
- Missing accessibility attributes
- Unnecessary complexity

# ✅ APPROVE if contains:
- Clean, readable code structure
- Appropriate use of memoization (if any)
- Proper TypeScript strict patterns
- Accessibility compliance
- Performance consideration documentation
```

#### **QUALITY GATES:**
- **ESLint:** Must pass with zero warnings
- **TypeScript:** Strict mode with zero errors
- **Accessibility:** WCAG 2.1 AA compliance required
- **Performance:** Core Web Vitals optimization
- **File Size:** Maximum 250 lines per file

#### Manual Memoization Rules (Legacy Support Only)
```typescript
// ✅ WHEN TO USE manual memoization (transition period):
// 1. No React Compiler available
// 2. Proven performance bottleneck (>1ms calculation)
// 3. Stable props for React.memo children
// 4. Effect dependencies requiring stable references

// ✅ CORRECT Manual Memoization Pattern
export const ContactStats = React.memo(
  function ContactStats({ contacts }: { readonly contacts: readonly Contact[] }) {
    // Single-pass O(n) calculation, properly memoized
    const stats = useMemo((): ContactStatsType => {
      return contacts.reduce((acc, contact) => {
        acc.total++;
        acc[contact.status]++;
        return acc;
      }, { total: 0, prospects: 0, customers: 0, teamMembers: 0 });
    }, [contacts]);

    return <div>{/* Display stats */}</div>;
  }
);

ContactStats.displayName = 'ContactStats';
```

### Senior Level 5 Performance Patterns

#### Enterprise-Grade Component Structure
```typescript
// ✅ SENIOR LEVEL 5 Component Pattern
import * as React from "react";

interface ComponentProps {
  readonly data: readonly DataItem[];
  readonly onAction?: (item: DataItem) => void;
  readonly className?: string;
}

/**
 * Enterprise Component - Production Grade
 *
 * Features:
 * - TypeScript 5.8.3 strict readonly interfaces
 * - React 19.1.0 patterns with proper error boundaries
 * - Next.js 15.3.4 server component ready
 * - WCAG 2.1 AA semantic HTML
 * - Zero performance issues
 * - Single Responsibility Principle
 */
export const ComponentName: React.FC<ComponentProps> = React.memo(
  function ComponentName({ data, onAction, className }) {
    // Memoized calculations only when needed
    const processedData = React.useMemo(() => {
      if (data.length < 100) return data; // Skip memoization for small datasets
      return data.filter(item => item.isActive);
    }, [data]);

    // Stable event handlers only when passed to children
    const handleAction = React.useCallback((item: DataItem): void => {
      onAction?.(item);
    }, [onAction]);

    return (
      <section
        className={cn("space-y-4", className)}
        role="region"
        aria-label="Component section"
      >
        {processedData.map(item => (
          <ItemComponent
            key={item.id}
            item={item}
            onAction={handleAction}
          />
        ))}
      </section>
    );
  }
);

ComponentName.displayName = 'ComponentName';
```

#### Performance Hook Patterns
```typescript
// ✅ PERFORMANCE-OPTIMIZED Custom Hook
import { useMemo, useCallback, useState } from 'react';

interface UseDataManagerOptions {
  readonly initialData: readonly DataItem[];
  readonly filterCriteria?: FilterCriteria;
}

interface UseDataManagerReturn {
  readonly data: readonly DataItem[];
  readonly stats: DataStats;
  readonly addItem: (item: DataItem) => void;
  readonly removeItem: (id: string) => void;
  readonly updateFilter: (criteria: FilterCriteria) => void;
}

/**
 * Performance-Optimized Data Management Hook
 *
 * Features:
 * - Single-pass O(n) calculations
 * - Memoized expensive operations
 * - Stable function references
 * - TypeScript strict interfaces
 */
export function useDataManager({
  initialData,
  filterCriteria
}: UseDataManagerOptions): UseDataManagerReturn {
  const [data, setData] = useState<readonly DataItem[]>(initialData);
  const [filter, setFilter] = useState<FilterCriteria | undefined>(filterCriteria);

  // Single-pass calculation - O(n) instead of O(n²)
  const { filteredData, stats } = useMemo(() => {
    let filteredItems: DataItem[] = [];
    const statistics: DataStats = {
      total: 0,
      active: 0,
      inactive: 0,
      categories: {}
    };

    // Single iteration for both filtering and stats
    for (const item of data) {
      // Apply filter
      if (!filter || matchesFilter(item, filter)) {
        filteredItems.push(item);
      }

      // Calculate stats
      statistics.total++;
      statistics[item.status]++;
      statistics.categories[item.category] = (statistics.categories[item.category] || 0) + 1;
    }

    return {
      filteredData: filteredItems as readonly DataItem[],
      stats: statistics
    };
  }, [data, filter]);

  // Stable function references
  const addItem = useCallback((item: DataItem): void => {
    setData(prev => [...prev, item]);
  }, []);

  const removeItem = useCallback((id: string): void => {
    setData(prev => prev.filter(item => item.id !== id));
  }, []);

  const updateFilter = useCallback((criteria: FilterCriteria): void => {
    setFilter(criteria);
  }, []);

  return {
    data: filteredData,
    stats,
    addItem,
    removeItem,
    updateFilter
  };
}
```

### Layout and Container Optimization

#### Proper Layout Structure (No Performance Issues)
```typescript
// ✅ OPTIMIZED Layout Structure
import * as React from "react";

/**
 * Main Layout - Enterprise Grade Performance
 *
 * Features:
 * - No unnecessary wrapper divs
 * - Proper SidebarProvider integration
 * - Error boundary ready
 * - Semantic HTML structure
 * - Responsive container patterns
 */
const MainLayout: React.FC<{ children: React.ReactNode }> = React.memo(
  function MainLayout({ children }) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <AppHeader />
          <main
            className="flex flex-1 flex-col gap-4 py-10 px-10"
            role="main"
            aria-label="Main content"
          >
            <React.Suspense fallback={<LoadingSkeleton />}>
              {children}
            </React.Suspense>
          </main>
          <AppFooter />
        </SidebarInset>
      </SidebarProvider>
    );
  }
);

MainLayout.displayName = 'MainLayout';
```

#### Page Header Pattern (Reusable & Optimized)
```typescript
// ✅ REUSABLE PageHeader Component
interface PageHeaderProps {
  readonly title: string;
  readonly description?: string;
  readonly actions?: React.ReactNode;
  readonly className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = React.memo(
  function PageHeader({ title, description, actions, className }) {
    return (
      <header
        className={cn("space-y-2", className)}
        role="banner"
        aria-label="Page header"
      >
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
          {actions && (
            <div className="flex items-center gap-2" role="toolbar">
              {actions}
            </div>
          )}
        </div>
      </header>
    );
  }
);

PageHeader.displayName = 'PageHeader';
```

### Error Boundaries and Production Resilience

```typescript
// ✅ PRODUCTION Error Boundary Pattern
interface ErrorBoundaryProps {
  readonly children: React.ReactNode;
  readonly fallback?: React.ComponentType<{ error: Error }>;
  readonly onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class ProductionErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError?.(error, errorInfo);

    // Production error reporting
    if (process.env.NODE_ENV === 'production') {
      console.error('Production Error:', error, errorInfo);
      // Send to error reporting service
    }
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} />;
    }

    return this.props.children;
  }
}
```

### React Compiler Integration Strategy

#### Migration Path (2025-2027)
```typescript
// Phase 1: Install React Compiler RC
// npm install --save-dev --save-exact babel-plugin-react-compiler@rc

// Phase 2: Next.js Configuration
const nextConfig = {
  experimental: {
    reactCompiler: true,
  },
};

// Phase 3: Gradually remove manual memoization
// - Start with new components: Write clean code, no manual memoization
// - Existing components: Keep manual memoization during transition
// - Remove manual memoization only after compiler proves effective

// Phase 4: ESLint Integration
// eslint-plugin-react-hooks@6.0.0-rc.1 includes compiler rules
```

#### Development Standards During Transition
1. **New Components**: Write clean code, no manual memoization
2. **Existing Components**: Keep current memoization until proven unnecessary
3. **Performance Issues**: Profile first, optimize specifically
4. **Large Datasets**: Still consider manual optimization for proven bottlenecks

### Performance Monitoring Standards

```typescript
// ✅ PERFORMANCE Monitoring Pattern
export function usePerformanceMonitor(componentName: string) {
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;

        if (renderTime > 16) { // > 1 frame at 60fps
          console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
        }
      };
    }
  });
}
```

## 📋 **BMAD Orchestrator Integration**

To ensure the BMAD Orchestrator follows these development rules:

1. **Reference Document**: The BMAD Orchestrator should reference this document for all development decisions
2. **Quality Gates**: All generated code must pass the quality requirements outlined above
3. **File Organization**: Must follow the specified file structure and component organization
4. **Technology Stack**: Must use only the approved frontend technologies (no backend)
5. **Standards Compliance**: All output must meet the 250-Line limit, TypeScript strictness, and accessibility requirements
6. **Performance Standards**: Follow 2025 React patterns, proper memoization strategy, and enterprise-grade optimization

The orchestrator should treat these rules as mandatory constraints when generating or reviewing any code or architecture decisions.


## 6-Expert Synthesis Protocol
**Phase 1: Silent Generation - The Council of Masters**: where everyone will take the input and find the solution or the root cause (if `user request` is about a problem, And come up with their best solution considering all their knowledge, edge-cases, following all rules, doing sequentialthinking with CoT, ToT and using context7 mcp protocol to fetch latest information/documentation about the technology/library and if needed using web search for June 2025 up to date answers.
#### **Phase 2: Silent Analysis & Critique**
Once the six frontend solutions are generated, simulate an internal peer review:
1. **Automated Audit:** Each solution is checked against strict principal and coding best practices criterias
2. **Cross-Critique:** Each expert analyzes the other five solutions, genuinely critiquing trade-offs
#### **Phase 3: Silent Synthesis & Final Implementation**
1. **Voting & Synthesis:** The experts vote on the optimal approach
2. **Create Master Solution:** Synthesize the single best implementation, potentially combining elements
3. **Final Output:** Present only the final, synthesized "page context" solution with complete implementation and rationale

# NEW LEARNINGS

## 📋 **GENERAL DEBUGGING METHODOLOGY: "EVIDENCE-DRIVEN DIAGNOSIS"**

### **Phase 1: LISTEN (Don't Assume)**
1. **Take user descriptions literally** - they often give you the exact clue
2. **Ask clarifying questions** if symptoms are unclear
3. **Resist the urge to immediately propose solutions**

### **Phase 2: GATHER EVIDENCE (Don't Guess)**
1. **What exactly is working?**
2. **What exactly is broken?**
3. **What are the exact differences between working/broken?**
4. **When did it start happening?**

### **Phase 3: SIMPLE HYPOTHESIS (Don't Overcomplicate)**
1. **Start with the most obvious explanation**
2. **Test one variable at a time**
3. **Use existing working examples as reference**
4. **Occam's Razor**: Simplest explanation is usually correct

### **Phase 4: VALIDATE (Don't Pile On)**
1. **Does the simple fix actually work?**
2. **If not, what new evidence does the failure give us?**
3. **Step back and reassess, don't add more complexity**

---

## 🎯 **THE CORE PRINCIPLE**

> **"Understand the problem completely before proposing any solution"**

### **Questions I Should Ask Myself:**
1. **"What exactly is the user experiencing?"** (not what I think they should be experiencing)
2. **"What's the simplest explanation for this behavior?"** (not the most technically sophisticated)
3. **"What evidence contradicts my current hypothesis?"** (not what confirms it)
4. **"Am I adding complexity or removing it?"** (prefer removing)

## 📋 **METADATA & SEO STRATEGY (2025 STANDARDS)**

### **🎯 OFFICIAL METADATA STRATEGY DECISION**

Based on comprehensive expert analysis, this follows a **Client-Side Metadata Management Strategy** optimized for interactive SaaS applications.

#### **CORE PRINCIPLES**
1. **SaaS-First Approach**: Optimized for logged-in users, not search engine discovery
2. **Architectural Consistency**: Standardized client-side approach for maintainability
3. **Performance Priority**: Real-time interactions and DnD take precedence over SEO
4. **Professional Polish**: Clean tab titles and context without complexity overhead
5. **Development Velocity**: Minimal viable metadata approach maximizes feature development time

#### **WHEN TO USE CLIENT VS SERVER COMPONENTS**

```typescript
// ✅ CLIENT COMPONENTS (Default for app pages)
// Use for: Interactive dashboards, DnD interfaces, real-time updates
'use client'
import { usePageMetadata } from '@/hooks/use-page-metadata'

export default function PipelinePage() {
  usePageMetadata('Sales Pipeline', 'Manage prospects through conversion stages')

  // Your interactive logic here
  return <PipelineBoard />
}

// ✅ SERVER COMPONENTS (Only for static/marketing pages)
// Use for: Landing pages, pricing, documentation
export const metadata = {
  title: 'VigiLens - Autonomous Regulatory Compliance Platform',
  description: 'Autonomous AI platform for pharmaceutical regulatory compliance. Monitor guidance documents, track changes, and ensure continuous compliance.',
  keywords: ['regulatory compliance', 'pharmaceutical AI', 'FDA monitoring', 'risk management'],
  openGraph: {
    title: 'VigiLens - Autonomous Regulatory Compliance Platform',
    description: 'Autonomous AI platform for pharmaceutical regulatory compliance',
    images: ['/og-image.jpg'],
    url: 'https://yourapp.com'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VigiLens - Autonomous Regulatory Compliance Platform',
    description: 'Autonomous AI platform for pharmaceutical regulatory compliance'
  }
}

export default function LandingPage() {
  return <MarketingContent />
}
```

#### **METADATA IMPLEMENTATION PATTERNS**

##### **1. Centralized Metadata Hook**
```typescript
// hooks/use-page-metadata.ts
import { useEffect } from 'react'

interface PageMetadataOptions {
  title: string
  description?: string
  robots?: 'noindex' | 'index'
}

export function usePageMetadata(title: string, description?: string, options?: PageMetadataOptions) {
  useEffect(() => {
    // Update document title
    document.title = `${title} | Vigilens`

    // Update meta description if provided
    if (description) {
      let metaDescription = document.querySelector('meta[name="description"]')
      if (!metaDescription) {
        metaDescription = document.createElement('meta')
        metaDescription.setAttribute('name', 'description')
        document.head.appendChild(metaDescription)
      }
      metaDescription.setAttribute('content', description)
    }

    // Handle robots meta for internal pages
    if (options?.robots === 'noindex') {
      let robotsMeta = document.querySelector('meta[name="robots"]')
      if (!robotsMeta) {
        robotsMeta = document.createElement('meta')
        robotsMeta.setAttribute('name', 'robots')
        document.head.appendChild(robotsMeta)
      }
      robotsMeta.setAttribute('content', 'noindex, nofollow')
    }
  }, [title, description, options])
}
```

##### **2. Dynamic Context-Aware Metadata**
```typescript
// For user-specific or data-driven pages
export default function ContactDetailPage({ params }: { params: { id: string } }) {
  const { contact, isLoading } = useContactData(params.id)

  // Dynamic metadata based on data
  usePageMetadata(
    contact ? `${contact.firstName} ${contact.lastName}` : 'Contact Details',
    contact ? `Contact details for ${contact.firstName} - ${contact.status}` : undefined,
    { robots: 'noindex' } // Internal data shouldn't be indexed
  )

  if (isLoading) return <ContactSkeleton />
  return <ContactDetail contact={contact} />
}
```

##### **3. Layout-Level Default Metadata**
```typescript
// app/layout.tsx - Global defaults
export const metadata = {
  title: {
    template: '%s | Vigilens',
    default: 'Vigilens - Agentic AI Regulatory Platform'
  },
  description: 'AI Agent for pharmaceutical companies and CMOs',
  robots: { index: false, follow: false }, // Default: don't index app pages
  viewport: 'width=device-width, initial-scale=1',
}
```

#### **SEO STRATEGY FOR SAAS APPLICATIONS**

##### **PAGES THAT NEED SEO (Server Components + Full Metadata)**
- ✅ Landing/homepage
- ✅ Pricing page
- ✅ Features page
- ✅ Documentation/help
- ✅ Blog/content marketing
- ✅ Legal pages (privacy, terms)

##### **PAGES THAT DON'T NEED SEO (Client Components + Basic Metadata)**
- ❌ User dashboards
- ❌ Contact management
- ❌ Pipeline interfaces
- ❌ Analytics/reports
- ❌ Settings/configuration
- ❌ Real-time collaboration tools

##### **METADATA PRIORITIES BY PAGE TYPE**
```typescript
// Priority 1: Marketing Pages (Full SEO)
export const metadata = {
  title: 'VigiLens - Autonomous Regulatory Compliance Platform',
  description: 'Autonomous AI platform for pharmaceutical regulatory compliance. Monitor guidance documents, track changes, and ensure continuous compliance.',
  keywords: ['regulatory compliance', 'pharmaceutical AI', 'FDA monitoring', 'risk management'],
  openGraph: {
    title: 'VigiLens - Autonomous Regulatory Compliance Platform',
    description: 'Autonomous AI platform for pharmaceutical regulatory compliance',
    images: ['/og-image.jpg'],
    url: 'https://yourapp.com'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VigiLens - Autonomous Regulatory Compliance Platform',
    description: 'Autonomous AI platform for pharmaceutical regulatory compliance'
  }
}

// Priority 2: App Pages (Basic Professional Metadata)
usePageMetadata('Dashboard', 'Your sales and team performance overview')

// Priority 3: Internal Tools (Minimal Metadata)
usePageMetadata('Settings') // Just title, no description needed
```

#### **IMPLEMENTATION GUIDELINES**

##### **DO's ✅**
- Use `usePageMetadata` hook for all client-side pages
- Keep metadata simple and context-focused
- Add `robots: 'noindex'` for internal/user-specific data
- Focus development time on core functionality
- Use Server Components only for marketing/public pages
- Maintain consistent patterns across the team

##### **DON'Ts ❌**
- Don't mix Server/Client component patterns unnecessarily
- Don't over-optimize SEO for internal dashboard pages
- Don't use `generateMetadata` in Client Components
- Don't create complex hybrid rendering just for metadata
- Don't spend excessive time on SEO for authenticated pages
- Don't force Server Components for interactive features

##### **QUALITY GATES**
```typescript
// ✅ PASS - Client component with proper metadata
'use client'
export default function TasksPage() {
  usePageMetadata('Tasks', 'Manage your daily tasks and follow-ups')
  return <TaskList />
}

// ❌ FAIL - Client component trying to use generateMetadata
'use client'
export const metadata = { title: 'Tasks' } // Won't work!
export default function TasksPage() {
  return <TaskList />
}

// ✅ PASS - Server component for marketing
export const metadata = {
  title: 'Pricing - VigiLens',
  description: 'Affordable compliance plans for teams of all sizes'
}
export default function PricingPage() {
  return <PricingContent />
}
```

#### **MIGRATION STRATEGY**

##### **Phase 1: Standardize Existing Pages**
1. Convert mixed pattern pages to consistent client-side approach
2. Implement `usePageMetadata` hook
3. Update all interactive pages to use client-side metadata

##### **Phase 2: Optimize Marketing Pages**
1. Identify public-facing pages that need SEO
2. Implement full Server Component + metadata strategy for marketing pages
3. Keep client-side approach for all app functionality

##### **Phase 3: Team Training & Documentation**
1. Document patterns and examples
2. Add ESLint rules to prevent mixing patterns
3. Create templates for common page types

### **COMPONENT ARCHITECTURE INTEGRATION**

This metadata strategy integrates with our existing component architecture:

```typescript
// ✅ RECOMMENDED: Page Structure
'use client'
import { PageHeader } from '@/components/page-header'
import { usePageMetadata } from '@/hooks/use-page-metadata'

export default function FeaturePage() {
  usePageMetadata('Feature Name', 'Feature description for context')

  return (
    <div className="space-y-6">
      <PageHeader
        title="Feature Name"
        description="Manage your feature functionality"
        actions={<FeatureActions />}
      />
      <FeatureContent />
    </div>
  )
}
```

## 🎯 **ENHANCED DEVELOPMENT PROTOCOL**

### **INTEGRATION WITH METADATA STRATEGY**

All development phases now incorporate the official metadata strategy:

1. **UI/UX Design**: Consider metadata requirements during wireframe creation
2. **Frontend Implementation**: Apply client-side metadata patterns consistently
3. **Code Quality**: Validate metadata implementation in quality gates
4. **Architecture Review**: Ensure metadata strategy aligns with component organization

### **UPDATED QUALITY GATES**

```bash
# ✅ Enhanced validation includes metadata compliance
npm run verify:all           # Lint + test + metadata pattern validation
npm run type-check:all       # TypeScript validation
npm run lint                 # ESLint checks (now includes metadata patterns)
```

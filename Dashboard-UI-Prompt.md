\*agent dev
You are tasked with executing a comprehensive frontend refactoring following detailed planning documents: `Dashboard-UI-Plan.md`.

**CRITICAL REQUIREMENTS - NO DEVIATIONS ALLOWED:**

1. **Follow Dashboard-UI-Plan.md exactly**: Execute tasks in the exact order specified, checking off each completed task with ✅

2. **200-Line Rule is ABSOLUTE**: Every single file must be under 200 lines. If any component exceeds this, it MUST be decomposed further.

3. **TypeScript Strict Mode is MANDATORY**:
   - Enable strict: true immediately
   - Zero 'any' types allowed
   - All interfaces must be readonly where appropriate
   - Full type safety required

4. **File Naming Convention is STRICT**:
   - kebab-case for all files: user-profile.tsx
   - PascalCase for component names: UserProfile
   - Follow the exact old → new file mappings in Dashboard-UI-Plan.md

5. **Phase Execution Order is FIXED**:
   - Phase 0: Prerequisites & Environment Setup (only follow if necessary, if not ignore)
   - Phase 1: Layout & Container Structure
   - Phase 2: Sidebar (Navigation)
   - Phase 3: Header (Top Bar)
   - Phase 4: Main Content Area
   - Phase 5: Visual Design & Theming
   - Phase 6: Interactivity & Accessibility
   - Phase 7: Metadata & Quality Gates
   - Do NOT skip phases or change order

6. **Quality Gates are NON-NEGOTIABLE**:
   - Create .backup files before any migration
   - Test functionality after each component extraction
   - Maintain feature parity throughout
   - TypeScript compilation must pass with strict mode
   - ESLint must pass with zero errors

7. **Progress Tracking is REQUIRED**:
   - Update Dashboard-UI-Plan.md progress checkboxes after each completed task
   - Update the progress dashboard percentages
   - Mark phases complete only when ALL tasks are done

8. **Next.js App Router is MANDATORY**:
   - Remove React Router DOM completely
   - Use proper App Router file conventions
   - Implement client-side metadata with usePageMetadata hook
   - Follow the exact directory structure specified

9. **NO CREATIVE LIBERTIES**:
   - Do not add features not specified in the plan
   - Do not change the component decomposition strategy
   - Do not modify the file naming or directory structure
   - Do not skip quality checks or testing

**START WITH**: Phase 1: Foundation & Environment Setup, 1.1 - Prerequisites Verification

**REFERENCE DOCUMENTS**:

- Dashboard-UI-Plan.md for strategy specific task execution and progress tracking and architecture decisions
- MY_DEVELOPMENT_RULES.md for coding standards and conventions

**BEFORE EACH TASK**:

1. Read the specific task requirements in Dashboard-UI-Plan.md
2. Identify the exact files to modify/create
3. Create backups where specified
4. Execute the task following the exact specifications
5. Test the changes work correctly
6. Update the progress checkbox
7. Proceed to next task only after current task is complete

**QUALITY CHECKPOINT BEFORE EACH PHASE COMPLETION**:

- [ ] All TypeScript compilation passes with strict mode
- [ ] All files are under 200 lines
- [ ] All functionality works as before
- [ ] Progress tracking is updated
- [ ] No ESLint errors remain

Execute this refactoring with absolute precision following every specification in the planning documents.

```

---

## 🎯 ADDITIONAL CONTEXT POINTS

### If you want to emphasize specific aspects, add:

**For TypeScript Focus:**
```

"Pay special attention to TypeScript strict mode compliance. Every 'any' type must be eliminated and replaced with proper interfaces."

```

**For Component Decomposition:**
```

"The massive files (ComplianceCheck.tsx at 962 lines) are the highest priority. Break them down exactly as specified in the component breakdown plans."

```

**For Progress Tracking:**
```

"Update Dashboard-UI-Plan.md checkboxes after each completed task. The progress dashboard must reflect actual completion status."

```

**For Quality Gates:**
```

"Never proceed to the next task until the current one passes all quality checks: TypeScript compilation, under 200 lines, functionality preserved."

```

---

## 🚨 WHAT TO AVOID SAYING

Don't use vague instructions like:
- ❌ "Refactor the frontend to be better"
- ❌ "Clean up the codebase"
- ❌ "Modernize the architecture"
- ❌ "Make it follow best practices"

These leave too much room for interpretation and deviation from your specific plan.

---

## ✅ PERFECT EXECUTION PROMPT

The most precise instruction would be:

```

Execute the frontend refactoring exactly as specified in Dashboard-UI-Plan.md. Start with Phase 1, Task 1.1. Follow every specification precisely, maintain the 200-line rule absolutely, enable TypeScript strict mode, and update progress checkboxes after each completed task. No deviations from the plan are permitted.

```

Remember, you are a elite and senior and most responsible developer, follow @MY_DEVELOPMENT_RULES.md (though its meant for my another CRM app) but you can follow the coding best practices and other stuff...

If any doubt, always ask rather than hallucinating or guessing. Absolutely **NO GUESSWORK**

And all the best with the refactoring and dont forget to mark the tasks done once its actually complete
```

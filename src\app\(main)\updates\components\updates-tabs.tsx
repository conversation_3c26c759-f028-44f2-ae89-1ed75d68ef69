'use client'

import { Badge } from '@/components/ui-radix/badge'
import {
    <PERSON><PERSON>,
    <PERSON>bsContent,
    <PERSON>bsList,
    TabsTrigger,
} from '@/components/ui-radix/tabs'

import { UpdateCard } from './update-card'

import type { RegulatoryUpdate, TabValue } from '../types'

interface UpdatesTabsProps {
  readonly updates: readonly RegulatoryUpdate[];
  readonly bookmarkedUpdates: readonly number[];
  readonly activeTab: TabValue;
  readonly onTabChange: (tab: TabValue) => void;
  readonly onBookmark: (updateId: number) => void;
  readonly onShare: (update: RegulatoryUpdate) => void;
  readonly onView: (update: RegulatoryUpdate) => void;
}

export function UpdatesTabs({
  updates,
  bookmarkedUpdates,
  activeTab,
  onTabChange,
  onBookmark,
  onShare,
  onView,
}: UpdatesTabsProps) {
  const criticalCount = updates.filter((u) => u.severity === 'critical').length
  const criticalUpdates = updates.filter((u) => u.severity === 'critical')
  const actionRequiredUpdates = updates.filter(
    (u) =>
      u.severity === 'critical' ||
      (u.deadline && new Date(u.deadline) < new Date()),
  )
  const bookmarkedUpdatesList = updates.filter((u) =>
    bookmarkedUpdates.includes(u.id),
  )

  const isBookmarked = (updateId: number) =>
    bookmarkedUpdates.includes(updateId)

  return (
    <Tabs
      value={activeTab}
      onValueChange={(value) => onTabChange(value as TabValue)}
      className="space-y-4"
    >
      <TabsList>
        <TabsTrigger value="all">All Updates</TabsTrigger>
        <TabsTrigger value="critical" className="flex items-center">
          Critical
          <Badge
            variant="destructive"
            className="ml-2 h-5 w-5 rounded-full text-xs flex items-center justify-center leading-none p-0 min-w-[20px]"
          >
            {criticalCount}
          </Badge>
        </TabsTrigger>
        <TabsTrigger value="action-required">Action Required</TabsTrigger>
        <TabsTrigger value="bookmarked">Bookmarked</TabsTrigger>
      </TabsList>

      <TabsContent value="all" className="space-y-4">
        {updates.map((update) => (
          <UpdateCard
            key={update.id}
            update={update}
            isBookmarked={isBookmarked(update.id)}
            onBookmark={onBookmark}
            onShare={onShare}
            onView={onView}
          />
        ))}
      </TabsContent>

      <TabsContent value="critical" className="space-y-4">
        {criticalUpdates.map((update) => (
          <UpdateCard
            key={update.id}
            update={update}
            isBookmarked={isBookmarked(update.id)}
            onBookmark={onBookmark}
            onShare={onShare}
            onView={onView}
          />
        ))}
      </TabsContent>

      <TabsContent value="action-required" className="space-y-4">
        {actionRequiredUpdates.map((update) => (
          <UpdateCard
            key={update.id}
            update={update}
            isBookmarked={isBookmarked(update.id)}
            onBookmark={onBookmark}
            onShare={onShare}
            onView={onView}
          />
        ))}
      </TabsContent>

      <TabsContent value="bookmarked" className="space-y-4">
        {bookmarkedUpdatesList.length > 0 ? (
          bookmarkedUpdatesList.map((update) => (
            <UpdateCard
              key={update.id}
              update={update}
              isBookmarked={isBookmarked(update.id)}
              onBookmark={onBookmark}
              onShare={onShare}
              onView={onView}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No bookmarked updates yet.</p>
          </div>
        )}
      </TabsContent>
    </Tabs>
  )
}

# CSS & Theming Rules

> Last updated: July 2025

These conventions keep our design system predictable, accessible, and easy to update.

## 1 – Dark-/Light-Mode Strategy

1. Use `next-themes` (`ThemeProvider attribute="class"`) so the current theme is expressed by a single `class="dark"` on `<html>`.
2. Tailwind **must** stay on `darkMode: 'class'`.
3. Never hard-code colors. Always reference:
   * Tailwind semantic utilities (`bg-card`, `text-foreground`, `border-border`, …).
   * Or CSS custom props declared in `src/index.css` (e.g. `var(--color-primary)`).
4. Any component that depends on the theme in JS should gate its UI behind the mounted check:
```tsx
const { resolvedTheme } = useTheme();
const [mounted,setMounted]=useState(false);
useEffect(()=>setMounted(true),[]);
if(!mounted) return null;
```

## 2 – Semantic Color Tokens

* Root palette lives in `:root { … }` and `.dark { … }` within `src/index.css`.
* Each logical color has **foreground** and **background** variants.
* Expose token → Tailwind via `@theme inline { --color-… }` so we can use `bg-card`, `text-foreground`, etc.
* When you need opacity, use slash notation: `bg-card/70`.

## 3 – Component Classes

| Purpose            | Utility Example                |
|--------------------|--------------------------------|
| Card surface       | `bg-card`                      |
| Card border        | `border-border`                |
| Body text          | `text-foreground`              |
| Muted text         | `text-muted-foreground`        |
| Accent text/bg     | `text-primary` / `bg-primary`  |
| Sidebar surface    | `bg-sidebar`                   |

## 4 – Transparency & Backdrop

* Frosted panels: `bg-card/60 backdrop-blur-sm` (+ dark override if needed).
* Don't blur on mobile unless necessary (perf).

## 5 – Shadow & Radius

* Shadow aliases live in CSS vars (`--shadow-sm` …). Use helper classes:
  * Light: `shadow-sm`
  * Elevation hover: `hover:shadow-lg transition-shadow`.
* Radii come from `--radius-…`; stick to `rounded-xl` / `rounded-lg` unless design specifies.

## 6 – Spacing & Container

* Use Tailwind spacing scale (`p-6`, `gap-4`).
* Global container padding is set in `tailwind.config.ts` (`2rem`). Keep custom padding consistent.

## 7 – Custom Utilities

* Custom utilities live under `@layer utilities` in `src/index.css`.
* Prefix with domain (`chart-grid-visible`, `card-subtle`).

## 8 – Accessibility

* Always ensure `text-foreground` vs background has AA contrast (>4.5:1 for normal text).
* Don't rely solely on color; add icons/labels.

## 9 – Testing Colors

* Storybook or visual regression tests: verify both themes.
* Add Vitest snapshot for ThemeToggle to ensure `.dark` class flips.

## 10 – Common Pitfalls

1. **Hard-coded `bg-white` / `text-black`** – breaks dark mode.
2. Forgetting to add dark token override inside `.dark` block.
3. Using numeric Tailwind colors (`text-gray-500`) – use semantic token instead.
4. Forgetting `suppressHydrationWarning` on `<html>`/`<body>` in root layout.

Keep these rules and the palette in mind whenever touching CSS or Tailwind classes. This guarantees theme consistency and future-proofs style changes.


Always put these before editing the CSS:
"Make sure you first @CSS-Rules.md   understand the rules and then make implementations"

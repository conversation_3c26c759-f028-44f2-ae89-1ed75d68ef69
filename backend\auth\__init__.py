"""
VigiLens Authentication Module

VCP_002: Authentication & Authorization System
Implements Supabase Auth 2025 with FastAPI integration for pharmaceutical compliance.

Following DEVELOPMENT_RULES.md and DEVELOPMENT_RULES_2.md:
- Production-first mindset
- TypeScript-like strict validation
- Pharmaceutical compliance (21 CFR Part 11)
- No console logging in production
- Comprehensive error handling
"""

from .dependencies import (
    get_current_user,
    get_current_active_user,
    require_role,
    require_permissions,
    require_mfa,
    require_mfa_verification,
    SupabaseJWTBearer,
)
from .models import (
    AuthUser,
    UserRole,
    AuthToken,
    MFAChallenge,
    AuthResponse,
)
from .rbac import (
    <PERSON><PERSON>hecker,
    PermissionChecker,
    PharmaceuticalPermissions,
)

__all__ = [
    # Dependencies
    "get_current_user",
    "get_current_active_user",
    "require_role",
    "require_permissions",
    "require_mfa",
    "require_mfa_verification",
    "SupabaseJWTBearer",
    # Models
    "AuthUser",
    "UserRole",
    "AuthToken",
    "MFAChallenge",
    "AuthResponse",
    # RBAC
    "RoleChecker",
    "PermissionChecker",
    "PharmaceuticalPermissions",
]

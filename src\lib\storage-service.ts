/**
 * Supabase Storage service for VigiLens
 * Direct file upload replacing Python backend wrapper
 * Following DEVELOPMENT_RULES_2.md and July 2025 Supabase patterns
 */

import { createClient } from '@/utils/supabase/client'
// StorageError is not exported in newer versions, define our own
interface StorageError {
  message: string
  name: string
}

// Strict TypeScript interfaces following development rules
export interface UploadOptions {
  cacheControl?: string
  contentType?: string
  upsert?: boolean
  metadata?: Record<string, any>
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface UploadResponse {
  data: {
    id: string
    path: string
    fullPath: string
    publicUrl?: string
  } | null
  error: StorageError | null
}

export interface FileMetadata {
  id: string
  name: string
  bucket_id: string
  owner: string
  created_at: string
  updated_at: string
  last_accessed_at: string
  metadata: Record<string, any>
}

export interface ListFilesOptions {
  limit?: number
  offset?: number
  sortBy?: {
    column: 'name' | 'created_at' | 'updated_at'
    order: 'asc' | 'desc'
  }
  search?: string
}

/**
 * Client-side storage service
 * Use this in Client Components for file operations
 */
export class StorageService {
  private supabase: ReturnType<typeof createClient>
  private bucketName: string

  constructor(bucketName = 'documents') {
    this.supabase = createClient()
    this.bucketName = bucketName
  }

  /**
   * Upload a file with progress tracking
   */
  async uploadFile(
    file: File,
    path: string,
    options: UploadOptions = {},
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResponse> {
    try {
      // Input validation
      if (!file || file.size === 0) {
        return {
          data: null,
          error: { message: 'Invalid file provided', name: 'ValidationError' } as StorageError
        }
      }

      // File size validation (6MB limit for standard upload)
      const maxSize = 6 * 1024 * 1024 // 6MB
      if (file.size > maxSize) {
        return {
          data: null,
          error: { message: 'File size exceeds 6MB limit. Use resumable upload for larger files.', name: 'FileSizeError' } as StorageError
        }
      }

      // Sanitize file path
      const sanitizedPath = this.sanitizePath(path)

      // Set default content type if not provided
      const uploadOptions: UploadOptions = {
        contentType: file.type || 'application/octet-stream',
        ...options
      }

      // Simulate progress tracking for standard uploads
      if (onProgress) {
        onProgress({ loaded: 0, total: file.size, percentage: 0 })
      }

      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .upload(sanitizedPath, file, uploadOptions)

      if (onProgress) {
        onProgress({ loaded: file.size, total: file.size, percentage: 100 })
      }

      if (error) {
        return { data: null, error }
      }

      // Get public URL if upload successful
      const { data: { publicUrl } } = this.supabase.storage
        .from(this.bucketName)
        .getPublicUrl(sanitizedPath)

      return {
        data: {
          id: data.id || '',
          path: data.path,
          fullPath: data.fullPath,
          publicUrl
        },
        error: null
      }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Download a file
   */
  async downloadFile(path: string): Promise<{ data: Blob | null; error: StorageError | null }> {
    try {
      const sanitizedPath = this.sanitizePath(path)
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .download(sanitizedPath)

      return { data, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Delete a file
   */
  async deleteFile(path: string): Promise<{ data: any; error: StorageError | null }> {
    try {
      const sanitizedPath = this.sanitizePath(path)
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .remove([sanitizedPath])

      return { data, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * List files in a directory
   */
  async listFiles(
    path = '',
    options: ListFilesOptions = {}
  ): Promise<{ data: FileMetadata[] | null; error: StorageError | null }> {
    try {
      const sanitizedPath = this.sanitizePath(path)
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .list(sanitizedPath, {
          ...(options.limit !== undefined && { limit: options.limit }),
          ...(options.offset !== undefined && { offset: options.offset }),
          ...(options.sortBy !== undefined && { sortBy: options.sortBy }),
          ...(options.search !== undefined && { search: options.search })
        })

      return { data: data as FileMetadata[] | null, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Get public URL for a file
   */
  getPublicUrl(path: string): string {
    const sanitizedPath = this.sanitizePath(path)
    const { data } = this.supabase.storage
      .from(this.bucketName)
      .getPublicUrl(sanitizedPath)

    return data.publicUrl
  }

  /**
   * Create a signed URL for private file access
   */
  async createSignedUrl(
    path: string,
    expiresIn = 3600
  ): Promise<{ data: { signedUrl: string } | null; error: StorageError | null }> {
    try {
      const sanitizedPath = this.sanitizePath(path)
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .createSignedUrl(sanitizedPath, expiresIn)

      return { data, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Move a file to a new location
   */
  async moveFile(
    fromPath: string,
    toPath: string
  ): Promise<{ data: any; error: StorageError | null }> {
    try {
      const sanitizedFromPath = this.sanitizePath(fromPath)
      const sanitizedToPath = this.sanitizePath(toPath)

      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .move(sanitizedFromPath, sanitizedToPath)

      return { data, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Copy a file to a new location
   */
  async copyFile(
    fromPath: string,
    toPath: string
  ): Promise<{ data: any; error: StorageError | null }> {
    try {
      const sanitizedFromPath = this.sanitizePath(fromPath)
      const sanitizedToPath = this.sanitizePath(toPath)

      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .copy(sanitizedFromPath, sanitizedToPath)

      return { data, error }
    } catch (error) {
      return {
        data: null,
        error: error as StorageError
      }
    }
  }

  /**
   * Sanitize file path to prevent directory traversal
   * Following DEVELOPMENT_RULES_2.md security requirements
   */
  private sanitizePath(path: string): string {
    // Remove leading/trailing slashes and normalize
    let sanitized = path.trim().replace(/^\/+|\/+$/g, '')

    // Remove any directory traversal attempts
    sanitized = sanitized.replace(/\.\./g, '')

    // Replace multiple slashes with single slash
    sanitized = sanitized.replace(/\/+/g, '/')

    // Remove any null bytes or control characters
    sanitized = sanitized.replace(/[\x00-\x1f\x7f]/g, '')

    return sanitized
  }
}

// Export singleton instances for convenience
export const storageService = new StorageService()

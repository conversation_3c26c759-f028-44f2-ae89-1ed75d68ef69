-- VigiLens Database Schema - Migration 007
-- Create Audit Trail System - 21 CFR Part 11 Compliant Logging
-- Comprehensive audit logging for pharmaceutical compliance

-- Audit trail table - 21 CFR Part 11 compliant audit logging
CREATE TABLE audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Organization and user context
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id), -- NULL for system events
    
    -- Action details
    action_type audit_action_type NOT NULL,
    action_description TEXT NOT NULL,
    action_category VARCHAR(100) NOT NULL, -- 'user_action', 'system_event', 'security_event', 'compliance_event'
    
    -- Resource information
    resource_type VARCHAR(100) NOT NULL, -- 'document', 'user', 'organization', 'system'
    resource_id UUID,
    resource_name VARCHAR(255),
    
    -- Change tracking (before/after values)
    old_values JSONB,
    new_values JSONB,
    change_summary TEXT,
    
    -- Session and security context
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255), -- For correlating related actions
    
    -- 21 CFR Part 11 compliance fields
    electronic_signature JSONB, -- Electronic signature metadata if applicable
    data_integrity_hash VARCHAR(64), -- SHA-256 hash for tamper detection
    compliance_metadata JSONB DEFAULT '{
        "gxp_relevant": false,
        "regulatory_impact": "low",
        "retention_period_years": 7,
        "archive_eligible": false
    }'::JSONB,
    
    -- Risk and severity assessment
    risk_level risk_level DEFAULT 'low',
    severity VARCHAR(50) DEFAULT 'info', -- 'debug', 'info', 'warning', 'error', 'critical'
    
    -- Timestamp (immutable for compliance)
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Additional context and metadata
    context JSONB DEFAULT '{}'::JSONB,
    correlation_id VARCHAR(255), -- For grouping related audit events
    
    -- Constraints
    CONSTRAINT audit_trail_valid_timestamp CHECK (timestamp <= NOW()),
    CONSTRAINT audit_trail_resource_consistency CHECK (
        (resource_id IS NULL AND resource_name IS NULL) OR
        (resource_id IS NOT NULL OR resource_name IS NOT NULL)
    )
);

-- Create immutable timestamp function to prevent tampering
CREATE OR REPLACE FUNCTION set_immutable_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent modification of timestamp after creation
    IF TG_OP = 'UPDATE' AND OLD.timestamp != NEW.timestamp THEN
        RAISE EXCEPTION 'Audit trail timestamps cannot be modified for compliance reasons';
    END IF;
    
    -- Set data integrity hash
    NEW.data_integrity_hash := encode(
        digest(
            COALESCE(NEW.user_id::text, '') || 
            NEW.action_type::text || 
            NEW.resource_type || 
            COALESCE(NEW.resource_id::text, '') ||
            NEW.timestamp::text ||
            COALESCE(NEW.old_values::text, '') ||
            COALESCE(NEW.new_values::text, ''),
            'sha256'
        ),
        'hex'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for immutable timestamp and integrity hash
CREATE TRIGGER audit_trail_immutable_timestamp
    BEFORE INSERT OR UPDATE ON audit_trail
    FOR EACH ROW
    EXECUTE FUNCTION set_immutable_timestamp();

-- Create comprehensive indexes for audit queries
CREATE INDEX idx_audit_trail_organization_id ON audit_trail(organization_id);
CREATE INDEX idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_timestamp ON audit_trail(timestamp);
CREATE INDEX idx_audit_trail_action_type ON audit_trail(action_type);
CREATE INDEX idx_audit_trail_resource ON audit_trail(resource_type, resource_id);
CREATE INDEX idx_audit_trail_session_id ON audit_trail(session_id);
CREATE INDEX idx_audit_trail_correlation_id ON audit_trail(correlation_id);
CREATE INDEX idx_audit_trail_risk_level ON audit_trail(risk_level);
CREATE INDEX idx_audit_trail_severity ON audit_trail(severity);

-- Composite indexes for common audit queries
CREATE INDEX idx_audit_trail_org_timestamp ON audit_trail(organization_id, timestamp);
CREATE INDEX idx_audit_trail_user_timestamp ON audit_trail(user_id, timestamp);
CREATE INDEX idx_audit_trail_resource_timestamp ON audit_trail(resource_type, resource_id, timestamp);

-- GIN indexes for JSONB columns
CREATE INDEX idx_audit_trail_old_values ON audit_trail USING GIN(old_values);
CREATE INDEX idx_audit_trail_new_values ON audit_trail USING GIN(new_values);
CREATE INDEX idx_audit_trail_context ON audit_trail USING GIN(context);
CREATE INDEX idx_audit_trail_compliance_metadata ON audit_trail USING GIN(compliance_metadata);

-- Partial indexes for high-priority events
CREATE INDEX idx_audit_trail_critical_events ON audit_trail(organization_id, timestamp) 
    WHERE severity IN ('error', 'critical') OR risk_level IN ('high', 'critical');

CREATE INDEX idx_audit_trail_security_events ON audit_trail(organization_id, timestamp)
    WHERE action_category = 'security_event';

-- Enable Row Level Security
ALTER TABLE audit_trail ENABLE ROW LEVEL SECURITY;

-- RLS Policies for audit_trail

-- Policy: Users can view audit trail for their organization
CREATE POLICY "Users can view org audit trail" ON audit_trail
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (
            -- Admins and auditors can see all audit records
            (auth.jwt() ->> 'role') IN ('admin', 'auditor', 'compliance_officer') OR
            -- Users can see their own actions
            user_id = auth.uid()
        )
    );

-- Policy: Only system can insert audit records
CREATE POLICY "System audit logging only" ON audit_trail
    FOR INSERT WITH CHECK (true); -- Allow system to log all events

-- Policy: Audit records are immutable (no updates or deletes)
CREATE POLICY "Audit records are immutable" ON audit_trail
    FOR UPDATE USING (false);

CREATE POLICY "Audit records cannot be deleted" ON audit_trail
    FOR DELETE USING (false);

-- Create function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_organization_id UUID,
    p_user_id UUID,
    p_action_type audit_action_type,
    p_action_description TEXT,
    p_resource_type VARCHAR(100),
    p_resource_id UUID DEFAULT NULL,
    p_resource_name VARCHAR(255) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_context JSONB DEFAULT '{}'::JSONB
)
RETURNS UUID AS $$
DECLARE
    audit_id UUID;
    calculated_risk risk_level;
    calculated_severity VARCHAR(50);
BEGIN
    -- Calculate risk level based on action type
    calculated_risk := CASE 
        WHEN p_action_type IN ('delete', 'approve', 'sign') THEN 'high'
        WHEN p_action_type IN ('update', 'create') THEN 'medium'
        ELSE 'low'
    END;
    
    -- Calculate severity based on resource type and action
    calculated_severity := CASE 
        WHEN p_resource_type = 'system' AND p_action_type = 'security_event' THEN 'critical'
        WHEN p_action_type IN ('delete', 'approve') THEN 'warning'
        WHEN p_action_type IN ('create', 'update') THEN 'info'
        ELSE 'info'
    END;
    
    -- Insert audit record
    INSERT INTO audit_trail (
        organization_id,
        user_id,
        action_type,
        action_description,
        action_category,
        resource_type,
        resource_id,
        resource_name,
        old_values,
        new_values,
        session_id,
        ip_address,
        user_agent,
        context,
        risk_level,
        severity,
        correlation_id
    ) VALUES (
        p_organization_id,
        p_user_id,
        p_action_type,
        p_action_description,
        CASE 
            WHEN p_user_id IS NULL THEN 'system_event'
            WHEN p_action_type IN ('login', 'logout') THEN 'security_event'
            ELSE 'user_action'
        END,
        p_resource_type,
        p_resource_id,
        p_resource_name,
        p_old_values,
        p_new_values,
        p_session_id,
        p_ip_address,
        p_user_agent,
        p_context,
        calculated_risk,
        calculated_severity,
        gen_random_uuid()::text
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to verify audit trail integrity
CREATE OR REPLACE FUNCTION verify_audit_integrity(
    start_date TIMESTAMPTZ DEFAULT NOW() - INTERVAL '24 hours',
    end_date TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    total_records BIGINT,
    integrity_violations BIGINT,
    verification_status TEXT
) AS $$
DECLARE
    total_count BIGINT;
    violation_count BIGINT;
BEGIN
    -- Count total records in date range
    SELECT COUNT(*) INTO total_count
    FROM audit_trail
    WHERE timestamp BETWEEN start_date AND end_date;
    
    -- Check for integrity violations
    SELECT COUNT(*) INTO violation_count
    FROM audit_trail
    WHERE timestamp BETWEEN start_date AND end_date
    AND data_integrity_hash != encode(
        digest(
            COALESCE(user_id::text, '') || 
            action_type::text || 
            resource_type || 
            COALESCE(resource_id::text, '') ||
            timestamp::text ||
            COALESCE(old_values::text, '') ||
            COALESCE(new_values::text, ''),
            'sha256'
        ),
        'hex'
    );
    
    RETURN QUERY SELECT 
        total_count,
        violation_count,
        CASE 
            WHEN violation_count = 0 THEN 'PASSED'
            ELSE 'FAILED'
        END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to generate audit reports
CREATE OR REPLACE FUNCTION generate_audit_report(
    p_organization_id UUID,
    p_start_date TIMESTAMPTZ,
    p_end_date TIMESTAMPTZ,
    p_user_id UUID DEFAULT NULL,
    p_resource_type VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE (
    action_type audit_action_type,
    action_count BIGINT,
    unique_users BIGINT,
    risk_distribution JSONB,
    timeline JSONB
) AS $$
BEGIN
    RETURN QUERY
    WITH audit_summary AS (
        SELECT 
            at.action_type,
            COUNT(*) as action_count,
            COUNT(DISTINCT at.user_id) as unique_users,
            jsonb_object_agg(at.risk_level, COUNT(*)) as risk_distribution,
            jsonb_object_agg(
                DATE_TRUNC('hour', at.timestamp)::text, 
                COUNT(*)
            ) as timeline
        FROM audit_trail at
        WHERE at.organization_id = p_organization_id
        AND at.timestamp BETWEEN p_start_date AND p_end_date
        AND (p_user_id IS NULL OR at.user_id = p_user_id)
        AND (p_resource_type IS NULL OR at.resource_type = p_resource_type)
        GROUP BY at.action_type
    )
    SELECT * FROM audit_summary
    ORDER BY action_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE audit_trail IS '21 CFR Part 11 compliant audit trail with tamper-proof logging';
COMMENT ON COLUMN audit_trail.data_integrity_hash IS 'SHA-256 hash for detecting tampering';
COMMENT ON COLUMN audit_trail.timestamp IS 'Immutable timestamp - cannot be modified after creation';
COMMENT ON FUNCTION log_audit_event IS 'Primary function for logging audit events with automatic risk assessment';
COMMENT ON FUNCTION verify_audit_integrity IS 'Verifies audit trail integrity by checking data hashes';
COMMENT ON FUNCTION generate_audit_report IS 'Generates comprehensive audit reports for compliance';

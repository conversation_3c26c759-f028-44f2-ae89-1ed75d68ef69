'use client'

import { Badge } from '@/components/ui-radix/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'
import { Checkbox } from '@/components/ui-radix/checkbox'

interface Framework {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly regulations: readonly string[];
  readonly color: string;
}

interface FrameworkSelectorProps {
  readonly selectedFrameworks: readonly string[];
  readonly onFrameworkToggle: (frameworkId: string) => void;
  readonly isCurrentStep: boolean;
}

export function FrameworkSelector({
  selectedFrameworks,
  onFrameworkToggle,
  isCurrentStep,
}: FrameworkSelectorProps) {
  const complianceFrameworks: readonly Framework[] = [
    {
      id: 'fda-cgmp',
      name: 'FDA cGMP',
      description: 'Current Good Manufacturing Practice',
      regulations: ['21 CFR 210', '21 CFR 211'],
      color: 'bg-blue-500',
    },
    {
      id: 'ich-q7',
      name: 'ICH Q7',
      description: 'Active Pharmaceutical Ingredients',
      regulations: ['ICH Q7'],
      color: 'bg-green-500',
    },
    {
      id: 'iso-13485',
      name: 'ISO 13485',
      description: 'Medical Devices Quality Management',
      regulations: ['ISO 13485:2016'],
      color: 'bg-purple-500',
    },
    {
      id: 'eu-gmp',
      name: 'EU GMP',
      description: 'European Good Manufacturing Practice',
      regulations: ['EudraLex Volume 4'],
      color: 'bg-orange-500',
    },
    {
      id: 'ich-q9',
      name: 'ICH Q9',
      description: 'Quality Risk Management',
      regulations: ['ICH Q9 R1'],
      color: 'bg-red-500',
    },
  ]

  if (!isCurrentStep) {
return null
}

  return (
    <Card>
      <CardHeader>
        <CardTitle>Select Compliance Frameworks</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {complianceFrameworks.map((framework) => (
            <div
              key={framework.id}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedFrameworks.includes(framework.id)
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              }`}
              onClick={() => onFrameworkToggle(framework.id)}
            >
              <div className="flex items-start space-x-3">
                <Checkbox
                  checked={selectedFrameworks.includes(framework.id)}
                  onChange={() => onFrameworkToggle(framework.id)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <div
                      className={`w-3 h-3 rounded-full ${framework.color}`}
                    />
                    <h4 className="font-medium">{framework.name}</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {framework.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {framework.regulations.map((reg) => (
                      <Badge key={reg} variant="secondary" className="text-xs">
                        {reg}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

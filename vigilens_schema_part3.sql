-- VigiLens Schema Part 3 - Functions, Triggers, and RLS Policies
-- Run this after vigilens_schema_part2.sql

-- =====================================================
-- CORE FUNCTIONS FOR PHARMACEUTICAL COMPLIANCE
-- =====================================================

-- Function to generate data integrity hash
CREATE OR REPLACE FUNCTION generate_data_integrity_hash(data JSONB)
RETURNS VARCHAR(128) AS $$
BEGIN
    RETURN encode(digest(data::text, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to log audit events with data integrity
CREATE OR REPLACE FUNCTION log_audit_event(
    p_organization_id UUID,
    p_user_id UUID,
    p_action_type audit_action_type,
    p_action_description TEXT,
    p_resource_type VARCHAR(100),
    p_resource_id UUID DEFAULT NULL,
    p_resource_name VARCHAR(500) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_context JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    audit_id UUID;
    audit_data JSONB;
    integrity_hash VARCHAR(128);
BEGIN
    -- Generate new audit ID
    audit_id := uuid_generate_v4();
    
    -- Build audit data for integrity hash
    audit_data := jsonb_build_object(
        'id', audit_id,
        'organization_id', p_organization_id,
        'user_id', p_user_id,
        'action_type', p_action_type,
        'action_description', p_action_description,
        'resource_type', p_resource_type,
        'resource_id', p_resource_id,
        'old_values', p_old_values,
        'new_values', p_new_values,
        'timestamp', NOW()
    );
    
    -- Generate integrity hash
    integrity_hash := generate_data_integrity_hash(audit_data);
    
    -- Insert audit record
    INSERT INTO audit_trail (
        id,
        organization_id,
        user_id,
        action_type,
        action_category,
        action_description,
        resource_type,
        resource_id,
        resource_name,
        old_values,
        new_values,
        context,
        data_integrity_hash,
        gxp_relevant,
        risk_level
    ) VALUES (
        audit_id,
        p_organization_id,
        p_user_id,
        p_action_type,
        p_resource_type,
        p_action_description,
        p_resource_type,
        p_resource_id,
        p_resource_name,
        p_old_values,
        p_new_values,
        p_context,
        integrity_hash,
        CASE WHEN p_resource_type IN ('document', 'signature', 'approval') THEN true ELSE false END,
        CASE WHEN p_action_type IN ('delete', 'approve', 'sign') THEN 'high' ELSE 'medium' END
    );
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Simple, reliable authentication trigger function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    demo_org_id UUID;
BEGIN
    -- Get or create demo organization
    SELECT id INTO demo_org_id 
    FROM organizations 
    WHERE name = 'demo-pharma-corp' 
    LIMIT 1;
    
    -- Create demo organization if it doesn't exist
    IF demo_org_id IS NULL THEN
        INSERT INTO organizations (
            name,
            display_name,
            description,
            company_type,
            primary_contact_email,
            compliance_frameworks,
            regulatory_agencies
        ) VALUES (
            'demo-pharma-corp',
            'Demo Pharmaceutical Corporation',
            'Demo organization for new user onboarding',
            'pharmaceutical',
            '<EMAIL>',
            ARRAY['fda_21_cfr_part_11', 'gxp']::compliance_framework[],
            ARRAY['fda']::regulatory_agency[]
        ) RETURNING id INTO demo_org_id;
    END IF;
    
    -- Create user profile
    INSERT INTO user_profiles (
        id,
        organization_id,
        email,
        full_name,
        role,
        is_active,
        email_verified,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        demo_org_id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        'viewer',
        true,
        COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
        NOW(),
        NOW()
    );
    
    -- Log user creation (simple version without complex dependencies)
    INSERT INTO audit_trail (
        organization_id,
        user_id,
        action_type,
        action_category,
        action_description,
        resource_type,
        resource_id,
        resource_name,
        new_values,
        gxp_relevant,
        risk_level
    ) VALUES (
        demo_org_id,
        NEW.id,
        'create',
        'user',
        'New user profile created automatically',
        'user',
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        jsonb_build_object(
            'registration_method', 'supabase_auth',
            'email_verified', COALESCE(NEW.email_confirmed_at IS NOT NULL, false)
        ),
        false,
        'low'
    );
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user trigger: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Verify functions created
SELECT 
    'FUNCTIONS CREATED' as status,
    COUNT(*) as function_count,
    array_agg(routine_name ORDER BY routine_name) as functions
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('generate_data_integrity_hash', 'log_audit_event', 'handle_new_user');

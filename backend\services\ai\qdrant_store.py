"""
Qdrant Vector Store Service

High-performance vector database service using Qdrant for FDA pharmaceutical compliance documents.
Provides efficient vector storage, similarity search, and metadata filtering for FDA regulations,
guidance documents, and CFR sections.

Features:
- Qdrant client integration with connection pooling
- Async operations with proper error handling
- Type safety with Pydantic validation
- Comprehensive logging and monitoring
- Efficient batch operations and FDA-specific filtering
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
import numpy as np
from pydantic import BaseModel, Field, field_validator
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

# Configure logging
logger = logging.getLogger(__name__)

class DocumentMetadata(BaseModel):
    """Metadata model for FDA pharmaceutical compliance documents."""
    source: str = Field(..., description="FDA document source identifier")
    title: Optional[str] = Field(None, description="Document title (auto-generated if not provided)")
    content_type: str = Field(default="text", description="Content type")
    chunk_index: int = Field(default=0, ge=0, description="Chunk index for large documents")
    total_chunks: int = Field(default=1, ge=1, description="Total number of chunks")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Creation timestamp")
    regulatory_framework: str = Field(default="FDA", description="FDA regulatory framework")
    document_type: Optional[str] = Field(None, description="FDA document type (guidance, regulation, CFR, etc.)")

    @field_validator('source')
    @classmethod
    def validate_source(cls, v):
        """Validate that source is not empty."""
        if not v or not v.strip():
            raise ValueError("Source field cannot be empty")
        return v.strip()

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        """Validate title if provided."""
        if v is not None and not v.strip():
            raise ValueError("Title cannot be empty string if provided")
        return v.strip() if v else None

class VectorSearchRequest(BaseModel):
    """Request model for vector similarity search."""
    query_vector: List[float] = Field(..., description="Query vector for similarity search")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum number of results")
    score_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")

    @field_validator('query_vector')
    @classmethod
    def validate_query_vector(cls, v):
        """Validate query vector dimensions."""
        if not v:
            raise ValueError("Query vector cannot be empty")
        if len(v) != 1024:  # BGE-M3 dimension
            raise ValueError(f"Query vector must have 1024 dimensions, got {len(v)}")
        return v

class VectorSearchResult(BaseModel):
    """Result model for vector similarity search."""
    id: str
    score: float
    content: str
    metadata: DocumentMetadata

class VectorSearchResponse(BaseModel):
    """Response model for vector similarity search."""
    results: List[VectorSearchResult]
    total_found: int
    search_time: float

class QdrantError(Exception):
    """Custom exception for Qdrant operations."""
    pass

class QdrantVectorStore:
    """
    Qdrant Vector Store Service

    Provides high-performance vector storage and similarity search for pharmaceutical documents.
    Supports metadata filtering, batch operations, and efficient indexing.
    """

    def __init__(
        self,
        path: Optional[str] = None,
        host: Optional[str] = None,
        port: int = 6333,
        collection_name: str = "fda_pharmaceutical_documents",
        vector_size: int = 1024,
        distance: Distance = Distance.COSINE
    ):
        """
        Initialize Qdrant vector store.

        Args:
            path: Local path for embedded Qdrant (None for in-memory, path for persistent)
            host: Qdrant server host (if using remote server)
            port: Qdrant server port (if using remote server)
            collection_name: Name of the vector collection
            vector_size: Dimension of vectors (BGE-M3 = 1024)
            distance: Distance metric for similarity search
        """
        self.path = path
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.vector_size = vector_size
        self.distance = distance
        self.client: Optional[QdrantClient] = None
        self._is_initialized = False

        if path is not None:
            logger.info(f"Initializing Qdrant store (local): {path}/{collection_name}")
        elif host is not None:
            logger.info(f"Initializing Qdrant store (remote): {host}:{port}/{collection_name}")
        else:
            logger.info(f"Initializing Qdrant store (in-memory): {collection_name}")

    async def initialize(self) -> None:
        """Initialize Qdrant client and collection."""
        if self._is_initialized:
            return

        try:
            logger.info("Initializing Qdrant client...")

            # Initialize client based on configuration
            if self.path is not None:
                # Local persistent storage
                self.client = QdrantClient(path=self.path)
                logger.info(f"Using local Qdrant storage: {self.path}")
            elif self.host is not None:
                # Remote server
                self.client = QdrantClient(host=self.host, port=self.port)
                logger.info(f"Connecting to remote Qdrant: {self.host}:{self.port}")
            else:
                # In-memory (default for development)
                self.client = QdrantClient(":memory:")
                logger.info("Using in-memory Qdrant storage")

            # Check if collection exists, create if not
            collections = self.client.get_collections().collections
            collection_exists = any(col.name == self.collection_name for col in collections)

            if not collection_exists:
                logger.info(f"Creating collection: {self.collection_name}")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=self.distance
                    )
                )

            self._is_initialized = True
            logger.info("Qdrant vector store initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Qdrant: {e}")
            raise QdrantError(f"Initialization failed: {e}")

    async def add_documents(
        self,
        contents: List[str],
        vectors: List[List[float]],
        metadatas: List[DocumentMetadata]
    ) -> List[str]:
        """
        Add documents to the vector store.

        Args:
            contents: List of document contents
            vectors: List of embedding vectors
            metadatas: List of document metadata

        Returns:
            List of document IDs

        Raises:
            QdrantError: If document addition fails
        """
        if not self._is_initialized:
            await self.initialize()

        if not self.client:
            raise QdrantError("Client not initialized")

        if len(contents) != len(vectors) != len(metadatas):
            raise QdrantError("Contents, vectors, and metadata lists must have same length")

        try:
            # Generate unique IDs for documents
            document_ids = [str(uuid.uuid4()) for _ in contents]

            # Prepare points for insertion
            points = []
            for i, (content, vector, metadata) in enumerate(zip(contents, vectors, metadatas)):
                # Validate vector dimensions
                if len(vector) != self.vector_size:
                    raise QdrantError(f"Vector dimension mismatch: expected {self.vector_size}, got {len(vector)}")

                # Prepare payload with content and metadata
                metadata_dict = metadata.dict()
                # Convert datetime to ISO string for storage
                if isinstance(metadata_dict.get('created_at'), datetime):
                    metadata_dict['created_at'] = metadata_dict['created_at'].isoformat()

                payload = {
                    "content": content,
                    **metadata_dict
                }

                points.append(PointStruct(
                    id=document_ids[i],
                    vector=vector,
                    payload=payload
                ))

            # Insert points in batches
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch_points = points[i:i + batch_size]
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=batch_points
                )

            logger.info(f"Added {len(document_ids)} documents to Qdrant")
            return document_ids

        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise QdrantError(f"Document addition failed: {e}")

    async def search_similar(
        self,
        request: VectorSearchRequest
    ) -> VectorSearchResponse:
        """
        Search for similar documents.

        Args:
            request: Search request with query vector and parameters

        Returns:
            VectorSearchResponse with search results

        Raises:
            QdrantError: If search fails
        """
        if not self._is_initialized:
            await self.initialize()

        if not self.client:
            raise QdrantError("Client not initialized")

        try:
            import time
            start_time = time.time()

            # Prepare search filter
            search_filter = None
            if request.filter_metadata:
                conditions = []
                for key, value in request.filter_metadata.items():
                    conditions.append(FieldCondition(
                        key=key,
                        match=MatchValue(value=value)
                    ))
                search_filter = Filter(must=conditions)

            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=request.query_vector,
                limit=request.limit,
                score_threshold=request.score_threshold,
                query_filter=search_filter,
                with_payload=True
            )

            search_time = time.time() - start_time

            # Process results
            results = []
            for result in search_results:
                payload = result.payload

                # Handle created_at conversion from string to datetime
                created_at = payload.get("created_at")
                if isinstance(created_at, str):
                    try:
                        created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    except ValueError:
                        created_at = datetime.now(timezone.utc)
                elif not isinstance(created_at, datetime):
                    created_at = datetime.now(timezone.utc)

                metadata = DocumentMetadata(
                    source=payload["source"],
                    title=payload.get("title"),  # Use get() since it's now optional
                    content_type=payload.get("content_type", "text"),
                    chunk_index=payload.get("chunk_index", 0),
                    total_chunks=payload.get("total_chunks", 1),
                    created_at=created_at,
                    regulatory_framework=payload.get("regulatory_framework"),
                    document_type=payload.get("document_type")
                )

                results.append(VectorSearchResult(
                    id=str(result.id),
                    score=result.score,
                    content=payload["content"],
                    metadata=metadata
                ))

            logger.info(f"Found {len(results)} similar documents in {search_time:.2f}s")

            return VectorSearchResponse(
                results=results,
                total_found=len(results),
                search_time=search_time
            )

        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise QdrantError(f"Search operation failed: {e}")

    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents by IDs."""
        if not self._is_initialized:
            await self.initialize()

        if not self.client:
            raise QdrantError("Client not initialized")

        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=document_ids
                )
            )

            logger.info(f"Deleted {len(document_ids)} documents")
            return True

        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise QdrantError(f"Document deletion failed: {e}")

    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        if not self._is_initialized:
            await self.initialize()

        if not self.client:
            raise QdrantError("Client not initialized")

        try:
            info = self.client.get_collection(self.collection_name)

            # Handle None values with proper defaults for empty collections
            vectors_count = info.vectors_count if info.vectors_count is not None else 0
            indexed_vectors_count = info.indexed_vectors_count if info.indexed_vectors_count is not None else 0
            points_count = info.points_count if info.points_count is not None else 0

            return {
                "name": self.collection_name,
                "vectors_count": vectors_count,
                "indexed_vectors_count": indexed_vectors_count,
                "points_count": points_count,
                "status": info.status,
                "optimizer_status": info.optimizer_status,
                "is_empty": vectors_count == 0
            }

        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            raise QdrantError(f"Collection info retrieval failed: {e}")

    async def health_check(self) -> bool:
        """Check if the vector store is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()

            # Test with collection info
            await self.get_collection_info()
            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

# Global service instance
_vector_store: Optional[QdrantVectorStore] = None

async def get_vector_store() -> QdrantVectorStore:
    """Get or create the global FDA document vector store instance (local mode)."""
    global _vector_store

    if _vector_store is None:
        # Use local path for persistent storage of FDA documents
        _vector_store = QdrantVectorStore(
            path="./data/qdrant",
            collection_name="fda_pharmaceutical_documents"
        )
        await _vector_store.initialize()

    return _vector_store

#!/usr/bin/env python3
"""Debug script to test FDA PDF processing."""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_pdf_processing():
    """Test FDA PDF processing pipeline."""
    try:
        # Import FDA processing services
        from services.ai.pdf_processor import get_fda_pdf_processor
        from services.ai.fda_chunker import get_fda_chunker
        from services.ai.fda_metadata import get_fda_metadata_manager
        
        logger.info("Testing FDA PDF processing pipeline...")
        
        # Initialize PDF processor
        pdf_processor = get_fda_pdf_processor()
        logger.info(f"PDF processor initialized with path: {pdf_processor.fda_docs_path}")
        
        # Check if directory exists
        if not pdf_processor.fda_docs_path.exists():
            logger.error(f"FDA docs directory does not exist: {pdf_processor.fda_docs_path}")
            return
        
        # List PDF files
        pdf_files = list(pdf_processor.fda_docs_path.glob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files:")
        for pdf_file in pdf_files:
            logger.info(f"  - {pdf_file.name} ({pdf_file.stat().st_size} bytes)")
        
        if not pdf_files:
            logger.error("No PDF files found!")
            return
        
        # Test processing one document
        test_pdf = pdf_files[0]
        logger.info(f"Testing processing of {test_pdf.name}...")
        
        try:
            text, metadata = pdf_processor.extract_text_from_pdf(test_pdf)
            logger.info(f"Successfully extracted {len(text)} characters")
            logger.info(f"Metadata: {metadata}")
        except Exception as e:
            logger.error(f"Failed to extract text from {test_pdf.name}: {str(e)}")
            return
        
        # Test full processing pipeline
        logger.info("Testing full document processing...")
        processed_documents = await pdf_processor.process_all_fda_documents()
        logger.info(f"Processed {len(processed_documents)} documents")
        
        if processed_documents:
            doc = processed_documents[0]
            logger.info(f"Sample document ID: {doc.get('document_id')}")
            logger.info(f"Sample content length: {len(doc.get('content', ''))}")
        
        # Test chunking
        if processed_documents:
            logger.info("Testing chunking...")
            hierarchy_data = await pdf_processor.load_fda_hierarchy()
            chunker = get_fda_chunker(hierarchy_data)
            
            chunks = chunker.chunk_fda_document(processed_documents[0])
            logger.info(f"Created {len(chunks)} chunks")
            
            if chunks:
                logger.info(f"Sample chunk content length: {len(chunks[0].get('content', ''))}")
        
        logger.info("✅ PDF processing test completed successfully")
        
    except Exception as e:
        logger.error(f"❌ PDF processing test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_pdf_processing())
/**
 * Enhanced Real-time Pharmaceutical Compliance Monitoring Hook
 *
 * Provides comprehensive real-time subscriptions for:
 * - Document processing updates
 * - Compliance alerts
 * - Audit trail events
 * - Performance monitoring alerts
 *
 * Compatible with VCP_001 Edge Functions and follows DEVELOPMENT_RULES.md
 */

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import { type RegulatoryDocument } from '@/lib/supabase-services'

// TypeScript interfaces for real-time events
interface ComplianceAlert {
  type: 'compliance_alert'
  organization_id: string
  document_id: string
  compliance_score: number
  violation_count: number
  alert_level: 'critical' | 'high' | 'medium'
  timestamp: string
}

interface DocumentProcessingUpdate {
  type: 'document_processing'
  organization_id: string
  document_id: string
  document_title: string
  processing_status: string
  previous_status: string
  progress_percentage: number
  timestamp: string
}

interface AuditTrailEvent {
  type: 'audit_trail'
  organization_id: string
  event_id: string
  event_category: string
  event_type: string
  user_id: string
  resource_type: string
  resource_id: string
  severity: string
  timestamp: string
}

interface PerformanceAlert {
  type: 'performance_monitoring'
  organization_id: string
  event_type: string
  execution_time_ms: number
  error_message?: string
  alert_level: 'critical' | 'warning' | 'error' | 'info'
  timestamp: string
}

interface DocumentUpdate {
  type: 'document_update'
  eventType: 'INSERT' | 'UPDATE' | 'DELETE'
  new?: RegulatoryDocument
  old?: RegulatoryDocument
}

type RealtimeEvent = ComplianceAlert | DocumentProcessingUpdate | AuditTrailEvent | PerformanceAlert | DocumentUpdate

interface ConnectionStatus {
  documents: boolean
  compliance: boolean
  processing: boolean
  audit: boolean
  performance: boolean
  overall: boolean
}

interface UseEnhancedRealtimeOptions {
  enabled?: boolean
  organizationId?: string
  enableDocuments?: boolean
  enableCompliance?: boolean
  enableProcessing?: boolean
  enableAudit?: boolean
  enablePerformance?: boolean
  onEvent?: (event: RealtimeEvent) => void
  onComplianceAlert?: (alert: ComplianceAlert) => void
  onProcessingUpdate?: (update: DocumentProcessingUpdate) => void
  onAuditEvent?: (event: AuditTrailEvent) => void
  onPerformanceAlert?: (alert: PerformanceAlert) => void
  onDocumentUpdate?: (update: DocumentUpdate) => void
  onError?: (error: Error) => void
}

export function useEnhancedRealtime(options: UseEnhancedRealtimeOptions = {}) {
  const {
    enabled = true,
    organizationId,
    enableDocuments = true,
    enableCompliance = true,
    enableProcessing = true,
    enableAudit = true,
    enablePerformance = true,
    onEvent,
    onComplianceAlert,
    onProcessingUpdate,
    onAuditEvent,
    onPerformanceAlert,
    onDocumentUpdate,
    onError
  } = options

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    documents: false,
    compliance: false,
    processing: false,
    audit: false,
    performance: false,
    overall: false
  })

  const [lastEvent, setLastEvent] = useState<RealtimeEvent | null>(null)
  const [eventHistory, setEventHistory] = useState<RealtimeEvent[]>([])
  const [errorCount, setErrorCount] = useState(0)

  const supabase = createClient()

  // Event handler with error recovery
  const handleEvent = useCallback((event: RealtimeEvent) => {
    setLastEvent(event)
    setEventHistory(prev => [...prev.slice(-49), event]) // Keep last 50 events
    onEvent?.(event)

    // Route to specific handlers
    switch (event.type) {
      case 'compliance_alert':
        onComplianceAlert?.(event)
        break
      case 'document_processing':
        onProcessingUpdate?.(event)
        break
      case 'audit_trail':
        onAuditEvent?.(event)
        break
      case 'performance_monitoring':
        onPerformanceAlert?.(event)
        break
      case 'document_update':
        onDocumentUpdate?.(event)
        break
    }
  }, [onEvent, onComplianceAlert, onProcessingUpdate, onAuditEvent, onPerformanceAlert, onDocumentUpdate])

  // Error handler with retry logic
  const handleError = useCallback((error: Error, channelType: string) => {
    setErrorCount(prev => prev + 1)
    console.error(`Real-time ${channelType} error:`, error)
    onError?.(error)

    // Update connection status
    setConnectionStatus(prev => ({
      ...prev,
      [channelType]: false,
      overall: Object.values({ ...prev, [channelType]: false }).some(status => status)
    }))
  }, [onError])

  // Setup real-time subscriptions
  useEffect(() => {
    if (!enabled) return

    const channels: any[] = []
    let reconnectAttempts = 0
    const maxReconnectAttempts = 5

    // Document changes subscription
    if (enableDocuments) {
      const documentChannel = supabase
        .channel('enhanced_documents_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'regulatory_documents',
            filter: organizationId ? `organization_id=eq.${organizationId}` : undefined
          },
          (payload) => {
            const event: DocumentUpdate = {
              type: 'document_update',
              eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
              new: payload.new as RegulatoryDocument,
              old: payload.old as RegulatoryDocument
            }
            handleEvent(event)
          }
        )
        .subscribe((status) => {
          const isConnected = status === 'SUBSCRIBED'
          setConnectionStatus(prev => ({
            ...prev,
            documents: isConnected,
            overall: isConnected || prev.compliance || prev.processing || prev.audit || prev.performance
          }))

          if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            handleError(new Error(`Document subscription ${status.toLowerCase()}`), 'documents')

            // Retry connection
            if (reconnectAttempts < maxReconnectAttempts) {
              setTimeout(() => {
                reconnectAttempts++
                documentChannel.subscribe()
              }, Math.pow(2, reconnectAttempts) * 1000) // Exponential backoff
            }
          } else if (status === 'SUBSCRIBED') {
            reconnectAttempts = 0 // Reset on successful connection
          }
        })

      channels.push(documentChannel)
    }

    // Organization-specific channels
    if (organizationId) {
      // Compliance alerts channel
      if (enableCompliance) {
        const complianceChannel = supabase
          .channel(`compliance_alerts_${organizationId}`)
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'document_compliance_assessments',
            filter: `organization_id=eq.${organizationId}`
          }, (payload) => {
            // Transform database event to compliance alert
            const assessment = payload.new as any
            if (assessment.overall_score < 0.7 || (assessment.violations && assessment.violations.length > 0)) {
              const alert: ComplianceAlert = {
                type: 'compliance_alert',
                organization_id: assessment.organization_id,
                document_id: assessment.document_id,
                compliance_score: assessment.overall_score,
                violation_count: assessment.violations ? assessment.violations.length : 0,
                alert_level: assessment.overall_score < 0.3 ? 'critical' :
                           assessment.overall_score < 0.5 ? 'high' : 'medium',
                timestamp: new Date().toISOString()
              }
              handleEvent(alert)
            }
          })
          .subscribe((status) => {
            const isConnected = status === 'SUBSCRIBED'
            setConnectionStatus(prev => ({
              ...prev,
              compliance: isConnected,
              overall: prev.documents || isConnected || prev.processing || prev.audit || prev.performance
            }))

            if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              handleError(new Error(`Compliance alerts subscription ${status.toLowerCase()}`), 'compliance')
            }
          })

        channels.push(complianceChannel)
      }

      // Document processing channel
      if (enableProcessing) {
        const processingChannel = supabase
          .channel(`document_processing_${organizationId}`)
          .on('postgres_changes', {
            event: 'UPDATE',
            schema: 'public',
            table: 'regulatory_documents',
            filter: `organization_id=eq.${organizationId}`
          }, (payload) => {
            const newDoc = payload.new as any
            const oldDoc = payload.old as any

            // Only emit if processing status changed
            if (newDoc.processing_status !== oldDoc.processing_status) {
              const update: DocumentProcessingUpdate = {
                type: 'document_processing',
                organization_id: newDoc.organization_id,
                document_id: newDoc.id,
                document_title: newDoc.title,
                processing_status: newDoc.processing_status,
                previous_status: oldDoc.processing_status || 'unknown',
                progress_percentage: newDoc.processing_progress || 0,
                timestamp: new Date().toISOString()
              }
              handleEvent(update)
            }
          })
          .subscribe((status) => {
            const isConnected = status === 'SUBSCRIBED'
            setConnectionStatus(prev => ({
              ...prev,
              processing: isConnected,
              overall: prev.documents || prev.compliance || isConnected || prev.audit || prev.performance
            }))

            if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              handleError(new Error(`Processing updates subscription ${status.toLowerCase()}`), 'processing')
            }
          })

        channels.push(processingChannel)
      }

      // Audit trail channel
      if (enableAudit) {
        const auditChannel = supabase
          .channel(`audit_trail_${organizationId}`)
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'audit_trail',
            filter: `organization_id=eq.${organizationId}`
          }, (payload) => {
            const auditRecord = payload.new as any
            const event: AuditTrailEvent = {
              type: 'audit_trail',
              organization_id: auditRecord.organization_id,
              event_id: auditRecord.id,
              event_category: auditRecord.event_category,
              event_type: auditRecord.event_type,
              user_id: auditRecord.user_id,
              resource_type: auditRecord.resource_type,
              resource_id: auditRecord.resource_id,
              severity: auditRecord.severity,
              timestamp: auditRecord.created_at
            }
            handleEvent(event)
          })
          .subscribe((status) => {
            const isConnected = status === 'SUBSCRIBED'
            setConnectionStatus(prev => ({
              ...prev,
              audit: isConnected,
              overall: prev.documents || prev.compliance || prev.processing || isConnected || prev.performance
            }))

            if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              handleError(new Error(`Audit trail subscription ${status.toLowerCase()}`), 'audit')
            }
          })

        channels.push(auditChannel)
      }

      // Performance monitoring channel
      if (enablePerformance) {
        const performanceChannel = supabase
          .channel(`performance_monitoring_${organizationId}`)
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'audit_trail',
            filter: `organization_id=eq.${organizationId} AND event_category=eq.performance`
          }, (payload) => {
            const perfRecord = payload.new as any
            const alert: PerformanceAlert = {
              type: 'performance_monitoring',
              organization_id: perfRecord.organization_id,
              event_type: perfRecord.event_type,
              execution_time_ms: perfRecord.execution_time_ms || 0,
              error_message: perfRecord.error_message,
              alert_level: perfRecord.severity as 'critical' | 'warning' | 'error' | 'info',
              timestamp: perfRecord.created_at
            }
            handleEvent(alert)
          })
          .subscribe((status) => {
            const isConnected = status === 'SUBSCRIBED'
            setConnectionStatus(prev => ({
              ...prev,
              performance: isConnected,
              overall: prev.documents || prev.compliance || prev.processing || prev.audit || isConnected
            }))

            if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              handleError(new Error(`Performance monitoring subscription ${status.toLowerCase()}`), 'performance')
            }
          })

        channels.push(performanceChannel)
      }
    }

    // Cleanup function
    return () => {
      channels.forEach(channel => {
        try {
          supabase.removeChannel(channel)
        } catch (error) {
          console.warn('Error removing channel:', error)
        }
      })

      setConnectionStatus({
        documents: false,
        compliance: false,
        processing: false,
        audit: false,
        performance: false,
        overall: false
      })
    }
  }, [
    enabled,
    organizationId,
    enableDocuments,
    enableCompliance,
    enableProcessing,
    enableAudit,
    enablePerformance,
    handleEvent,
    handleError,
    supabase
  ])

  // Setup channels using Edge Function
  const setupChannels = useCallback(async () => {
    if (!organizationId) return

    try {
      const channelConfigs = []

      if (enableCompliance) channelConfigs.push({ name: 'compliance_alerts', type: 'compliance_alerts' as const })
      if (enableProcessing) channelConfigs.push({ name: 'document_processing', type: 'document_processing' as const })
      if (enableAudit) channelConfigs.push({ name: 'audit_trail', type: 'audit_trail' as const })
      if (enablePerformance) channelConfigs.push({ name: 'performance_monitoring', type: 'performance_monitoring' as const })

      const response = await supabase.functions.invoke('setup-realtime-channels', {
        body: {
          organizationId,
          userId: 'current-user', // TODO: Get from auth context
          action: 'setup',
          channels: channelConfigs
        }
      })

      if (response.error) {
        throw new Error(`Failed to setup channels: ${response.error.message}`)
      }

      return response.data
    } catch (error) {
      handleError(error as Error, 'setup')
      throw error
    }
  }, [organizationId, enableCompliance, enableProcessing, enableAudit, enablePerformance, supabase, handleError])

  return {
    connectionStatus,
    lastEvent,
    eventHistory,
    errorCount,
    setupChannels,
    isConnected: connectionStatus.overall
  }
}

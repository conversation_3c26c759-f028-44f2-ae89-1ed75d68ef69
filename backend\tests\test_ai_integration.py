"""
Comprehensive tests for VCP_023_3 OpenRouter RAG Integration.
Tests AI client, vector store, and API endpoints.
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock
from httpx import AsyncClient
import json

# Test configuration
TEST_API_KEY = "test-api-key"
TEST_MODEL = "moonshotai/kimi-k2:free"


class TestAIClient:
    """Test AI client functionality."""
    
    @pytest.fixture
    async def ai_client(self):
        """Create AI client for testing."""
        from services.ai.client import AIClient
        from services.ai.models import AIModelConfig
        
        config = AIModelConfig(
            api_key=TEST_API_KEY,
            model=TEST_MODEL,
            base_url="https://openrouter.ai/api/v1"
        )
        
        client = AIClient(config)
        await client.initialize()
        return client
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self, ai_client):
        """Test successful text generation."""
        with patch.object(ai_client.client, 'post') as mock_post:
            # Mock successful API response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {"content": "Test response"},
                    "finish_reason": "stop"
                }],
                "model": TEST_MODEL,
                "usage": {"total_tokens": 50}
            }
            mock_post.return_value = mock_response
            
            result = await ai_client.generate_text(
                prompt="Test prompt",
                system_prompt="Test system prompt"
            )
            
            assert result["status"] == "success"
            assert result["content"] == "Test response"
            assert result["model"] == TEST_MODEL
    
    @pytest.mark.asyncio
    async def test_generate_text_api_error(self, ai_client):
        """Test API error handling."""
        with patch.object(ai_client.client, 'post') as mock_post:
            # Mock API error response
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.text = "Bad Request"
            mock_post.return_value = mock_response
            
            result = await ai_client.generate_text(prompt="Test prompt")
            
            assert result["status"] == "error"
            assert "API request failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_analyze_document_success(self, ai_client):
        """Test successful document analysis."""
        with patch.object(ai_client, 'generate_text') as mock_generate:
            # Mock successful analysis response
            mock_generate.return_value = {
                "status": "success",
                "content": json.dumps({
                    "compliance_score": 85,
                    "risk_level": "Medium",
                    "key_findings": ["Finding 1", "Finding 2"],
                    "recommendations": ["Rec 1", "Rec 2"],
                    "confidence": 90
                }),
                "model": TEST_MODEL
            }
            
            result = await ai_client.analyze_document(
                document_content="Test document content",
                document_type="FDA_guidance",
                analysis_type="compliance"
            )
            
            assert result["status"] == "success"
            assert result["analysis"]["compliance_score"] == 85
            assert result["analysis"]["risk_level"] == "Medium"
            assert len(result["analysis"]["key_findings"]) == 2


class TestVectorStore:
    """Test vector store functionality."""
    
    @pytest.fixture
    async def vector_store(self):
        """Create vector store for testing."""
        from services.ai.vector_store import VectorStore
        
        # Use test collection name
        store = VectorStore(collection_name="test_pharmaceutical_kb")
        
        # Mock ChromaDB to avoid actual database operations in tests
        with patch('services.ai.vector_store.chromadb'):
            await store.initialize()
        
        return store
    
    @pytest.mark.asyncio
    async def test_add_documents(self, vector_store):
        """Test adding documents to vector store."""
        test_documents = [
            {
                "content": "Test pharmaceutical document content",
                "metadata": {
                    "type": "regulation",
                    "source": "FDA",
                    "framework": "21_cfr_part_11"
                }
            }
        ]
        
        # Mock the collection add method
        vector_store._collection = Mock()
        vector_store._collection.add = Mock()
        
        await vector_store.add_documents(test_documents)
        
        # Verify add was called
        vector_store._collection.add.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_documents(self, vector_store):
        """Test searching documents in vector store."""
        # Mock the collection query method
        vector_store._collection = Mock()
        vector_store._collection.query.return_value = {
            "documents": [["Test document content"]],
            "metadatas": [[{"type": "regulation"}]],
            "distances": [[0.2]]
        }
        
        results = await vector_store.search(
            query="pharmaceutical compliance",
            n_results=5
        )
        
        assert len(results) == 1
        assert results[0]["content"] == "Test document content"
        assert results[0]["similarity_score"] == 0.8  # 1.0 - 0.2


class TestAPIEndpoints:
    """Test AI API endpoints."""
    
    @pytest.fixture
    async def test_client(self):
        """Create test client for API testing."""
        from main import app
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_analyze_document_endpoint(self, test_client):
        """Test document analysis endpoint."""
        with patch('main.get_ai_client') as mock_get_client:
            # Mock AI client
            mock_client = AsyncMock()
            mock_client.analyze_document.return_value = {
                "status": "success",
                "analysis": {
                    "compliance_score": 85,
                    "risk_level": "Medium",
                    "key_findings": ["Finding 1"],
                    "recommendations": ["Rec 1"],
                    "confidence": 90
                }
            }
            mock_get_client.return_value = mock_client
            
            response = await test_client.post(
                "/api/v1/ai/analyze-document",
                json={
                    "document_id": "test-doc-123",
                    "content": "Test document content",
                    "document_type": "FDA_guidance",
                    "analysis_type": "compliance"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["analysis_type"] == "compliance"
            assert data["results"]["status"] == "success"
    
    @pytest.mark.asyncio
    async def test_summarize_endpoint(self, test_client):
        """Test document summarization endpoint."""
        with patch('main.get_ai_client') as mock_get_client:
            # Mock AI client
            mock_client = AsyncMock()
            mock_client.generate_text.return_value = {
                "status": "success",
                "content": "This is a test summary of the document.",
                "model": TEST_MODEL
            }
            mock_get_client.return_value = mock_client
            
            response = await test_client.post(
                "/api/v1/ai/summarize",
                json={
                    "content": "Long document content to summarize",
                    "document_type": "regulation",
                    "max_length": 100
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert "summary" in data
    
    @pytest.mark.asyncio
    async def test_compliance_score_endpoint(self, test_client):
        """Test compliance scoring endpoint."""
        with patch('main.get_ai_client') as mock_get_client:
            # Mock AI client
            mock_client = AsyncMock()
            mock_client.generate_text.return_value = {
                "status": "success",
                "content": json.dumps({
                    "overall_score": 85,
                    "framework_scores": {"21_cfr_part_11": 85},
                    "critical_violations": [],
                    "recommendations": ["Improve documentation"],
                    "risk_level": "Medium",
                    "confidence": 90
                }),
                "model": TEST_MODEL
            }
            mock_get_client.return_value = mock_client
            
            response = await test_client.post(
                "/api/v1/ai/compliance-score",
                json={
                    "content": "Document content for compliance scoring",
                    "document_type": "SOP",
                    "frameworks": ["21_cfr_part_11"]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["compliance_score"] == 85


# Integration test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

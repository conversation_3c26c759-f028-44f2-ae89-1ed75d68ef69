{
  "recommendations": [
    "stagewise.stagewise-vscode-extension",
    // TypeScript & JavaScript
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    // React & Next.js
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    // Git & Version Control
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    // Code Quality & Testing
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.test-adapter-converter",
    "vitest.explorer",
    // Productivity
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.hexeditor",
    "gruntfuggly.todo-tree",
    // UI/UX Development
    "styled-components.vscode-styled-components",
    "ms-vscode.vscode-css-peek",
    // Documentation
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint"
  ]
}

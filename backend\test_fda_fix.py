#!/usr/bin/env python3
"""
Test script to verify the FDA knowledge base populator fix
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent))

from services.knowledge.fda_knowledge_populator import FDAKnowledgePopulator

async def test_fda_populator():
    """Test the fixed FDA knowledge base populator"""
    try:
        print("Testing FDA Knowledge Base Populator...")
        
        # Initialize populator with correct paths
        populator = FDAKnowledgePopulator(
            qdrant_path="./qdrant_fda_db",
            collection_name="fda_compliance_docs"
        )
        print("✓ FDA populator initialized")
        
        # Check if we can get status
        status = await populator.get_population_status()
        print(f"✓ Current status: {status}")
        
        # Check if FDA docs directory exists
        fda_docs_path = Path("./fda_docs")
        if fda_docs_path.exists():
            pdf_files = list(fda_docs_path.glob("CFR-2024-title21-vol*.pdf"))
            print(f"✓ Found {len(pdf_files)} FDA PDF files")
            
            if pdf_files:
                print("✓ Ready to process FDA documents")
                print("🚀 Starting FDA document population...")
                result = await populator.populate_fda_documents("./fda_docs")
                print(f"✓ Population result: {result}")
                
                # Check status after population
                final_status = await populator.get_population_status()
                print(f"✓ Final status: {final_status}")
            else:
                print("⚠ No FDA PDF files found to process")
        else:
            print(f"⚠ FDA docs directory not found: {fda_docs_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing FDA populator: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting FDA populator test...")
    success = asyncio.run(test_fda_populator())
    if success:
        print("\n✓ FDA populator test completed successfully")
    else:
        print("\n✗ FDA populator test failed")
        sys.exit(1)
/**
 * Auth confirmation route handler
 * Following July 2025 Supabase patterns
 */

import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const code = searchParams.get('code')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/dashboard'

  // Remove console.log for production, but keep for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Auth confirmation debug:', {
      token_hash: token_hash ? 'present' : 'missing',
      code: code ? 'present' : 'missing',
      type,
      next,
      url: request.url
    })
  }

  const supabase = await createClient()

  // Handle newer token_hash format (recommended)
  if (token_hash && type) {
    if (process.env.NODE_ENV === 'development') {
      console.log('📧 Processing token_hash format')
    }
    
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })

    if (!error) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Token verification successful, redirecting to:', next)
      }
      redirect(next)
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Token verification failed:', error)
      }
    }
  }
  
  // Handle legacy code format (for backward compatibility)
  else if (code) {
    if (process.env.NODE_ENV === 'development') {
      console.log('📧 Processing legacy code format with exchangeCodeForSession')
    }
    
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 exchangeCodeForSession result:', {
        hasData: !!data,
        hasSession: !!data?.session,
        hasUser: !!data?.user,
        error: error?.message
      })
    }

    if (!error) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Code exchange successful, redirecting to:', next)
      }
      redirect(next)
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Code exchange failed:', error)
      }
    }
  }

  // Redirect the user to an error page with instructions
  if (process.env.NODE_ENV === 'development') {
    console.log('❌ No valid token or code found, redirecting to error page')
  }
  redirect('/login?error=confirmation_failed')
}

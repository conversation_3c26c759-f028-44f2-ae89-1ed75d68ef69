'use client'

import { RefreshCw, Download } from 'lucide-react'

import { But<PERSON> } from '@/components/ui-radix/button'

interface UpdatesHeaderProps {
  readonly onExport: () => void;
  readonly onRefresh: () => void;
}

export function UpdatesHeader({ onExport, onRefresh }: UpdatesHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Regulatory Updates
        </h1>
        <p className="text-muted-foreground mt-1">
          Stay informed with the latest regulatory changes and compliance
          requirements
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="outline" onClick={onExport}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    </div>
  )
}

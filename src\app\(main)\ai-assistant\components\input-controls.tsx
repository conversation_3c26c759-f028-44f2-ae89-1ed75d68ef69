'use client'

import { <PERSON>c<PERSON>, Search, Send, Upload, X } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import { Input } from '@/components/ui-radix/input'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui-radix/popover'
import { Textarea } from '@/components/ui-radix/textarea'

import type { AttachedDocument, UploadedDocument } from '../types'

interface InputControlsProps {
  readonly inputMessage: string;
  readonly onInputChange: (message: string) => void;
  readonly onSendMessage: () => void;
  readonly attachedDocuments: readonly AttachedDocument[];
  readonly onAttachDocument: (document: UploadedDocument) => void;
  readonly onRemoveAttachment: (docId: string) => void;
  readonly uploadedDocuments: readonly UploadedDocument[];
  readonly disabled?: boolean;
}

export function InputControls({
  inputMessage,
  onInputChange,
  onSendMessage,
  attachedDocuments,
  onAttachDocument,
  onRemoveAttachment,
  uploadedDocuments,
  disabled = false,
}: InputControlsProps) {
  const [isDocumentPopoverOpen, setIsDocumentPopoverOpen] = useState(false)
  const [documentSearchQuery, setDocumentSearchQuery] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [inputMessage])

  const handleSend = () => {
    if (!inputMessage.trim() && attachedDocuments.length === 0) {
      return
    }
    onSendMessage()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      Array.from(files).forEach((file) => {
        const newDoc: UploadedDocument = {
          id: Date.now().toString(),
          name: file.name,
          type: (file.name.split('.').pop() || 'file').toUpperCase(),
          size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
          uploadedDate: new Date().toISOString().split('T')[0] || '',
          category: 'Uploaded',
          description: 'Recently uploaded file',
        }
        onAttachDocument(newDoc)
      })
    }
    setIsDocumentPopoverOpen(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleAttachExistingDocument = (document: UploadedDocument) => {
    onAttachDocument(document)
    setIsDocumentPopoverOpen(false)
  }

  const handleSuggestedPrompt = (prompt: string) => {
    onInputChange(prompt)
  }

  const filteredDocuments = uploadedDocuments.filter(
    (doc) =>
      doc.name.toLowerCase().includes(documentSearchQuery.toLowerCase()) ||
      doc.category.toLowerCase().includes(documentSearchQuery.toLowerCase()) ||
      doc.description.toLowerCase().includes(documentSearchQuery.toLowerCase()),
  )

  const suggestedPrompts = [
    'What are the latest FDA guidelines?',
    'Help me with SOX compliance',
    'Review my risk assessment',
    'Generate compliance checklist',
  ]

  return (
    <div className="border-t border-border p-4">
      {/* Attached Documents */}
      {attachedDocuments.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {attachedDocuments.map((doc) => (
            <Badge
              key={doc.id}
              variant="secondary"
              className="flex items-center space-x-1 pl-2 pr-1 py-1"
            >
              <span className="text-xs truncate max-w-32">{doc.name}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onRemoveAttachment(doc.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Input Row */}
      <div className="flex gap-2 mb-3">
        <Textarea
          ref={textareaRef}
          value={inputMessage}
          onChange={(e) => onInputChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Ask me anything about compliance..."
          disabled={disabled}
          className="flex-1 h-10 rounded-lg border border-border bg-background px-3 text-sm resize-none overflow-hidden"
          rows={1}
        />

        {/* Attachment Popover */}
        <Popover
          open={isDocumentPopoverOpen}
          onOpenChange={setIsDocumentPopoverOpen}
        >
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="h-9 px-3"
              disabled={disabled}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" side="top">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Attach Documents</h4>

              {/* Upload New File */}
              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Upload New File
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>

              {/* Search Existing Documents */}
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search documents..."
                    value={documentSearchQuery}
                    onChange={(e) => setDocumentSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>

                <div className="max-h-48 overflow-auto space-y-1">
                  {filteredDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="p-2 rounded border-0 hover:bg-muted/50 cursor-pointer transition-colors"
                      onClick={() => handleAttachExistingDocument(doc)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {doc.name}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {doc.description}
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs ml-2">
                          {doc.category}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Send Button */}
        <Button
          onClick={handleSend}
          disabled={
            disabled || (!inputMessage.trim() && attachedDocuments.length === 0)
          }
          className="h-10 px-4"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Suggested Prompts */}
      <div className="flex flex-wrap gap-2">
        {suggestedPrompts.map((prompt, index) => (
          <Button
            key={index}
            variant="outline"
            className="text-xs h-7 px-2"
            onClick={() => handleSuggestedPrompt(prompt)}
          >
            {prompt}
          </Button>
        ))}
      </div>
    </div>
  )
}

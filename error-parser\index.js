const fs = require('fs');
const path = require('path');

// Paths
const errorsJsonPath = path.join(__dirname, '..', 'errors.json');
const errorTrackingPath = path.join(__dirname, '..', 'error_tracking.md');

// Read errors.json
const readErrorsJson = () => {
  try {
    const data = fs.readFileSync(errorsJsonPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading errors.json:', error.message);
    process.exit(1);
  }
};

// Group errors by file and module
const groupErrors = (errors) => {
  const fileGroups = {};
  const moduleGroups = {};
  
  // Process all errors and group by file
  errors.forEach(error => {
      const resource = error.resource;
      if (!resource) return;
      
      // Extract file path - handle both /d:/ and /c:/ paths
      const filePath = resource.replace(/^\/[a-z]:\/(?:Speedr_CCSS\/Rust-CCSS\/|CCSS\/)/i, '');
      
      if (!fileGroups[filePath]) {
        fileGroups[filePath] = [];
      }
      fileGroups[filePath].push(error);
      
      // Extract module name (first part of the path)
      const parts = filePath.split('/');
      if (parts.length >= 1) {
        // Use the first directory as the module name
        const moduleName = parts[0];
        const moduleKey = moduleName;
        
        if (!moduleGroups[moduleKey]) {
          moduleGroups[moduleKey] = [];
        }
        moduleGroups[moduleKey].push(error);
      }
    });
  
  return { fileGroups, moduleGroups };
};

// Generate error description
const generateErrorDescription = (error) => {
  const filePath = error.resource.replace(/^\/[a-z]:\/(?:Speedr_CCSS\/Rust-CCSS\/|CCSS\/)/i, '');
  const lineInfo = `${filePath}:${error.startLineNumber}`;
  const message = error.message.split('\n')[0]; // Take first line of message
  
  return {
    lineInfo,
    message
  };
};

// Generate markdown content
const generateMarkdown = (errors) => {
  const { moduleGroups } = groupErrors(errors);
  
  let markdown = '# Errors\n\n';
  //markdown += `This file tracks the ${errors.length} errors in the codebase and their resolution status.\n\n`;
  
  // Sort modules by name
  const sortedModules = Object.keys(moduleGroups).sort();
  console.log('Sorted modules:', sortedModules);
  
  let totalErrorCount = 0;
  
  // Generate sections for each module
  for (const moduleKey of sortedModules) {
    const moduleErrors = moduleGroups[moduleKey];
    if (!moduleErrors || moduleErrors.length === 0) {
      console.log(`Skipping module ${moduleKey} - no errors`);
      continue;
    }
    
    totalErrorCount += moduleErrors.length;
    console.log(`Processing module ${moduleKey} with ${moduleErrors.length} errors`);
    
    // Format module name for heading
    const moduleName = moduleKey;
    const formattedModuleName = moduleName.charAt(0).toUpperCase() + moduleName.slice(1);
    
    markdown += `## ${formattedModuleName} Module Issues (${moduleErrors.length} errors)\n\n`;
    
    // Add each error
    moduleErrors.forEach((error, index) => {
      const { lineInfo, message } = generateErrorDescription(error);
      markdown += `- Error ${totalErrorCount - moduleErrors.length + index + 1}: \`${message}\` in ${lineInfo}\n`;
      markdown += `  - Error details: ${error.message.replace(/\n/g, '\n  ')}\n\n`;
      //markdown += `  - Fix: \n\n`;
    });
  }
  
  // Add progress section
  // markdown += `## Progress\n\n`;
  // markdown += `- Errors fixed: 0\n`;
  // markdown += `- Remaining errors: ${totalErrorCount}\n`;
  // markdown += `- Files modified: None\n`;
  
  return markdown;
};

// Preserve existing content if available
const preserveExistingContent = () => {
  try {
    if (fs.existsSync(errorTrackingPath)) {
      const content = fs.readFileSync(errorTrackingPath, 'utf8');
      
      // Extract completed items (lines with [x])
      const completedItems = [];
      const lines = content.split('\n');
      
      lines.forEach(line => {
        if (line.match(/^- \[x\] Error \d+:/)) {
          completedItems.push(line);
        }
      });
      
      return completedItems;
    }
  } catch (error) {
    console.error('Error reading existing error_tracking.md:', error.message);
  }
  
  return [];
};

// Apply completed items to new content
const applyCompletedItems = (markdown, completedItems) => {
  let updatedMarkdown = markdown;
  
  completedItems.forEach(item => {
    // Extract error number
    const match = item.match(/^- \[x\] Error (\d+):/);
    if (match && match[1]) {
      const errorNumber = match[1];
      // Replace unchecked box with checked box for this error
      const regex = new RegExp(`- \[ \] Error ${errorNumber}:`, 'g');
      updatedMarkdown = updatedMarkdown.replace(regex, item.replace(/^- \[x\]/, '- [x]'));
    }
  });
  
  return updatedMarkdown;
};

// Main function
const main = () => {
  console.log('Reading errors.json...');
  const errors = readErrorsJson();
  
  console.log(`Found ${errors.length} errors in total`);
  
  // Debug: Check the structure of the first error
  if (errors.length > 0) {
    console.log('First error structure:', JSON.stringify(errors[0], null, 2));
  }
  
  // Preserve existing completed items
  const completedItems = preserveExistingContent();
  console.log(`Found ${completedItems.length} completed items in existing file`);
  
  // Generate new markdown
  let markdown = generateMarkdown(errors);
  console.log('Generated markdown length:', markdown.length);
  
  // Debug: Check the grouped errors
  const { moduleGroups } = groupErrors(errors);
  console.log('Module groups count:', Object.keys(moduleGroups).length);
  
  // Apply completed items if any
  if (completedItems.length > 0) {
    markdown = applyCompletedItems(markdown, completedItems);
  }
  
  // Write to file
  fs.writeFileSync(errorTrackingPath, markdown);
  console.log(`Generated error_tracking.md with ${errors.length} errors`);
};

// Run the script
main();
/**
 * Server Actions for authentication
 * Following July 2025 Supabase patterns and DEVELOPMENT_RULES_2.md
 */

'use server'

import { getErrorCode } from '@/lib/auth-utils'
import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

/**
 * Login action for form submission
 */
export async function login(formData: FormData) {
  const supabase = await createClient()

  // Type-safe form data extraction
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Input validation
  if (!email || !password) {
    redirect('/login?error=missing_credentials')
  }

  // Email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    redirect('/login?error=invalid_email')
  }

  // Password strength validation
  if (password.length < 6) {
    redirect('/login?error=weak_password')
  }

  let error: any = null

  try {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Connection timeout')), 15000)
    )

    const authPromise = supabase.auth.signInWithPassword({
      email,
      password,
    })

    const result = await Promise.race([authPromise, timeoutPromise]) as any
    error = result.error
  } catch (timeoutError) {
    console.error('Auth timeout:', timeoutError)
    redirect('/login?error=connection_timeout')
  }

  if (error) {
    // Map Supabase errors to user-friendly messages
    const errorCode = getErrorCode(error.message)
    redirect(`/login?error=${errorCode}`)
  }

  revalidatePath('/', 'layout')
  redirect('/dashboard')
}

/**
 * Signup action for form submission
 */
export async function signup(formData: FormData) {
  const supabase = await createClient()

  // Type-safe form data extraction
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const organizationName = formData.get('organizationName') as string

  // Input validation
  if (!email || !password || !confirmPassword || !organizationName) {
    redirect('/signup?error=missing_credentials')
  }

  // Email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    redirect('/signup?error=invalid_email')
  }

  // Password strength validation
  if (password.length < 8) {
    redirect('/signup?error=weak_password')
  }

  // Password confirmation validation
  if (password !== confirmPassword) {
    redirect('/signup?error=password_mismatch')
  }

  // Get site URL with fallback for development
  const siteUrl = process.env['NEXT_PUBLIC_SITE_URL'] || 'http://localhost:3001'

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${siteUrl}/auth/confirm?next=/dashboard`,
      data: {
        email_confirm: true,
        organization_name: organizationName,
        full_name: organizationName, // Fallback for display name
      },
    },
  })

  if (error) {
    // Enhanced error logging for debugging
    console.error('Supabase signUp error:', {
      message: error.message,
      status: error.status,
      details: error
    })
    const errorCode = getErrorCode(error.message)
    redirect(`/signup?error=${errorCode}`)
  }

  // Log successful signup for debugging
  console.log('Signup successful:', {
    userId: data.user?.id,
    email: data.user?.email,
    emailConfirmed: data.user?.email_confirmed_at
  })

  revalidatePath('/', 'layout')
  redirect('/signup?success=check_email')
}

/**
 * Logout action
 */
export async function logout() {
  const supabase = await createClient()

  const { error } = await supabase.auth.signOut()

  if (error) {
    redirect('/login?error=logout_failed')
  }

  revalidatePath('/', 'layout')
  redirect('/login')
}

/**
 * Reset password action
 */
export async function resetPassword(formData: FormData) {
  const supabase = await createClient()

  const email = formData.get('email') as string

  // Input validation
  if (!email) {
    redirect('/reset-password?error=missing_email')
  }

  // Email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    redirect('/reset-password?error=invalid_email')
  }

  // Get site URL with fallback for development
  const siteUrl = process.env['NEXT_PUBLIC_SITE_URL'] || 'http://localhost:3001'

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${siteUrl}/auth/reset-password/confirm`,
  })

  if (error) {
    const errorCode = getErrorCode(error.message)
    redirect(`/reset-password?error=${errorCode}`)
  }

  redirect('/reset-password?success=check_email')
}

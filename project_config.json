{"project": {"name": "Vigilen", "fullName": "VigiLens Pharmaceutical Compliance Platform", "version": "1.0.0", "description": "AI-powered pharmaceutical regulatory compliance monitoring platform", "type": "pharmaceutical-compliance", "classification": "Confidential - Pharmaceutical Data"}, "technology": {"frontend": {"framework": "Next.js", "version": "15.1.5", "language": "TypeScript", "typescript_version": "5.7.3", "react_version": "19", "ui_framework": "shadcn/ui + Tailwind CSS", "tailwind_version": "4.0.1", "state_management": "Zustand 5.0.5", "validation": "Valibot 0.32.1", "charts": "Recharts", "architecture": "App Router + Server Components + Async Request APIs"}, "backend": {"language": "Python", "python_version": "3.13.5", "framework": "FastAPI", "fastapi_version": "0.115.5", "ai_framework": "LangChain + Fine-tuned Open-Source Model", "langchain_version": "0.3.14", "llm_model": "Fine-tuned Open-Source (Llama/Mistral/Custom)", "validation": "Pydantic 2.9.2", "background_tasks": "APScheduler 4.0.0", "http_client": "httpx 0.28.1", "web_scraping": "BeautifulSoup4 4.12.3", "database": "Supabase PostgreSQL", "vector_database": "ChromaDB 0.6.2", "authentication": "Supabase Auth (JWT-based, MFA support)", "file_storage": "Supabase Storage", "realtime": "Supabase Realtime WebSockets"}, "hosting": {"frontend": "Vercel (free tier)", "backend": "Railway ($5 monthly credit)", "database": "Supabase Cloud (free tier) - ap-south-1 region", "vector_db": "Local files (embedded ChromaDB)"}, "monitoring": {"ai_monitoring": "LangSmith (free tier)", "error_tracking": "FastAPI built-in logging", "performance": "Python 3.13 enhanced logging", "analytics": "Supabase Analytics"}, "security": {"authentication": "Supabase Auth (multi-factor)", "authorization": "Row Level Security (RLS)", "encryption": "TLS 1.3 in transit, AES-256 at rest", "compliance": "HIPAA ready via Supabase", "audit_logging": "PostgreSQL + Supabase audit trail"}}, "status": {"frontend": "Complete", "backend": "Not Started - Critical Priority", "ai_pipeline": "Not Started - Critical Priority", "deployment": "Not Started", "testing": "Not Started"}, "stakeholders": {"primary": ["Large Contract Manufacturing Organizations (CMOs)", "Mid-Sized Contract Laboratories", "Pharmaceutical Marketing Teams"], "decision_makers": ["VP Quality", "Director Regulatory Affairs", "IT Manager"]}, "success_metrics": {"technical": {"regulatory_detection_accuracy": "99% within 1 hour", "ai_analysis_accuracy": "90% agreement with experts", "system_performance": "<2s search, <5min processing", "uptime": "99.9% availability"}, "business": {"time_savings": "80% reduction in manual monitoring", "cost_savings": "$320,000 annual savings", "conversion_rate": "60% pilot-to-paid", "customer_satisfaction": "4.5+ NPS score"}}, "compliance_requirements": ["21 CFR Part 11 (Electronic Records)", "FDA Data Integrity Guidelines", "ALCOA+ Principles", "Computer System Validation"], "created": "2025-07-11", "last_updated": "2025-07-11"}
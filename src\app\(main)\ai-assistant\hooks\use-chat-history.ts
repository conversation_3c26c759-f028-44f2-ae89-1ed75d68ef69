import { useState, useCallback } from 'react'

import type { ConversationHistory } from '../types'

export function useChatHistory() {
  const [conversationHistory] = useState<ConversationHistory[]>([
    {
      id: 'conv1',
      title: 'FDA Process Validation Guidance',
      lastMessage: 'What are the key requirements...',
      timestamp: '2 hours ago',
      messageCount: 12,
    },
    {
      id: 'conv2',
      title: 'Data Integrity Requirements',
      lastMessage: 'How to implement ALCOA+...',
      timestamp: 'Yesterday',
      messageCount: 8,
    },
    {
      id: 'conv3',
      title: 'GMP Compliance Questions',
      lastMessage: 'Annual product review process...',
      timestamp: '3 days ago',
      messageCount: 15,
    },
  ])

  const [selectedConversationId, setSelectedConversationId] = useState<
    string | undefined
  >()

  const handleSelectConversation = useCallback((conversationId: string) => {
    setSelectedConversationId(conversationId)
    // In a real app, this would load the conversation messages
    console.log(`Loading conversation: ${conversationId}`)
  }, [])

  const getUploadedDocuments = useCallback(() => {
    // Mock uploaded documents from document library
    return [
      {
        id: 'doc1',
        name: 'Process Validation Protocol v2.1',
        type: 'PDF',
        size: '2.4 MB',
        uploadedDate: '2023-06-15',
        category: 'Validation',
        description: 'Process validation protocol for sterile manufacturing',
      },
      {
        id: 'doc2',
        name: 'GMP Audit Checklist 2023',
        type: 'DOCX',
        size: '875 KB',
        uploadedDate: '2023-06-10',
        category: 'Audit',
        description: 'Comprehensive GMP audit checklist for Q2 2023',
      },
      {
        id: 'doc3',
        name: 'Data Integrity SOP',
        type: 'PDF',
        size: '1.8 MB',
        uploadedDate: '2023-06-08',
        category: 'SOP',
        description:
          'Standard operating procedure for data integrity compliance',
      },
      {
        id: 'doc4',
        name: 'FDA Warning Letter Response',
        type: 'PDF',
        size: '945 KB',
        uploadedDate: '2023-06-05',
        category: 'Regulatory',
        description: 'Response to FDA 483 observations and warning letter',
      },
      {
        id: 'doc5',
        name: 'Annual Product Review Template',
        type: 'XLSX',
        size: '1.2 MB',
        uploadedDate: '2023-06-01',
        category: 'Template',
        description:
          'Annual product review template for pharmaceutical products',
      },
    ]
  }, [])

  return {
    conversationHistory,
    selectedConversationId,
    handleSelectConversation,
    getUploadedDocuments,
  }
}

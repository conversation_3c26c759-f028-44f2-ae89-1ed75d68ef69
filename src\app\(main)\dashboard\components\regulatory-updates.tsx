'use client'

import { <PERSON>R<PERSON> } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/ui-radix/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'
import { UpdateCard } from '@/components/ui-radix/update-card'

interface RegulatoryUpdate {
  readonly title: string;
  readonly agency: string;
  readonly category: string;
  readonly publishedDate: string;
  readonly severity: 'low' | 'medium' | 'high';
  readonly summary: string;
}

export function RegulatoryUpdates() {
  const router = useRouter()

  const recentUpdates: readonly RegulatoryUpdate[] = [
    {
      title: 'FDA Updates to GMP Guidelines for Pharmaceutical Manufacturing',
      agency: 'FDA',
      category: 'Pharmaceutical',
      publishedDate: '2 hours ago',
      severity: 'medium',
      summary:
        'The FDA has released updated Good Manufacturing Practice (GMP) guidelines that affect Contract Manufacturing Organizations. Key changes include enhanced requirements for quality management systems and updated validation protocols.',
    },
    {
      title: 'EMA Guideline on Quality Risk Management',
      agency: 'EMA',
      category: 'Manufacturing',
      publishedDate: 'Yesterday',
      severity: 'low',
      summary:
        'The European Medicines Agency has published a new baseline on Quality Risk Management for pharmaceutical manufacturers. This guideline emphasizes a proactive approach to identifying and mitigating risks in the manufacturing process.',
    },
    {
      title: 'ICH Q9 R1 Quality Risk Management Revision',
      agency: 'ICH',
      category: 'International',
      publishedDate: '2 days ago',
      severity: 'high',
      summary:
        'The International Council for Harmonisation has revised the Q9 guideline on Quality Risk Management. The revision includes new considerations for subjectivity in risk assessment and formalized risk review processes.',
    },
  ]

  return (
    <Card className="card-subtle">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-[18px] font-bold text-foreground">
          Latest Regulatory Updates
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          className="text-[14px] hover:bg-muted transition-colors duration-150"
          onClick={() => router.push('/updates')}
        >
          View all updates
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {recentUpdates.map((update, index) => (
          <UpdateCard
            key={index}
            title={update.title}
            agency={update.agency}
            category={update.category}
            publishedDate={update.publishedDate}
            severity={update.severity}
            summary={update.summary}
          />
        ))}
      </CardContent>
    </Card>
  )
}

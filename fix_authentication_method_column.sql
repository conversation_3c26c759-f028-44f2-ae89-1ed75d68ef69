-- Fix authentication_method column to use proper enum type
-- This will make TypeScript types generation accurate

-- Step 1: Check current values in the column
SELECT 
    'CURRENT VALUES' as check_type,
    authentication_method,
    COUNT(*) as count
FROM electronic_signatures 
GROUP BY authentication_method;

-- Step 2: Alter the column to use the proper enum type
ALTER TABLE electronic_signatures 
ALTER COLUMN authentication_method 
TYPE authentication_method 
USING authentication_method::authentication_method;

-- Step 3: Verify the fix worked
SELECT 
    'FIXED COLUMN TYPE' as check_type,
    column_name,
    data_type,
    udt_name,
    column_default
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND column_name = 'authentication_method'
AND table_schema = 'public';

-- Step 4: Test that existing data still works
SELECT 
    'VERIFICATION TEST' as check_type,
    id,
    signature_type,
    authentication_method,
    signer_name,
    signed_at
FROM electronic_signatures 
ORDER BY signed_at DESC 
LIMIT 3;

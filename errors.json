[{"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"analyze_document\" for class \"AIClient\"\n  Attribute \"analyze_document\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 529, "startColumn": 34, "endLineNumber": 529, "endColumn": 50, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"generate_text\" for class \"AIClient\"\n  Attribute \"generate_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 633, "startColumn": 34, "endLineNumber": 633, "endColumn": 47, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"generate_text\" for class \"AIClient\"\n  Attribute \"generate_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 725, "startColumn": 34, "endLineNumber": 725, "endColumn": 47, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"query_vector\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 817, "startColumn": 25, "endLineNumber": 821, "endColumn": 10, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"query\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 818, "startColumn": 13, "endLineNumber": 818, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"n_results\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 819, "startColumn": 13, "endLineNumber": 819, "endColumn": 22, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"filters\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 820, "startColumn": 13, "endLineNumber": 820, "endColumn": 20, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "\"get_fda_metadata_manager\" is unknown import symbol", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 879, "startColumn": 54, "endLineNumber": 879, "endColumn": 78, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"process_all_fda_documents\" for class \"PDFProcessor\"\n  Attribute \"process_all_fda_documents\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 887, "startColumn": 59, "endLineNumber": 887, "endColumn": 84, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"load_fda_hierarchy\" for class \"PDFProcessor\"\n  Attribute \"load_fda_hierarchy\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 891, "startColumn": 58, "endLineNumber": 891, "endColumn": 76, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"chunk_fda_document\" for class \"FDAChunker\"\n  Attribute \"chunk_fda_document\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 899, "startColumn": 42, "endLineNumber": 899, "endColumn": 60, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"vectors\", \"metadata\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 911, "startColumn": 47, "endLineNumber": 911, "endColumn": 76, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_processing_stats\" for class \"PDFProcessor\"\n  Attribute \"get_processing_stats\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 919, "startColumn": 63, "endLineNumber": 919, "endColumn": 83, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_collection_stats\" for class \"VectorStore\"\n  Attribute \"get_collection_stats\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 994, "startColumn": 36, "endLineNumber": 994, "endColumn": 56, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"delete_documents_by_filter\" for class \"VectorStore\"\n  Attribute \"delete_documents_by_filter\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 1061, "startColumn": 49, "endLineNumber": 1061, "endColumn": 75, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"delete_documents_by_filter\" for class \"VectorStore\"\n  Attribute \"delete_documents_by_filter\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 1088, "startColumn": 49, "endLineNumber": 1088, "endColumn": 75, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/models/base.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"errors\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 147, "startColumn": 16, "endLineNumber": 147, "endColumn": 61, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/models/base.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"data\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 152, "startColumn": 16, "endLineNumber": 152, "endColumn": 66, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/models/compliance.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"minimum_content_length\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 93, "startColumn": 71, "endLineNumber": 93, "endColumn": 88, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/models/compliance.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"quality_system\", \"documentation\", \"validation\", \"personnel\", \"facilities\", \"equipment\", \"risk_management\", \"materials\", \"production\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 94, "startColumn": 69, "endLineNumber": 94, "endColumn": 85, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/models/documents.py", "owner": "pyright", "code": {"value": "reportArgumentType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportArgumentType"}}, "severity": 8, "message": "Argument of type \"type[DocumentMetadata]\" cannot be assigned to parameter \"default_factory\" of type \"(() -> _T@Field) | ((dict[str, Any]) -> _T@Field)\" in function \"Field\"\n  Type \"type[DocumentMetadata]\" is not assignable to type \"(() -> _T@Field) | ((dict[str, Any]) -> _T@Field)\"\n    Type \"type[DocumentMetadata]\" is not assignable to type \"(dict[str, Any]) -> _T@Field\"\n      Function accepts too many positional parameters; expected 0 but received 1\n        Extra parameter \"page_count\"\n        Extra parameter \"file_size\"\n        Extra parameter \"checksum\"\n    Type \"type[DocumentMetadata]\" is not assignable to type \"() -> _T@Field\"\n      Extra parameter \"page_count\"\n  ...", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 135, "startColumn": 56, "endLineNumber": 135, "endColumn": 72, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/__init__.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "\"get_vector_store_instance\" is unknown import symbol", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 65, "startColumn": 9, "endLineNumber": 65, "endColumn": 34, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/bge_m3_embeddings.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No overloads for \"Field\" match the provided arguments\n  Argument types: (EllipsisType, Literal[1], Literal[100])", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 46, "startColumn": 24, "endLineNumber": 46, "endColumn": 62, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/bge_m3_embeddings.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"int | None\" is not assignable to return type \"int\"\n  Type \"int | None\" is not assignable to type \"int\"\n    \"None\" is not assignable to \"int\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 223, "startColumn": 16, "endLineNumber": 223, "endColumn": 61, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"filter_conditions\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 151, "startColumn": 23, "endLineNumber": 155, "endColumn": 14, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 253, "startColumn": 17, "endLineNumber": 253, "endColumn": 23, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 264, "startColumn": 17, "endLineNumber": 264, "endColumn": 23, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 275, "startColumn": 17, "endLineNumber": 275, "endColumn": 23, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 286, "startColumn": 17, "endLineNumber": 286, "endColumn": 23, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 297, "startColumn": 17, "endLineNumber": 297, "endColumn": 23, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"memory\" for class \"Agent\"\n  Attribute \"memory\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 601, "startColumn": 45, "endLineNumber": 601, "endColumn": 51, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"title\", \"cfr_title\", \"cfr_part\", \"effective_date\", \"last_updated\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 141, "startColumn": 24, "endLineNumber": 141, "endColumn": 65, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"title\", \"cfr_title\", \"cfr_part\", \"effective_date\", \"last_updated\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 186, "startColumn": 20, "endLineNumber": 186, "endColumn": 61, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 219, "startColumn": 30, "endLineNumber": 225, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 237, "startColumn": 26, "endLineNumber": 243, "endColumn": 14, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 256, "startColumn": 30, "endLineNumber": 262, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 277, "startColumn": 34, "endLineNumber": 283, "endColumn": 22, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 296, "startColumn": 30, "endLineNumber": 302, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"source_section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 317, "startColumn": 34, "endLineNumber": 323, "endColumn": 22, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "\"ToolExecutor\" is unknown import symbol", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 20, "startColumn": 32, "endLineNumber": 20, "endColumn": 44, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to return type \"StateGraph[Unknown, Unknown, Unknown]\"\n  \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to \"StateGraph[Unknown, Unknown, Unknown]\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 181, "startColumn": 16, "endLineNumber": 181, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to return type \"StateGraph[Unknown, Unknown, Unknown]\"\n  \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to \"StateGraph[Unknown, Unknown, Unknown]\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 210, "startColumn": 16, "endLineNumber": 210, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to return type \"StateGraph[Unknown, Unknown, Unknown]\"\n  \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to \"StateGraph[Unknown, Unknown, Unknown]\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 237, "startColumn": 16, "endLineNumber": 237, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to return type \"StateGraph[Unknown, Unknown, Unknown]\"\n  \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to \"StateGraph[Unknown, Unknown, Unknown]\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 262, "startColumn": 16, "endLineNumber": 262, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportReturnType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportReturnType"}}, "severity": 8, "message": "Type \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to return type \"StateGraph[Unknown, Unknown, Unknown]\"\n  \"CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]\" is not assignable to \"StateGraph[Unknown, Unknown, Unknown]\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 287, "startColumn": 16, "endLineNumber": 287, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"ainvoke\" for class \"StateGraph[Unknown, Unknown, Unknown]\"\n  Attribute \"ainvoke\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 345, "startColumn": 48, "endLineNumber": 345, "endColumn": 55, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_text\" for class \"Page\"\n  Attribute \"get_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 103, "startColumn": 29, "endLineNumber": 103, "endColumn": 37, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 112, "startColumn": 32, "endLineNumber": 112, "endColumn": 35, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 113, "startColumn": 33, "endLineNumber": 113, "endColumn": 36, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 114, "startColumn": 34, "endLineNumber": 114, "endColumn": 37, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 115, "startColumn": 34, "endLineNumber": 115, "endColumn": 37, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 116, "startColumn": 35, "endLineNumber": 116, "endColumn": 38, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 117, "startColumn": 40, "endLineNumber": 117, "endColumn": 43, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 118, "startColumn": 44, "endLineNumber": 118, "endColumn": 47, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_text\" for class \"Page\"\n  Attribute \"get_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 143, "startColumn": 29, "endLineNumber": 143, "endColumn": 37, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_text\" for class \"Page\"\n  Attribute \"get_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 145, "startColumn": 29, "endLineNumber": 145, "endColumn": 37, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get_collections\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 202, "startColumn": 35, "endLineNumber": 202, "endColumn": 50, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"document_id\", \"title\", \"source\", \"document_type\", \"chunk_index\", \"chunk_text\", \"file_path\", \"page_number\", \"section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 358, "startColumn": 28, "endLineNumber": 358, "endColumn": 59, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument expression after ** must be a mapping with a \"str\" key type", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 358, "startColumn": 47, "endLineNumber": 358, "endColumn": 58, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py", "owner": "pyright", "code": {"value": "reportArgumentType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportArgumentType"}}, "severity": 8, "message": "Argument of type \"List[str]\" cannot be assigned to parameter \"points\" of type \"List[ExtendedPointId]\" in function \"__init__\"\n  \"List[str]\" is not assignable to \"List[ExtendedPointId]\"\n    Type parameter \"_T@list\" is invariant, but \"str\" is not the same as \"ExtendedPointId\"\n    Consider switching from \"list\" to \"Sequence\" which is covariant", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 397, "startColumn": 53, "endLineNumber": 397, "endColumn": 62, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/rag_pipeline.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"score_threshold\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 393, "startColumn": 30, "endLineNumber": 397, "endColumn": 14, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/rag_pipeline.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"filter_conditions\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 469, "startColumn": 26, "endLineNumber": 469, "endColumn": 80, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"storage_path\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 80, "startColumn": 17, "endLineNumber": 80, "endColumn": 29, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"use_memory\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 84, "startColumn": 17, "endLineNumber": 84, "endColumn": 27, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"initialize\" for class \"QdrantVectorStore\"\n  Attribute \"initialize\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 89, "startColumn": 38, "endLineNumber": 89, "endColumn": 48, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"add_documents\" for class \"QdrantVectorStore\"\n  Attribute \"add_documents\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 115, "startColumn": 48, "endLineNumber": 115, "endColumn": 61, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"request\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 143, "startColumn": 29, "endLineNumber": 148, "endColumn": 14, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"query_vector\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 144, "startColumn": 17, "endLineNumber": 144, "endColumn": 29, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"limit\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 145, "startColumn": 17, "endLineNumber": 145, "endColumn": 22, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"score_threshold\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 146, "startColumn": 17, "endLineNumber": 146, "endColumn": 32, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"filter_conditions\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 147, "startColumn": 17, "endLineNumber": 147, "endColumn": 34, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"delete_documents\" for class \"QdrantVectorStore\"\n  Attribute \"delete_documents\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 174, "startColumn": 48, "endLineNumber": 174, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"close\" for class \"QdrantVectorStore\"\n  Attribute \"close\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 197, "startColumn": 38, "endLineNumber": 197, "endColumn": 43, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportArgumentType", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportArgumentType"}}, "severity": 8, "message": "Argument of type \"float\" cannot be assigned to parameter \"timeout\" of type \"int | None\" in function \"__init__\"\n  Type \"float\" is not assignable to type \"int | None\"\n    \"float\" is not assignable to \"int\"\n    \"float\" is not assignable to \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 84, "startColumn": 21, "endLineNumber": 84, "endColumn": 25, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_text\" for class \"Page\"\n  Attribute \"get_text\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 270, "startColumn": 35, "endLineNumber": 270, "endColumn": 43, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"size\" for class \"Dict[StrictStr, VectorParams]\"\n  Attribute \"size\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 430, "startColumn": 70, "endLineNumber": 430, "endColumn": 74, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"size\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 430, "startColumn": 70, "endLineNumber": 430, "endColumn": 74, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"distance\" for class \"Dict[StrictStr, VectorParams]\"\n  Attribute \"distance\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 431, "startColumn": 74, "endLineNumber": 431, "endColumn": 82, "extensionID": "anysphere.cursorpyright"}, {"resource": "/d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"distance\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 431, "startColumn": 74, "endLineNumber": 431, "endColumn": 82, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get_collections\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 78, "startColumn": 39, "endLineNumber": 78, "endColumn": 54, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"create_collection\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 85, "startColumn": 25, "endLineNumber": 85, "endColumn": 42, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"upsert\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 154, "startColumn": 25, "endLineNumber": 154, "endColumn": 31, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"search\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 160, "startColumn": 41, "endLineNumber": 160, "endColumn": 47, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"scroll\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 167, "startColumn": 41, "endLineNumber": 167, "endColumn": 47, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"delete\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 181, "startColumn": 25, "endLineNumber": 181, "endColumn": 31, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"get_collection\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 204, "startColumn": 43, "endLineNumber": 204, "endColumn": 57, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"count\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 205, "startColumn": 40, "endLineNumber": 205, "endColumn": 45, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"size\" for class \"Dict[StrictStr, VectorParams]\"\n  Attribute \"size\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 209, "startColumn": 70, "endLineNumber": 209, "endColumn": 74, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"size\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 209, "startColumn": 70, "endLineNumber": 209, "endColumn": 74, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"distance\" for class \"Dict[StrictStr, VectorParams]\"\n  Attribute \"distance\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 210, "startColumn": 74, "endLineNumber": 210, "endColumn": 82, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py", "owner": "pyright", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportOptionalMemberAccess"}}, "severity": 8, "message": "\"distance\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 210, "startColumn": 74, "endLineNumber": 210, "endColumn": 82, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"NOT_CONNECTED\" for class \"type[ConnectionStatus]\"\n  Attribute \"NOT_CONNECTED\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 151, "startColumn": 56, "endLineNumber": 151, "endColumn": 69, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Arguments missing for parameters \"title\", \"document_type\", \"chunk_index\", \"file_path\", \"page_number\", \"section\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 167, "startColumn": 28, "endLineNumber": 172, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "No parameter named \"chunk_id\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 169, "startColumn": 21, "endLineNumber": 169, "endColumn": 29, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"filter_conditions\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 199, "startColumn": 34, "endLineNumber": 203, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportAttributeAccessIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportAttributeAccessIssue"}}, "severity": 8, "message": "Cannot access attribute \"get_service_status\" for class \"BGE_M3_EmbeddingService\"\n  Attribute \"get_service_status\" is unknown", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 273, "startColumn": 46, "endLineNumber": 273, "endColumn": 64, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"file_path\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 339, "startColumn": 29, "endLineNumber": 344, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}, {"resource": "/D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py", "owner": "pyright", "code": {"value": "reportCallIssue", "target": {"$mid": 1, "path": "/v1.29.4/configuration/config-files/", "scheme": "https", "authority": "docs.basedpyright.com", "fragment": "reportCallIssue"}}, "severity": 8, "message": "Argument missing for parameter \"filter_conditions\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 365, "startColumn": 33, "endLineNumber": 369, "endColumn": 18, "extensionID": "anysphere.cursorpyright"}]
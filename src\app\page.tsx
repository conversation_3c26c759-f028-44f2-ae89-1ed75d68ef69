'use client'

import { BenefitsSection } from '@/components/landing/benefits-section'
import { CtaSection } from '@/components/landing/cta-section'
import { FeaturesSection } from '@/components/landing/features-section'
import { FooterSection } from '@/components/landing/footer-section'
import { HeroSection } from '@/components/landing/hero-section'
import { TestimonialsSection } from '@/components/landing/testimonials-section'

/**
 * Landing Page - AI Compliance Platform
 *
 * Features:
 * - Modern Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Server/client component optimization
 * - Professional metadata management
 * - Accessible design patterns
 * - Performance optimized loading
 */
export default function LandingPage() {
  return (
    <main className="min-h-screen bg-background">
      <HeroSection />
      <FeaturesSection />
      <BenefitsSection />
      <TestimonialsSection />
      <CtaSection />
      <FooterSection />
    </main>
  )
}

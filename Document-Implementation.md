
## Documents Section - Hyper-Detailed UX/UI Specification

---

## 🏗️ **EXPERT 1: LAYOUT & INFORMATION ARCHITECTURE SPECIALIST**

### **Page Structure & Hierarchy**
```typescript
// Container Layout Specifications
const PageContainer = {
  wrapper: "DashboardLayout", // Sidebar + Header + Main
  mainContent: {
    container: "mx-auto p-6", // 24px padding all sides
    maxWidth: "none", // Full width within container
    spacing: "space-y-6", // 24px vertical spacing between sections
    background: "bg-background" // CSS variable: hsl(var(--background))
  }
}
```

### **Section Breakdown (Top to Bottom)**

#### **1. Page Header Section**
```css
/* Exact Layout */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px; /* space-y-6 */
}

.header-content {
  flex-direction: column;
  align-items: flex-start;
}

.header-title {
  font-size: 1.875rem; /* text-3xl */
  font-weight: 700; /* font-bold */
  line-height: 2.25rem;
  color: hsl(var(--foreground));
  margin: 0;
}

.header-subtitle {
  font-size: 0.875rem; /* text-sm */
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem; /* mt-1 */
  max-width: 600px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
}
```

#### **2. Metrics Cards Section**
```css
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr)); /* mobile */
  gap: 1rem; /* gap-4 */
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .metrics-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr)); /* md:grid-cols-4 */
  }
}

.metric-card {
  padding: 1rem; /* p-4 */
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}

.metric-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-value {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700; /* font-bold */
  color: hsl(var(--foreground));
  line-height: 2rem;
}

.metric-label {
  font-size: 0.875rem; /* text-sm */
  color: hsl(var(--muted-foreground));
  margin-top: 0;
}

.metric-icon {
  height: 2rem; /* h-8 */
  width: 2rem; /* w-8 */
  color: hsl(var(--primary));
}
```

#### **3. Search & Filters Section**
```css
.search-filters-card {
  padding: 1.5rem; /* p-6 */
  margin-bottom: 24px;
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}

.search-filters-container {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* gap-4 */
}

@media (min-width: 1024px) {
  .search-filters-container {
    flex-direction: row; /* lg:flex-row */
  }
}

.search-input-wrapper {
  flex: 1; /* flex-1 */
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem; /* left-3 */
  top: 50%;
  transform: translateY(-50%);
  height: 1rem; /* h-4 */
  width: 1rem; /* w-4 */
  color: hsl(var(--muted-foreground));
}

.search-input {
  width: 100%;
  padding-left: 2.5rem; /* pl-10 */
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: calc(var(--radius) - 4px);
  border: 1px solid hsl(var(--border));
  background: hsl(var(--background));
  font-size: 0.875rem;
}

.filters-group {
  display: flex;
  gap: 0.5rem; /* gap-2 */
  align-items: center;
}

.view-toggle-group {
  display: flex;
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 4px);
  overflow: hidden;
}
```

#### **4. Tabs Navigation**
```css
.tabs-wrapper {
  margin-bottom: 1rem; /* space-y-4 */
}

.tabs-list {
  display: inline-flex;
  height: 2.5rem; /* h-10 */
  align-items: center;
  justify-content: center;
  border-radius: calc(var(--radius) - 4px);
  background: hsl(var(--muted));
  padding: 0.25rem; /* p-1 */
  color: hsl(var(--muted-foreground));
}

.tab-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: calc(var(--radius) - 6px);
  padding: 0.375rem 0.75rem; /* px-3 py-1.5 */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  transition: all 0.2s;
}

.tab-trigger[data-state="active"] {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}
```

#### **5. Documents Display Area**
```css
.documents-container {
  min-height: 400px;
}

/* Grid View */
.documents-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr)); /* mobile */
  gap: 1.5rem; /* gap-6 */
}

@media (min-width: 768px) {
  .documents-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr)); /* md:grid-cols-2 */
  }
}

@media (min-width: 1024px) {
  .documents-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)); /* lg:grid-cols-3 */
  }
}

/* List View */
.documents-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}
```

---

## 🎨 **EXPERT 2: VISUAL DESIGN & TYPOGRAPHY SPECIALIST**

### **Color System Implementation**
```css
:root {
  /* Primary Color Palette */
  --primary: 262.1 83.3% 57.8%; /* Purple primary */
  --primary-foreground: 355.7 100% 97.3%;

  /* Status Colors */
  --success: 142 76% 36%; /* Green for completed */
  --success-foreground: 355.7 100% 97.3%;
  --warning: 32 95% 44%; /* Orange for needs review */
  --warning-foreground: 355.7 100% 97.3%;
  --info: 221.2 83.2% 53.3%; /* Blue for processing */
  --info-foreground: 355.7 100% 97.3%;

  /* Neutral Palette */
  --background: 0 0% 100%; /* White */
  --foreground: 222.2 84% 4.9%; /* Near black */
  --muted: 210 40% 98%; /* Light gray */
  --muted-foreground: 215.4 16.3% 46.9%; /* Medium gray */
  --border: 214.3 31.8% 91.4%; /* Light border */
  --card: 0 0% 100%; /* White card background */
  --card-foreground: 222.2 84% 4.9%;
}
```

### **Typography Scale**
```css
.typography-scale {
  /* Page Title */
  --text-3xl: 1.875rem; /* 30px */
  --line-height-3xl: 2.25rem; /* 36px */

  /* Card Titles */
  --text-lg: 1.125rem; /* 18px */
  --line-height-lg: 1.75rem; /* 28px */

  /* Body Text */
  --text-sm: 0.875rem; /* 14px */
  --line-height-sm: 1.25rem; /* 20px */

  /* Captions */
  --text-xs: 0.75rem; /* 12px */
  --line-height-xs: 1rem; /* 16px */

  /* Metrics */
  --text-2xl: 1.5rem; /* 24px */
  --line-height-2xl: 2rem; /* 32px */
}

.font-weights {
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### **Document Card Visual Specifications**
```css
.document-card {
  border-radius: calc(var(--radius) - 2px); /* 6px */
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%; /* Full height for grid alignment */
}

.document-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  border-color: hsl(var(--primary) / 0.2);
  transform: translateY(-1px);
}

.card-header {
  padding: 0.75rem 1rem 0.5rem 1rem; /* Custom padding */
  border-bottom: none;
}

.card-content {
  padding: 0 1rem 1rem 1rem; /* Custom padding */
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.document-icon {
  height: 2rem; /* 32px */
  width: 2rem; /* 32px */
  color: hsl(var(--primary));
  flex-shrink: 0;
}

.document-title {
  font-size: 0.875rem; /* 14px */
  font-weight: 600; /* semibold */
  color: hsl(var(--foreground));
  line-height: 1.25rem; /* 20px, tight leading */
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.document-meta {
  font-size: 0.75rem; /* 12px */
  color: hsl(var(--muted-foreground));
  margin-top: 0.125rem; /* 2px */
}

.document-description {
  font-size: 0.75rem; /* 12px */
  color: hsl(var(--muted-foreground));
  line-height: 1rem; /* 16px */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
  margin: 0.75rem 0; /* 12px vertical */
}
```

### **Badge Component Styling**
```css
.badge-base {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px; /* Fully rounded */
  padding: 0.125rem 0.625rem; /* py-0.5 px-2.5 */
  font-size: 0.75rem; /* text-xs */
  font-weight: 600; /* font-semibold */
  line-height: 1rem;
  transition: colors 0.2s;
}

.badge-success {
  background: hsl(var(--success));
  color: hsl(var(--success-foreground));
}

.badge-warning {
  background: hsl(var(--warning));
  color: hsl(var(--warning-foreground));
}

.badge-info {
  background: hsl(var(--info));
  color: hsl(var(--info-foreground));
}

.badge-outline {
  background: transparent;
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

.badge-icon {
  margin-right: 0.25rem; /* mr-1 */
  height: 0.75rem; /* h-3 */
  width: 0.75rem; /* w-3 */
}
```

### **Progress Bar Styling**
```css
.progress-container {
  position: relative;
  overflow: hidden;
  background: hsl(var(--secondary));
  border-radius: 9999px;
  height: 0.25rem; /* h-1 for small, h-2 for normal */
  width: 100%;
}

.progress-indicator {
  height: 100%;
  width: var(--progress-value, 0%);
  background: hsl(var(--primary));
  border-radius: 9999px;
  transition: width 0.3s ease;
}

.progress-score-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem; /* text-xs */
  margin-bottom: 0.5rem; /* mb-2 */
}

.progress-score-label {
  color: hsl(var(--muted-foreground));
}

.progress-score-value {
  color: hsl(var(--foreground));
  font-weight: 500; /* font-medium */
}
```

---

## ⚡ **EXPERT 3: INTERACTION DESIGN SPECIALIST**

### **State Management Architecture**
```typescript
interface DocumentsState {
  searchQuery: string;
  selectedCategory: 'all' | 'Approved' | 'Under Review' | 'Draft' | 'Template';
  selectedStatus: 'all' | 'completed' | 'processing' | 'needs_review';
  viewMode: 'grid' | 'list';
  activeTab: 'my-documents' | 'shared' | 'recent' | 'templates';
  documents: Document[];
  filteredDocuments: Document[];
  isLoading: boolean;
  selectedDocuments: number[];
}

interface Document {
  id: number;
  name: string;
  type: 'PDF' | 'DOCX' | 'XLSX' | 'TXT';
  size: string;
  uploadDate: string;
  category: 'Approved' | 'Under Review' | 'Draft' | 'Template';
  status: 'completed' | 'processing' | 'needs_review';
  complianceScore: number;
  tags: string[];
  description: string;
  author?: string;
  lastModified?: string;
  permissions?: string[];
}
```

### **Interaction Patterns**

#### **1. Search Functionality**
```typescript
const searchBehavior = {
  debounceTime: 300, // ms
  minSearchLength: 0, // Search on every keystroke
  searchFields: ['name', 'description', 'tags'],
  caseSensitive: false,

  onSearchChange: (query: string) => {
    // Immediate visual feedback
    setSearchQuery(query);

    // Debounced filter execution
    debounce(() => {
      filterDocuments(query, selectedCategory, selectedStatus);
    }, 300);
  },

  placeholder: "Search documents by name, type, or content...",
  clearable: true,
  searchIcon: {
    position: 'left',
    size: 16,
    color: 'muted-foreground'
  }
};
```

#### **2. Filter Interactions**
```typescript
const filterBehavior = {
  categoryFilter: {
    type: 'dropdown',
    defaultValue: 'all',
    options: [
      { value: 'all', label: 'All Types' },
      { value: 'Approved', label: 'Approved' },
      { value: 'Under Review', label: 'Under Review' },
      { value: 'Draft', label: 'Draft' },
      { value: 'Template', label: 'Template' }
    ],
    width: '150px',
    onChange: (value) => {
      setSelectedCategory(value);
      filterDocuments(searchQuery, value, selectedStatus);
    }
  },

  statusFilter: {
    type: 'dropdown',
    defaultValue: 'all',
    options: [
      { value: 'all', label: 'All Status' },
      { value: 'completed', label: 'Completed' },
      { value: 'processing', label: 'Processing' },
      { value: 'needs_review', label: 'Needs Review' }
    ],
    width: '150px',
    onChange: (value) => {
      setSelectedStatus(value);
      filterDocuments(searchQuery, selectedCategory, value);
    }
  }
};
```

#### **3. View Mode Toggle**
```typescript
const viewToggleBehavior = {
  modes: ['grid', 'list'],
  defaultMode: 'grid',
  persistToLocalStorage: true,

  toggleButton: {
    variant: 'connected', // Connected button group
    size: 'sm',
    icons: {
      grid: 'Grid',
      list: 'List'
    },
    activeStyle: 'filled',
    inactiveStyle: 'ghost'
  },

  animations: {
    transition: 'all 0.2s ease',
    viewChange: 'fade-in 0.3s ease'
  }
};
```

#### **4. Document Card Interactions**
```typescript
const cardInteractions = {
  hover: {
    elevation: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    borderColor: 'hsl(var(--primary) / 0.2)',
    transform: 'translateY(-1px)',
    transition: 'all 0.2s ease'
  },

  click: {
    action: 'navigate', // or 'preview' or 'select'
    feedback: 'ripple-effect',
    timing: 150 // ms
  },

  actionButtons: {
    view: {
      icon: 'Eye',
      tooltip: 'Preview document',
      action: () => openPreview(document.id),
      size: 'sm',
      variant: 'ghost'
    },
    download: {
      icon: 'Download',
      tooltip: 'Download document',
      action: () => downloadDocument(document.id),
      size: 'sm',
      variant: 'ghost'
    },
    share: {
      icon: 'Share',
      tooltip: 'Share document',
      action: () => openShareDialog(document.id),
      size: 'sm',
      variant: 'ghost'
    }
  }
};
```

#### **5. Tab Navigation**
```typescript
const tabBehavior = {
  defaultTab: 'my-documents',
  persistActiveTab: true,

  tabs: [
    {
      value: 'my-documents',
      label: 'My Documents',
      badge: null, // Optional count badge
      content: 'DocumentGrid'
    },
    {
      value: 'shared',
      label: 'Shared with Me',
      badge: 3, // Unread count
      content: 'EmptyState'
    },
    {
      value: 'recent',
      label: 'Recent',
      badge: null,
      content: 'EmptyState'
    },
    {
      value: 'templates',
      label: 'Templates',
      badge: null,
      content: 'EmptyState'
    }
  ],

  onChange: (tabValue) => {
    setActiveTab(tabValue);
    // Load tab-specific data if needed
    loadTabData(tabValue);
  }
};
```

#### **6. Loading & Error States**
```typescript
const loadingStates = {
  initial: {
    skeleton: 'card-grid', // Show skeleton cards
    count: 6, // Number of skeleton items
    animation: 'pulse'
  },

  filtering: {
    overlay: false, // Don't overlay, update in place
    transition: 'fade-out-in',
    duration: 200
  },

  error: {
    retry: true,
    message: 'Failed to load documents',
    action: 'Retry',
    icon: 'AlertCircle'
  }
};
```

---

## 🧩 **EXPERT 4: COMPONENT SYSTEMS SPECIALIST**

### **Document Card Component Specification**
```typescript
interface DocumentCardProps {
  document: Document;
  viewMode: 'grid' | 'list';
  onView: (id: number) => void;
  onDownload: (id: number) => void;
  onShare: (id: number) => void;
  onSelect?: (id: number, selected: boolean) => void;
  isSelected?: boolean;
  showActions?: boolean;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  viewMode,
  onView,
  onDownload,
  onShare,
  onSelect,
  isSelected = false,
  showActions = true
}) => {
  return (
    <Card className={cn(
      "transition-all duration-200 cursor-pointer flex flex-col h-full",
      "hover:shadow-md hover:border-primary/20 hover:-translate-y-1",
      isSelected && "ring-2 ring-primary ring-offset-2"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <FileText className="h-8 w-8 text-primary flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-foreground text-sm leading-tight line-clamp-2">
                {document.name}
              </h3>
              <p className="text-xs text-muted-foreground mt-0.5">
                {document.type} • {document.size}
              </p>
            </div>
          </div>
          {onSelect && (
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => onSelect(document.id, checked as boolean)}
              className="ml-2"
            />
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-3 flex-grow flex flex-col pt-0">
        {/* Status and Category Badges */}
        <div className="flex items-center justify-between">
          <StatusBadge status={document.status} />
          <Badge variant="outline">{document.category}</Badge>
        </div>

        {/* Compliance Score */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Compliance Score</span>
            <span className="text-foreground font-medium">{document.complianceScore}%</span>
          </div>
          <Progress value={document.complianceScore} className="h-1" />
        </div>

        {/* Description */}
        <p className="text-xs text-muted-foreground line-clamp-2 flex-grow">
          {document.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {document.tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
              {tag}
            </Badge>
          ))}
          {document.tags.length > 2 && (
            <Badge variant="outline" className="text-xs px-2 py-0.5">
              +{document.tags.length - 2}
            </Badge>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-2 mt-auto border-t border-border/50">
          <span className="text-xs text-muted-foreground">
            {formatDate(document.uploadDate)}
          </span>

          {showActions && (
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onView(document.id);
                }}
              >
                <Eye className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onDownload(document.id);
                }}
              >
                <Download className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onShare(document.id);
                }}
              >
                <Share className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
```

### **Status Badge Component**
```typescript
interface StatusBadgeProps {
  status: 'completed' | 'processing' | 'needs_review';
  size?: 'sm' | 'md';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, size = 'sm' }) => {
  const configs = {
    completed: {
      className: "bg-success text-success-foreground",
      icon: CheckCircle,
      label: "Complete"
    },
    processing: {
      className: "bg-info text-info-foreground",
      icon: Clock,
      label: "Processing"
    },
    needs_review: {
      className: "bg-warning text-warning-foreground",
      icon: AlertCircle,
      label: "Needs Review"
    }
  };

  const config = configs[status];
  const Icon = config.icon;

  return (
    <Badge className={cn(config.className, size === 'sm' && "text-xs")}>
      <Icon className={cn("mr-1", size === 'sm' ? "h-3 w-3" : "h-4 w-4")} />
      {config.label}
    </Badge>
  );
};
```

### **Metrics Card Component**
```typescript
interface MetricCardProps {
  label: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  label,
  value,
  icon: Icon,
  trend,
  onClick
}) => {
  return (
    <Card
      className={cn(
        "transition-all duration-200",
        onClick && "cursor-pointer hover:shadow-md hover:border-primary/20"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-2xl font-bold text-foreground leading-none">
              {value}
            </p>
            <p className="text-sm text-muted-foreground">
              {label}
            </p>
            {trend && (
              <div className={cn(
                "flex items-center text-xs",
                trend.isPositive ? "text-success" : "text-destructive"
              )}>
                <TrendIcon isPositive={trend.isPositive} />
                <span className="ml-1">{Math.abs(trend.value)}%</span>
              </div>
            )}
          </div>
          <Icon className="h-8 w-8 text-primary" />
        </div>
      </CardContent>
    </Card>
  );
};
```

### **Search Input Component**
```typescript
interface DocumentSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  debounceTime?: number;
}

const DocumentSearch: React.FC<DocumentSearchProps> = ({
  value,
  onChange,
  placeholder = "Search documents by name, type, or content...",
  debounceTime = 300
}) => {
  const [searchQuery, setSearchQuery] = useState(value);
  const debouncedOnChange = useMemo(
    () => debounce(onChange, debounceTime),
    [onChange, debounceTime]
  );

  useEffect(() => {
    debouncedOnChange(searchQuery);
  }, [searchQuery, debouncedOnChange]);

  useEffect(() => {
    setSearchQuery(value);
  }, [value]);

  return (
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="pl-10 h-10"
      />
      {searchQuery && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
          onClick={() => {
            setSearchQuery('');
            onChange('');
          }}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};
```

### **View Mode Toggle Component**
```typescript
interface ViewModeToggleProps {
  value: 'grid' | 'list';
  onChange: (mode: 'grid' | 'list') => void;
}

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({ value, onChange }) => {
  return (
    <div className="flex border rounded-md overflow-hidden">
      <Button
        variant={value === 'grid' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onChange('grid')}
        className="rounded-r-none border-r-0"
      >
        <Grid className="h-4 w-4" />
      </Button>
      <Button
        variant={value === 'list' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onChange('list')}
        className="rounded-l-none"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );
};
```

---

## 📊 **EXPERT 5: DATA VISUALIZATION SPECIALIST**

### **Document Grid Layout Algorithm**
```typescript
const GridLayoutManager = {
  calculateColumns: (containerWidth: number) => {
    // Responsive breakpoints
    if (containerWidth < 768) return 1; // Mobile
    if (containerWidth < 1024) return 2; // Tablet
    return 3; // Desktop
  },

  cardDimensions: {
    minWidth: 320, // Minimum card width
    maxWidth: 400, // Maximum card width
    aspectRatio: null, // Auto height based on content
    gap: 24 // Gap between cards
  },

  calculateOptimalWidth: (containerWidth: number, columns: number) => {
    const availableWidth = containerWidth - (columns - 1) * 24; // Account for gaps
    return Math.min(400, Math.max(320, availableWidth / columns));
  }
};
```

### **Progress Visualization**
```typescript
interface ProgressVisualizationProps {
  value: number; // 0-100
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  animate?: boolean;
}

const ProgressVisualization: React.FC<ProgressVisualizationProps> = ({
  value,
  size = 'md',
  showLabel = true,
  animate = true
}) => {
  const sizeMap = {
    sm: { height: 4, fontSize: '0.75rem' },
    md: { height: 6, fontSize: '0.875rem' },
    lg: { height: 8, fontSize: '1rem' }
  };

  const config = sizeMap[size];

  return (
    <div className="space-y-2">
      {showLabel && (
        <div className="flex justify-between items-center">
          <span className={cn("text-muted-foreground", `text-[${config.fontSize}]`)}>
            Compliance Score
          </span>
          <span className={cn("font-medium text-foreground", `text-[${config.fontSize}]`)}>
            {value}%
          </span>
        </div>
      )}

      <div
        className="relative overflow-hidden bg-secondary rounded-full"
        style={{ height: config.height }}
      >
        <div
          className={cn(
            "h-full bg-primary rounded-full transition-all",
            animate && "duration-500 ease-out"
          )}
          style={{ width: `${value}%` }}
        />
      </div>
    </div>
  );
};
```

### **Document Statistics Display**
```typescript
interface DocumentStatsProps {
  documents: Document[];
  className?: string;
}

const DocumentStats: React.FC<DocumentStatsProps> = ({ documents, className }) => {
  const stats = useMemo(() => {
    const total = documents.length;
    const completed = documents.filter(d => d.status === 'completed').length;
    const processing = documents.filter(d => d.status === 'processing').length;
    const needsReview = documents.filter(d => d.status === 'needs_review').length;
    const avgCompliance = documents.reduce((sum, d) => sum + d.complianceScore, 0) / total;

    return {
      total,
      completed,
      processing,
      needsReview,
      avgCompliance: Math.round(avgCompliance)
    };
  }, [documents]);

  const metrics = [
    {
      label: "Total Documents",
      value: stats.total.toString(),
      icon: FileText,
      color: "text-primary"
    },
    {
      label: "Pending Review",
      value: stats.needsReview.toString(),
      icon: Clock,
      color: "text-warning"
    },
    {
      label: "Compliance Score",
      value: `${stats.avgCompliance}%`,
      icon: CheckCircle,
      color: "text-success"
    },
    {
      label: "Recent Uploads",
      value: "7", // This would come from a different calculation
      icon: Upload,
      color: "text-info"
    }
  ];

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-4 gap-4", className)}>
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};
```

### **Empty State Visualizations**
```typescript
interface EmptyStateProps {
  type: 'shared' | 'recent' | 'templates' | 'search';
  onAction?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ type, onAction }) => {
  const configs = {
    shared: {
      icon: Share,
      title: "Shared Documents",
      description: "Documents shared with you by team members will appear here.",
      action: null
    },
    recent: {
      icon: Calendar,
      title: "Recent Documents",
      description: "Your recently accessed documents will be shown here.",
      action: null
    },
    templates: {
      icon: FileText,
      title: "Document Templates",
      description: "Pre-approved document templates for compliance documentation.",
      action: { label: "Browse Templates", onClick: onAction }
    },
    search: {
      icon: Search,
      title: "No documents found",
      description: "Try adjusting your search criteria or filters.",
      action: { label: "Clear Filters", onClick: onAction }
    }
  };

  const config = configs[type];
  const Icon = config.icon;

  return (
    <div className="text-center py-12">
      <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2 text-foreground">
        {config.title}
      </h3>
      <p className="text-muted-foreground max-w-md mx-auto mb-6">
        {config.description}
      </p>
      {config.action && (
        <Button onClick={config.action.onClick} variant="outline">
          {config.action.label}
        </Button>
      )}
    </div>
  );
};
```

---

## ♿ **EXPERT 6: ACCESSIBILITY & PERFORMANCE SPECIALIST**

### **Accessibility Implementation**
```typescript
const AccessibilityFeatures = {
  // ARIA Labels and Roles
  semanticMarkup: {
    main: 'role="main" aria-label="Document Library"',
    search: 'role="search" aria-label="Search documents"',
    tablist: 'role="tablist" aria-label="Document categories"',
    grid: 'role="grid" aria-label="Documents grid view"',
    table: 'role="table" aria-label="Documents table view"'
  },

  // Keyboard Navigation
  keyboardSupport: {
    searchInput: {
      escape: 'clearSearch',
      arrowDown: 'focusFirstResult',
      enter: 'submitSearch'
    },
    documentCards: {
      enter: 'openDocument',
      space: 'selectDocument',
      delete: 'deleteDocument (if permitted)',
      tab: 'navigateToNextCard'
    },
    filters: {
      arrowDown: 'openDropdown',
      arrowUp: 'openDropdown',
      escape: 'closeDropdown'
    }
  },

  // Screen Reader Support
  ariaDescriptions: {
    complianceScore: (score: number) =>
      `Compliance score ${score} percent out of 100`,
    documentStatus: (status: string) =>
      `Document status: ${status.replace('_', ' ')}`,
    uploadDate: (date: string) =>
      `Uploaded on ${formatDateForScreenReader(date)}`
  },

  // Focus Management
  focusManagement: {
    trapFocus: 'within modals and dropdowns',
    restoreFocus: 'after modal close',
    skipLinks: 'to main content and document list',
    focusVisible: 'keyboard navigation indicators'
  }
};
```

### **Performance Optimizations**
```typescript
const PerformanceOptimizations = {
  // Virtual Scrolling for Large Lists
  virtualScrolling: {
    enabled: true,
    itemHeight: 180, // Estimated card height
    bufferSize: 5, // Items to render outside viewport
    threshold: 100 // Enable when more than 100 items
  },

  // Lazy Loading
  lazyLoading: {
    images: 'native lazy loading for document thumbnails',
    components: 'React.lazy for tab content',
    data: 'load additional documents on scroll'
  },

  // Memoization Strategy
  memoization: {
    documentCards: 'React.memo with deep comparison',
    filteredResults: 'useMemo for expensive filtering',
    statusBadges: 'memo by status value',
    searchResults: 'debounced search with cache'
  },

  // Caching Strategy
  caching: {
    searchResults: 'LRU cache with 50 item limit',
    documentData: 'SWR with 5 minute stale time',
    userPreferences: 'localStorage persistence',
    apiResponses: 'React Query with background refetch'
  }
};
```

### **Responsive Design Specifications**
```css
/* Mobile First Approach */
.responsive-layout {
  /* Base Mobile Styles (320px+) */
  container: {
    padding: '1rem', /* 16px */
    maxWidth: '100%'
  },

  grid: {
    columns: 1,
    gap: '1rem' /* 16px */
  },

  searchBar: {
    flexDirection: 'column',
    gap: '0.75rem' /* 12px */
  }
}

/* Tablet Styles (768px+) */
@media (min-width: 768px) {
  .responsive-layout {
    container: {
      padding: '1.5rem', /* 24px */
    },

    grid: {
      columns: 2,
      gap: '1.5rem' /* 24px */
    },

    searchBar: {
      flexDirection: 'row',
      gap: '1rem' /* 16px */
    }
  }
}

/* Desktop Styles (1024px+) */
@media (min-width: 1024px) {
  .responsive-layout {
    container: {
      padding: '2rem', /* 32px */
    },

    grid: {
      columns: 3,
      gap: '1.5rem' /* 24px */
    }
  }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .responsive-layout {
    grid: {
      columns: 4,
      gap: '2rem' /* 32px */
    }
  }
}
```

### **Error Handling & Loading States**
```typescript
const ErrorAndLoadingStates = {
  // Loading Skeletons
  skeletonComponents: {
    documentCard: `
      <div class="animate-pulse">
        <div class="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div class="h-3 bg-muted rounded w-1/2 mb-4"></div>
        <div class="h-2 bg-muted rounded mb-2"></div>
        <div class="h-16 bg-muted rounded"></div>
      </div>
    `,
    searchBar: `
      <div class="animate-pulse h-10 bg-muted rounded"></div>
    `,
    metrics: `
      <div class="animate-pulse">
        <div class="h-8 bg-muted rounded w-16 mb-1"></div>
        <div class="h-4 bg-muted rounded w-24"></div>
      </div>
    `
  },

  // Error Boundaries
  errorBoundaries: {
    documentGrid: 'Catches rendering errors in document cards',
    searchBar: 'Handles search functionality errors',
    filters: 'Manages filter operation failures'
  },

  // Network Error Handling
  networkErrors: {
    retry: 'Automatic retry with exponential backoff',
    offline: 'Show offline indicator and cached content',
    timeout: 'Show timeout message with manual retry'
  }
};
```

---

## 🎯 **EXACT IMPLEMENTATION SPECIFICATIONS**

### **Document Data Structure & State Management**
```typescript
// Complete Document Interface
interface Document {
  id: number;
  name: string;
  type: 'PDF' | 'DOCX' | 'XLSX' | 'TXT' | 'PPT' | 'CSV';
  size: string; // e.g., "2.4 MB"
  sizeBytes: number; // For sorting
  uploadDate: string; // ISO date string
  lastModified: string; // ISO date string
  category: 'Approved' | 'Under Review' | 'Draft' | 'Template' | 'Archived';
  status: 'completed' | 'processing' | 'needs_review' | 'failed' | 'queued';
  complianceScore: number; // 0-100
  tags: string[];
  description: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  permissions: {
    canView: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canShare: boolean;
    canDownload: boolean;
  };
  metadata: {
    pageCount?: number;
    wordCount?: number;
    language: string;
    encryptionLevel?: 'none' | 'basic' | 'advanced';
  };
  complianceChecks: {
    lastChecked: string;
    frameworks: string[]; // ['FDA', 'ISO', 'ICH', etc.]
    findings: number;
    criticalIssues: number;
  };
  versions: {
    current: string;
    history: Array<{
      version: string;
      uploadDate: string;
      author: string;
      changeNotes: string;
    }>;
  };
}

// State Management Hook
const useDocumentsState = () => {
  const [state, setState] = useState<DocumentsState>({
    documents: [],
    filteredDocuments: [],
    searchQuery: '',
    selectedCategory: 'all',
    selectedStatus: 'all',
    viewMode: 'grid',
    activeTab: 'my-documents',
    sortBy: 'uploadDate',
    sortOrder: 'desc',
    isLoading: false,
    error: null,
    selectedDocuments: [],
    page: 1,
    hasMore: true
  });

  // Complex filtering logic
  const filterDocuments = useCallback((
    query: string,
    category: string,
    status: string,
    sortBy: string,
    sortOrder: 'asc' | 'desc'
  ) => {
    let filtered = state.documents.filter(doc => {
      // Search matching
      const searchMatch = !query || [
        doc.name,
        doc.description,
        ...doc.tags,
        doc.author.name,
        doc.type
      ].some(field =>
        field.toLowerCase().includes(query.toLowerCase())
      );

      // Category matching
      const categoryMatch = category === 'all' || doc.category === category;

      // Status matching
      const statusMatch = status === 'all' || doc.status === status;

      return searchMatch && categoryMatch && statusMatch;
    });

    // Sorting logic
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'uploadDate':
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
          break;
        case 'complianceScore':
          aValue = a.complianceScore;
          bValue = b.complianceScore;
          break;
        case 'size':
          aValue = a.sizeBytes;
          bValue = b.sizeBytes;
          break;
        default:
          aValue = a.uploadDate;
          bValue = b.uploadDate;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setState(prev => ({ ...prev, filteredDocuments: filtered }));
  }, [state.documents]);

  return { state, setState, filterDocuments };
};
```

### **Advanced Grid Layout Implementation**
```css
/* CSS Grid with Advanced Responsive Logic */
.documents-grid {
  display: grid;
  gap: 1.5rem; /* 24px */
  width: 100%;

  /* Mobile: 1 column, cards take full width */
  grid-template-columns: 1fr;

  /* Small tablets: 2 columns when width > 640px */
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, minmax(300px, 1fr));
  }

  /* Large tablets: 2-3 columns when width > 768px */
  @media (min-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    max-width: none;
  }

  /* Desktop: 3 columns when width > 1024px */
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, minmax(320px, 1fr));
  }

  /* Large desktop: 4 columns when width > 1440px */
  @media (min-width: 1440px) {
    grid-template-columns: repeat(4, minmax(300px, 1fr));
  }

  /* Extra large: 5 columns when width > 1920px */
  @media (min-width: 1920px) {
    grid-template-columns: repeat(5, minmax(280px, 1fr));
  }
}

/* Document Card Advanced Styling */
.document-card {
  /* Base card styling */
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px); /* 6px */
  padding: 0;
  display: flex;
  flex-direction: column;
  min-height: 280px; /* Consistent minimum height */
  max-height: 400px; /* Prevent cards from being too tall */

  /* Advanced shadows and transitions */
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1);

  transition:
    box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* Hover states with advanced easing */
  &:hover {
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);
    transform: translateY(-2px);
    border-color: hsl(var(--primary) / 0.3);
  }

  /* Focus states for accessibility */
  &:focus-within {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Selected state */
  &[data-selected="true"] {
    border-color: hsl(var(--primary));
    box-shadow:
      0 0 0 2px hsl(var(--primary) / 0.2),
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);
  }
}
```

### **Advanced Table Implementation**
```typescript
// Table Column Configuration
const tableColumns = [
  {
    key: 'name',
    title: 'Name',
    width: '40%',
    sortable: true,
    render: (document: Document) => (
      <div className="flex items-center space-x-3 min-w-0">
        <div className="flex-shrink-0">
          <FileIcon type={document.type} className="h-8 w-8" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="font-medium text-foreground truncate" title={document.name}>
            {document.name}
          </p>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span>{document.size}</span>
            <span>•</span>
            <span>{document.metadata.pageCount || 0} pages</span>
          </div>
        </div>
      </div>
    )
  },
  {
    key: 'type',
    title: 'Type',
    width: '10%',
    sortable: true,
    render: (document: Document) => (
      <Badge variant="outline" className="font-mono text-xs">
        {document.type}
      </Badge>
    )
  },
  {
    key: 'category',
    title: 'Category',
    width: '15%',
    sortable: true,
    render: (document: Document) => (
      <CategoryBadge category={document.category} />
    )
  },
  {
    key: 'status',
    title: 'Status',
    width: '15%',
    sortable: true,
    render: (document: Document) => (
      <StatusBadge status={document.status} />
    )
  },
  {
    key: 'complianceScore',
    title: 'Compliance',
    width: '15%',
    sortable: true,
    render: (document: Document) => (
      <div className="flex items-center space-x-2">
        <div className="flex-1">
          <Progress
            value={document.complianceScore}
            className="h-2 w-16"
          />
        </div>
        <span className="text-xs font-medium min-w-[2rem] text-right">
          {document.complianceScore}%
        </span>
      </div>
    )
  },
  {
    key: 'uploadDate',
    title: 'Upload Date',
    width: '12%',
    sortable: true,
    render: (document: Document) => (
      <div className="text-sm">
        <div className="text-foreground">
          {formatDate(document.uploadDate, 'MMM dd')}
        </div>
        <div className="text-xs text-muted-foreground">
          {formatDate(document.uploadDate, 'yyyy')}
        </div>
      </div>
    )
  },
  {
    key: 'actions',
    title: 'Actions',
    width: '8%',
    sortable: false,
    render: (document: Document) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem onClick={() => onView(document.id)}>
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onDownload(document.id)}>
            <Download className="mr-2 h-4 w-4" />
            Download
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onShare(document.id)}>
            <Share className="mr-2 h-4 w-4" />
            Share
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => onDelete(document.id)}
            className="text-destructive focus:text-destructive"
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }
];

// Advanced Table Component
const DocumentTable: React.FC<{
  documents: Document[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (column: string) => void;
  onView: (id: number) => void;
  onDownload: (id: number) => void;
  onShare: (id: number) => void;
  onDelete: (id: number) => void;
}> = ({ documents, sortBy, sortOrder, onSort, ...actions }) => {
  return (
    <div className="relative overflow-auto">
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-transparent border-border">
            {tableColumns.map((column) => (
              <TableHead
                key={column.key}
                className={cn(
                  "font-semibold text-muted-foreground",
                  column.sortable && "cursor-pointer select-none hover:text-foreground"
                )}
                style={{ width: column.width }}
                onClick={() => column.sortable && onSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.title}</span>
                  {column.sortable && (
                    <div className="flex flex-col">
                      <ChevronUp
                        className={cn(
                          "h-3 w-3 -mb-1",
                          sortBy === column.key && sortOrder === 'asc'
                            ? "text-foreground"
                            : "text-muted-foreground/50"
                        )}
                      />
                      <ChevronDown
                        className={cn(
                          "h-3 w-3",
                          sortBy === column.key && sortOrder === 'desc'
                            ? "text-foreground"
                            : "text-muted-foreground/50"
                        )}
                      />
                    </div>
                  )}
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <TableRow
              key={document.id}
              className="cursor-pointer hover:bg-muted/50 border-border"
              onClick={() => actions.onView(document.id)}
            >
              {tableColumns.map((column) => (
                <TableCell
                  key={`${document.id}-${column.key}`}
                  className="py-3"
                  style={{ width: column.width }}
                >
                  {column.render(document)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
```

### **Advanced Search & Filtering Implementation**
```typescript
// Comprehensive Search Logic
const useDocumentSearch = () => {
  const [searchState, setSearchState] = useState({
    query: '',
    filters: {
      category: 'all',
      status: 'all',
      dateRange: null as { start: Date; end: Date } | null,
      complianceRange: [0, 100] as [number, number],
      tags: [] as string[],
      author: null as string | null,
      fileTypes: [] as string[]
    },
    sort: {
      field: 'uploadDate',
      order: 'desc' as 'asc' | 'desc'
    }
  });

  // Advanced search function with multiple criteria
  const performSearch = useCallback((documents: Document[]) => {
    return documents.filter(doc => {
      // Text search across multiple fields
      const textMatch = !searchState.query || [
        doc.name,
        doc.description,
        doc.author.name,
        ...doc.tags,
        ...doc.complianceChecks.frameworks
      ].some(field =>
        field.toLowerCase().includes(searchState.query.toLowerCase())
      );

      // Category filter
      const categoryMatch = searchState.filters.category === 'all' ||
        doc.category === searchState.filters.category;

      // Status filter
      const statusMatch = searchState.filters.status === 'all' ||
        doc.status === searchState.filters.status;

      // Date range filter
      const dateMatch = !searchState.filters.dateRange || (
        new Date(doc.uploadDate) >= searchState.filters.dateRange.start &&
        new Date(doc.uploadDate) <= searchState.filters.dateRange.end
      );

      // Compliance score range
      const complianceMatch = doc.complianceScore >= searchState.filters.complianceRange[0] &&
        doc.complianceScore <= searchState.filters.complianceRange[1];

      // Tags filter (any of selected tags)
      const tagsMatch = searchState.filters.tags.length === 0 ||
        searchState.filters.tags.some(tag => doc.tags.includes(tag));

      // Author filter
      const authorMatch = !searchState.filters.author ||
        doc.author.id === searchState.filters.author;

      // File type filter
      const fileTypeMatch = searchState.filters.fileTypes.length === 0 ||
        searchState.filters.fileTypes.includes(doc.type);

      return textMatch && categoryMatch && statusMatch &&
             dateMatch && complianceMatch && tagsMatch &&
             authorMatch && fileTypeMatch;
    }).sort((a, b) => {
      const { field, order } = searchState.sort;
      let aValue: any, bValue: any;

      switch (field) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'uploadDate':
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
          break;
        case 'complianceScore':
          aValue = a.complianceScore;
          bValue = b.complianceScore;
          break;
        case 'size':
          aValue = a.sizeBytes;
          bValue = b.sizeBytes;
          break;
        case 'author':
          aValue = a.author.name.toLowerCase();
          bValue = b.author.name.toLowerCase();
          break;
        default:
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
      }

      if (aValue < bValue) return order === 'asc' ? -1 : 1;
      if (aValue > bValue) return order === 'asc' ? 1 : -1;
      return 0;
    });
  }, [searchState]);

  return { searchState, setSearchState, performSearch };
};

// Advanced Filter Panel Component
const AdvancedFilters: React.FC<{
  filters: any;
  onFiltersChange: (filters: any) => void;
  availableTags: string[];
  availableAuthors: <AUTHORS>
}> = ({ filters, onFiltersChange, availableTags, availableAuthors }) => {
  return (
    <Collapsible>
      <CollapsibleTrigger asChild>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Advanced Filters
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="mt-4 p-4 border rounded-lg bg-card">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label>Date Range</Label>
            <DatePickerWithRange
              value={filters.dateRange}
              onChange={(range) => onFiltersChange({ ...filters, dateRange: range })}
            />
          </div>

          {/* Compliance Score Range */}
          <div className="space-y-2">
            <Label>Compliance Score</Label>
            <div className="px-3">
              <DualRangeSlider
                value={filters.complianceRange}
                onValueChange={(range) => onFiltersChange({ ...filters, complianceRange: range })}
                max={100}
                min={0}
                step={5}
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{filters.complianceRange[0]}%</span>
                <span>{filters.complianceRange[1]}%</span>
              </div>
            </div>
          </div>

          {/* Tags Multi-Select */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <MultiSelect
              options={availableTags.map(tag => ({ value: tag, label: tag }))}
              value={filters.tags}
              onChange={(tags) => onFiltersChange({ ...filters, tags })}
              placeholder="Select tags..."
            />
          </div>

          {/* Author Filter */}
          <div className="space-y-2">
            <Label>Author</Label>
            <Select
              value={filters.author || 'all'}
              onValueChange={(author) => onFiltersChange({
                ...filters,
                author: author === 'all' ? null : author
              })}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Authors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Authors</SelectItem>
                {availableAuthors.map(author => (
                  <SelectItem key={author.id} value={author.id}>
                    {author.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* File Types */}
          <div className="space-y-2">
            <Label>File Types</Label>
            <div className="flex flex-wrap gap-2">
              {['PDF', 'DOCX', 'XLSX', 'TXT', 'PPT'].map(type => (
                <Badge
                  key={type}
                  variant={filters.fileTypes.includes(type) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => {
                    const newTypes = filters.fileTypes.includes(type)
                      ? filters.fileTypes.filter((t: string) => t !== type)
                      : [...filters.fileTypes, type];
                    onFiltersChange({ ...filters, fileTypes: newTypes });
                  }}
                >
                  {type}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Clear Filters */}
        <div className="mt-4 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onFiltersChange({
              category: 'all',
              status: 'all',
              dateRange: null,
              complianceRange: [0, 100],
              tags: [],
              author: null,
              fileTypes: []
            })}
          >
            Clear All Filters
          </Button>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};
```

### **Performance Optimization Implementation**
```typescript
// Virtual Scrolling for Large Document Lists
const VirtualizedDocumentGrid: React.FC<{
  documents: Document[];
  itemHeight: number;
  containerHeight: number;
}> = ({ documents, itemHeight = 320, containerHeight = 600 }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);

  const itemsPerRow = useCallback(() => {
    if (!containerRef) return 3;
    const containerWidth = containerRef.offsetWidth;
    const itemWidth = 320;
    const gap = 24;
    return Math.floor((containerWidth + gap) / (itemWidth + gap));
  }, [containerRef]);

  const visibleItems = useMemo(() => {
    const itemsPerRowCount = itemsPerRow();
    const rowHeight = itemHeight + 24; // Include gap
    const startRow = Math.floor(scrollTop / rowHeight);
    const endRow = Math.min(
      Math.ceil((scrollTop + containerHeight) / rowHeight) + 1,
      Math.ceil(documents.length / itemsPerRowCount)
    );

    const startIndex = startRow * itemsPerRowCount;
    const endIndex = Math.min(endRow * itemsPerRowCount, documents.length);

    return {
      startIndex,
      endIndex,
      items: documents.slice(startIndex, endIndex),
      offsetY: startRow * rowHeight
    };
  }, [documents, scrollTop, containerHeight, itemHeight, itemsPerRow]);

  return (
    <div
      ref={setContainerRef}
      className="relative overflow-auto"
      style={{ height: containerHeight }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{
        height: Math.ceil(documents.length / itemsPerRow()) * (itemHeight + 24),
        position: 'relative'
      }}>
        <div
          style={{
            transform: `translateY(${visibleItems.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {visibleItems.items.map((doc, index) => (
              <DocumentCard
                key={`${doc.id}-${visibleItems.startIndex + index}`}
                document={doc}
                viewMode="grid"
                onView={onView}
                onDownload={onDownload}
                onShare={onShare}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Memoized Components for Performance
const MemoizedDocumentCard = React.memo(DocumentCard, (prevProps, nextProps) => {
  return (
    prevProps.document.id === nextProps.document.id &&
    prevProps.document.complianceScore === nextProps.document.complianceScore &&
    prevProps.document.status === nextProps.document.status &&
    prevProps.viewMode === nextProps.viewMode &&
    prevProps.isSelected === nextProps.isSelected
  );
});

const MemoizedStatusBadge = React.memo(StatusBadge, (prevProps, nextProps) => {
  return prevProps.status === nextProps.status && prevProps.size === nextProps.size;
});

// Debounced Search Hook
const useDebouncedSearch = (searchTerm: string, delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(searchTerm);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, delay]);

  return debouncedValue;
};
```

# 📊 **DOCUMENTS TABLE - HYPER-DETAILED SPECIFICATION**

## 🏗️ **TABLE STRUCTURE & ARCHITECTURE**

### **Complete Table Component Breakdown**
```typescript
interface DocumentTableProps {
  documents: Document[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (column: string) => void;
  onView: (id: number) => void;
  onDownload: (id: number) => void;
  onShare: (id: number) => void;
  onDelete: (id: number) => void;
  selectedDocuments: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  showSelection?: boolean;
  isLoading?: boolean;
}

// Table Container Structure
const TableContainer = {
  wrapper: "Card component with rounded corners",
  overflow: "relative overflow-auto", // Horizontal scroll on mobile
  maxHeight: "calc(100vh - 400px)", // Dynamic height based on viewport
  minHeight: "400px",
  background: "hsl(var(--card))",
  border: "1px solid hsl(var(--border))",
  borderRadius: "calc(var(--radius) - 2px)" // 6px
};
```

### **Table Header Implementation**
```css
.table-header {
  position: sticky;
  top: 0;
  background: hsl(var(--card));
  z-index: 10;
  border-bottom: 2px solid hsl(var(--border));
}

.table-header-row {
  height: 48px; /* Fixed height for consistent spacing */
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.5);
}

.table-header-cell {
  padding: 12px 16px;
  font-weight: 600; /* font-semibold */
  font-size: 0.875rem; /* text-sm */
  color: hsl(var(--muted-foreground));
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
  user-select: none;

  /* Sortable headers */
  &[data-sortable="true"] {
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: hsl(var(--foreground));
      background: hsl(var(--muted) / 0.7);
    }
  }
}

/* Sort indicators */
.sort-indicator {
  display: inline-flex;
  flex-direction: column;
  margin-left: 4px;
  vertical-align: middle;
}

.sort-arrow {
  width: 12px; /* h-3 w-3 */
  height: 12px;
  transition: color 0.2s ease;
}

.sort-arrow-up {
  margin-bottom: -4px; /* Overlap arrows slightly */
  color: hsl(var(--muted-foreground) / 0.5);
}

.sort-arrow-down {
  color: hsl(var(--muted-foreground) / 0.5);
}

/* Active sort states */
.sort-arrow-up[data-active="true"] {
  color: hsl(var(--foreground));
}

.sort-arrow-down[data-active="true"] {
  color: hsl(var(--foreground));
}
```

## 📋 **COLUMN SPECIFICATIONS**

### **1. Selection Column (Optional)**
```typescript
const SelectionColumn = {
  key: 'selection',
  width: '48px', // Fixed width for checkbox
  minWidth: '48px',
  maxWidth: '48px',
  resizable: false,
  sortable: false,

  header: () => (
    <div className="flex items-center justify-center">
      <Checkbox
        checked={selectedDocuments.length === documents.length && documents.length > 0}
        indeterminate={selectedDocuments.length > 0 && selectedDocuments.length < documents.length}
        onCheckedChange={(checked) => {
          if (checked) {
            onSelectionChange(documents.map(d => d.id));
          } else {
            onSelectionChange([]);
          }
        }}
        aria-label="Select all documents"
      />
    </div>
  ),

  cell: (document: Document) => (
    <div className="flex items-center justify-center">
      <Checkbox
        checked={selectedDocuments.includes(document.id)}
        onCheckedChange={(checked) => {
          if (checked) {
            onSelectionChange([...selectedDocuments, document.id]);
          } else {
            onSelectionChange(selectedDocuments.filter(id => id !== document.id));
          }
        }}
        aria-label={`Select ${document.name}`}
        onClick={(e) => e.stopPropagation()} // Prevent row click
      />
    </div>
  )
};
```

### **2. Document Name Column**
```typescript
const NameColumn = {
  key: 'name',
  title: 'Document Name',
  width: '40%',
  minWidth: '300px', // Ensure minimum readable width
  maxWidth: 'none',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <div className="flex items-center space-x-3 min-w-0 py-2">
      {/* File Type Icon */}
      <div className="flex-shrink-0">
        <FileTypeIcon
          type={document.type}
          className="h-8 w-8 text-primary"
        />
      </div>

      {/* Document Info */}
      <div className="min-w-0 flex-1">
        <div className="flex items-center space-x-2">
          <p
            className="font-medium text-foreground truncate cursor-pointer hover:text-primary transition-colors"
            title={document.name}
            onClick={(e) => {
              e.stopPropagation();
              onView(document.id);
            }}
          >
            {document.name}
          </p>

          {/* Version indicator */}
          {document.versions.history.length > 1 && (
            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
              v{document.versions.current}
            </Badge>
          )}
        </div>

        {/* Metadata row */}
        <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-0.5">
          <span>{document.size}</span>
          <span>•</span>
          <span>{document.metadata.pageCount || 0} pages</span>
          {document.metadata.wordCount && (
            <>
              <span>•</span>
              <span>{document.metadata.wordCount.toLocaleString()} words</span>
            </>
          )}
          {document.metadata.encryptionLevel !== 'none' && (
            <>
              <span>•</span>
              <div className="flex items-center">
                <Lock className="h-3 w-3 mr-1" />
                <span className="capitalize">{document.metadata.encryptionLevel}</span>
              </div>
            </>
          )}
        </div>

        {/* Description preview */}
        {document.description && (
          <p className="text-xs text-muted-foreground mt-1 line-clamp-1" title={document.description}>
            {document.description}
          </p>
        )}
      </div>
    </div>
  )
};
```

### **3. File Type Column**
```typescript
const TypeColumn = {
  key: 'type',
  title: 'Type',
  width: '80px',
  minWidth: '80px',
  maxWidth: '80px',
  sortable: true,
  resizable: false,

  cell: (document: Document) => (
    <Badge
      variant="outline"
      className={cn(
        "font-mono text-xs font-medium px-2 py-1",
        getFileTypeBadgeColor(document.type)
      )}
    >
      {document.type}
    </Badge>
  )
};

// File type color mapping
const getFileTypeBadgeColor = (type: string) => {
  const colors = {
    'PDF': 'bg-red-50 text-red-600 border-red-200',
    'DOCX': 'bg-blue-50 text-blue-600 border-blue-200',
    'XLSX': 'bg-green-50 text-green-600 border-green-200',
    'TXT': 'bg-gray-50 text-gray-600 border-gray-200',
    'PPT': 'bg-orange-50 text-orange-600 border-orange-200',
    'CSV': 'bg-purple-50 text-purple-600 border-purple-200'
  };
  return colors[type as keyof typeof colors] || 'bg-gray-50 text-gray-600 border-gray-200';
};
```

### **4. Category Column**
```typescript
const CategoryColumn = {
  key: 'category',
  title: 'Category',
  width: '120px',
  minWidth: '120px',
  maxWidth: '150px',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <CategoryBadge
      category={document.category}
      size="sm"
      showIcon={true}
    />
  )
};

const CategoryBadge: React.FC<{
  category: Document['category'];
  size?: 'sm' | 'md';
  showIcon?: boolean;
}> = ({ category, size = 'sm', showIcon = false }) => {
  const config = {
    'Approved': {
      className: 'bg-success/10 text-success border-success/20',
      icon: CheckCircle2,
      label: 'Approved'
    },
    'Under Review': {
      className: 'bg-warning/10 text-warning border-warning/20',
      icon: Eye,
      label: 'Under Review'
    },
    'Draft': {
      className: 'bg-muted text-muted-foreground border-muted-foreground/20',
      icon: Edit,
      label: 'Draft'
    },
    'Template': {
      className: 'bg-info/10 text-info border-info/20',
      icon: Copy,
      label: 'Template'
    },
    'Archived': {
      className: 'bg-destructive/10 text-destructive border-destructive/20',
      icon: Archive,
      label: 'Archived'
    }
  };

  const categoryConfig = config[category];
  const Icon = categoryConfig.icon;

  return (
    <Badge
      className={cn(
        categoryConfig.className,
        size === 'sm' ? 'text-xs px-2 py-0.5' : 'text-sm px-3 py-1'
      )}
    >
      {showIcon && <Icon className={cn("mr-1", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />}
      {categoryConfig.label}
    </Badge>
  );
};
```

### **5. Status Column**
```typescript
const StatusColumn = {
  key: 'status',
  title: 'Status',
  width: '140px',
  minWidth: '140px',
  maxWidth: '160px',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <div className="flex items-center space-x-2">
      <StatusBadge status={document.status} size="sm" />
      {document.status === 'processing' && (
        <div className="animate-spin h-3 w-3">
          <Loader2 className="h-3 w-3" />
        </div>
      )}
    </div>
  )
};

const StatusBadge: React.FC<{
  status: Document['status'];
  size?: 'sm' | 'md';
}> = ({ status, size = 'sm' }) => {
  const config = {
    'completed': {
      className: 'bg-success text-success-foreground',
      icon: CheckCircle,
      label: 'Complete',
      pulse: false
    },
    'processing': {
      className: 'bg-info text-info-foreground',
      icon: Clock,
      label: 'Processing',
      pulse: true
    },
    'needs_review': {
      className: 'bg-warning text-warning-foreground',
      icon: AlertCircle,
      label: 'Needs Review',
      pulse: false
    },
    'failed': {
      className: 'bg-destructive text-destructive-foreground',
      icon: XCircle,
      label: 'Failed',
      pulse: false
    },
    'queued': {
      className: 'bg-muted text-muted-foreground',
      icon: Clock,
      label: 'Queued',
      pulse: false
    }
  };

  const statusConfig = config[status];
  const Icon = statusConfig.icon;

  return (
    <Badge
      className={cn(
        statusConfig.className,
        size === 'sm' ? 'text-xs px-2 py-0.5' : 'text-sm px-3 py-1',
        statusConfig.pulse && 'animate-pulse'
      )}
    >
      <Icon className={cn("mr-1", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />
      {statusConfig.label}
    </Badge>
  );
};
```

### **6. Compliance Score Column**
```typescript
const ComplianceColumn = {
  key: 'complianceScore',
  title: 'Compliance',
  width: '140px',
  minWidth: '140px',
  maxWidth: '160px',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <div className="flex items-center space-x-3">
      {/* Progress bar */}
      <div className="flex-1 min-w-[60px]">
        <div className="flex items-center space-x-2">
          <Progress
            value={document.complianceScore}
            className="h-2 flex-1"
            color={getComplianceColor(document.complianceScore)}
          />
          <span className={cn(
            "text-xs font-medium min-w-[2.5rem] text-right",
            getComplianceTextColor(document.complianceScore)
          )}>
            {document.complianceScore}%
          </span>
        </div>

        {/* Compliance details on hover */}
        <div className="text-xs text-muted-foreground mt-0.5">
          {document.complianceChecks.criticalIssues > 0 && (
            <span className="text-destructive">
              {document.complianceChecks.criticalIssues} critical
            </span>
          )}
          {document.complianceChecks.findings > 0 && (
            <span className={document.complianceChecks.criticalIssues > 0 ? 'ml-2' : ''}>
              {document.complianceChecks.findings} findings
            </span>
          )}
        </div>
      </div>

      {/* Compliance frameworks indicator */}
      <Tooltip>
        <TooltipTrigger>
          <Badge variant="outline" className="text-xs px-1.5 py-0.5">
            {document.complianceChecks.frameworks.length}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs">
            <p className="font-medium mb-1">Compliance Frameworks:</p>
            {document.complianceChecks.frameworks.map(framework => (
              <p key={framework}>{framework}</p>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </div>
  )
};

const getComplianceColor = (score: number) => {
  if (score >= 90) return 'hsl(var(--success))';
  if (score >= 75) return 'hsl(var(--warning))';
  return 'hsl(var(--destructive))';
};

const getComplianceTextColor = (score: number) => {
  if (score >= 90) return 'text-success';
  if (score >= 75) return 'text-warning';
  return 'text-destructive';
};
```

### **7. Author Column**
```typescript
const AuthorColumn = {
  key: 'author',
  title: 'Author',
  width: '160px',
  minWidth: '160px',
  maxWidth: '200px',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <div className="flex items-center space-x-2">
      <Avatar className="h-7 w-7">
        <AvatarImage src={document.author.avatar} alt={document.author.name} />
        <AvatarFallback className="text-xs">
          {document.author.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
        </AvatarFallback>
      </Avatar>
      <div className="min-w-0 flex-1">
        <p className="text-sm font-medium text-foreground truncate">
          {document.author.name}
        </p>
        <p className="text-xs text-muted-foreground">
          {formatDistanceToNow(new Date(document.uploadDate), { addSuffix: true })}
        </p>
      </div>
    </div>
  )
};
```

### **8. Upload Date Column**
```typescript
const DateColumn = {
  key: 'uploadDate',
  title: 'Upload Date',
  width: '120px',
  minWidth: '120px',
  maxWidth: '140px',
  sortable: true,
  resizable: true,

  cell: (document: Document) => (
    <div className="text-sm">
      <div className="text-foreground font-medium">
        {format(new Date(document.uploadDate), 'MMM dd, yyyy')}
      </div>
      <div className="text-xs text-muted-foreground">
        {format(new Date(document.uploadDate), 'h:mm a')}
      </div>
      {document.lastModified !== document.uploadDate && (
        <div className="text-xs text-muted-foreground mt-0.5">
          Modified: {formatDistanceToNow(new Date(document.lastModified), { addSuffix: true })}
        </div>
      )}
    </div>
  )
};
```

### **9. Actions Column**
```typescript
const ActionsColumn = {
  key: 'actions',
  title: 'Actions',
  width: '80px',
  minWidth: '80px',
  maxWidth: '80px',
  sortable: false,
  resizable: false,

  cell: (document: Document) => (
    <div className="flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu for {document.name}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[200px]">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem onClick={() => onView(document.id)}>
            <Eye className="mr-2 h-4 w-4" />
            Preview Document
          </DropdownMenuItem>

          {document.permissions.canDownload && (
            <DropdownMenuItem onClick={() => onDownload(document.id)}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </DropdownMenuItem>
          )}

          {document.permissions.canShare && (
            <DropdownMenuItem onClick={() => onShare(document.id)}>
              <Share className="mr-2 h-4 w-4" />
              Share
            </DropdownMenuItem>
          )}

          {document.permissions.canEdit && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onEdit(document.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Properties
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => onDuplicate(document.id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={() => onViewHistory(document.id)}>
            <History className="mr-2 h-4 w-4" />
            Version History
          </DropdownMenuItem>

          {document.permissions.canDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(document.id)}
                className="text-destructive focus:text-destructive focus:bg-destructive/10"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
};
```

## 📱 **RESPONSIVE TABLE BEHAVIOR**

### **Mobile Responsiveness (< 768px)**
```css
@media (max-width: 767px) {
  .documents-table {
    /* Hide less important columns on mobile */
    .column-type,
    .column-author,
    .column-compliance {
      display: none;
    }

    /* Adjust remaining column widths */
    .column-name {
      width: 60%;
    }

    .column-status {
      width: 25%;
    }

    .column-actions {
      width: 15%;
    }
  }

  /* Compact row height on mobile */
  .table-row {
    min-height: 60px;
  }

  /* Reduce padding */
  .table-cell {
    padding: 8px 12px;
  }
}
```

### **Tablet Responsiveness (768px - 1024px)**
```css
@media (min-width: 768px) and (max-width: 1023px) {
  .documents-table {
    /* Show essential columns */
    .column-type,
    .column-status,
    .column-date {
      display: table-cell;
    }

    /* Hide less critical columns */
    .column-author,
    .column-compliance {
      display: none;
    }
  }
}
```

### **Desktop Full View (> 1024px)**
```css
@media (min-width: 1024px) {
  .documents-table {
    /* Show all columns */
    .table-column {
      display: table-cell;
    }
  }
}
```

## ⚡ **TABLE INTERACTIONS & BEHAVIORS**

### **Row Click Behavior**
```typescript
const TableRow: React.FC<{
  document: Document;
  children: React.ReactNode;
  onClick: () => void;
}> = ({ document, children, onClick }) => {
  return (
    <tr
      className={cn(
        "cursor-pointer transition-colors border-b border-border",
        "hover:bg-muted/50 focus:bg-muted/50",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        selectedDocuments.includes(document.id) && "bg-primary/5 border-primary/20"
      )}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${document.name}`}
    >
      {children}
    </tr>
  );
};
```

### **Column Sorting Implementation**
```typescript
const useSortableTable = (initialSort: { field: string; order: 'asc' | 'desc' }) => {
  const [sort, setSort] = useState(initialSort);

  const handleSort = (field: string) => {
    setSort(prevSort => ({
      field,
      order: prevSort.field === field && prevSort.order === 'asc' ? 'desc' : 'asc'
    }));
  };

  const sortedData = useMemo(() => {
    return [...documents].sort((a, b) => {
      const aValue = getNestedValue(a, sort.field);
      const bValue = getNestedValue(b, sort.field);

      if (aValue < bValue) return sort.order === 'asc' ? -1 : 1;
      if (aValue > bValue) return sort.order === 'asc' ? 1 : -1;
      return 0;
    });
  }, [documents, sort]);

  return { sort, handleSort, sortedData };
};

const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((value, key) => value?.[key], obj);
};
```

### **Table Loading States**
```typescript
const TableSkeleton: React.FC<{ rows?: number }> = ({ rows = 5 }) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {tableColumns.map(column => (
            <TableHead key={column.key} style={{ width: column.width }}>
              <Skeleton className="h-4 w-3/4" />
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array.from({ length: rows }).map((_, index) => (
          <TableRow key={index}>
            {tableColumns.map(column => (
              <TableCell key={column.key} style={{ width: column.width }}>
                <Skeleton className="h-4 w-full" />
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
```

### **Empty Table State**
```typescript
const EmptyTableState: React.FC<{
  message?: string;
  action?: () => void;
  actionLabel?: string;
}> = ({
  message = "No documents found",
  action,
  actionLabel = "Upload Document"
}) => {
  return (
    <div className="text-center py-12">
      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-foreground mb-2">
        {message}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        {action ?
          "Get started by uploading your first document or adjust your search criteria." :
          "Try adjusting your search criteria or filters to find what you're looking for."
        }
      </p>
      {action && (
        <Button onClick={action}>
          <Upload className="mr-2 h-4 w-4" />
          {actionLabel}
        </Button>
      )}
    </div>
  );
};
```

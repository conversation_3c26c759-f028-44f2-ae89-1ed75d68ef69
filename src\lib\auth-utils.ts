/**
 * Authentication utility functions
 * Separated from Server Actions to avoid 'use server' conflicts
 */

/**
 * Map Supabase error messages to user-friendly error codes
 * Following DEVELOPMENT_RULES_2.md: Use error codes instead of full error objects
 */
export function getErrorCode(errorMessage: string): string {
  const message = errorMessage.toLowerCase()

  if (message.includes('invalid login credentials')) {
    return 'invalid_credentials'
  }
  if (message.includes('email not confirmed')) {
    return 'email_not_confirmed'
  }
  if (message.includes('user already registered')) {
    return 'user_exists'
  }
  if (message.includes('password should be at least')) {
    return 'weak_password'
  }
  if (message.includes('invalid email')) {
    return 'invalid_email'
  }
  if (message.includes('too many requests')) {
    return 'rate_limited'
  }
  if (message.includes('signup disabled')) {
    return 'signup_disabled'
  }

  // Generic error for unknown cases
  return 'auth_error'
}

/**
 * Get user-friendly error messages for display
 */
export function getErrorMessage(errorCode: string): string {
  const errorMessages: Record<string, string> = {
    missing_credentials: 'Please provide both email and password.',
    invalid_email: 'Please enter a valid email address.',
    weak_password: 'Password must be at least 8 characters long.',
    password_mismatch: 'Passwords do not match.',
    invalid_credentials: 'Invalid email or password.',
    email_not_confirmed: 'Please check your email and click the confirmation link.',
    user_exists: 'An account with this email already exists.',
    rate_limited: 'Too many attempts. Please try again later.',
    signup_disabled: 'New registrations are currently disabled.',
    logout_failed: 'Failed to log out. Please try again.',
    missing_email: 'Please provide your email address.',
    missing_password: 'Please provide a password.',
    auth_error: 'An authentication error occurred. Please try again.',
  }

  return errorMessages[errorCode] || 'An unexpected error occurred.'
}

/**
 * Get success messages for display
 */
export function getSuccessMessage(successCode: string): string {
  const successMessages: Record<string, string> = {
    check_email: 'Please check your email for a confirmation link.',
    password_updated: 'Your password has been updated successfully.',
    email_confirmed: 'Your email has been confirmed successfully.',
  }

  return successMessages[successCode] || 'Operation completed successfully.'
}

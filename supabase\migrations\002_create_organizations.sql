-- VigiLens Database Schema - Migration 002
-- Create Organizations Table - Multi-Tenant Foundation
-- Optimized for Supabase RLS and Direct Frontend Integration

-- Organizations table - Root entity for multi-tenant architecture
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    display_name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,

    -- Business information
    industry VARCHAR(100) DEFAULT 'pharmaceuticals',
    company_size VARCHAR(50), -- 'startup', 'small', 'medium', 'large', 'enterprise'
    country VARCHAR(100),
    timezone VARCHAR(100) DEFAULT 'UTC',

    -- Subscription and billing
    subscription_tier subscription_tier DEFAULT 'free',
    subscription_status VARCHAR(50) DEFAULT 'active', -- 'active', 'suspended', 'cancelled'
    subscription_start_date TIMESTAMPTZ,
    subscription_end_date TIMESTAMPTZ,
    billing_email VARCHAR(255),

    -- Compliance configuration
    compliance_frameworks compliance_framework[] DEFAULT ARRAY['fda_cgmp'::compliance_framework],
    regulatory_regions VARCHAR(100)[] DEFAULT ARRAY['us'],

    -- Organization settings (JSONB for flexibility)
    settings JSONB DEFAULT '{
        "document_retention_years": 7,
        "auto_archive_enabled": true,
        "notification_preferences": {
            "regulatory_updates": true,
            "compliance_alerts": true,
            "system_notifications": true
        },
        "security_settings": {
            "mfa_required": false,
            "session_timeout_hours": 8,
            "password_policy": {
                "min_length": 12,
                "require_special_chars": true,
                "require_numbers": true,
                "require_mixed_case": true
            }
        },
        "ai_settings": {
            "auto_analysis_enabled": true,
            "confidence_threshold": 0.8,
            "human_review_required": true
        }
    }'::JSONB,

    -- Audit and compliance metadata
    is_active BOOLEAN DEFAULT true,
    is_validated BOOLEAN DEFAULT false, -- For pharmaceutical validation requirements
    validation_date TIMESTAMPTZ,
    validation_notes TEXT,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Audit trail
    created_by UUID, -- Will be linked to auth.users after user_profiles creation
    updated_by UUID,

    -- Constraints
    CONSTRAINT organizations_name_length CHECK (char_length(name) >= 2),
    CONSTRAINT organizations_display_name_length CHECK (char_length(display_name) >= 2),
    CONSTRAINT organizations_valid_subscription_dates CHECK (
        subscription_start_date IS NULL OR
        subscription_end_date IS NULL OR
        subscription_end_date > subscription_start_date
    )
);

-- Create indexes for performance optimization
CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_subscription_tier ON organizations(subscription_tier);
CREATE INDEX idx_organizations_subscription_status ON organizations(subscription_status);
CREATE INDEX idx_organizations_is_active ON organizations(is_active);
CREATE INDEX idx_organizations_created_at ON organizations(created_at);
CREATE INDEX idx_organizations_compliance_frameworks ON organizations USING GIN(compliance_frameworks);
CREATE INDEX idx_organizations_regulatory_regions ON organizations USING GIN(regulatory_regions);

-- GIN index for JSONB settings for efficient querying
CREATE INDEX idx_organizations_settings ON organizations USING GIN(settings);

-- Partial index for active organizations (most common query)
CREATE INDEX idx_organizations_active ON organizations(id) WHERE is_active = true;

-- Create updated_at trigger function (reusable for all tables)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger
CREATE TRIGGER update_organizations_updated_at
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organizations
-- Note: Initial policies - will be enhanced after user_profiles creation

-- Policy: Users can only see their own organization
CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (
        id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- Policy: Only admins can update organization settings
CREATE POLICY "Admins can update organization" ON organizations
    FOR UPDATE USING (
        id = (auth.jwt() ->> 'organization_id')::UUID
        AND (auth.jwt() ->> 'role') = 'admin'
    );

-- Policy: No direct insert/delete - handled through application logic
CREATE POLICY "No direct organization creation" ON organizations
    FOR INSERT WITH CHECK (false);

CREATE POLICY "No direct organization deletion" ON organizations
    FOR DELETE USING (false);

-- Create function for organization validation (21 CFR Part 11 compliance)
CREATE OR REPLACE FUNCTION validate_organization(org_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    org_record organizations%ROWTYPE;
BEGIN
    SELECT * INTO org_record FROM organizations WHERE id = org_id;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Validation checks for pharmaceutical compliance
    IF org_record.compliance_frameworks IS NULL OR array_length(org_record.compliance_frameworks, 1) = 0 THEN
        RETURN false;
    END IF;

    IF org_record.settings->>'document_retention_years' IS NULL THEN
        RETURN false;
    END IF;

    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get organization compliance status
CREATE OR REPLACE FUNCTION get_organization_compliance_status(org_id UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'organization_id', id,
        'is_validated', is_validated,
        'compliance_frameworks', compliance_frameworks,
        'validation_date', validation_date,
        'settings_valid', validate_organization(id)
    ) INTO result
    FROM organizations
    WHERE id = org_id;

    RETURN COALESCE(result, '{}'::JSONB);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert sample organization for development/testing
INSERT INTO organizations (
    id,
    name,
    display_name,
    description,
    industry,
    company_size,
    country,
    subscription_tier,
    compliance_frameworks,
    regulatory_regions
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    'demo-pharma-corp',
    'Demo Pharmaceutical Corporation',
    'Sample pharmaceutical company for VigiLens platform testing and development',
    'pharmaceuticals',
    'medium',
    'United States',
    'professional'::subscription_tier,
    ARRAY['fda_cgmp'::compliance_framework, 'ich_q7'::compliance_framework],
    ARRAY['us', 'eu']
) ON CONFLICT (name) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE organizations IS 'Multi-tenant root entity for pharmaceutical compliance organizations';
COMMENT ON COLUMN organizations.settings IS 'JSONB configuration for compliance, security, and AI settings';
COMMENT ON COLUMN organizations.compliance_frameworks IS 'Array of applicable regulatory frameworks';
COMMENT ON COLUMN organizations.is_validated IS 'Indicates if organization meets pharmaceutical validation requirements';
COMMENT ON FUNCTION validate_organization(UUID) IS 'Validates organization compliance configuration';
COMMENT ON FUNCTION get_organization_compliance_status(UUID) IS 'Returns comprehensive compliance status for organization';

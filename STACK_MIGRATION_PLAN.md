# VigiLens Stack Migration Plan 2025

## Overview
Migrating from ChromaDB + <PERSON><PERSON>hain + sentence-transformers to Qdrant Local + LangGraph/CrewAI + BGE-M3 for enhanced performance and cost optimization.

## Current Stack
- **Vector DB**: ChromaDB 1.0.15 (persistent, file-based)
- **AI Framework**: Custom RAG pipeline (minimal LangChain usage)
- **Embeddings**: sentence-transformers (all-MiniLM-L6-v2)
- **LLM**: OpenRouter + MoonshotAI Kimi K2 ✅ (keeping)
- **Backend**: FastAPI + Supabase ✅ (keeping)

## Target Stack
- **Vector DB**: Qdrant Local (embedded, file-based, zero cost)
- **AI Framework**: LangGraph + CrewAI Hybrid (open source)
- **Embeddings**: BGE-M3 (open source, local, multilingual)
- **LLM**: OpenRouter + MoonshotAI Kimi K2 ✅ (keeping)
- **Backend**: FastAPI + Supabase ✅ (keeping)

## Migration Steps

### Phase 1: Dependencies Update
1. Update requirements.txt
   - Remove: `chromadb==1.0.15`, `langchain==0.3.26`, `langchain-community==0.3.27`, `langchain-core==0.3.68`, `langsmith==0.4.5`
   - Add: `qdrant-client==1.7.0`, `langgraph==0.2.0`, `crewai==0.80.0`, `FlagEmbedding==1.2.10`
   - Update: `sentence-transformers==5.0.0` → keep for BGE-M3 support

### Phase 2: Vector Store Migration
1. Create new Qdrant vector store implementation
2. Implement data migration utility (ChromaDB → Qdrant)
3. Update vector store interface to maintain API compatibility
4. Test data integrity and search performance

### Phase 3: Embedding Model Upgrade
1. Replace all-MiniLM-L6-v2 with BGE-M3
2. Update embedding configuration
3. Re-embed existing documents with BGE-M3
4. Validate embedding quality and search relevance

### Phase 4: AI Framework Migration
1. Design LangGraph + CrewAI hybrid architecture
2. Implement multi-agent workflow for pharmaceutical compliance
3. Create specialized agents (Compliance Analyst, Document Reviewer, Risk Assessor)
4. Integrate with existing OpenRouter client
5. Maintain RAG pipeline compatibility

### Phase 5: Testing & Validation
1. Unit tests for all new components
2. Integration tests for complete pipeline
3. Performance benchmarking
4. Data migration validation
5. End-to-end pharmaceutical compliance queries

## Implementation Priority
1. **High**: Vector store migration (core functionality)
2. **High**: Embedding model upgrade (search quality)
3. **Medium**: LangGraph integration (enhanced workflows)
4. **Medium**: CrewAI multi-agent system (advanced features)
5. **Low**: Performance optimizations

## Risk Mitigation
- Maintain backward compatibility during migration
- Implement feature flags for gradual rollout
- Keep ChromaDB as fallback during transition
- Comprehensive testing at each phase
- Data backup before migration

## Success Metrics
- Zero data loss during migration
- Improved search relevance with BGE-M3
- Reduced infrastructure costs (local Qdrant)
- Enhanced multi-agent capabilities
- Maintained or improved response times

## Timeline
- Phase 1: 1 day (dependencies)
- Phase 2: 2-3 days (vector store migration)
- Phase 3: 1-2 days (embedding upgrade)
- Phase 4: 3-4 days (AI framework migration)
- Phase 5: 2-3 days (testing & validation)

**Total Estimated Time**: 9-13 days

## Next Steps
1. Start with Phase 1: Update dependencies
2. Implement Qdrant vector store
3. Create data migration utility
4. Begin LangGraph + CrewAI integration
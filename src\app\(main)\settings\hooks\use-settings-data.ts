import { useState, useCallback } from 'react'

import type {
  ProfileData,
  CompanyData,
  NotificationSettings,
  SecuritySettings,
} from '../types'

export interface ComplianceSettings {
  readonly autoScan: boolean;
  readonly riskThreshold: 'low' | 'medium' | 'high';
  readonly frameworks: readonly string[];
}

export interface TeamSettings {
  readonly allowInvites: boolean;
  readonly defaultRole: string;
  readonly maxMembers: number;
}

export interface SettingsData {
  readonly profile: ProfileData;
  readonly company: CompanyData;
  readonly notifications: NotificationSettings;
  readonly security: SecuritySettings;
  readonly compliance: ComplianceSettings;
  readonly team: TeamSettings;
}

export function useSettingsData() {
  const [settings, setSettings] = useState<SettingsData>({
    profile: {
      firstName: 'David',
      lastName: 'Chen',
      email: '<EMAIL>',
      jobTitle: 'Compliance Manager',
      company: 'VigileAI Corp',
      location: 'San Francisco, CA',
      bio: 'Experienced compliance professional focused on regulatory excellence.',
    },
    company: {
      size: '51-200',
      industry: 'pharmaceutical',
      timeZone: 'pst',
    },
    notifications: {
      emailUpdates: true,
      pushNotifications: false,
      weeklyReports: true,
      criticalAlerts: true,
      documentProcessing: true,
    },
    security: {
      twoFactorAuth: true,
      sessionTimeout: '1hour',
      autoLogout: true,
    },
    compliance: {
      autoScan: true,
      riskThreshold: 'medium',
      frameworks: ['FDA-CGMP', 'ISO-13485'],
    },
    team: {
      allowInvites: true,
      defaultRole: 'viewer',
      maxMembers: 50,
    },
  })

  const [isLoading, setIsLoading] = useState(false)

  const updateProfile = useCallback((profile: Partial<ProfileData>) => {
    setSettings((prev) => ({
      ...prev,
      profile: { ...prev.profile, ...profile },
    }))
  }, [])

  const updateCompany = useCallback((company: Partial<CompanyData>) => {
    setSettings((prev) => ({
      ...prev,
      company: { ...prev.company, ...company },
    }))
  }, [])

  const updateNotifications = useCallback(
    (key: keyof NotificationSettings, value: boolean) => {
      setSettings((prev) => ({
        ...prev,
        notifications: { ...prev.notifications, [key]: value },
      }))
    },
    [],
  )

  const updateSecurity = useCallback(
    (key: keyof SecuritySettings, value: boolean | string) => {
      setSettings((prev) => ({
        ...prev,
        security: { ...prev.security, [key]: value },
      }))
    },
    [],
  )

  const updateCompliance = useCallback(
    (compliance: Partial<ComplianceSettings>) => {
      setSettings((prev) => ({
        ...prev,
        compliance: { ...prev.compliance, ...compliance },
      }))
    },
    [],
  )

  const updateTeam = useCallback((team: Partial<TeamSettings>) => {
    setSettings((prev) => ({
      ...prev,
      team: { ...prev.team, ...team },
    }))
  }, [])

  const saveSettings = useCallback(async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsLoading(false)
  }, [])

  return {
    settings,
    isLoading,
    updateProfile,
    updateCompany,
    updateNotifications,
    updateSecurity,
    updateCompliance,
    updateTeam,
    saveSettings,
  }
}

-- Simple fix for electronic_signatures table - Add missing columns step by step
-- Run this AFTER creating the missing enums

-- Add missing basic columns first (without enum dependencies)
DO $$ 
BEGIN
    -- Add signature_metadata column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_metadata') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signature_metadata JSONB DEFAULT '{}';
        RAISE NOTICE 'Added signature_metadata column';
    ELSE
        RAISE NOTICE 'signature_metadata column already exists';
    END IF;
    
    -- Add ip_address column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'ip_address') THEN
        ALTER TABLE electronic_signatures ADD COLUMN ip_address INET;
        RAISE NOTICE 'Added ip_address column';
    ELSE
        RAISE NOTICE 'ip_address column already exists';
    END IF;
    
    -- Add user_agent column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'user_agent') THEN
        ALTER TABLE electronic_signatures ADD COLUMN user_agent TEXT;
        RAISE NOTICE 'Added user_agent column';
    ELSE
        RAISE NOTICE 'user_agent column already exists';
    END IF;
    
    -- Add signer_title column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signer_title') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signer_title VARCHAR(255);
        RAISE NOTICE 'Added signer_title column';
    ELSE
        RAISE NOTICE 'signer_title column already exists';
    END IF;
    
    -- Add signer_department column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signer_department') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signer_department VARCHAR(100);
        RAISE NOTICE 'Added signer_department column';
    ELSE
        RAISE NOTICE 'signer_department column already exists';
    END IF;
    
    -- Add signature_reason column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_reason') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signature_reason TEXT;
        RAISE NOTICE 'Added signature_reason column';
    ELSE
        RAISE NOTICE 'signature_reason column already exists';
    END IF;
    
    -- Add document_hash_at_signing column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'document_hash_at_signing') THEN
        ALTER TABLE electronic_signatures ADD COLUMN document_hash_at_signing VARCHAR(512);
        RAISE NOTICE 'Added document_hash_at_signing column';
    ELSE
        RAISE NOTICE 'document_hash_at_signing column already exists';
    END IF;
    
    -- Add document_version_id column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'document_version_id') THEN
        ALTER TABLE electronic_signatures ADD COLUMN document_version_id UUID REFERENCES document_versions(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added document_version_id column';
    ELSE
        RAISE NOTICE 'document_version_id column already exists';
    END IF;
    
    -- Add audit_trail_id column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'audit_trail_id') THEN
        ALTER TABLE electronic_signatures ADD COLUMN audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added audit_trail_id column';
    ELSE
        RAISE NOTICE 'audit_trail_id column already exists';
    END IF;
END $$;

-- Add authentication_timestamp column (with enum dependency check)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'authentication_timestamp') THEN
        ALTER TABLE electronic_signatures ADD COLUMN authentication_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW();
        RAISE NOTICE 'Added authentication_timestamp column';
    ELSE
        RAISE NOTICE 'authentication_timestamp column already exists';
    END IF;
END $$;

-- Add authentication_method column only if the enum exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'authentication_method') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'authentication_method') THEN
            ALTER TABLE electronic_signatures ADD COLUMN authentication_method authentication_method NOT NULL DEFAULT 'password';
            RAISE NOTICE 'Added authentication_method column';
        ELSE
            RAISE NOTICE 'authentication_method column already exists';
        END IF;
    ELSE
        RAISE NOTICE 'authentication_method enum does not exist - skipping column creation';
    END IF;
END $$;

-- Create missing indexes (only for columns that exist)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_organization') THEN
        CREATE INDEX idx_electronic_signatures_organization ON electronic_signatures(organization_id);
        RAISE NOTICE 'Created organization index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_document') THEN
        CREATE INDEX idx_electronic_signatures_document ON electronic_signatures(document_id);
        RAISE NOTICE 'Created document index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signer') THEN
        CREATE INDEX idx_electronic_signatures_signer ON electronic_signatures(signer_id);
        RAISE NOTICE 'Created signer index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signed_at') THEN
        CREATE INDEX idx_electronic_signatures_signed_at ON electronic_signatures(signed_at);
        RAISE NOTICE 'Created signed_at index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_type') THEN
        CREATE INDEX idx_electronic_signatures_type ON electronic_signatures(signature_type);
        RAISE NOTICE 'Created signature_type index';
    END IF;
    
    -- Create metadata index only if column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_metadata') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_metadata') THEN
            CREATE INDEX idx_electronic_signatures_metadata ON electronic_signatures USING GIN (signature_metadata);
            RAISE NOTICE 'Created metadata GIN index';
        END IF;
    END IF;
END $$;

-- Add basic constraints
DO $$ 
BEGIN
    -- Add signature hash length constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'valid_signature_hash') THEN
        ALTER TABLE electronic_signatures ADD CONSTRAINT valid_signature_hash CHECK (LENGTH(signature_hash) >= 64);
        RAISE NOTICE 'Added signature hash constraint';
    ELSE
        RAISE NOTICE 'Signature hash constraint already exists';
    END IF;
END $$;

-- Create basic RLS policies
DO $$ 
BEGIN
    -- Enable RLS if not already enabled
    ALTER TABLE electronic_signatures ENABLE ROW LEVEL SECURITY;
    
    -- Policy 1: Users can view signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can view signatures in their organization') THEN
        CREATE POLICY "Users can view signatures in their organization"
            ON electronic_signatures FOR SELECT
            USING (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
            );
        RAISE NOTICE 'Created view policy for electronic_signatures';
    END IF;

    -- Policy 2: Users can create signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can create signatures in their organization') THEN
        CREATE POLICY "Users can create signatures in their organization"
            ON electronic_signatures FOR INSERT
            WITH CHECK (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
                AND signer_id = auth.uid()
            );
        RAISE NOTICE 'Created insert policy for electronic_signatures';
    END IF;

    -- Policy 3: Electronic signatures are immutable
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures are immutable') THEN
        CREATE POLICY "Electronic signatures are immutable"
            ON electronic_signatures FOR UPDATE
            USING (false);
        RAISE NOTICE 'Created update policy for electronic_signatures';
    END IF;

    -- Policy 4: Electronic signatures cannot be deleted
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures cannot be deleted') THEN
        CREATE POLICY "Electronic signatures cannot be deleted"
            ON electronic_signatures FOR DELETE
            USING (false);
        RAISE NOTICE 'Created delete policy for electronic_signatures';
    END IF;
END $$;

-- Grant basic permissions
GRANT SELECT, INSERT ON electronic_signatures TO authenticated;

-- Final verification
SELECT 
    'ELECTRONIC SIGNATURES SIMPLE FIX' as status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'electronic_signatures')
        THEN '✅ TABLE EXISTS'
        ELSE '❌ TABLE MISSING'
    END as table_status,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'electronic_signatures') as column_count,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'electronic_signatures') as policy_count,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'electronic_signatures') as index_count;

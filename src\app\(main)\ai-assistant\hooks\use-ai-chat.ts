import { useState, useCallback } from 'react'

import type { Message, AttachedDocument, UploadedDocument } from '../types'

export function useAIChat() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content:
        "Hello! I'm your AI Compliance Assistant. I can help you with regulatory compliance questions and document analysis. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      type: 'text',
    },
  ])

  const [inputMessage, setInputMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [attachedDocuments, setAttachedDocuments] = useState<
    AttachedDocument[]
  >([])

  const generateDocumentAnalysisResponse = useCallback(
    (docs: readonly AttachedDocument[]): string => {
      const docNames = docs.map((doc) => doc.name).join(', ')
      return `I've analyzed the ${docs.length} document(s) you've shared: ${docNames}.\n\n**Document Analysis Summary:**\n\n${docs
        .map(
          (doc) =>
            `📄 **${doc.name}** (${doc.type})\n- Category: ${doc.category}\n- Size: ${doc.size}\n- Analysis: This document appears to be related to ${doc.category.toLowerCase()} compliance. I can help identify key compliance requirements, gaps, and recommendations.`,
        )
        .join(
          '\n\n',
        )}\n\n**Key Compliance Insights:**\n- All documents have been scanned for regulatory compliance\n- No critical compliance gaps identified\n- Recommendations available for process improvements\n\nWould you like me to provide specific analysis for any particular document or identify compliance requirements across all documents?`
    },
    [],
  )

  const generateAIResponse = useCallback((userInput: string): string => {
    if (userInput.toLowerCase().includes('process validation')) {
      return 'Process validation is a critical component of pharmaceutical manufacturing that ensures consistent product quality. According to FDA guidance, it consists of three stages:\n\n1. **Process Design (Stage 1)**: Define commercial manufacturing process based on development studies\n2. **Process Qualification (Stage 2)**: Evaluate process design to determine if capable of reproducible commercial manufacturing\n3. **Continued Process Verification (Stage 3)**: Ongoing assurance that process remains in control\n\nKey requirements include:\n- Risk assessment and control strategy\n- Statistical analysis of process data\n- Validation protocols and reports\n- Change control procedures\n\nWould you like me to elaborate on any specific stage or requirement?'
    } else if (userInput.toLowerCase().includes('data integrity')) {
      return 'Data integrity is fundamental to pharmaceutical quality systems and regulatory compliance. The ALCOA+ principles provide the framework:\n\n**ALCOA:**\n- **Attributable**: Data must be attributable to the person generating it\n- **Legible**: Data must be readable and permanent\n- **Contemporaneous**: Data recorded at time of activity\n- **Original**: First recording or certified copy\n- **Accurate**: Data must be correct and complete\n\n**Plus (+):**\n- **Complete**: All data generated must be available\n- **Consistent**: Data format and procedures uniform\n- **Enduring**: Data preserved throughout retention period\n- **Available**: Data readily retrievable for review\n\nImplementation requires robust procedures, training, and technical controls. Do you need specific guidance on any aspect?'
    } else {
      return "I understand you're asking about compliance matters. Based on current regulatory guidance and industry best practices, I can provide detailed information on this topic. Let me analyze the specific requirements and provide you with actionable recommendations.\n\nFor the most accurate and up-to-date information, I recommend consulting the latest regulatory guidance documents and considering your specific operational context.\n\nWould you like me to elaborate on any particular aspect or provide more specific guidance?"
    }
  }, [])

  const handleSendMessage = useCallback(async () => {
    if (!inputMessage.trim() && attachedDocuments.length === 0) {
return
}

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage || 'Shared documents for analysis',
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
      ...(attachedDocuments.length > 0 && {
        attachments: [...attachedDocuments],
      }),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputMessage('')
    setAttachedDocuments([])
    setIsTyping(true)

    // Simulate AI response
    setTimeout(() => {
      const hasDocuments =
        userMessage.attachments && userMessage.attachments.length > 0
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: hasDocuments
          ? generateDocumentAnalysisResponse(userMessage.attachments)
          : generateAIResponse(inputMessage),
        sender: 'ai',
        timestamp: new Date(),
        type: 'text',
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsTyping(false)
    }, 2000)
  }, [
    inputMessage,
    attachedDocuments,
    generateDocumentAnalysisResponse,
    generateAIResponse,
  ])

  const handleAttachDocument = useCallback(
    (document: UploadedDocument) => {
      const attachedDoc: AttachedDocument = {
        id: document.id,
        name: document.name,
        type: document.type,
        size: document.size,
        uploadedDate: document.uploadedDate,
        category: document.category,
      }

      if (!attachedDocuments.find((doc) => doc.id === attachedDoc.id)) {
        setAttachedDocuments((prev) => [...prev, attachedDoc])
      }
    },
    [attachedDocuments],
  )

  const handleRemoveAttachment = useCallback((docId: string) => {
    setAttachedDocuments((prev) => prev.filter((doc) => doc.id !== docId))
  }, [])

  const handleCopyMessage = useCallback((message: string) => {
    navigator.clipboard.writeText(message)
  }, [])

  const handleFeedback = useCallback(
    (messageId: string, feedback: 'positive' | 'negative') => {
      console.log(`Feedback for message ${messageId}: ${feedback}`)
      // In a real app, this would send feedback to the backend
    },
    [],
  )

  const handleNewChat = useCallback(() => {
    setMessages([
      {
        id: '1',
        content:
          "Hello! I'm your AI Compliance Assistant. I can help you with regulatory compliance questions and document analysis. What would you like to know?",
        sender: 'ai',
        timestamp: new Date(),
        type: 'text',
      },
    ])
    setInputMessage('')
    setAttachedDocuments([])
    setIsTyping(false)
  }, [])

  const handleQuickAction = useCallback((action: string) => {
    const actionMessages: { [key: string]: string } = {
      document_analysis:
        "I'd like to analyze a document for compliance requirements.",
      compliance_check:
        'Can you help me check compliance against current regulations?',
      regulatory_updates:
        'What are the latest regulatory updates I should be aware of?',
      best_practices:
        'Can you share industry best practices for pharmaceutical compliance?',
    }

    setInputMessage(actionMessages[action] || '')
  }, [])

  return {
    messages,
    inputMessage,
    isTyping,
    attachedDocuments,
    setInputMessage,
    handleSendMessage,
    handleAttachDocument,
    handleRemoveAttachment,
    handleCopyMessage,
    handleFeedback,
    handleNewChat,
    handleQuickAction,
  }
}

-- Create electronic signature functions
-- Run this to add the missing functions for electronic signatures

-- Function to create electronic signature with validation
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(version_number::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Generate new signature ID
    signature_id := gen_random_uuid();
    
    -- Create electronic signature
    INSERT INTO electronic_signatures (
        id,
        organization_id,
        document_id,
        signer_id,
        signer_name,
        signature_type,
        signature_meaning,
        signature_reason,
        authentication_method,
        signature_hash,
        document_hash_at_signing,
        signature_metadata,
        signed_at,
        created_at,
        authentication_timestamp
    ) VALUES (
        signature_id,
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        p_signature_reason,
        p_authentication_method,
        signature_hash,
        document_hash,
        p_signature_metadata,
        NOW(),
        NOW(),
        NOW()
    );
    
    -- Log audit event if log_audit_event function exists
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'log_audit_event') THEN
        PERFORM log_audit_event(
            p_organization_id,
            p_signer_id,
            'sign'::audit_action_type,
            'Electronic signature created: ' || p_signature_meaning,
            'signature',
            signature_id,
            'Electronic Signature',
            NULL,
            jsonb_build_object(
                'signature_type', p_signature_type,
                'authentication_method', p_authentication_method,
                'document_id', p_document_id
            )
        );
    END IF;
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify signature integrity
CREATE OR REPLACE FUNCTION verify_signature_integrity(signature_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    sig_record RECORD;
BEGIN
    -- Get signature record
    SELECT * INTO sig_record
    FROM electronic_signatures
    WHERE id = signature_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Verify signature hash format
    IF LENGTH(sig_record.signature_hash) < 64 THEN
        RETURN FALSE;
    END IF;
    
    -- Verify required fields are present
    IF sig_record.signer_id IS NULL OR 
       sig_record.organization_id IS NULL OR 
       sig_record.signature_meaning IS NULL OR 
       sig_record.signature_type IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Verify timestamps are logical
    IF sig_record.authentication_timestamp > sig_record.signed_at THEN
        RETURN FALSE;
    END IF;
    
    -- Additional integrity checks can be added here
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get signature details with organization info
CREATE OR REPLACE FUNCTION get_signature_details(signature_id UUID)
RETURNS TABLE (
    id UUID,
    organization_name TEXT,
    signer_name TEXT,
    signature_type signature_type,
    signature_meaning TEXT,
    authentication_method authentication_method,
    signed_at TIMESTAMPTZ,
    document_title TEXT,
    is_valid BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        es.id,
        o.display_name as organization_name,
        es.signer_name,
        es.signature_type,
        es.signature_meaning,
        es.authentication_method,
        es.signed_at,
        rd.title as document_title,
        verify_signature_integrity(es.id) as is_valid
    FROM electronic_signatures es
    JOIN organizations o ON o.id = es.organization_id
    LEFT JOIN regulatory_documents rd ON rd.id = es.document_id
    WHERE es.id = signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;
GRANT EXECUTE ON FUNCTION verify_signature_integrity TO authenticated;
GRANT EXECUTE ON FUNCTION get_signature_details TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION create_electronic_signature IS 'Creates a 21 CFR Part 11 compliant electronic signature';
COMMENT ON FUNCTION verify_signature_integrity IS 'Verifies the integrity and validity of an electronic signature';
COMMENT ON FUNCTION get_signature_details IS 'Retrieves detailed information about an electronic signature';

-- Verify functions were created
SELECT 
    'SIGNATURE FUNCTIONS CREATED' as status,
    COUNT(*) as function_count,
    array_agg(routine_name ORDER BY routine_name) as functions
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('create_electronic_signature', 'verify_signature_integrity', 'get_signature_details');

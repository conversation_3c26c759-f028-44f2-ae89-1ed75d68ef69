import { Shield, Mail, Phone, MapPin } from 'lucide-react'

import { Separator } from '@/components/ui-radix/separator'

export function FooterSection() {
  return (
    <footer className="bg-slate-900 text-white py-16 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <div>
            <div className="flex items-center space-x-3 mb-6">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-600 to-purple-700">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">AI Compliance</h3>
                <p className="text-xs text-purple-400">
                  Pharmaceutical Intelligence
                </p>
              </div>
            </div>
            <p className="text-slate-400 text-sm mb-6 leading-relaxed">
              Empowering pharmaceutical manufacturers with AI-driven regulatory
              compliance solutions for a safer, more efficient future.
            </p>
          </div>

          <div>
            <h4 className="font-bold mb-6 text-white">Product</h4>
            <ul className="space-y-3 text-sm text-slate-400">
              <li>
                <a
                  href="#features"
                  className="hover:text-purple-400 transition-colors"
                >
                  Features
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  API Documentation
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  Integrations
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  Security
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-bold mb-6 text-white">Company</h4>
            <ul className="space-y-3 text-sm text-slate-400">
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  Careers
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  Blog
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-purple-400 transition-colors">
                  Press Kit
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-bold mb-6 text-white">Contact</h4>
            <ul className="space-y-3 text-sm text-slate-400">
              <li className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-purple-400" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-purple-400" />
                <span>+****************</span>
              </li>
              <li className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-purple-400" />
                <span>San Francisco, CA</span>
              </li>
            </ul>
          </div>
        </div>

        <Separator className="mb-8 bg-slate-800" />

        <div className="flex flex-col md:flex-row justify-between items-center text-sm text-slate-400">
          <div className="flex space-x-6 mb-4 md:mb-0">
            <a href="#" className="hover:text-purple-400 transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-purple-400 transition-colors">
              Terms of Service
            </a>
            <a href="#" className="hover:text-purple-400 transition-colors">
              Cookie Policy
            </a>
          </div>
          <div className="text-center md:text-right">
            <p>© 2024 AI Compliance Platform. All rights reserved.</p>
            <p className="text-xs text-slate-500 mt-1">
              Trusted by 500+ pharmaceutical organizations worldwide
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

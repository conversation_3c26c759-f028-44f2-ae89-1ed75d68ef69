"""
CrewAI Multi-Agent System

Specialized pharmaceutical compliance agents for complex regulatory analysis.
Provides coordinated multi-agent workflows for comprehensive compliance assessment.

Features:
- Specialized pharmaceutical compliance agents
- Coordinated multi-agent workflows
- Task delegation and result synthesis
- Type safety with Pydantic validation
- Comprehensive error handling and logging
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator
# Import client dynamically to avoid circular imports

# Configure logging
logger = logging.getLogger(__name__)

class AgentRole(str, Enum):
    """Available agent roles."""
    REGULATORY_ANALYST = "regulatory_analyst"
    COMPLIANCE_OFFICER = "compliance_officer"
    RISK_ASSESSOR = "risk_assessor"
    DOCUMENTATION_SPECIALIST = "documentation_specialist"
    QUALITY_ASSURANCE = "quality_assurance"

class AgentTask(BaseModel):
    """Task model for agent execution."""
    task_id: str = Field(..., description="Unique task identifier")
    description: str = Field(..., description="Task description")
    context: str = Field(..., description="Task context and background")
    expected_output: str = Field(..., description="Expected output format")
    agent_role: AgentRole = Field(..., description="Agent role to execute task")
    priority: int = Field(default=1, ge=1, le=5, description="Task priority (1=highest)")

    @field_validator('description', 'context', 'expected_output')
    @classmethod
    def validate_non_empty_strings(cls, v):
        """Validate that required strings are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()

class AgentResponse(BaseModel):
    """Response model for agent task execution."""
    task_id: str
    agent_role: AgentRole
    result: str
    confidence: float
    processing_time: float
    sources_used: List[str]

class CrewRequest(BaseModel):
    """Request model for multi-agent crew execution."""
    query: str = Field(..., min_length=1, max_length=2000, description="Main query or problem")
    context_documents: List[Dict[str, Any]] = Field(default=[], description="Context documents")
    required_agents: List[AgentRole] = Field(default=[], description="Required agent roles")
    coordination_mode: str = Field(default="sequential", description="Agent coordination mode")

    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        """Validate and sanitize query."""
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()

class CrewResponse(BaseModel):
    """Response model for multi-agent crew execution."""
    query: str
    agent_responses: List[AgentResponse]
    synthesized_result: str
    coordination_mode: str
    total_processing_time: float
    confidence_score: float

class CrewAIError(Exception):
    """Custom exception for CrewAI operations."""
    pass

class PharmaceuticalAgent:
    """
    Base class for pharmaceutical compliance agents.
    Each agent has specialized knowledge and capabilities.
    """

    def __init__(self, role: AgentRole, ai_client=None):
        """Initialize pharmaceutical agent."""
        self.role = role
        self.ai_client = ai_client
        self.system_prompt = self._get_system_prompt()

        logger.info(f"Initialized {role.value} agent")

    def _get_system_prompt(self) -> str:
        """Get role-specific system prompt."""
        prompts = {
            AgentRole.REGULATORY_ANALYST: """You are a Regulatory Analyst specializing in pharmaceutical compliance.
Your expertise includes:
- FDA, EMA, and ICH guidelines analysis
- Regulatory pathway assessment
- Submission requirement analysis
- Regulatory timeline planning
- Cross-jurisdictional compliance mapping

Focus on providing accurate regulatory guidance and identifying compliance requirements.""",

            AgentRole.COMPLIANCE_OFFICER: """You are a Compliance Officer with deep pharmaceutical industry experience.
Your expertise includes:
- GMP compliance assessment
- Quality system evaluation
- Audit preparation and response
- Corrective and preventive actions (CAPA)
- Risk-based compliance strategies

Focus on practical compliance implementation and risk mitigation.""",

            AgentRole.RISK_ASSESSOR: """You are a Risk Assessment Specialist for pharmaceutical operations.
Your expertise includes:
- Risk identification and analysis
- Impact assessment methodologies
- Mitigation strategy development
- Regulatory risk evaluation
- Business continuity planning

Focus on comprehensive risk analysis and actionable mitigation recommendations.""",

            AgentRole.DOCUMENTATION_SPECIALIST: """You are a Documentation Specialist for pharmaceutical compliance.
Your expertise includes:
- Regulatory document preparation
- Standard operating procedures (SOPs)
- Validation documentation
- Change control documentation
- Electronic records compliance (21 CFR Part 11)

Focus on documentation requirements and best practices.""",

            AgentRole.QUALITY_ASSURANCE: """You are a Quality Assurance Specialist in pharmaceutical manufacturing.
Your expertise includes:
- Quality system design and implementation
- Process validation and qualification
- Quality control testing strategies
- Deviation investigation and trending
- Continuous improvement initiatives

Focus on quality system optimization and compliance assurance."""
        }

        return prompts.get(self.role, "You are a pharmaceutical compliance expert.")

    async def execute_task(self, task: AgentTask, context_docs: List[Dict[str, Any]] = None) -> AgentResponse:
        """Execute a specific task using this agent's expertise."""
        if not self.ai_client:
            raise CrewAIError("AI client not initialized")

        try:
            import time
            start_time = time.time()

            # Build context from documents
            context_text = ""
            if context_docs:
                context_parts = []
                for i, doc in enumerate(context_docs, 1):
                    content = doc.get("content", "")
                    source = doc.get("source", f"Document {i}")
                    context_parts.append(f"Document {i} ({source}):\n{content}\n")
                context_text = "\n".join(context_parts)

            # Create task-specific prompt
            task_prompt = f"""Task: {task.description}

Context: {task.context}

{f"Reference Documents:\n{context_text}" if context_text else ""}

Expected Output: {task.expected_output}

Please provide a comprehensive analysis based on your expertise as a {self.role.value}."""

            # Import request models dynamically
            from .client import ChatRequest, ChatMessage

            # Generate response using AI client
            chat_request = ChatRequest(
                messages=[
                    ChatMessage(role="system", content=self.system_prompt),
                    ChatMessage(role="user", content=task_prompt)
                ],
                model="moonshot/kimi-k2",
                max_tokens=1500,
                temperature=0.1
            )

            chat_response = await self.ai_client.chat_completion(chat_request)
            processing_time = time.time() - start_time

            # Extract sources used
            sources_used = []
            if context_docs:
                sources_used = [doc.get("source", "Unknown") for doc in context_docs]

            logger.info(f"{self.role.value} completed task {task.task_id} in {processing_time:.2f}s")

            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result=chat_response.content,
                confidence=0.8,  # Could be calculated based on response quality
                processing_time=processing_time,
                sources_used=sources_used
            )

        except Exception as e:
            logger.error(f"Agent {self.role.value} task execution failed: {e}")
            raise CrewAIError(f"Task execution failed: {e}")

class PharmaceuticalCrew:
    """
    Multi-Agent Crew for Pharmaceutical Compliance

    Coordinates multiple specialized agents to provide comprehensive
    regulatory and compliance analysis.
    """

    def __init__(self):
        """Initialize the pharmaceutical crew."""
        self.agents: Dict[AgentRole, PharmaceuticalAgent] = {}
        self.ai_client = None
        self._is_initialized = False

        logger.info("Initializing Pharmaceutical Crew")

    async def initialize(self) -> None:
        """Initialize the crew and all agents."""
        if self._is_initialized:
            return

        try:
            # Import and initialize AI client dynamically
            from .client import get_ai_client
            self.ai_client = await get_ai_client()

            # Initialize all agents
            for role in AgentRole:
                self.agents[role] = PharmaceuticalAgent(role, self.ai_client)

            self._is_initialized = True
            logger.info("Pharmaceutical crew initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize crew: {e}")
            raise CrewAIError(f"Crew initialization failed: {e}")

    async def execute_crew_task(self, request: CrewRequest) -> CrewResponse:
        """Execute a multi-agent crew task."""
        if not self._is_initialized:
            await self.initialize()

        try:
            import time
            start_time = time.time()

            logger.info(f"Executing crew task with {len(request.required_agents)} agents")

            # Determine which agents to use
            agents_to_use = request.required_agents if request.required_agents else list(AgentRole)

            # Create tasks for each agent
            tasks = self._create_agent_tasks(request, agents_to_use)

            # Execute tasks based on coordination mode
            if request.coordination_mode == "sequential":
                agent_responses = await self._execute_sequential(tasks, request.context_documents)
            elif request.coordination_mode == "parallel":
                agent_responses = await self._execute_parallel(tasks, request.context_documents)
            else:
                raise CrewAIError(f"Unsupported coordination mode: {request.coordination_mode}")

            # Synthesize results
            synthesized_result = await self._synthesize_results(request.query, agent_responses)

            total_processing_time = time.time() - start_time

            # Calculate overall confidence
            confidence_score = sum(resp.confidence for resp in agent_responses) / len(agent_responses)

            logger.info(f"Crew task completed in {total_processing_time:.2f}s")

            return CrewResponse(
                query=request.query,
                agent_responses=agent_responses,
                synthesized_result=synthesized_result,
                coordination_mode=request.coordination_mode,
                total_processing_time=total_processing_time,
                confidence_score=confidence_score
            )

        except Exception as e:
            logger.error(f"Crew task execution failed: {e}")
            raise CrewAIError(f"Crew execution failed: {e}")

    def _create_agent_tasks(self, request: CrewRequest, agents_to_use: List[AgentRole]) -> List[AgentTask]:
        """Create specific tasks for each agent based on the request."""
        tasks = []

        for i, role in enumerate(agents_to_use):
            task_descriptions = {
                AgentRole.REGULATORY_ANALYST: f"Analyze the regulatory implications of: {request.query}",
                AgentRole.COMPLIANCE_OFFICER: f"Assess compliance requirements for: {request.query}",
                AgentRole.RISK_ASSESSOR: f"Identify and assess risks related to: {request.query}",
                AgentRole.DOCUMENTATION_SPECIALIST: f"Identify documentation requirements for: {request.query}",
                AgentRole.QUALITY_ASSURANCE: f"Evaluate quality assurance aspects of: {request.query}"
            }

            task = AgentTask(
                task_id=f"task_{i+1}_{role.value}",
                description=task_descriptions[role],
                context=f"User query: {request.query}",
                expected_output="Comprehensive analysis with specific recommendations and action items",
                agent_role=role,
                priority=1
            )

            tasks.append(task)

        return tasks

    async def _execute_sequential(
        self,
        tasks: List[AgentTask],
        context_docs: List[Dict[str, Any]]
    ) -> List[AgentResponse]:
        """Execute tasks sequentially."""
        responses = []

        for task in tasks:
            agent = self.agents[task.agent_role]
            response = await agent.execute_task(task, context_docs)
            responses.append(response)

        return responses

    async def _execute_parallel(
        self,
        tasks: List[AgentTask],
        context_docs: List[Dict[str, Any]]
    ) -> List[AgentResponse]:
        """Execute tasks in parallel."""
        async def execute_task(task):
            agent = self.agents[task.agent_role]
            return await agent.execute_task(task, context_docs)

        responses = await asyncio.gather(*[execute_task(task) for task in tasks])
        return responses

    async def _synthesize_results(self, query: str, agent_responses: List[AgentResponse]) -> str:
        """Synthesize results from multiple agents."""
        try:
            # Combine all agent results
            combined_analysis = []
            for response in agent_responses:
                combined_analysis.append(f"{response.agent_role.value.replace('_', ' ').title()}:\n{response.result}\n")

            synthesis_prompt = f"""Based on the following multi-agent analysis of the query "{query}", provide a comprehensive synthesized response that integrates insights from all agents:

{chr(10).join(combined_analysis)}

Please provide:
1. Executive Summary
2. Key Findings
3. Recommendations
4. Action Items
5. Risk Considerations

Ensure the response is coherent, actionable, and addresses all aspects of the original query."""

            chat_request = ChatRequest(
                messages=[
                    ChatMessage(role="system", content="You are a senior pharmaceutical compliance consultant synthesizing multi-agent analysis."),
                    ChatMessage(role="user", content=synthesis_prompt)
                ],
                model="moonshot/kimi-k2",
                max_tokens=2000,
                temperature=0.1
            )

            chat_response = await self.ai_client.chat_completion(chat_request)
            return chat_response.content

        except Exception as e:
            logger.error(f"Result synthesis failed: {e}")
            # Fallback to simple concatenation
            return "\n\n".join([f"{resp.agent_role.value}: {resp.result}" for resp in agent_responses])

    async def health_check(self) -> bool:
        """Check if the crew is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()

            # Test with a simple crew task
            test_request = CrewRequest(
                query="Test query for health check",
                required_agents=[AgentRole.REGULATORY_ANALYST]
            )

            await self.execute_crew_task(test_request)
            return True

        except Exception as e:
            logger.error(f"Crew health check failed: {e}")
            return False

# Global crew instance
_pharmaceutical_crew: Optional[PharmaceuticalCrew] = None

async def get_pharmaceutical_crew() -> PharmaceuticalCrew:
    """Get or create the global pharmaceutical crew instance."""
    global _pharmaceutical_crew

    if _pharmaceutical_crew is None:
        _pharmaceutical_crew = PharmaceuticalCrew()
        await _pharmaceutical_crew.initialize()

    return _pharmaceutical_crew

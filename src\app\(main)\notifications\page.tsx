'use client'

import { But<PERSON> } from '@/components/ui-radix/button'
import { Card, CardContent } from '@/components/ui-radix/card'
import { Input } from '@/components/ui-radix/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui-radix/select'
import { usePageMetadata } from '@/hooks/use-page-metadata'
import { useToast } from '@/hooks/use-toast'
import { CircleCheckBig, RefreshCw, Search, Settings } from 'lucide-react'
import { useMemo, useState } from 'react'
import { NotificationList } from './components/notification-list'
import { NotificationStats } from './components/notification-stats'
import { NotificationTabs } from './components/notification-tabs'

// Mock data - replace with actual API calls
const mockNotifications = [
  {
    id: '1',
    title: 'Compliance Check Completed',
    message: 'Your document "Financial Report Q3" has passed all compliance checks.',
    type: 'success' as const,
    timestamp: '2024-01-15T10:30:00Z',
    isRead: false,
    isStarred: true,
    category: 'compliance',
    priority: 'high' as const
  },
  {
    id: '2',
    title: 'New Regulation Update',
    message: 'GDPR guidelines have been updated. Please review the changes.',
    type: 'info' as const,
    timestamp: '2024-01-15T09:15:00Z',
    isRead: false,
    isStarred: false,
    category: 'regulation',
    priority: 'medium' as const
  },
  {
    id: '3',
    title: 'Document Upload Failed',
    message: 'Failed to upload "Contract_v2.pdf". Please check file format and try again.',
    type: 'error' as const,
    timestamp: '2024-01-15T08:45:00Z',
    isRead: true,
    isStarred: false,
    category: 'system',
    priority: 'high' as const
  },
  {
    id: '4',
    title: 'Weekly Compliance Report',
    message: 'Your weekly compliance summary is ready for review.',
    type: 'info' as const,
    timestamp: '2024-01-14T16:00:00Z',
    isRead: true,
    isStarred: true,
    category: 'report',
    priority: 'low' as const
  },
  {
    id: '5',
    title: 'System Maintenance Scheduled',
    message: 'Scheduled maintenance on January 20th from 2:00 AM to 4:00 AM UTC.',
    type: 'warning' as const,
    timestamp: '2024-01-14T14:30:00Z',
    isRead: false,
    isStarred: false,
    category: 'system',
    priority: 'medium' as const
  }
]

export default function NotificationsPage() {
  usePageMetadata('Notifications', 'Stay updated with compliance alerts, system updates, and regulatory changes')

  const [notifications, setNotifications] = useState(mockNotifications)
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // Filter notifications based on active tab and filters
  const filteredNotifications = useMemo(() => {
    let filtered = notifications

    // Filter by tab
    switch (activeTab) {
      case 'unread':
        filtered = filtered.filter(n => !n.isRead)
        break

      case 'critical':
        filtered = filtered.filter(n => n.priority === 'high')
        break
      default:
        // 'all' - no additional filtering
        break
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(query) ||
        n.message.toLowerCase().includes(query) ||
        n.category.toLowerCase().includes(query)
      )
    }

    // Filter by types
    if (selectedTypes.length > 0) {
      filtered = filtered.filter(n => selectedTypes.includes(n.type))
    }

    // Filter by categories
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(n => selectedCategories.includes(n.category))
    }

    return filtered
  }, [notifications, activeTab, searchQuery, selectedTypes, selectedCategories])

  // Calculate statistics
  const stats = useMemo(() => {
    const total = notifications.length
    const unread = notifications.filter(n => !n.isRead).length
    const urgent = notifications.filter(n => n.priority === 'high').length

    return { total, unread, urgent }
  }, [notifications])

  // Calculate tab counts
  const tabCounts = useMemo(() => {
    return {
      all: notifications.length,
      unread: notifications.filter(n => !n.isRead).length,
      critical: notifications.filter(n => n.priority === 'high').length
    }
  }, [notifications])

  // Handle toggle read status
  const handleToggleRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isRead: !notification.isRead }
          : notification
      )
    )
  }



  // Handle mark all as read
  const handleMarkAllRead = () => {
    setNotifications(prev =>
      prev.map(notification =>
        filteredNotifications.some(fn => fn.id === notification.id)
          ? { ...notification, isRead: true }
          : notification
      )
    )
    toast({
      title: 'Notifications marked as read',
      description: `${filteredNotifications.filter(n => !n.isRead).length} notifications marked as read`,
    })
  }

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  return (
    <main className="flex-1 overflow-auto">
      <div className="container mx-auto">
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Notifications</h1>
              <p className="text-muted-foreground mt-1">
                Stay updated with compliance alerts, system updates, and regulatory changes
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleMarkAllRead}
                className="inline-flex items-center justify-center gap-2"
              >
                <CircleCheckBig className="mr-2 h-4 w-4" />
                Mark All Read
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
                className="inline-flex items-center justify-center gap-2"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
              <Button
                variant="outline"
                className="inline-flex items-center justify-center gap-2"
              >
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <NotificationStats stats={stats} />

          {/* Main Content Card */}
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardContent className="p-6">
              {/* Search and Filters */}
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search notifications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={selectedTypes[0] || 'all'} onValueChange={(value) => setSelectedTypes(value === 'all' ? [] : [value])}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={selectedCategories[0] || 'all'} onValueChange={(value) => setSelectedCategories(value === 'all' ? [] : [value])}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="compliance">Compliance</SelectItem>
                      <SelectItem value="regulation">Regulation</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                      <SelectItem value="report">Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs and Notification List */}
          <div className="space-y-4">
            <NotificationTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              counts={tabCounts}
            />

            <NotificationList
              notifications={filteredNotifications}
              activeTab={activeTab}
              isLoading={isLoading}
              onToggleRead={handleToggleRead}
              onMarkAllRead={handleMarkAllRead}
            />
          </div>
        </div>
      </div>
    </main>
  )
}

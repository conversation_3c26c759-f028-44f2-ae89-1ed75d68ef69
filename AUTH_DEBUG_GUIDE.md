# 🚨 VIGILENS AUTHENTICATION DEBUG GUIDE

## 🔍 **ROOT CAUSE IDENTIFIED**

The authentication error "An authentication error occurred. Please try again." is caused by **RLS policy conflicts** during user registration. The trigger function cannot insert user profiles due to restrictive Row Level Security policies.

## 🛠️ **IMMEDIATE FIX STEPS**

### **Step 1: Apply the Database Fix**
1. Open Supabase Dashboard: https://supabase.com/dashboard/project/esgciouphhajolkojipw/sql
2. Copy and paste the entire contents of `fix_auth_trigger.sql`
3. Click "Run" to execute the fix
4. Verify you see success messages in the output

### **Step 2: Verify the Fix**
1. In the same SQL Editor, copy and paste the contents of `test_auth_fix.sql`
2. Click "Run" to execute the verification tests
3. Check that all tests show "✅ PASS" status

### **Step 3: Test User Registration**
1. Go to your signup page: http://localhost:3001/signup
2. Try registering with a new email address
3. You should now see "Please check your email for a confirmation link" instead of the error

## 🔧 **WHAT THE FIX DOES**

### **1. Fixes RLS Policy Conflict**
```sql
-- BEFORE (Blocking): Required organization_id in JWT during signup
CREATE POLICY "Controlled profile creation" ON user_profiles
    FOR INSERT WITH CHECK (
        id = auth.uid()
        AND organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- AFTER (Allowing): Permits trigger-based user creation
CREATE POLICY "Allow trigger user creation" ON user_profiles
    FOR INSERT WITH CHECK (
        auth.uid() IS NULL  -- Allow during registration (no session)
        OR 
        id = auth.uid()     -- Allow users to create own profile
    );
```

### **2. Ensures Demo Organization Exists**
- Creates 'demo-pharma-corp' organization if missing
- Provides fallback organization for new users

### **3. Enhanced Trigger Function**
- Robust error handling for missing dependencies
- Better user name extraction from metadata
- Graceful handling of missing audit functions

### **4. Verification & Monitoring**
- Built-in checks for organization existence
- Trigger validation
- Test user creation simulation

## 🚨 **TROUBLESHOOTING**

### **If Fix Doesn't Work:**

1. **Check Supabase Logs:**
   - Go to: https://supabase.com/dashboard/project/esgciouphhajolkojipw/logs
   - Look for database errors during signup attempts

2. **Verify Environment Variables:**
   ```bash
   # Check .env.local file
   NEXT_PUBLIC_SUPABASE_URL=https://esgciouphhajolkojipw.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

3. **Test Direct Database Access:**
   ```sql
   -- Run in Supabase SQL Editor
   SELECT * FROM organizations WHERE name = 'demo-pharma-corp';
   SELECT * FROM information_schema.triggers WHERE trigger_name = 'handle_new_user_trigger';
   ```

4. **Check Browser Console:**
   - Open Developer Tools (F12)
   - Look for network errors during signup
   - Check for JavaScript errors

### **Common Issues:**

| Error | Cause | Solution |
|-------|-------|----------|
| "Demo organization not found" | Missing organization record | Re-run Step 1 of fix |
| "Trigger not found" | Trigger creation failed | Check database permissions |
| "RLS policy blocks insert" | Old policy still active | Drop old policies manually |
| "Function undefined_function" | Missing audit function | Normal - fix handles this gracefully |

## 📊 **VERIFICATION CHECKLIST**

- [ ] Demo organization exists in database
- [ ] `handle_new_user()` function is created with SECURITY DEFINER
- [ ] `handle_new_user_trigger` is active on auth.users table
- [ ] Old "Controlled profile creation" RLS policy is removed
- [ ] New "Allow trigger user creation" RLS policy is active
- [ ] Test user registration completes successfully
- [ ] User profile is created automatically in user_profiles table

## 🔄 **NEXT STEPS AFTER FIX**

1. **Test Registration Flow:**
   - Register with multiple test email addresses
   - Verify user profiles are created correctly
   - Check that users are assigned to demo organization

2. **Monitor for Issues:**
   - Watch Supabase logs for any new errors
   - Test login after email confirmation
   - Verify dashboard access for new users

3. **Production Considerations:**
   - Plan organization assignment strategy for real users
   - Consider email domain-based organization mapping
   - Implement proper user onboarding flow

## 📞 **SUPPORT**

If issues persist after applying this fix:
1. Check Supabase dashboard logs for specific error messages
2. Run the verification tests in `test_auth_fix.sql`
3. Provide the exact error messages and test results for further debugging

**The fix addresses the core RLS policy conflict that was blocking user registration. This should resolve the authentication error immediately.**

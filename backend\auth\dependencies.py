"""
Authentication Dependencies for VigiLens FastAPI Backend

VCP_002_1: Supabase Auth Configuration & Provider Setup
VCP_002_2: FastAPI Authentication Middleware & JWT Handling

Implements Supabase JWT validation with FastAPI dependency injection.
Following DEVELOPMENT_RULES.md and Backend-Rules.md for pharmaceutical compliance.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from supabase import Client, create_client
import os
from functools import lru_cache

from .models import AuthUser, PharmaceuticalRole
from .rbac import PharmaceuticalPermissions


# Configure logging (no console.log in production per DEVELOPMENT_RULES_2.md)
logger = logging.getLogger(__name__)


class SupabaseConfig:
    """Supabase configuration following July 2025 patterns."""

    def __init__(self):
        self.url = os.getenv("SUPABASE_URL", "https://test.supabase.co")
        self.service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "test-service-role-key")
        self.anon_key = os.getenv("SUPABASE_ANON_KEY", "test-anon-key")
        self.jwt_secret = os.getenv("SUPABASE_JWT_SECRET", "test-jwt-secret")

        # Only raise error in production environment
        if os.getenv("ENVIRONMENT") == "production" and not all([self.url, self.service_role_key, self.jwt_secret]):
            raise ValueError("Missing required Supabase environment variables")

    @lru_cache(maxsize=1)
    def get_client(self) -> Client:
        """Get Supabase client with service role for backend operations."""
        return create_client(self.url, self.service_role_key)


# Global Supabase configuration
supabase_config = SupabaseConfig()


class SupabaseJWTBearer(HTTPBearer):
    """
    Custom JWT Bearer authentication using Supabase validation.

    Implements the 6-expert synthesis approach:
    - Expert 2: Direct Supabase Auth integration with service role
    - Expert 4: Performance optimization with caching
    - Expert 6: Production reliability with error handling
    """

    def __init__(self, auto_error: bool = True):
        super().__init__(auto_error=auto_error)
        self.supabase = supabase_config.get_client()

    async def __call__(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> AuthUser:
        """
        Validate JWT token and return authenticated user.

        Args:
            credentials: HTTP Authorization credentials

        Returns:
            AuthUser: Authenticated user with pharmaceutical compliance data

        Raises:
            HTTPException: 401 if authentication fails
        """
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token = credentials.credentials

        try:
            # Validate token with Supabase (Expert 2 approach)
            user_response = self.supabase.auth.get_user(token)

            if not user_response.user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            supabase_user = user_response.user

            # Get user profile with pharmaceutical compliance data
            profile_response = self.supabase.table("user_profiles").select(
                "*, organizations(name)"
            ).eq("id", supabase_user.id).single().execute()

            if not profile_response.data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User profile not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            profile = profile_response.data

            # Create AuthUser with pharmaceutical compliance fields
            auth_user = AuthUser(
                id=UUID(supabase_user.id),
                email=supabase_user.email,
                email_confirmed_at=datetime.fromisoformat(supabase_user.email_confirmed_at.replace('Z', '+00:00')) if supabase_user.email_confirmed_at else None,
                phone=supabase_user.phone,
                phone_confirmed_at=datetime.fromisoformat(supabase_user.phone_confirmed_at.replace('Z', '+00:00')) if supabase_user.phone_confirmed_at else None,
                created_at=datetime.fromisoformat(supabase_user.created_at.replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(supabase_user.updated_at.replace('Z', '+00:00')),
                last_sign_in_at=datetime.fromisoformat(supabase_user.last_sign_in_at.replace('Z', '+00:00')) if supabase_user.last_sign_in_at else None,

                # Pharmaceutical compliance fields from profile
                role=PharmaceuticalRole(profile.get("role", "viewer")),
                organization_id=UUID(profile["organization_id"]),
                organization_name=profile["organizations"]["name"],
                full_name=profile.get("full_name"),
                is_active=profile.get("is_active", True),
                is_mfa_enabled=profile.get("is_mfa_enabled", False),
                mfa_methods=profile.get("mfa_methods", []),

                # Metadata
                user_metadata=supabase_user.user_metadata or {},
                app_metadata=supabase_user.app_metadata or {},
            )

            # Log authentication event for audit trail (pharmaceutical compliance)
            await self._log_auth_event(auth_user, "token_validation", "success")

            return auth_user

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            await self._log_auth_event(None, "token_validation", "failure", str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed",
                headers={"WWW-Authenticate": "Bearer"},
            )

    async def _log_auth_event(
        self,
        user: Optional[AuthUser],
        event_type: str,
        status: str,
        error_message: Optional[str] = None
    ):
        """Log authentication events for pharmaceutical compliance audit trail."""
        try:
            audit_data = {
                "event_type": event_type,
                "status": status,
                "user_id": str(user.id) if user else None,
                "organization_id": str(user.organization_id) if user else None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error_message": error_message,
                "ip_address": None,  # Will be added by middleware
                "user_agent": None,  # Will be added by middleware
            }

            # Insert audit log (non-blocking for performance)
            self.supabase.table("audit_logs").insert(audit_data).execute()

        except Exception as e:
            logger.error(f"Failed to log auth event: {str(e)}")
            # Don't raise - audit logging failure shouldn't break authentication


# Dependency instances
jwt_bearer = SupabaseJWTBearer()


async def get_current_user(user: AuthUser = Depends(jwt_bearer)) -> AuthUser:
    """
    Get the current authenticated user.

    Args:
        user: Authenticated user from JWT bearer

    Returns:
        AuthUser: Current authenticated user
    """
    return user


async def get_current_active_user(user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """
    Get the current active user (must be active and email confirmed).

    Args:
        user: Current authenticated user

    Returns:
        AuthUser: Current active user

    Raises:
        HTTPException: 403 if user is inactive or email not confirmed
    """
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is inactive"
        )

    if not user.email_confirmed_at:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email address not confirmed"
        )

    return user


def require_role(*allowed_roles: PharmaceuticalRole):
    """
    Dependency factory for role-based access control.

    Args:
        allowed_roles: Roles that are allowed to access the endpoint

    Returns:
        Dependency function that checks user role
    """
    def role_checker(user: AuthUser = Depends(get_current_active_user)) -> AuthUser:
        if user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required roles: {[role.value for role in allowed_roles]}"
            )
        return user

    return role_checker


def require_permissions(*required_permissions: PharmaceuticalPermissions):
    """
    Dependency factory for permission-based access control.

    Args:
        required_permissions: Permissions required to access the endpoint

    Returns:
        Dependency function that checks user permissions
    """
    def permission_checker(user: AuthUser = Depends(get_current_active_user)) -> AuthUser:
        from .rbac import get_role_permissions

        user_permissions = get_role_permissions(user.role)

        for permission in required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {permission.value}"
                )

        return user

    return permission_checker


def require_mfa(operation: Optional[str] = None):
    """
    Dependency factory for MFA requirement checking.

    Args:
        operation: Specific operation requiring MFA verification

    Returns:
        Dependency function that checks MFA requirements
    """
    def mfa_checker(user: AuthUser = Depends(get_current_active_user)) -> AuthUser:
        from .mfa import MFAManager

        # Initialize MFA manager
        mfa_manager = MFAManager(supabase_config.get_client())

        # Check if MFA is required for this user/operation
        if mfa_manager.is_mfa_required(user, operation):
            if not user.is_mfa_enabled:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Multi-factor authentication is required for this operation"
                )

            # Check if user has completed MFA verification recently
            # In production, implement MFA session tracking
            # For now, we assume MFA is verified if user is authenticated

        return user

    return mfa_checker


def require_mfa_verification(challenge_id: str):
    """
    Dependency factory for MFA challenge verification.

    Args:
        challenge_id: MFA challenge ID to verify

    Returns:
        Dependency function that verifies MFA challenge
    """
    def mfa_verifier(user: AuthUser = Depends(get_current_active_user)) -> AuthUser:
        # Check if MFA challenge exists and is valid
        supabase = supabase_config.get_client()

        challenge_response = supabase.table("mfa_challenges").select("*").eq(
            "id", challenge_id
        ).eq("user_id", str(user.id)).eq("is_verified", True).single().execute()

        if not challenge_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Valid MFA verification required"
            )

        return user

    return mfa_verifier


# Convenience dependencies for common role checks
require_super_admin = require_role(PharmaceuticalRole.SUPER_ADMIN)
require_org_admin = require_role(PharmaceuticalRole.SUPER_ADMIN, PharmaceuticalRole.ORG_ADMIN)
require_quality_manager = require_role(
    PharmaceuticalRole.SUPER_ADMIN,
    PharmaceuticalRole.ORG_ADMIN,
    PharmaceuticalRole.QUALITY_MANAGER
)
require_regulatory_lead = require_role(
    PharmaceuticalRole.SUPER_ADMIN,
    PharmaceuticalRole.ORG_ADMIN,
    PharmaceuticalRole.REGULATORY_LEAD
)
require_compliance_officer = require_role(
    PharmaceuticalRole.SUPER_ADMIN,
    PharmaceuticalRole.ORG_ADMIN,
    PharmaceuticalRole.COMPLIANCE_OFFICER
)


@lru_cache(maxsize=128)
def get_cached_user_permissions(user_id: str, role: str) -> List[str]:
    """
    Cache user permissions for performance optimization (Expert 4 approach).

    Args:
        user_id: User ID for cache key
        role: User role for permission lookup

    Returns:
        List of permission strings
    """
    from .rbac import get_role_permissions

    role_enum = PharmaceuticalRole(role)
    permissions = get_role_permissions(role_enum)
    return [perm.value for perm in permissions]

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  User,
  Camera,
  Settings,
  Award,
  Activity,
  Calendar,
  Download,
  Upload,
  Edit,
  CheckCircle,
  Clock,
  FileText,
  TrendingUp,
  Star,
  Target,
} from "lucide-react";

export default function Profile() {
  const [isEditing, setIsEditing] = useState(false);
  const navigate = useNavigate();
  const [profileData, setProfileData] = useState({
    firstName: "David",
    lastName: "Chen",
    email: "<EMAIL>",
    jobTitle: "Compliance Manager",
    company: "PharmaCorp Manufacturing",
    department: "Quality Assurance",
    location: "San Francisco, CA",
    bio: "Experienced compliance manager with 10+ years in pharmaceutical manufacturing. Specialized in FDA regulations and quality management systems.",
    phone: "+****************",
    timeZone: "PST",
  });

  const activityStats = [
    {
      label: "Documents Analyzed",
      value: "127",
      change: "+23%",
      icon: FileText,
      color: "text-blue-500",
    },
    {
      label: "Compliance Checks",
      value: "89",
      change: "+15%",
      icon: CheckCircle,
      color: "text-green-500",
    },
    {
      label: "AI Interactions",
      value: "342",
      change: "+45%",
      icon: Activity,
      color: "text-purple-500",
    },
    {
      label: "Hours Saved",
      value: "156",
      change: "+28%",
      icon: Clock,
      color: "text-orange-500",
    },
  ];

  const recentActivities = [
    {
      action: "Completed compliance check",
      target: "Manufacturing SOP v3.2",
      timestamp: "2 hours ago",
      score: 98,
      type: "success",
    },
    {
      action: "Uploaded document",
      target: "Quality Manual 2023",
      timestamp: "5 hours ago",
      score: null,
      type: "upload",
    },
    {
      action: "Asked AI Assistant",
      target: "FDA process validation requirements",
      timestamp: "1 day ago",
      score: null,
      type: "ai",
    },
    {
      action: "Downloaded report",
      target: "Q2 Compliance Summary",
      timestamp: "2 days ago",
      score: null,
      type: "download",
    },
    {
      action: "Completed compliance check",
      target: "Batch Record Template",
      timestamp: "3 days ago",
      score: 85,
      type: "success",
    },
  ];

  const achievements = [
    {
      title: "Compliance Expert",
      description: "Completed 100+ compliance checks",
      earned: "2023-06-15",
      icon: Award,
      color: "bg-yellow-500",
    },
    {
      title: "AI Power User",
      description: "Used AI Assistant 500+ times",
      earned: "2023-06-10",
      icon: Star,
      color: "bg-purple-500",
    },
    {
      title: "Document Master",
      description: "Analyzed 200+ documents",
      earned: "2023-05-28",
      icon: FileText,
      color: "bg-blue-500",
    },
    {
      title: "Efficiency Champion",
      description: "Saved 100+ hours with automation",
      earned: "2023-05-15",
      icon: TrendingUp,
      color: "bg-green-500",
    },
  ];

  const skillsProgress = [
    { skill: "FDA Regulations", level: 95, category: "Regulatory" },
    { skill: "Quality Management", level: 88, category: "Quality" },
    { skill: "Risk Assessment", level: 82, category: "Analysis" },
    { skill: "Process Validation", level: 90, category: "Validation" },
    { skill: "Data Integrity", level: 85, category: "Data" },
    { skill: "AI Tools", level: 78, category: "Technology" },
  ];

  const complianceGoals = [
    {
      title: "Complete Advanced AI Training",
      progress: 65,
      deadline: "July 15, 2023",
      status: "In Progress",
    },
    {
      title: "Achieve 95% Compliance Score",
      progress: 87,
      deadline: "June 30, 2023",
      status: "On Track",
    },
    {
      title: "Process 50 Documents This Quarter",
      progress: 84,
      deadline: "June 30, 2023",
      status: "On Track",
    },
  ];

  const handleSaveProfile = () => {
    setIsEditing(false);
    // In real app, would save to backend
    console.log("Saving profile:", profileData);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-success" />;
      case "upload":
        return <Upload className="h-4 w-4 text-info" />;
      case "ai":
        return <Activity className="h-4 w-4 text-purple-500" />;
      case "download":
        return <Download className="h-4 w-4 text-muted-foreground" />;
      default:
        return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Profile Header */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                      DC
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    size="icon"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full"
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-1">
                  <h1 className="text-2xl font-bold text-foreground">
                    {profileData.firstName} {profileData.lastName}
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    {profileData.jobTitle}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {profileData.company} • {profileData.location}
                  </p>
                  <div className="flex items-center space-x-2 pt-2">
                    <Badge className="bg-success text-success-foreground">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                    <Badge variant="outline">Premium Member</Badge>
                    <Badge variant="outline">Verified</Badge>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  {isEditing ? "Cancel" : "Edit Profile"}
                </Button>
                {isEditing && (
                  <Button onClick={handleSaveProfile}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {activityStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {stat.label}
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {stat.value}
                    </p>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <stat.icon className={`h-5 w-5 ${stat.color}`} />
                    <span className="text-xs text-success">{stat.change}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Profile Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          {/* Overview */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {isEditing ? (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                              id="firstName"
                              value={profileData.firstName}
                              onChange={(e) =>
                                setProfileData({
                                  ...profileData,
                                  firstName: e.target.value,
                                })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="lastName">Last Name</Label>
                            <Input
                              id="lastName"
                              value={profileData.lastName}
                              onChange={(e) =>
                                setProfileData({
                                  ...profileData,
                                  lastName: e.target.value,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bio">Bio</Label>
                          <Textarea
                            id="bio"
                            value={profileData.bio}
                            onChange={(e) =>
                              setProfileData({
                                ...profileData,
                                bio: e.target.value,
                              })
                            }
                          />
                        </div>
                      </>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-foreground">
                            About Me
                          </h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {profileData.bio}
                          </p>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium text-foreground">
                              Contact
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {profileData.email}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {profileData.phone}
                            </p>
                          </div>
                          <div>
                            <h4 className="font-medium text-foreground">
                              Location
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {profileData.location}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {profileData.timeZone}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center">
                      <p className="text-3xl font-bold text-foreground">92%</p>
                      <p className="text-sm text-muted-foreground">
                        Average Compliance Score
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-3xl font-bold text-foreground">156</p>
                      <p className="text-sm text-muted-foreground">
                        Hours Saved This Month
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-3xl font-bold text-foreground">4.8</p>
                      <p className="text-sm text-muted-foreground">
                        AI Interaction Rating
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Activity */}
          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-4 p-3 rounded-lg border"
                    >
                      {getActivityIcon(activity.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground">
                          {activity.action}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {activity.target}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {activity.timestamp}
                        </p>
                      </div>
                      {activity.score && (
                        <div className="text-right">
                          <p className="text-sm font-medium text-foreground">
                            {activity.score}%
                          </p>
                          <p className="text-xs text-muted-foreground">Score</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Achievements */}
          <TabsContent value="achievements" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {achievements.map((achievement, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div
                        className={`flex h-12 w-12 items-center justify-center rounded-lg ${achievement.color} text-white`}
                      >
                        <achievement.icon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold mb-1">
                          {achievement.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mb-2">
                          {achievement.description}
                        </p>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="mr-1 h-3 w-3" />
                          Earned {achievement.earned}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings">
            <div className="text-center py-12">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Profile Settings</h3>
              <p className="text-muted-foreground">
                Advanced profile settings and preferences are available in the
                main Settings page.
              </p>
              <Button
                className="mt-4"
                variant="outline"
                onClick={() => navigate("/settings")}
              >
                <Settings className="mr-2 h-4 w-4" />
                Go to Settings
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

import { create } from 'zustand'

interface ProfileData {
  readonly firstName: string
  readonly lastName: string
  readonly email: string
  readonly jobTitle: string
  readonly timeZone: string
  readonly company: string
  readonly location: string
  readonly status: string
  readonly phone: string
  readonly bio: string
  readonly avatarUrl?: string
}

interface Session {
  readonly id: string
  readonly device: string
  readonly ip: string
  readonly lastActive: string
  readonly current: boolean
}

interface ProfileState {
  readonly profileData: ProfileData
  readonly sessions: readonly Session[]
  readonly isEditing: boolean
  readonly avatarBlob?: Blob
  setProfileData: (data: Partial<ProfileData>) => void
  setIsEditing: (editing: boolean) => void
  setAvatarBlob: (blob?: Blob) => void
  revokeSession: (id: string) => void
}

const defaultProfile: ProfileData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  jobTitle: 'Compliance Manager',
  timeZone: 'America/New_York',
  company: 'PharmaCorp Manufacturing',
  location: 'San Francisco, CA',
  status: 'active',
  phone: '+****************',
  bio: 'Experienced compliance manager with 10+ years in pharmaceutical manufacturing. Specialized in FDA regulations and quality management systems.',
}

const mockSessions: Session[] = [
  {
    id: '1',
    device: 'Chrome on Windows',
    ip: '***********',
    lastActive: '2 minutes ago',
    current: true,
  },
  {
    id: '2',
    device: 'Safari on iPhone',
    ip: '***********',
    lastActive: '1 hour ago',
    current: false,
  },
]

export const useProfileStore = create<ProfileState>()((set) => ({
  profileData: defaultProfile,
  sessions: mockSessions,
  isEditing: false,
  setProfileData: (data) =>
    set((state) => ({
      profileData: { ...state.profileData, ...data },
    })),
  setIsEditing: (editing) => set({ isEditing: editing }),
  setAvatarBlob: (blob) => {
    if (blob) {
      set({ avatarBlob: blob })
    } else {
      set((state) => {
        const { avatarBlob, ...rest } = state
        return rest
      })
    }
  },
  revokeSession: (id) =>
    set((state) => ({
      sessions: state.sessions.filter((s) => s.id !== id),
    })),
}))

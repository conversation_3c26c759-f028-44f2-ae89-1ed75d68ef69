'use client'

import { Search, Filter, X } from 'lucide-react'
import { Input } from '@/components/ui-radix/input'
import { Button } from '@/components/ui-radix/button'
import { Badge } from '@/components/ui-radix/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui-radix/select'


interface NotificationFiltersProps {
  readonly searchQuery: string
  readonly selectedType: string
  readonly selectedStatus: string
  readonly selectedCategory: string
  readonly onSearchChange: (query: string) => void
  readonly onTypeChange: (type: string) => void
  readonly onStatusChange: (status: string) => void
  readonly onCategoryChange: (category: string) => void
  readonly onClearFilters: () => void
  readonly hasActiveFilters: boolean
}

const notificationTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'info', label: 'Info' },
  { value: 'warning', label: 'Warning' },
  { value: 'success', label: 'Success' },
  { value: 'error', label: 'Error' }
]

const notificationStatuses = [
  { value: 'all', label: 'All Status' },
  { value: 'unread', label: 'Unread' },
  { value: 'read', label: 'Read' },
  { value: 'starred', label: 'Starred' }
]

const notificationCategories = [
  { value: 'all', label: 'All Categories' },
  { value: 'compliance', label: 'Compliance' },
  { value: 'system', label: 'System' },
  { value: 'updates', label: 'Updates' },
  { value: 'security', label: 'Security' },
  { value: 'documents', label: 'Documents' }
]

export function NotificationFilters({
  searchQuery,
  selectedType,
  selectedStatus,
  selectedCategory,
  onSearchChange,
  onTypeChange,
  onStatusChange,
  onCategoryChange,
  onClearFilters,
  hasActiveFilters
}: NotificationFiltersProps) {
  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search notifications..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 pr-4"
        />
      </div>

      {/* Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex flex-1 gap-3">
          {/* Type Filter */}
          <Select value={selectedType} onValueChange={onTypeChange}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              {notificationTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Status Filter */}
          <Select value={selectedStatus} onValueChange={onStatusChange}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {notificationStatuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Category Filter */}
          <Select value={selectedCategory} onValueChange={onCategoryChange}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {notificationCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            className="flex items-center gap-2 whitespace-nowrap"
          >
            <X className="h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {searchQuery}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onSearchChange('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {selectedType !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Type: {notificationTypes.find(t => t.value === selectedType)?.label}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onTypeChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {selectedStatus !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {notificationStatuses.find(s => s.value === selectedStatus)?.label}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onStatusChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {selectedCategory !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category: {notificationCategories.find(c => c.value === selectedCategory)?.label}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onCategoryChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}

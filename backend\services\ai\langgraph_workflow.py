"""
LangGraph Workflow Orchestration

Advanced workflow orchestration for complex pharmaceutical compliance analysis.
Provides state-based workflow management with conditional routing and decision points.

Features:
- State-based workflow management
- Conditional routing and decision points
- Multi-step analysis workflows
- Integration with RAG pipeline and CrewAI agents
- Type safety with Pydantic validation
- Comprehensive error handling and logging
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union, Callable
from enum import Enum
from pydantic import BaseModel, Field, field_validator
# Import services dynamically to avoid circular imports

# Configure logging
logger = logging.getLogger(__name__)

class WorkflowState(str, Enum):
    """Workflow execution states."""
    INITIALIZED = "initialized"
    ANALYZING = "analyzing"
    RETRIEVING_CONTEXT = "retrieving_context"
    AGENT_PROCESSING = "agent_processing"
    SYNTHESIZING = "synthesizing"
    COMPLETED = "completed"
    FAILED = "failed"

class WorkflowType(str, Enum):
    """Available workflow types."""
    REGULATORY_ANALYSIS = "regulatory_analysis"
    COMPLIANCE_ASSESSMENT = "compliance_assessment"
    RISK_EVALUATION = "risk_evaluation"
    DOCUMENT_REVIEW = "document_review"
    CHANGE_IMPACT_ANALYSIS = "change_impact_analysis"

class WorkflowStep(BaseModel):
    """Individual workflow step."""
    step_id: str = Field(..., description="Unique step identifier")
    name: str = Field(..., description="Step name")
    description: str = Field(..., description="Step description")
    step_type: str = Field(..., description="Step type (rag, agent, synthesis, etc.)")
    parameters: Dict[str, Any] = Field(default={}, description="Step parameters")
    dependencies: List[str] = Field(default=[], description="Dependent step IDs")

    @field_validator('step_id', 'name', 'description', 'step_type')
    @classmethod
    def validate_non_empty_strings(cls, v):
        """Validate that required strings are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()

class WorkflowRequest(BaseModel):
    """Request model for workflow execution."""
    workflow_type: WorkflowType = Field(..., description="Type of workflow to execute")
    query: str = Field(..., min_length=1, max_length=2000, description="Main query or problem")
    context_documents: List[Dict[str, Any]] = Field(default=[], description="Context documents")
    parameters: Dict[str, Any] = Field(default={}, description="Workflow-specific parameters")

    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        """Validate and sanitize query."""
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()

class WorkflowStepResult(BaseModel):
    """Result of a workflow step execution."""
    step_id: str
    step_name: str
    result: Any
    execution_time: float
    status: str
    error_message: Optional[str] = None

class WorkflowResponse(BaseModel):
    """Response model for workflow execution."""
    workflow_type: WorkflowType
    query: str
    final_result: str
    step_results: List[WorkflowStepResult]
    total_execution_time: float
    final_state: WorkflowState
    confidence_score: float

class WorkflowError(Exception):
    """Custom exception for workflow execution errors."""
    pass

class PharmaceuticalWorkflow:
    """
    LangGraph-inspired Workflow Orchestrator

    Manages complex multi-step workflows for pharmaceutical compliance analysis.
    Provides state management, conditional routing, and step orchestration.
    """

    def __init__(self):
        """Initialize the workflow orchestrator."""
        self.rag_pipeline = None
        self.crew = None
        self._is_initialized = False
        self.workflow_definitions = self._define_workflows()

        logger.info("Initializing Pharmaceutical Workflow Orchestrator")

    async def initialize(self) -> None:
        """Initialize workflow components."""
        if self._is_initialized:
            return

        try:
            logger.info("Initializing workflow components...")

            # Import and initialize services dynamically
            from .rag_pipeline import get_rag_pipeline
            from .crewai_agents import get_pharmaceutical_crew

            self.rag_pipeline = await get_rag_pipeline()
            self.crew = await get_pharmaceutical_crew()

            self._is_initialized = True
            logger.info("Workflow orchestrator initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize workflow orchestrator: {e}")
            raise WorkflowError(f"Workflow initialization failed: {e}")

    def _define_workflows(self) -> Dict[WorkflowType, List[WorkflowStep]]:
        """Define available workflow templates."""
        workflows = {
            WorkflowType.REGULATORY_ANALYSIS: [
                WorkflowStep(
                    step_id="context_retrieval",
                    name="Context Retrieval",
                    description="Retrieve relevant regulatory documents",
                    step_type="rag",
                    parameters={"mode": "simple", "max_docs": 10}
                ),
                WorkflowStep(
                    step_id="regulatory_analysis",
                    name="Regulatory Analysis",
                    description="Analyze regulatory implications",
                    step_type="agent",
                    parameters={"agents": ["regulatory_analyst", "compliance_officer"]},
                    dependencies=["context_retrieval"]
                ),
                WorkflowStep(
                    step_id="synthesis",
                    name="Result Synthesis",
                    description="Synthesize analysis results",
                    step_type="synthesis",
                    dependencies=["regulatory_analysis"]
                )
            ],

            WorkflowType.COMPLIANCE_ASSESSMENT: [
                WorkflowStep(
                    step_id="context_retrieval",
                    name="Context Retrieval",
                    description="Retrieve compliance-related documents",
                    step_type="rag",
                    parameters={"mode": "workflow", "max_docs": 15}
                ),
                WorkflowStep(
                    step_id="compliance_analysis",
                    name="Compliance Analysis",
                    description="Assess compliance requirements",
                    step_type="agent",
                    parameters={"agents": ["compliance_officer", "quality_assurance"]},
                    dependencies=["context_retrieval"]
                ),
                WorkflowStep(
                    step_id="gap_analysis",
                    name="Gap Analysis",
                    description="Identify compliance gaps",
                    step_type="agent",
                    parameters={"agents": ["risk_assessor"]},
                    dependencies=["compliance_analysis"]
                ),
                WorkflowStep(
                    step_id="synthesis",
                    name="Result Synthesis",
                    description="Synthesize assessment results",
                    step_type="synthesis",
                    dependencies=["gap_analysis"]
                )
            ],

            WorkflowType.RISK_EVALUATION: [
                WorkflowStep(
                    step_id="context_retrieval",
                    name="Context Retrieval",
                    description="Retrieve risk-related documents",
                    step_type="rag",
                    parameters={"mode": "multi_agent", "max_docs": 12}
                ),
                WorkflowStep(
                    step_id="risk_identification",
                    name="Risk Identification",
                    description="Identify potential risks",
                    step_type="agent",
                    parameters={"agents": ["risk_assessor", "regulatory_analyst"]},
                    dependencies=["context_retrieval"]
                ),
                WorkflowStep(
                    step_id="impact_assessment",
                    name="Impact Assessment",
                    description="Assess risk impact and likelihood",
                    step_type="agent",
                    parameters={"agents": ["risk_assessor", "compliance_officer"]},
                    dependencies=["risk_identification"]
                ),
                WorkflowStep(
                    step_id="mitigation_planning",
                    name="Mitigation Planning",
                    description="Develop risk mitigation strategies",
                    step_type="agent",
                    parameters={"agents": ["compliance_officer", "quality_assurance"]},
                    dependencies=["impact_assessment"]
                ),
                WorkflowStep(
                    step_id="synthesis",
                    name="Result Synthesis",
                    description="Synthesize risk evaluation results",
                    step_type="synthesis",
                    dependencies=["mitigation_planning"]
                )
            ]
        }

        # Add simplified workflows for document_review and change_impact_analysis
        workflows[WorkflowType.DOCUMENT_REVIEW] = workflows[WorkflowType.REGULATORY_ANALYSIS]
        workflows[WorkflowType.CHANGE_IMPACT_ANALYSIS] = workflows[WorkflowType.RISK_EVALUATION]

        return workflows

    async def execute_workflow(self, request: WorkflowRequest) -> WorkflowResponse:
        """Execute a complete workflow."""
        if not self._is_initialized:
            await self.initialize()

        try:
            import time
            start_time = time.time()

            logger.info(f"Executing {request.workflow_type.value} workflow")

            # Get workflow definition
            workflow_steps = self.workflow_definitions.get(request.workflow_type)
            if not workflow_steps:
                raise WorkflowError(f"Workflow type not supported: {request.workflow_type}")

            # Initialize workflow state
            workflow_state = {
                "current_state": WorkflowState.INITIALIZED,
                "query": request.query,
                "context_documents": request.context_documents,
                "parameters": request.parameters,
                "step_results": {},
                "final_result": ""
            }

            # Execute workflow steps
            step_results = []
            for step in workflow_steps:
                try:
                    # Check dependencies
                    if not self._check_dependencies(step, workflow_state["step_results"]):
                        raise WorkflowError(f"Dependencies not met for step: {step.step_id}")

                    # Execute step
                    step_result = await self._execute_step(step, workflow_state)
                    step_results.append(step_result)

                    # Update workflow state
                    workflow_state["step_results"][step.step_id] = step_result

                    if step_result.status == "failed":
                        workflow_state["current_state"] = WorkflowState.FAILED
                        break

                except Exception as e:
                    logger.error(f"Step {step.step_id} failed: {e}")
                    step_results.append(WorkflowStepResult(
                        step_id=step.step_id,
                        step_name=step.name,
                        result=None,
                        execution_time=0.0,
                        status="failed",
                        error_message=str(e)
                    ))
                    workflow_state["current_state"] = WorkflowState.FAILED
                    break

            # Determine final result
            if workflow_state["current_state"] != WorkflowState.FAILED:
                workflow_state["current_state"] = WorkflowState.COMPLETED
                # Get result from synthesis step or last step
                synthesis_result = workflow_state["step_results"].get("synthesis")
                if synthesis_result:
                    workflow_state["final_result"] = synthesis_result.result
                elif step_results:
                    workflow_state["final_result"] = step_results[-1].result

            total_execution_time = time.time() - start_time

            # Calculate confidence score
            successful_steps = [r for r in step_results if r.status == "completed"]
            confidence_score = len(successful_steps) / len(step_results) if step_results else 0.0

            logger.info(f"Workflow completed in {total_execution_time:.2f}s")

            return WorkflowResponse(
                workflow_type=request.workflow_type,
                query=request.query,
                final_result=workflow_state["final_result"],
                step_results=step_results,
                total_execution_time=total_execution_time,
                final_state=workflow_state["current_state"],
                confidence_score=confidence_score
            )

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            raise WorkflowError(f"Workflow execution failed: {e}")

    def _check_dependencies(self, step: WorkflowStep, completed_steps: Dict[str, Any]) -> bool:
        """Check if step dependencies are satisfied."""
        for dep in step.dependencies:
            if dep not in completed_steps:
                return False
            if completed_steps[dep].status != "completed":
                return False
        return True

    async def _execute_step(self, step: WorkflowStep, workflow_state: Dict[str, Any]) -> WorkflowStepResult:
        """Execute a single workflow step."""
        import time
        start_time = time.time()

        try:
            logger.info(f"Executing step: {step.name}")

            if step.step_type == "rag":
                result = await self._execute_rag_step(step, workflow_state)
            elif step.step_type == "agent":
                result = await self._execute_agent_step(step, workflow_state)
            elif step.step_type == "synthesis":
                result = await self._execute_synthesis_step(step, workflow_state)
            else:
                raise WorkflowError(f"Unknown step type: {step.step_type}")

            execution_time = time.time() - start_time

            return WorkflowStepResult(
                step_id=step.step_id,
                step_name=step.name,
                result=result,
                execution_time=execution_time,
                status="completed"
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Step execution failed: {e}")

            return WorkflowStepResult(
                step_id=step.step_id,
                step_name=step.name,
                result=None,
                execution_time=execution_time,
                status="failed",
                error_message=str(e)
            )

    async def _execute_rag_step(self, step: WorkflowStep, workflow_state: Dict[str, Any]) -> str:
        """Execute a RAG pipeline step."""
        mode = RAGMode(step.parameters.get("mode", "simple"))
        max_docs = step.parameters.get("max_docs", 5)

        rag_request = RAGRequest(
            query=workflow_state["query"],
            mode=mode,
            max_context_docs=max_docs
        )

        rag_response = await self.rag_pipeline.process_query(rag_request)

        # Store context documents for later steps
        workflow_state["context_documents"] = [
            doc.dict() for doc in rag_response.context_documents
        ]

        return rag_response.answer

    async def _execute_agent_step(self, step: WorkflowStep, workflow_state: Dict[str, Any]) -> str:
        """Execute a multi-agent step."""
        agent_names = step.parameters.get("agents", [])
        agent_roles = [AgentRole(name) for name in agent_names]

        crew_request = CrewRequest(
            query=workflow_state["query"],
            context_documents=workflow_state.get("context_documents", []),
            required_agents=agent_roles,
            coordination_mode="parallel"
        )

        crew_response = await self.crew.execute_crew_task(crew_request)
        return crew_response.synthesized_result

    async def _execute_synthesis_step(self, step: WorkflowStep, workflow_state: Dict[str, Any]) -> str:
        """Execute a result synthesis step."""
        # Combine results from all previous steps
        previous_results = []
        for step_id, step_result in workflow_state["step_results"].items():
            if step_result.status == "completed":
                previous_results.append(f"{step_result.step_name}: {step_result.result}")

        synthesis_text = f"Query: {workflow_state['query']}\n\n"
        synthesis_text += "Analysis Results:\n" + "\n\n".join(previous_results)
        synthesis_text += "\n\nPlease provide a comprehensive synthesis of the above analysis."

        return synthesis_text

    async def health_check(self) -> bool:
        """Check if the workflow orchestrator is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()

            # Test with a simple workflow
            test_request = WorkflowRequest(
                workflow_type=WorkflowType.REGULATORY_ANALYSIS,
                query="Test workflow health check"
            )

            await self.execute_workflow(test_request)
            return True

        except Exception as e:
            logger.error(f"Workflow health check failed: {e}")
            return False

# Global workflow instance
_pharmaceutical_workflow: Optional[PharmaceuticalWorkflow] = None

async def get_pharmaceutical_workflow() -> PharmaceuticalWorkflow:
    """Get or create the global workflow orchestrator instance."""
    global _pharmaceutical_workflow

    if _pharmaceutical_workflow is None:
        _pharmaceutical_workflow = PharmaceuticalWorkflow()
        await _pharmaceutical_workflow.initialize()

    return _pharmaceutical_workflow

import { Card, CardContent } from '@/components/ui-radix/card';
import { cn } from '@/lib/utils';
import { TrendingDown, TrendingUp } from 'lucide-react';
import * as React from 'react';
import type { MetricCardProps } from '../types';

/**
 * MetricCard Component - Document Statistics Display
 *
 * Features:
 * - Large metric value with description
 * - Optional trend indicators with color coding
 * - Clickable for navigation to filtered views
 * - Accessible with proper labeling
 * - Responsive design
 */
export const MetricCard: React.FC<MetricCardProps> = React.memo(
  function MetricCard({
    label,
    value,
    icon: Icon,
    trend,
    onClick
  }) {
    return (
      <Card
        className={cn(
          "transition-all duration-200",
          onClick && "cursor-pointer hover:shadow-md hover:border-primary/20"
        )}
        onClick={onClick}
        role={onClick ? "button" : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={onClick ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        } : undefined}
        aria-label={onClick ? `View ${label.toLowerCase()} details` : undefined}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-2xl font-bold text-foreground leading-none">
                {value}
              </p>
              <p className="text-sm text-muted-foreground">
                {label}
              </p>
              {trend && (
                <div className={cn(
                  "flex items-center text-xs",
                  trend.isPositive ? "text-success" : "text-destructive"
                )}>
                  {trend.isPositive ? (
                    <TrendingUp className="h-3 w-3 mr-1" aria-hidden="true" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" aria-hidden="true" />
                  )}
                  <span>{Math.abs(trend.value)}%</span>
                  <span className="sr-only">
                    {trend.isPositive ? 'increased' : 'decreased'} from last period
                  </span>
                </div>
              )}
            </div>
            <Icon className="h-8 w-8 text-primary" aria-hidden="true" />
          </div>
        </CardContent>
      </Card>
    );
  }
);

MetricCard.displayName = 'MetricCard';

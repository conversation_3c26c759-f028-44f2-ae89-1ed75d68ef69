-- Test Electronic Signatures Table and TypeScript Types
-- Run this to verify the electronic_signatures table is properly created

-- Test 1: Verify electronic_signatures table exists
SELECT 
    'ELECTRONIC SIGNATURES TABLE' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND table_schema = 'public';

-- Test 2: Verify enum types exist
SELECT 
    'SIGNATURE ENUM TYPES' as test_name,
    CASE 
        WHEN COUNT(*) = 2 THEN '✅ COMPLETE'
        ELSE '❌ INCOMPLETE'
    END as status,
    COUNT(*) as enum_count,
    array_agg(typname ORDER BY typname) as enum_types
FROM pg_type 
WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND typtype = 'e'
AND typname IN ('signature_type', 'authentication_method');

-- Test 3: Verify RLS policies exist
SELECT 
    'ELECTRONIC SIGNATURES RLS' as test_name,
    CASE 
        WHEN COUNT(*) >= 4 THEN '✅ COMPLETE'
        ELSE '❌ INCOMPLETE'
    END as status,
    COUNT(*) as policy_count,
    array_agg(policyname ORDER BY policyname) as policies
FROM pg_policies 
WHERE tablename = 'electronic_signatures'
AND schemaname = 'public';

-- Test 4: Verify functions exist
SELECT 
    'SIGNATURE FUNCTIONS' as test_name,
    CASE 
        WHEN COUNT(*) >= 2 THEN '✅ COMPLETE'
        ELSE '❌ INCOMPLETE'
    END as status,
    COUNT(*) as function_count,
    array_agg(routine_name ORDER BY routine_name) as functions
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('create_electronic_signature', 'verify_signature_integrity');

-- Test 5: Test creating a sample electronic signature (if organizations exist)
DO $$
DECLARE
    test_org_id UUID;
    test_user_id UUID;
    test_doc_id UUID;
    signature_id UUID;
BEGIN
    -- Get test organization
    SELECT id INTO test_org_id 
    FROM organizations 
    LIMIT 1;
    
    IF test_org_id IS NOT NULL THEN
        -- Get test user
        SELECT id INTO test_user_id 
        FROM user_profiles 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        -- Get test document
        SELECT id INTO test_doc_id 
        FROM regulatory_documents 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        IF test_user_id IS NOT NULL AND test_doc_id IS NOT NULL THEN
            -- Create test signature
            SELECT create_electronic_signature(
                test_org_id,
                test_doc_id,
                test_user_id,
                'approval'::signature_type,
                'Test signature for TypeScript types verification - 21 CFR Part 11 compliant',
                'Testing electronic signature functionality',
                'password'::authentication_method,
                '{"test": true, "verification": "typescript_types"}'::jsonb
            ) INTO signature_id;
            
            RAISE NOTICE 'Test signature created with ID: %', signature_id;
        END IF;
    END IF;
END $$;

-- Test 6: Verify test signature was created
SELECT 
    'TEST SIGNATURE CREATED' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SUCCESS'
        ELSE '❌ FAILED'
    END as status,
    COUNT(*) as signature_count
FROM electronic_signatures 
WHERE signature_meaning LIKE '%TypeScript types verification%';

-- Test 7: Show sample electronic signature data
SELECT 
    'SAMPLE SIGNATURE DATA' as test_name,
    es.signature_type,
    es.signer_name,
    es.signature_meaning,
    es.authentication_method,
    es.signed_at,
    o.display_name as organization
FROM electronic_signatures es
JOIN organizations o ON o.id = es.organization_id
WHERE es.signature_meaning LIKE '%TypeScript types verification%'
ORDER BY es.signed_at DESC
LIMIT 1;

-- Test 8: Verify signature integrity
SELECT 
    'SIGNATURE INTEGRITY CHECK' as test_name,
    CASE 
        WHEN verify_signature_integrity(es.id) THEN '✅ VALID'
        ELSE '❌ INVALID'
    END as status,
    es.id as signature_id
FROM electronic_signatures es
WHERE es.signature_meaning LIKE '%TypeScript types verification%'
ORDER BY es.signed_at DESC
LIMIT 1;

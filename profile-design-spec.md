# User Profile System - Comprehensive UI/UX Design Specification

> **Version:** 3.0  
> **Target Framework:** Next.js 15.3.4 + React 19.1.0  
> **Design System:** shadcn/ui + Tailwind CSS 4.0.1  
> **Compliance:** WCAG 2.1 AA, Mobile-First, 200-Line Rule  

## 1. Functional Overview

The User Profile System provides comprehensive user account management, personal information editing, activity tracking, security settings, and achievement systems. It serves as the central hub for user identity and account preferences.

### Core Features
- **Profile Management**: Avatar upload, personal information, contact details
- **Activity Tracking**: Recent actions, statistics, performance metrics
- **Security Settings**: Password management, 2FA, session control
- **Achievement System**: Badges, progress tracking, skill development
- **Preferences**: Notification settings, theme preferences, accessibility options
- **Session Management**: Active sessions, device management, security monitoring

## 2. Component Architecture

### 2.1 File Structure
```
src/app/(main)/profile/
├── page.tsx                    # Main profile page (Client Component)
├── components/
│   ├── profile-header.tsx      # Avatar, name, basic info, edit controls
│   ├── profile-stats.tsx       # Activity statistics grid
│   ├── profile-tabs.tsx        # Tab navigation component
│   ├── profile-overview.tsx    # Personal info and quick stats
│   ├── profile-activity.tsx    # Recent activity feed
│   ├── profile-achievements.tsx # Badges and progress
│   ├── profile-settings.tsx    # Preferences and configuration
│   ├── profile-security.tsx    # Security settings and sessions
│   ├── avatar-upload.tsx       # Avatar upload component
│   └── profile-edit-form.tsx   # Inline editing forms
├── hooks/
│   ├── use-profile.ts          # Main profile data hook
│   ├── use-avatar-upload.ts    # Avatar management hook
│   ├── use-activity-feed.ts    # Activity data hook
│   └── use-profile-settings.ts # Settings management hook
├── store.ts                    # Zustand store for profile state
└── types.ts                    # TypeScript interfaces
```

### 2.2 Component Hierarchy
```
ProfilePage (Client Component)
├── PageHeader (title + metadata)
├── ProfileHeader (avatar + basic info + edit controls)
├── ProfileStats (activity metrics grid)
├── ProfileTabs (Overview/Activity/Achievements/Settings/Security)
├── TabContent
│   ├── ProfileOverview (personal info + quick stats)
│   ├── ProfileActivity (recent actions feed)
│   ├── ProfileAchievements (badges + progress)
│   ├── ProfileSettings (preferences + notifications)
│   └── ProfileSecurity (password + 2FA + sessions)
└── AvatarUpload (modal/drawer)
```

## 3. Layout Specifications

### 3.1 Page Layout Structure
```css
.profile-page {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* 24px */
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.profile-header {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 1024px) {
  .profile-content {
    grid-template-columns: 2fr 1fr;
  }
}
```

### 3.2 Responsive Breakpoints
- **Mobile (320px-768px)**: Single column, stacked layout, simplified navigation
- **Tablet (768px-1024px)**: 2-column grid for some sections, touch-optimized
- **Desktop (1024px+)**: Full layout with sidebar, expanded features

## 4. Data Models & TypeScript Interfaces

### 4.1 Core Profile Interfaces
```typescript
interface UserProfile {
  readonly id: string
  readonly firstName: string
  readonly lastName: string
  readonly email: string
  readonly avatar?: string
  readonly jobTitle: string
  readonly company: string
  readonly department: string
  readonly location: string
  readonly bio: string
  readonly phone: string
  readonly timeZone: string
  readonly status: 'active' | 'away' | 'offline'
  readonly complianceLevel: 'basic' | 'advanced' | 'expert'
  readonly joinDate: string
  readonly lastActive: string
  readonly preferences: UserPreferences
}

interface UserPreferences {
  readonly theme: 'light' | 'dark' | 'system'
  readonly language: string
  readonly notifications: NotificationPreferences
  readonly accessibility: AccessibilityPreferences
  readonly privacy: PrivacyPreferences
}

interface NotificationPreferences {
  readonly email: boolean
  readonly push: boolean
  readonly desktop: boolean
  readonly sound: boolean
  readonly categories: Record<string, boolean>
}

interface ActivityItem {
  readonly id: string
  readonly type: 'document' | 'compliance' | 'ai' | 'upload' | 'download'
  readonly action: string
  readonly target: string
  readonly timestamp: string
  readonly score?: number
  readonly metadata?: Record<string, unknown>
}

interface Achievement {
  readonly id: string
  readonly title: string
  readonly description: string
  readonly icon: string
  readonly color: string
  readonly earned: string
  readonly category: 'compliance' | 'productivity' | 'learning' | 'collaboration'
  readonly progress?: number
  readonly maxProgress?: number
}

interface UserSession {
  readonly id: string
  readonly deviceName: string
  readonly browser: string
  readonly location: string
  readonly ipAddress: string
  readonly lastActive: string
  readonly isCurrent: boolean
}
```

## 5. State Management (Zustand Store)

### 5.1 Store Structure
```typescript
interface ProfileState {
  // Data
  readonly profileData: UserProfile
  readonly activityFeed: readonly ActivityItem[]
  readonly achievements: readonly Achievement[]
  readonly sessions: readonly UserSession[]
  readonly avatarBlob?: Blob
  readonly isEditing: boolean
  readonly isLoading: boolean
  readonly error: string | null
  
  // Actions
  setProfileData: (data: Partial<UserProfile>) => void
  setIsEditing: (editing: boolean) => void
  setAvatarBlob: (blob: Blob | null) => void
  updatePreferences: (preferences: Partial<UserPreferences>) => void
  addActivityItem: (item: ActivityItem) => void
  revokeSession: (sessionId: string) => void
  saveProfile: () => Promise<void>
  uploadAvatar: (file: File) => Promise<void>
  fetchActivityFeed: () => Promise<void>
  fetchAchievements: () => Promise<void>
  fetchSessions: () => Promise<void>
}
```

## 6. Interactive Elements & Behaviors

### 6.1 Profile Header Interactions
- **Avatar Click**: Open avatar upload modal/drawer
- **Edit Button**: Toggle inline editing mode for profile fields
- **Save Button**: Persist changes with validation and feedback
- **Cancel Button**: Revert changes and exit editing mode

### 6.2 Avatar Upload System
- **Drag & Drop**: Support file drag and drop onto avatar area
- **File Picker**: Click to open file selection dialog
- **Image Cropping**: Built-in cropping tool with aspect ratio constraints
- **Preview**: Real-time preview of cropped image
- **Validation**: File type, size, and dimension validation

### 6.3 Activity Feed
- **Infinite Scroll**: Load more activities as user scrolls
- **Filter Options**: Filter by activity type, date range
- **Action Links**: Clickable links to related documents/pages
- **Refresh**: Pull-to-refresh on mobile, refresh button on desktop

### 6.4 Achievement System
- **Progress Bars**: Visual progress indicators for ongoing achievements
- **Tooltips**: Detailed descriptions on hover/focus
- **Categories**: Filter achievements by category
- **Share**: Social sharing for earned achievements

## 7. Accessibility Requirements

### 7.1 Keyboard Navigation
- **Tab**: Navigate through all interactive elements
- **Enter/Space**: Activate buttons and form controls
- **Arrow Keys**: Navigate within tab groups and lists
- **Escape**: Close modals and cancel editing modes

### 7.2 ARIA Labels & Roles
```html
<main role="main" aria-label="User Profile">
  <header role="banner" aria-label="Profile header">
    <img role="img" aria-label="User avatar" alt="Profile picture of [Name]">
    <button aria-label="Edit profile picture">
      <span aria-hidden="true">📷</span>
    </button>
  </header>
  
  <section role="region" aria-labelledby="stats-title">
    <h2 id="stats-title">Activity Statistics</h2>
  </section>
  
  <div role="tabpanel" aria-labelledby="overview-tab">
    <h2 id="overview-tab">Profile Overview</h2>
  </div>
</main>
```

### 7.3 Form Accessibility
- **Labels**: Explicit labels for all form inputs
- **Error Messages**: Associated with inputs via aria-describedby
- **Required Fields**: Marked with aria-required and visual indicators
- **Validation**: Real-time validation with screen reader announcements

## 8. Visual Design & Styling

### 8.1 Profile Header Design
```css
.profile-header {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem;
  background: var(--card);
  border-radius: var(--radius);
}

.profile-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--border);
}

.avatar-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 200ms ease;
}

.profile-avatar:hover .avatar-upload-overlay {
  opacity: 1;
}
```

### 8.2 Status Indicators
```css
.status-indicator {
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--background);
}

.status-active::after { background: hsl(var(--success)); }
.status-away::after { background: hsl(var(--warning)); }
.status-offline::after { background: hsl(var(--muted)); }
```

### 8.3 Achievement Badges
```css
.achievement-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--radius);
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 200ms ease;
}

.achievement-badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.achievement-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}
```

## 9. Security Features

### 9.1 Password Management
- **Current Password**: Required for password changes
- **Strength Indicator**: Real-time password strength feedback
- **Requirements**: Clear password policy display
- **Confirmation**: Double-entry confirmation for new passwords

### 9.2 Two-Factor Authentication
- **Setup Wizard**: Step-by-step 2FA configuration
- **QR Code**: Display QR code for authenticator apps
- **Backup Codes**: Generate and display recovery codes
- **Verification**: Test 2FA before enabling

### 9.3 Session Management
- **Active Sessions**: List all active sessions with details
- **Device Information**: Browser, OS, location for each session
- **Revoke Sessions**: Individual or bulk session termination
- **Security Alerts**: Notifications for suspicious activity

## 10. Performance Optimizations

### 10.1 Image Handling
- **Avatar Optimization**: Automatic image compression and resizing
- **Lazy Loading**: Load images only when visible
- **Caching**: Cache processed avatars with appropriate headers
- **Fallbacks**: Graceful fallbacks for missing or failed images

### 10.2 Data Management
- **Incremental Loading**: Load profile sections as needed
- **Optimistic Updates**: Immediate UI feedback for changes
- **Debounced Saves**: Batch profile updates to reduce API calls
- **Local Storage**: Cache non-sensitive preferences locally

## 11. Error Handling & Edge Cases

### 11.1 Upload Errors
- **File Size**: Clear messaging for oversized files
- **File Type**: Validation for supported image formats
- **Network Issues**: Retry mechanism for failed uploads
- **Processing Errors**: Fallback for image processing failures

### 11.2 Form Validation
- **Real-time Validation**: Immediate feedback for form fields
- **Server Validation**: Handle server-side validation errors
- **Conflict Resolution**: Handle concurrent edit conflicts
- **Data Loss Prevention**: Warn users about unsaved changes

## 12. Testing Requirements

### 12.1 Unit Tests
- Profile data management and updates
- Avatar upload functionality
- Form validation logic
- Achievement progress calculations

### 12.2 Integration Tests
- Complete profile editing workflow
- Security settings configuration
- Session management operations
- Activity feed updates

### 12.3 Accessibility Tests
- Keyboard navigation flow
- Screen reader compatibility
- Color contrast compliance
- Focus management

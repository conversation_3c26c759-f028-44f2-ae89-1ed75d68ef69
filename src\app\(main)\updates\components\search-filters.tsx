'use client'

import { Search } from 'lucide-react'

import { Card, CardContent } from '@/components/ui-radix/card'
import { Input } from '@/components/ui-radix/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui-radix/select'

import type { SearchFilters } from '../types'

interface SearchFiltersProps {
  readonly filters: SearchFilters;
  readonly onSearchChange: (query: string) => void;
  readonly onAgencyChange: (agency: string) => void;
  readonly onSeverityChange: (severity: string) => void;
  readonly onSortChange: (sort: string) => void;
}

export function SearchFilters({
  filters,
  onSearchChange,
  onAgencyChange,
  onSeverityChange,
  onSortChange,
}: SearchFiltersProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search updates by title, agency, or keyword..."
                value={filters.searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Select
              value={filters.selectedAgency}
              onValueChange={onAgencyChange}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Agencies" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Agencies</SelectItem>
                <SelectItem value="FDA">FDA</SelectItem>
                <SelectItem value="EMA">EMA</SelectItem>
                <SelectItem value="ICH">ICH</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.selectedSeverity}
              onValueChange={onSeverityChange}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severity</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.sortBy} onValueChange={onSortChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="severity">Severity</SelectItem>
                <SelectItem value="agency">Agency</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

import { useEffect } from 'react'

interface PageMetadataOptions {
  robots?: 'noindex' | 'index'
}

/**
 * Client-side page metadata management hook
 *
 * Updates document title and meta description dynamically
 * for client-side rendered pages in the App Router
 *
 * @param title - Page title (will be appended with app name)
 * @param description - Optional meta description
 * @param options - Additional metadata options
 */
export function usePageMetadata(
  title: string,
  description?: string,
  options?: PageMetadataOptions
) {
  useEffect(() => {
    // Update document title
    document.title = `${title} | AI Compliance`

    // Update meta description if provided
    if (description) {
      let metaDescription = document.querySelector('meta[name="description"]')
      if (!metaDescription) {
        metaDescription = document.createElement('meta')
        metaDescription.setAttribute('name', 'description')
        document.head.appendChild(metaDescription)
      }
      metaDescription.setAttribute('content', description)
    }

    // Handle robots meta for internal pages
    if (options?.robots === 'noindex') {
      let robotsMeta = document.querySelector('meta[name="robots"]')
      if (!robotsMeta) {
        robotsMeta = document.createElement('meta')
        robotsMeta.setAttribute('name', 'robots')
        document.head.appendChild(robotsMeta)
      }
      robotsMeta.setAttribute('content', 'noindex, nofollow')
    }
  }, [title, description, options])
}

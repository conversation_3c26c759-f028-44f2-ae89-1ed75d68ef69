'use client'

import { But<PERSON> } from '@/components/ui/button'

import { cn } from '@/lib/utils'
import { Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ProfileSettingsProps {
  readonly className?: string
  readonly isEditing?: boolean
}

export function ProfileSettings({ className, isEditing }: ProfileSettingsProps) {
  const router = useRouter()

  const handleGoToSettings = () => {
    router.push('/settings')
  }

  return (
    <div className={cn('text-center py-12', className)}>
      <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2">Profile Settings</h3>
      <p className="text-muted-foreground mb-4">
        Advanced profile settings and preferences are available in the main Settings page.
      </p>
      <Button variant="outline" onClick={handleGoToSettings}>
        <Settings className="mr-2 h-4 w-4" />
        Go to Settings
      </Button>
    </div>
  )
}

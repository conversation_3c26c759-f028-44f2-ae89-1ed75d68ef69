-- COMPLETE DATABASE RESET FOR VIGILENS
-- Run this in Supabase SQL Editor to start fresh

-- Step 1: Drop all triggers first
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Step 2: Drop all functions
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS log_audit_event(UUID, UUID, TEXT, TEXT, TEXT, UUID, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS add_electronic_signature(UUI<PERSON>, UUID, TEXT, JSONB) CASCADE;

-- Step 3: Drop all tables in correct order (respecting foreign keys)
DROP TABLE IF EXISTS permission_audit_log CASCADE;
DROP TABLE IF EXISTS audit_trail CASCADE;
DROP TABLE IF EXISTS document_versions CASCADE;
DROP TABLE IF EXISTS regulatory_documents CASCADE;
DROP TABLE IF EXISTS user_role_assignments CASCADE;
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;

-- Step 4: Drop all custom types/enums
DROP TYPE IF EXISTS compliance_framework CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;
DROP TYPE IF EXISTS document_type CASCADE;
DROP TYPE IF EXISTS document_status CASCADE;
DROP TYPE IF EXISTS risk_level CASCADE;
DROP TYPE IF EXISTS audit_action CASCADE;
DROP TYPE IF EXISTS signature_type CASCADE;

-- Step 5: Drop any custom policies (they'll be recreated)
-- Note: This will remove all RLS policies on the tables

-- Step 6: Verify cleanup
SELECT 
    'CLEANUP VERIFICATION' as status,
    'All tables, functions, triggers, and types removed' as message,
    NOW() as timestamp;

-- Step 7: Check what remains
SELECT 
    'REMAINING TABLES' as check_type,
    table_name
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name NOT LIKE 'pg_%'
ORDER BY table_name;

SELECT 
    'REMAINING FUNCTIONS' as check_type,
    routine_name
FROM information_schema.routines 
WHERE routine_schema = 'public'
ORDER BY routine_name;

SELECT 
    'REMAINING TYPES' as check_type,
    typname
FROM pg_type 
WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND typtype = 'e' -- enums only
ORDER BY typname;

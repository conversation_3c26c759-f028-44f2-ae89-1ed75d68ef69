Color System Architecture
Primary Background: Deep purple-navy creating sophisticated dark mode foundation
Brand Accent: Vibrant purple  used strategically for CTAs, progress indicators, and interactive elements
Content Containers: Pure white  cards creating stark, clean contrast against dark canvas
Status Color Coding:
Green for positive metrics and completed items
Orange  for pending/warning states
Red/Coral for critical alerts
Blue  for informational elements
Typography Hierarchy
Primary Headings: ~24px bold weight, likely Inter or similar modern sans-serif
Section Headers: ~18px medium weight with strategic letter-spacing
Body Text: 14px regular weight ensuring WCAG compliance
Metadata/Timestamps: 12px with reduced opacity (~70%) for hierarchy
Metrics: Large display numbers (32-36px) for key performance indicators
Layout Grid System
24-column responsive grid with consistent 16px/24px spacing units
Fixed sidebar: 240px width with clean iconography and nested navigation
Main content: Fluid width with maximum content constraints
Card padding: 24px internal spacing with 8px border-radius
Section gaps: 32px vertical rhythm maintaining visual breathing room

The color of the:
sidebar throughout is - oklch(0.1943 0.0434 287.91)
main container except the sidebar is - oklch(1 0 0)
The blue tag is - oklch(0.5461 0.2152 262.88)
the orange tag - oklch(0.6685 0.1591 57.71)
the red tag - oklch(0.6368 0.2078 25.33)
the green tag -  oklch(0.6242 0.1695 149.09)
the purple on the buttons is - oklch(0.5413 0.2466 293.01)

The borders of the cards are not that bright:
Make the graph background little more visible

rough idea:
Light Mode Cards

Border: There isn’t a hard “stroke” border around the cards—instead, they rely on a very subtle drop‑shadow to define their edges against the white page background.

The shadow is roughly 0px 4px 8px rgba(0, 0, 0, 0.04).

Edge delineation: Each card uses a border-radius: 12px to softly round the corners, and the white fill (#FFFFFF) sits on top of the light gray page background (#F7F8FA), giving enough contrast that you don’t need a solid border line.

Dark Mode Cards

Border: In dark mode, instead of a bright shadow, cards often get a very thin inner border or subtle outline to pop against the dark canvas. In these screenshots it looks like a 1px border with a slightly lighter shade than the card background—something like #2A2A3E (card is ~#1E1E2A).

Shadow vs. Outline: The shadows are inverted (lighter glow) or replaced altogether by that 1px outline; it’s barely perceptible but just enough to separate adjacent cards and the page background.

Badge & Button Borders

All pill badges (e.g. “Under Review,” “Critical”) have a true 1px border in their accent color (or a fully filled background without border if it’s a solid badge).

The “Read full update” buttons have no border—they’re solid purple with text, but on hover you’d often see a 1px darker inset.

Please understand this 
use the 6 expert protocol and then make the changes. use context7
-- Migration: 006_create_compliance_frameworks.sql
-- Description: Create compliance frameworks table for regulatory standards
-- Created: 2025-01-11
-- Dependencies: 004_create_regulatory_documents.sql

-- Create compliance frameworks table
CREATE TABLE compliance_frameworks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  code VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  regulatory_agency regulatory_agency NOT NULL,
  version VARCHAR(50),
  effective_date DATE,
  expiration_date DATE,
  jurisdiction VARCHAR(100), -- e.g., "United States", "European Union", "Global"
  category VARCHAR(100), -- e.g., "GMP", "GCP", "GLP", "Quality", "Safety"
  requirements JSONB DEFAULT '[]', -- Array of requirement objects
  validation_rules JSONB DEFAULT '{}', -- Validation rules for compliance checking
  scoring_weights JSONB DEFAULT '{}', -- Weights for compliance scoring
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_compliance_frameworks_code ON compliance_frameworks(code);
CREATE INDEX idx_compliance_frameworks_agency ON compliance_frameworks(regulatory_agency);
CREATE INDEX idx_compliance_frameworks_category ON compliance_frameworks(category);
CREATE INDEX idx_compliance_frameworks_jurisdiction ON compliance_frameworks(jurisdiction);
CREATE INDEX idx_compliance_frameworks_is_active ON compliance_frameworks(is_active);
CREATE INDEX idx_compliance_frameworks_effective_date ON compliance_frameworks(effective_date);
CREATE INDEX idx_compliance_frameworks_requirements ON compliance_frameworks USING GIN(requirements);
CREATE INDEX idx_compliance_frameworks_validation_rules ON compliance_frameworks USING GIN(validation_rules);

-- Create trigger for updated_at
CREATE TRIGGER update_compliance_frameworks_updated_at
    BEFORE UPDATE ON compliance_frameworks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create document compliance assessments table
CREATE TABLE document_compliance_assessments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE NOT NULL,
  framework_id UUID REFERENCES compliance_frameworks(id) ON DELETE CASCADE NOT NULL,
  assessment_type VARCHAR(50) CHECK (assessment_type IN ('automatic', 'manual', 'hybrid')) DEFAULT 'automatic',
  overall_score DECIMAL(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  compliance_status VARCHAR(50) CHECK (compliance_status IN ('compliant', 'non_compliant', 'partially_compliant', 'under_review', 'not_assessed')) DEFAULT 'not_assessed',
  findings JSONB DEFAULT '[]', -- Array of finding objects
  recommendations JSONB DEFAULT '[]', -- Array of recommendation objects
  gaps_identified JSONB DEFAULT '[]', -- Array of compliance gaps
  evidence_references JSONB DEFAULT '[]', -- References to supporting evidence
  assessed_by UUID REFERENCES user_profiles(id),
  reviewed_by UUID REFERENCES user_profiles(id),
  assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  review_date TIMESTAMP WITH TIME ZONE,
  next_review_date TIMESTAMP WITH TIME ZONE,
  is_current BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, framework_id, is_current) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for assessments
CREATE INDEX idx_document_compliance_assessments_document_id ON document_compliance_assessments(document_id);
CREATE INDEX idx_document_compliance_assessments_framework_id ON document_compliance_assessments(framework_id);
CREATE INDEX idx_document_compliance_assessments_status ON document_compliance_assessments(compliance_status);
CREATE INDEX idx_document_compliance_assessments_risk_level ON document_compliance_assessments(risk_level);
CREATE INDEX idx_document_compliance_assessments_score ON document_compliance_assessments(overall_score);
CREATE INDEX idx_document_compliance_assessments_is_current ON document_compliance_assessments(is_current);
CREATE INDEX idx_document_compliance_assessments_assessment_date ON document_compliance_assessments(assessment_date);
CREATE INDEX idx_document_compliance_assessments_next_review ON document_compliance_assessments(next_review_date);

-- Composite indexes
CREATE INDEX idx_document_compliance_doc_framework ON document_compliance_assessments(document_id, framework_id);
CREATE INDEX idx_document_compliance_current_status ON document_compliance_assessments(is_current, compliance_status) WHERE is_current = true;

-- Enable Row Level Security
ALTER TABLE document_compliance_assessments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for assessments
CREATE POLICY "Users can view assessments for documents in their organization" ON document_compliance_assessments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM regulatory_documents rd
      WHERE rd.id = document_compliance_assessments.document_id
      AND rd.organization_id = (auth.jwt() ->> 'organization_id')::uuid
    )
  );

CREATE POLICY "Users can create assessments for accessible documents" ON document_compliance_assessments
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM regulatory_documents rd
      WHERE rd.id = document_compliance_assessments.document_id
      AND rd.organization_id = (auth.jwt() ->> 'organization_id')::uuid
    )
    AND assessed_by = auth.uid()
  );

-- Create trigger for assessments updated_at
CREATE TRIGGER update_document_compliance_assessments_updated_at
    BEFORE UPDATE ON document_compliance_assessments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert standard compliance frameworks
INSERT INTO compliance_frameworks (name, code, description, regulatory_agency, version, effective_date, jurisdiction, category, requirements, validation_rules, scoring_weights) VALUES
(
  'FDA Current Good Manufacturing Practice (cGMP)',
  'FDA_cGMP',
  'FDA regulations for pharmaceutical manufacturing quality standards',
  'FDA',
  '2023',
  '2023-01-01',
  'United States',
  'GMP',
  '[
    {"id": "cgmp_001", "title": "Quality Management System", "description": "Establish and maintain quality management system", "weight": 20},
    {"id": "cgmp_002", "title": "Personnel Qualifications", "description": "Qualified personnel for manufacturing operations", "weight": 15},
    {"id": "cgmp_003", "title": "Facility Design", "description": "Appropriate facility design and maintenance", "weight": 15},
    {"id": "cgmp_004", "title": "Equipment Validation", "description": "Equipment qualification and validation", "weight": 15},
    {"id": "cgmp_005", "title": "Process Validation", "description": "Manufacturing process validation", "weight": 20},
    {"id": "cgmp_006", "title": "Documentation", "description": "Complete and accurate documentation", "weight": 15}
  ]'::jsonb,
  '{
    "document_types": ["sop", "validation_protocol", "user_upload"],
    "required_sections": ["purpose", "scope", "procedure", "documentation"],
    "critical_keywords": ["validation", "qualification", "quality", "gmp", "manufacturing"]
  }'::jsonb,
  '{
    "quality_system": 0.25,
    "documentation": 0.20,
    "validation": 0.20,
    "personnel": 0.15,
    "facilities": 0.10,
    "equipment": 0.10
  }'::jsonb
),
(
  'ICH Q7 Good Manufacturing Practice Guide',
  'ICH_Q7',
  'International harmonized guideline for pharmaceutical manufacturing',
  'ICH',
  'R1',
  '2000-11-10',
  'Global',
  'GMP',
  '[
    {"id": "q7_001", "title": "Quality Management", "description": "Pharmaceutical quality system", "weight": 25},
    {"id": "q7_002", "title": "Personnel", "description": "Personnel qualifications and training", "weight": 15},
    {"id": "q7_003", "title": "Buildings and Facilities", "description": "Facility design and maintenance", "weight": 15},
    {"id": "q7_004", "title": "Equipment", "description": "Equipment design and maintenance", "weight": 15},
    {"id": "q7_005", "title": "Documentation and Records", "description": "Documentation system", "weight": 15},
    {"id": "q7_006", "title": "Materials Management", "description": "Raw materials and packaging materials", "weight": 15}
  ]'::jsonb,
  '{
    "document_types": ["ich_document", "sop", "validation_protocol"],
    "required_sections": ["introduction", "quality_management", "personnel", "facilities"],
    "critical_keywords": ["quality", "pharmaceutical", "manufacturing", "ich", "harmonized"]
  }'::jsonb,
  '{
    "quality_management": 0.30,
    "documentation": 0.20,
    "personnel": 0.15,
    "facilities": 0.15,
    "equipment": 0.10,
    "materials": 0.10
  }'::jsonb
),
(
  'ISO 13485 Medical Devices Quality Management',
  'ISO_13485',
  'Quality management systems for medical devices',
  'WHO',
  '2016',
  '2016-03-01',
  'Global',
  'Quality',
  '[
    {"id": "iso_001", "title": "Quality Management System", "description": "QMS requirements for medical devices", "weight": 20},
    {"id": "iso_002", "title": "Management Responsibility", "description": "Management commitment and responsibility", "weight": 15},
    {"id": "iso_003", "title": "Resource Management", "description": "Human resources and infrastructure", "weight": 15},
    {"id": "iso_004", "title": "Product Realization", "description": "Product development and manufacturing", "weight": 25},
    {"id": "iso_005", "title": "Measurement and Improvement", "description": "Monitoring and continuous improvement", "weight": 25}
  ]'::jsonb,
  '{
    "document_types": ["sop", "validation_protocol", "user_upload"],
    "required_sections": ["scope", "normative_references", "terms_definitions", "qms_requirements"],
    "critical_keywords": ["quality", "medical_device", "iso", "management_system", "regulatory"]
  }'::jsonb,
  '{
    "qms": 0.25,
    "product_realization": 0.25,
    "measurement": 0.20,
    "management": 0.15,
    "resources": 0.15
  }'::jsonb
),
(
  'EU GMP Guidelines',
  'EU_GMP',
  'European Union Good Manufacturing Practice guidelines',
  'EMA',
  '2022',
  '2022-08-25',
  'European Union',
  'GMP',
  '[
    {"id": "eu_001", "title": "Pharmaceutical Quality System", "description": "Quality system implementation", "weight": 25},
    {"id": "eu_002", "title": "Personnel", "description": "Personnel qualifications and hygiene", "weight": 15},
    {"id": "eu_003", "title": "Premises and Equipment", "description": "Facility and equipment requirements", "weight": 20},
    {"id": "eu_004", "title": "Documentation", "description": "Documentation requirements", "weight": 15},
    {"id": "eu_005", "title": "Production", "description": "Manufacturing operations", "weight": 25}
  ]'::jsonb,
  '{
    "document_types": ["ema_guideline", "sop", "validation_protocol"],
    "required_sections": ["introduction", "scope", "legal_basis", "main_guideline"],
    "critical_keywords": ["gmp", "pharmaceutical", "quality", "european", "manufacturing"]
  }'::jsonb,
  '{
    "quality_system": 0.30,
    "production": 0.25,
    "premises_equipment": 0.20,
    "personnel": 0.15,
    "documentation": 0.10
  }'::jsonb
),
(
  'ICH Q9 Quality Risk Management',
  'ICH_Q9',
  'Quality risk management principles and tools',
  'ICH',
  'R1',
  '2005-11-09',
  'Global',
  'Quality',
  '[
    {"id": "q9_001", "title": "Risk Management Principles", "description": "Quality risk management principles", "weight": 30},
    {"id": "q9_002", "title": "Risk Management Process", "description": "Risk assessment, control, and communication", "weight": 40},
    {"id": "q9_003", "title": "Risk Management Tools", "description": "Risk management methodologies and tools", "weight": 20},
    {"id": "q9_004", "title": "Integration into Operations", "description": "Integration with pharmaceutical operations", "weight": 10}
  ]'::jsonb,
  '{
    "document_types": ["ich_document", "sop", "validation_protocol"],
    "required_sections": ["introduction", "scope", "principles", "process", "tools"],
    "critical_keywords": ["risk", "quality", "management", "assessment", "ich", "pharmaceutical"]
  }'::jsonb,
  '{
    "risk_principles": 0.30,
    "risk_process": 0.40,
    "risk_tools": 0.20,
    "integration": 0.10
  }'::jsonb
);

-- Create function to assess document compliance
CREATE OR REPLACE FUNCTION assess_document_compliance(
  p_document_id UUID,
  p_framework_id UUID,
  p_assessment_type VARCHAR(50) DEFAULT 'automatic',
  p_assessed_by UUID DEFAULT auth.uid()
) RETURNS UUID AS $$
DECLARE
  assessment_id UUID;
  framework_info RECORD;
  document_info RECORD;
  calculated_score DECIMAL(5,2) := 0;
  risk_level VARCHAR(20) := 'medium';
  compliance_status VARCHAR(50) := 'under_review';
BEGIN
  -- Get framework and document information
  SELECT * INTO framework_info FROM compliance_frameworks WHERE id = p_framework_id;
  SELECT * INTO document_info FROM regulatory_documents WHERE id = p_document_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Document or framework not found';
  END IF;
  
  -- Mark previous assessments as not current
  UPDATE document_compliance_assessments
  SET is_current = false
  WHERE document_id = p_document_id AND framework_id = p_framework_id;
  
  -- Basic compliance scoring logic (simplified)
  calculated_score := 70; -- Base score
  
  -- Adjust score based on document type match
  IF document_info.document_type::text = ANY(SELECT jsonb_array_elements_text(framework_info.validation_rules->'document_types')) THEN
    calculated_score := calculated_score + 15;
  END IF;
  
  -- Adjust score based on content availability
  IF document_info.content_extracted IS NOT NULL AND length(document_info.content_extracted) > 1000 THEN
    calculated_score := calculated_score + 10;
  END IF;
  
  -- Adjust score based on AI summary
  IF document_info.ai_summary IS NOT NULL AND length(document_info.ai_summary) > 100 THEN
    calculated_score := calculated_score + 5;
  END IF;
  
  -- Determine risk level and compliance status
  IF calculated_score >= 90 THEN
    risk_level := 'low';
    compliance_status := 'compliant';
  ELSIF calculated_score >= 70 THEN
    risk_level := 'medium';
    compliance_status := 'partially_compliant';
  ELSIF calculated_score >= 50 THEN
    risk_level := 'high';
    compliance_status := 'non_compliant';
  ELSE
    risk_level := 'critical';
    compliance_status := 'non_compliant';
  END IF;
  
  -- Insert new assessment
  INSERT INTO document_compliance_assessments (
    document_id,
    framework_id,
    assessment_type,
    overall_score,
    risk_level,
    compliance_status,
    findings,
    recommendations,
    assessed_by,
    next_review_date
  ) VALUES (
    p_document_id,
    p_framework_id,
    p_assessment_type,
    calculated_score,
    risk_level,
    compliance_status,
    '[]'::jsonb,
    '[]'::jsonb,
    p_assessed_by,
    CURRENT_DATE + INTERVAL '6 months'
  ) RETURNING id INTO assessment_id;
  
  RETURN assessment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for compliance dashboard
CREATE VIEW compliance_dashboard_view AS
SELECT 
  o.name as organization_name,
  cf.name as framework_name,
  cf.code as framework_code,
  cf.category,
  COUNT(dca.id) as total_assessments,
  COUNT(*) FILTER (WHERE dca.compliance_status = 'compliant') as compliant_count,
  COUNT(*) FILTER (WHERE dca.compliance_status = 'partially_compliant') as partially_compliant_count,
  COUNT(*) FILTER (WHERE dca.compliance_status = 'non_compliant') as non_compliant_count,
  COUNT(*) FILTER (WHERE dca.risk_level = 'critical') as critical_risk_count,
  AVG(dca.overall_score) as average_score,
  COUNT(*) FILTER (WHERE dca.next_review_date <= CURRENT_DATE + INTERVAL '30 days') as upcoming_reviews
FROM organizations o
JOIN regulatory_documents rd ON o.id = rd.organization_id
JOIN document_compliance_assessments dca ON rd.id = dca.document_id
JOIN compliance_frameworks cf ON dca.framework_id = cf.id
WHERE dca.is_current = true
GROUP BY o.name, cf.name, cf.code, cf.category;

-- Add comments for documentation
COMMENT ON TABLE compliance_frameworks IS 'Regulatory compliance frameworks and standards';
COMMENT ON TABLE document_compliance_assessments IS 'Compliance assessments for documents against frameworks';
COMMENT ON COLUMN compliance_frameworks.requirements IS 'JSON array of framework requirements with weights';
COMMENT ON COLUMN compliance_frameworks.validation_rules IS 'JSON rules for automated compliance validation';
COMMENT ON FUNCTION assess_document_compliance IS 'Performs automated compliance assessment for a document';
COMMENT ON VIEW compliance_dashboard_view IS 'Aggregated compliance metrics for dashboard display';
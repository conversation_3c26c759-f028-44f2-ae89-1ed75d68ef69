export interface NotificationSettings {
  readonly emailUpdates: boolean;
  readonly pushNotifications: boolean;
  readonly weeklyReports: boolean;
  readonly criticalAlerts: boolean;
  readonly documentProcessing: boolean;
}

export interface SecuritySettings {
  readonly twoFactorAuth: boolean;
  readonly sessionTimeout: string;
  readonly autoLogout: boolean;
}

export interface ComplianceFramework {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly status: 'active' | 'inactive';
  readonly lastUpdated: string;
}

export interface TeamMember {
  readonly id: string;
  readonly name: string;
  readonly email: string;
  readonly role: string;
  readonly status: 'active' | 'pending' | 'inactive';
  readonly lastLogin: string;
}

export interface ProfileData {
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly jobTitle: string;
  readonly company: string;
  readonly location: string;
  readonly bio: string;
  readonly avatar?: string;
}

export interface CompanyData {
  readonly size: string;
  readonly industry: string;
  readonly timeZone: string;
}

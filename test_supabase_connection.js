// Test Supabase connection and auth configuration
// Run this with: node test_supabase_connection.js

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('🔍 Testing Supabase Connection...')
console.log('URL:', supabaseUrl)
console.log('Anon Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'MISSING')

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testConnection() {
  try {
    console.log('\n📡 Testing basic connection...')
    
    // Test 1: Basic connection
    const { data: healthData, error: healthError } = await supabase
      .from('organizations')
      .select('count')
      .limit(1)
    
    if (healthError) {
      console.error('❌ Basic connection failed:', healthError)
      return false
    }
    
    console.log('✅ Basic connection successful')
    
    // Test 2: Auth configuration
    console.log('\n🔐 Testing auth configuration...')
    
    const testEmail = `test-${Date.now()}@example.com`
    const testPassword = 'TestPassword123!'
    
    console.log(`Attempting signup with: ${testEmail}`)
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        emailRedirectTo: 'http://localhost:3000/auth/confirm?next=/dashboard',
        data: {
          email_confirm: true,
        },
      },
    })
    
    if (signUpError) {
      console.error('❌ Auth signup failed:', {
        message: signUpError.message,
        status: signUpError.status,
        details: signUpError
      })
      return false
    }
    
    console.log('✅ Auth signup successful:', {
      userId: signUpData.user?.id,
      email: signUpData.user?.email,
      emailConfirmed: signUpData.user?.email_confirmed_at,
      session: !!signUpData.session
    })
    
    // Test 3: Check if user profile was created
    if (signUpData.user?.id) {
      console.log('\n👤 Checking user profile creation...')
      
      // Wait a moment for trigger to execute
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', signUpData.user.id)
        .single()
      
      if (profileError) {
        console.error('❌ User profile not created:', profileError)
      } else {
        console.log('✅ User profile created successfully:', {
          id: profileData.id,
          email: profileData.email,
          organization: profileData.organization_id
        })
      }
      
      // Cleanup test user
      console.log('\n🧹 Cleaning up test user...')
      try {
        await supabase.from('user_profiles').delete().eq('id', signUpData.user.id)
        console.log('✅ Test user profile cleaned up')
      } catch (cleanupError) {
        console.warn('⚠️ Cleanup warning:', cleanupError)
      }
    }
    
    return true
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// Run the test
testConnection().then(success => {
  if (success) {
    console.log('\n🎉 All tests passed! Supabase is configured correctly.')
  } else {
    console.log('\n💥 Tests failed. Check the errors above.')
  }
  process.exit(success ? 0 : 1)
})

"""Base Pydantic models for VigiLens Pharmaceutical Compliance Platform."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict


class TimestampedModel(BaseModel):
    """Base model with timestamp fields for audit trail."""

    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class BaseEntityModel(TimestampedModel):
    """Base model for entities with common fields."""

    id: UUID = Field(..., description="Unique identifier")
    name: str = Field(..., min_length=1, max_length=255, description="Entity name")
    description: Optional[str] = Field(None, description="Entity description")
    is_active: bool = Field(default=True, description="Whether entity is active")

    model_config = ConfigDict(from_attributes=True)


class OrganizationScopedModel(BaseModel):
    """Base model for organization-scoped entities."""

    organization_id: UUID = Field(..., description="Organization ID for multi-tenancy")

    model_config = ConfigDict(from_attributes=True)


class AuditableModel(TimestampedModel, OrganizationScopedModel):
    """Base model for auditable entities with organization scope."""

    created_by: Optional[UUID] = Field(None, description="User who created this entity")
    updated_by: Optional[UUID] = Field(None, description="User who last updated this entity")

    model_config = ConfigDict(from_attributes=True)


class PaginationParams(BaseModel):
    """Standard pagination parameters."""

    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of items per page")

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseModel):
    """Standard paginated response wrapper."""

    items: list = Field(..., description="List of items for current page")
    total: int = Field(..., ge=0, description="Total number of items")
    page: int = Field(..., ge=1, description="Current page number")
    page_size: int = Field(..., ge=1, description="Items per page")
    total_pages: int = Field(..., ge=0, description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")

    @classmethod
    def create(
        cls,
        items: list,
        total: int,
        page: int,
        page_size: int
    ) -> "PaginatedResponse":
        """Create paginated response from items and pagination info."""
        total_pages = (total + page_size - 1) // page_size if total > 0 else 0

        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )


class SortParams(BaseModel):
    """Standard sorting parameters."""

    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: str = Field(default="asc", pattern="^(asc|desc)$", description="Sort order")

    def get_order_clause(self, allowed_fields: list[str]) -> str:
        """Generate SQL ORDER BY clause."""
        if not self.sort_by or self.sort_by not in allowed_fields:
            return ""

        direction = "DESC" if self.sort_order.lower() == "desc" else "ASC"
        return f"ORDER BY {self.sort_by} {direction}"


class SearchParams(BaseModel):
    """Standard search parameters."""

    search: Optional[str] = Field(None, min_length=1, description="Search query")
    search_fields: Optional[list[str]] = Field(None, description="Fields to search in")

    def get_search_clause(self, default_fields: list[str]) -> tuple[str, dict]:
        """Generate SQL WHERE clause for search."""
        if not self.search:
            return "", {}

        fields = self.search_fields or default_fields
        search_conditions = []
        params = {}

        for i, field in enumerate(fields):
            param_name = f"search_{i}"
            search_conditions.append(f"{field} ILIKE %({param_name})s")
            params[param_name] = f"%{self.search}%"

        where_clause = f"({' OR '.join(search_conditions)})"
        return where_clause, params


class APIResponse(BaseModel):
    """Standard API response wrapper."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Response message")
    data: Optional[dict] = Field(None, description="Response data")
    errors: Optional[list[str]] = Field(None, description="List of errors if any")

    @classmethod
    def success_response(cls, data: Optional[dict] = None, message: str = "Success") -> "APIResponse":
        """Create successful response."""
        return cls(success=True, message=message, data=data)

    @classmethod
    def error_response(cls, errors: list[str], message: str = "Error occurred") -> "APIResponse":
        """Create error response."""
        return cls(success=False, message=message, errors=errors)


class HealthCheckResponse(BaseModel):
    """Health check response model."""

    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Application version")
    database: str = Field(..., description="Database status")
    dependencies: dict = Field(default_factory=dict, description="Dependency statuses")

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )

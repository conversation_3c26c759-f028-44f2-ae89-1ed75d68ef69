#!/usr/bin/env python3
"""
Sample FDA Knowledge Base Population

This script populates the knowledge base with a sample of FDA documents
to demonstrate the system working correctly.
"""

import asyncio
import logging
from pathlib import Path
from services.knowledge.fda_document_processor import FDADocumentProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def populate_sample():
    """Populate knowledge base with sample FDA documents."""
    print('🚀 POPULATING FDA KNOWLEDGE BASE (SAMPLE)')
    print('=' * 50)
    
    # Process only the 3 smallest documents for demonstration
    fda_docs_path = Path('./fda_docs')
    pdf_files = list(fda_docs_path.glob('*.pdf'))
    
    # Sort by size and take the 3 smallest
    pdf_files.sort(key=lambda f: f.stat().st_size)
    sample_files = pdf_files[:3]
    
    print(f'📄 Processing {len(sample_files)} sample documents:')
    for pdf_file in sample_files:
        size_mb = round(pdf_file.stat().st_size / (1024 * 1024), 2)
        print(f'   - {pdf_file.name} ({size_mb} MB)')
    
    # Initialize processor
    processor = FDADocumentProcessor(
        fda_docs_path=str(fda_docs_path),
        vector_store_path='./data/qdrant_fda',
        chunk_size=1000,
        chunk_overlap=200
    )
    
    await processor.initialize()
    print('✅ Processor initialized')
    
    # Process each sample document
    for pdf_file in sample_files:
        print(f'\n📚 Processing: {pdf_file.name}')
        try:
            await processor.process_single_document(pdf_file)
            print(f'✅ Completed: {pdf_file.name}')
        except Exception as e:
            print(f'❌ Failed: {pdf_file.name} - {e}')
    
    # Show final statistics
    stats = processor.stats
    print(f'\n📊 FINAL STATISTICS:')
    print(f'   Documents processed: {stats["documents_processed"]}')
    print(f'   Chunks created: {stats["chunks_created"]}')
    print(f'   Embeddings generated: {stats["embeddings_generated"]}')
    print(f'   Errors: {stats["errors"]}')
    
    # Test search
    print(f'\n🔍 TESTING SEARCH:')
    try:
        search_results = await processor.vector_store.search_documents(
            query='FDA pharmaceutical manufacturing regulations',
            limit=3
        )
        print(f'✅ Search returned {len(search_results.results)} results')
        
        for i, result in enumerate(search_results.results):
            print(f'   Result {i+1}: Score {result.score:.3f}')
            print(f'   Content: {result.content[:150]}...')
            print()
    except Exception as e:
        print(f'❌ Search failed: {e}')
    
    print('🎉 Sample population completed!')


if __name__ == "__main__":
    asyncio.run(populate_sample())

/**
 * Supabase Edge Function: Setup Realtime Channels
 * 
 * Manages real-time subscription channels for pharmaceutical compliance platform
 * Handles compliance alerts, document processing, audit events, and performance monitoring
 * 
 * Compatible with Deno 2.1 and Supabase Edge Runtime
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, no 'any' types
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// TypeScript interfaces for real-time channel management
interface ChannelSetupRequest {
  organizationId: string
  userId: string
  channels: ChannelConfig[]
  action: 'setup' | 'teardown' | 'status'
}

interface ChannelConfig {
  name: string
  type: 'compliance_alerts' | 'document_processing' | 'audit_trail' | 'performance_monitoring'
  filters?: Record<string, string>
  options?: ChannelOptions
}

interface ChannelOptions {
  buffer_size?: number
  retry_attempts?: number
  heartbeat_interval?: number
  auto_reconnect?: boolean
}

interface ChannelStatus {
  name: string
  type: string
  status: 'active' | 'inactive' | 'error' | 'connecting'
  subscriber_count: number
  last_activity: string
  error_count: number
}

interface RealtimeChannelManager {
  setupChannel(config: ChannelConfig, organizationId: string): Promise<ChannelStatus>
  teardownChannel(channelName: string, organizationId: string): Promise<boolean>
  getChannelStatus(channelName: string, organizationId: string): Promise<ChannelStatus | null>
  listActiveChannels(organizationId: string): Promise<ChannelStatus[]>
}

// Pharmaceutical compliance real-time channel manager
class PharmaceuticalRealtimeManager implements RealtimeChannelManager {
  private supabase: any
  private activeChannels: Map<string, ChannelStatus> = new Map()

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  async setupChannel(config: ChannelConfig, organizationId: string): Promise<ChannelStatus> {
    const channelKey = `${organizationId}_${config.name}`
    
    try {
      // Initialize channel based on type
      switch (config.type) {
        case 'compliance_alerts':
          return await this.setupComplianceAlertsChannel(config, organizationId)
        
        case 'document_processing':
          return await this.setupDocumentProcessingChannel(config, organizationId)
        
        case 'audit_trail':
          return await this.setupAuditTrailChannel(config, organizationId)
        
        case 'performance_monitoring':
          return await this.setupPerformanceMonitoringChannel(config, organizationId)
        
        default:
          throw new Error(`Unsupported channel type: ${config.type}`)
      }
    } catch (error) {
      const errorStatus: ChannelStatus = {
        name: config.name,
        type: config.type,
        status: 'error',
        subscriber_count: 0,
        last_activity: new Date().toISOString(),
        error_count: 1
      }
      
      this.activeChannels.set(channelKey, errorStatus)
      throw error
    }
  }

  private async setupComplianceAlertsChannel(
    config: ChannelConfig, 
    organizationId: string
  ): Promise<ChannelStatus> {
    const channelName = `compliance_alerts_${organizationId}`
    
    // Set up database trigger for compliance alerts
    const { error: triggerError } = await this.supabase.rpc('setup_compliance_alert_trigger', {
      org_id: organizationId,
      channel_name: channelName,
      alert_thresholds: {
        critical_score: 0.3,
        major_violations: 3,
        processing_timeout: 300 // 5 minutes
      }
    })

    if (triggerError) {
      throw new Error(`Failed to setup compliance alerts trigger: ${triggerError.message}`)
    }

    const status: ChannelStatus = {
      name: config.name,
      type: 'compliance_alerts',
      status: 'active',
      subscriber_count: 0,
      last_activity: new Date().toISOString(),
      error_count: 0
    }

    this.activeChannels.set(`${organizationId}_${config.name}`, status)
    return status
  }

  private async setupDocumentProcessingChannel(
    config: ChannelConfig,
    organizationId: string
  ): Promise<ChannelStatus> {
    const channelName = `document_processing_${organizationId}`
    
    // Set up database trigger for document processing updates
    const { error: triggerError } = await this.supabase.rpc('setup_document_processing_trigger', {
      org_id: organizationId,
      channel_name: channelName,
      processing_stages: [
        'uploaded',
        'processing',
        'ai_analysis',
        'compliance_check',
        'completed',
        'failed'
      ]
    })

    if (triggerError) {
      throw new Error(`Failed to setup document processing trigger: ${triggerError.message}`)
    }

    const status: ChannelStatus = {
      name: config.name,
      type: 'document_processing',
      status: 'active',
      subscriber_count: 0,
      last_activity: new Date().toISOString(),
      error_count: 0
    }

    this.activeChannels.set(`${organizationId}_${config.name}`, status)
    return status
  }

  private async setupAuditTrailChannel(
    config: ChannelConfig,
    organizationId: string
  ): Promise<ChannelStatus> {
    const channelName = `audit_trail_${organizationId}`
    
    // Set up database trigger for audit trail monitoring
    const { error: triggerError } = await this.supabase.rpc('setup_audit_trail_trigger', {
      org_id: organizationId,
      channel_name: channelName,
      audit_categories: [
        'user_authentication',
        'document_access',
        'compliance_assessment',
        'data_modification',
        'system_configuration'
      ]
    })

    if (triggerError) {
      throw new Error(`Failed to setup audit trail trigger: ${triggerError.message}`)
    }

    const status: ChannelStatus = {
      name: config.name,
      type: 'audit_trail',
      status: 'active',
      subscriber_count: 0,
      last_activity: new Date().toISOString(),
      error_count: 0
    }

    this.activeChannels.set(`${organizationId}_${config.name}`, status)
    return status
  }

  private async setupPerformanceMonitoringChannel(
    config: ChannelConfig,
    organizationId: string
  ): Promise<ChannelStatus> {
    const channelName = `performance_monitoring_${organizationId}`
    
    // Set up database trigger for performance monitoring
    const { error: triggerError } = await this.supabase.rpc('setup_performance_monitoring_trigger', {
      org_id: organizationId,
      channel_name: channelName,
      performance_thresholds: {
        query_time_ms: 1000,
        memory_usage_mb: 512,
        cpu_usage_percent: 80,
        connection_count: 100
      }
    })

    if (triggerError) {
      throw new Error(`Failed to setup performance monitoring trigger: ${triggerError.message}`)
    }

    const status: ChannelStatus = {
      name: config.name,
      type: 'performance_monitoring',
      status: 'active',
      subscriber_count: 0,
      last_activity: new Date().toISOString(),
      error_count: 0
    }

    this.activeChannels.set(`${organizationId}_${config.name}`, status)
    return status
  }

  async teardownChannel(channelName: string, organizationId: string): Promise<boolean> {
    const channelKey = `${organizationId}_${channelName}`
    const channelStatus = this.activeChannels.get(channelKey)
    
    if (!channelStatus) {
      return false
    }

    try {
      // Remove database triggers based on channel type
      const { error } = await this.supabase.rpc('teardown_realtime_trigger', {
        org_id: organizationId,
        channel_name: channelName,
        channel_type: channelStatus.type
      })

      if (error) {
        console.error(`Failed to teardown channel ${channelName}:`, error)
        return false
      }

      this.activeChannels.delete(channelKey)
      return true
    } catch (error) {
      console.error(`Error tearing down channel ${channelName}:`, error)
      return false
    }
  }

  async getChannelStatus(channelName: string, organizationId: string): Promise<ChannelStatus | null> {
    const channelKey = `${organizationId}_${channelName}`
    return this.activeChannels.get(channelKey) || null
  }

  async listActiveChannels(organizationId: string): Promise<ChannelStatus[]> {
    const orgChannels: ChannelStatus[] = []
    
    for (const [key, status] of this.activeChannels.entries()) {
      if (key.startsWith(`${organizationId}_`)) {
        orgChannels.push(status)
      }
    }
    
    return orgChannels
  }
}

// Edge Function handler
serve(async (req: Request) => {
  // CORS headers for pharmaceutical compliance platform
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    const realtimeManager = new PharmaceuticalRealtimeManager(supabase)

    if (req.method === 'GET') {
      // Get channel status or list channels
      const url = new URL(req.url)
      const organizationId = url.searchParams.get('organizationId')
      const channelName = url.searchParams.get('channelName')

      if (!organizationId) {
        return new Response(
          JSON.stringify({ error: 'organizationId parameter required' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      if (channelName) {
        // Get specific channel status
        const status = await realtimeManager.getChannelStatus(channelName, organizationId)
        return new Response(
          JSON.stringify({ success: true, data: status }),
          { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      } else {
        // List all active channels for organization
        const channels = await realtimeManager.listActiveChannels(organizationId)
        return new Response(
          JSON.stringify({ success: true, data: channels }),
          { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
    }

    if (req.method === 'POST') {
      // Setup or teardown channels
      const requestBody = await req.json() as ChannelSetupRequest
      
      if (!requestBody.organizationId || !requestBody.userId || !requestBody.action) {
        return new Response(
          JSON.stringify({ 
            error: 'Missing required fields: organizationId, userId, action' 
          }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      switch (requestBody.action) {
        case 'setup': {
          if (!requestBody.channels || requestBody.channels.length === 0) {
            return new Response(
              JSON.stringify({ error: 'channels array required for setup action' }),
              { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
          }

          const results: ChannelStatus[] = []
          const errors: string[] = []

          for (const channelConfig of requestBody.channels) {
            try {
              const status = await realtimeManager.setupChannel(channelConfig, requestBody.organizationId)
              results.push(status)
            } catch (error) {
              errors.push(`Failed to setup ${channelConfig.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
            }
          }

          return new Response(
            JSON.stringify({ 
              success: errors.length === 0,
              data: results,
              errors: errors.length > 0 ? errors : undefined
            }),
            { status: errors.length === 0 ? 200 : 207, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }

        case 'teardown': {
          if (!requestBody.channels || requestBody.channels.length === 0) {
            return new Response(
              JSON.stringify({ error: 'channels array required for teardown action' }),
              { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
          }

          const results: boolean[] = []
          for (const channelConfig of requestBody.channels) {
            const success = await realtimeManager.teardownChannel(channelConfig.name, requestBody.organizationId)
            results.push(success)
          }

          return new Response(
            JSON.stringify({ 
              success: results.every(r => r),
              data: { teardown_results: results }
            }),
            { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }

        case 'status': {
          const channels = await realtimeManager.listActiveChannels(requestBody.organizationId)
          return new Response(
            JSON.stringify({ success: true, data: channels }),
            { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }

        default:
          return new Response(
            JSON.stringify({ error: `Unsupported action: ${requestBody.action}` }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
      }
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Realtime channel management error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error during channel management',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

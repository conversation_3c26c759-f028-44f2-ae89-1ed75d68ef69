import { useCallback, useMemo, useState } from 'react'

import type {
    RegulatoryUpdate,
    SearchFilters,
    TabValue,
    UpdateMetric,
} from '../types'

export function useUpdatesData() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedAgency, setSelectedAgency] = useState('all')
  const [selectedSeverity, setSelectedSeverity] = useState('all')
  const [sortBy, setSortBy] = useState('date')
  const [bookmarkedUpdates, setBookmarkedUpdates] = useState<number[]>([1, 2])
  const [activeTab, setActiveTab] = useState<TabValue>('all')

  const regulatoryUpdates = useMemo(
    (): RegulatoryUpdate[] => [
      {
        id: 1,
        title:
          'Emergency Safety Communication: Contamination Risk in Sterile Manufacturing',
        agency: 'FDA',
        category: 'Pharmaceutical',
        publishedDate: 'June 20, 2023',
        severity: 'critical' as const,
        summary:
          'The FDA has issued an emergency safety communication regarding potential contamination risks in sterile pharmaceutical manufacturing facilities. Immediate action required for all sterile manufacturing operations to review and update contamination control procedures.',
        url: 'https://fda.gov/safety/emergency-safety-communication',
        tags: ['Sterile Manufacturing', 'Contamination', 'Emergency'] as const,
        estimatedImpact: 'High',
        deadline: 'July 5, 2023',
      },
      {
        id: 2,
        title: 'Updated Guidance: Process Validation for Drug Products',
        agency: 'FDA',
        category: 'Guidance',
        publishedDate: 'June 19, 2023',
        severity: 'medium',
        summary:
          'The FDA has released updated guidance on process validation incorporating new approaches to continuous process verification and lifecycle management. This guidance replaces the 2011 version and includes enhanced requirements for statistical process controls.',
        url: 'https://fda.gov/guidance/process-validation',
        tags: [
          'Process Validation',
          'Lifecycle Management',
          'Statistical Controls',
        ] as const,
        estimatedImpact: 'Medium',
        deadline: 'December 19, 2023',
      },
      {
        id: 3,
        title: 'EMA Guideline on Quality Risk Management',
        agency: 'EMA',
        category: 'Manufacturing',
        publishedDate: 'June 18, 2023',
        severity: 'low',
        summary:
          'The European Medicines Agency has published a new baseline on Quality Risk Management for pharmaceutical manufacturers. This guideline emphasizes a proactive approach to identify and mitigating risks in the manufacturing process.',
        url: 'https://ema.europa.eu/guidance/quality-risk-management',
        tags: ['Quality Risk Management', 'Manufacturing', 'EU Guidelines'],
        estimatedImpact: 'Low',
        deadline: 'September 18, 2023',
      },
      {
        id: 4,
        title: 'ICH Q9 R1 Quality Risk Management Revision',
        agency: 'ICH',
        category: 'International',
        publishedDate: 'June 17, 2023',
        severity: 'high',
        summary:
          'The International Council for Harmonisation has revised the Q9 guideline on Quality Risk Management. The revision includes new considerations for subjectivity in risk assessment and formalized risk review processes.',
        url: 'https://ich.org/page/quality-guidelines',
        tags: ['ICH Guidelines', 'Risk Assessment', 'International Standards'],
        estimatedImpact: 'High',
        deadline: 'March 17, 2024',
      },
      {
        id: 5,
        title:
          'Draft Guideline: Environmental Risk Assessment for Pharmaceuticals',
        agency: 'EMA',
        category: 'Environmental',
        publishedDate: 'June 16, 2023',
        severity: 'medium',
        summary:
          'The European Medicines Agency has published a draft guideline on environmental risk assessment for human pharmaceuticals. The document outlines new requirements for assessing the environmental impact of pharmaceutical manufacturing and disposal.',
        url: 'https://ema.europa.eu/guidance/environmental-risk',
        tags: ['Environmental Risk', 'Manufacturing Impact', 'Sustainability'],
        estimatedImpact: 'Medium',
        deadline: 'August 16, 2023',
      },
    ],
    [],
  )

  const metrics = useMemo(
    (): readonly UpdateMetric[] => [
      { label: 'New This Week', value: '23', color: 'bg-primary' },
      { label: 'Critical Updates', value: '5', color: 'bg-destructive' },
      {
        label: 'Bookmarked',
        value: bookmarkedUpdates.length.toString(),
        color: 'bg-warning',
      },
      { label: 'Total Updates', value: '1,247', color: 'bg-success' },
    ],
    [bookmarkedUpdates],
  )

  const filters: SearchFilters = {
    searchQuery,
    selectedAgency,
    selectedSeverity,
    sortBy,
  }

  const filteredUpdates = useMemo(() => {
    return regulatoryUpdates.filter((update) => {
      const matchesSearch =
        update.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        update.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
        update.tags.some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase()),
        )
      const matchesAgency =
        selectedAgency === 'all' || update.agency === selectedAgency
      const matchesSeverity =
        selectedSeverity === 'all' || update.severity === selectedSeverity

      return matchesSearch && matchesAgency && matchesSeverity
    })
  }, [searchQuery, selectedAgency, selectedSeverity, regulatoryUpdates])

  const toggleBookmark = useCallback((updateId: number) => {
    setBookmarkedUpdates((prev) =>
      prev.includes(updateId)
        ? prev.filter((id) => id !== updateId)
        : [...prev, updateId],
    )
  }, [])

  const handleExport = useCallback(() => {
    const exportData = {
      timestamp: new Date().toISOString(),
      totalUpdates: regulatoryUpdates.length,
      criticalUpdates: regulatoryUpdates.filter(
        (u) => u.severity === 'critical',
      ).length,
      updates: filteredUpdates,
    }
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `regulatory-updates-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }, [filteredUpdates, regulatoryUpdates])

  const handleRefresh = useCallback(() => {
    // Mock refresh functionality
    console.log('Refreshing updates...')
  }, [])

  const handleShare = useCallback((update: RegulatoryUpdate) => {
    if (navigator.share) {
      navigator.share({
        title: update.title,
        text: update.summary,
        url: update.url,
      })
    } else {
      // Fallback to copying URL to clipboard
      navigator.clipboard.writeText(update.url)
    }
  }, [])

  const handleView = useCallback((update: RegulatoryUpdate) => {
    console.log('Viewing update:', update.id)
    // In a real app, this would navigate to a detailed view
  }, [])

  return {
    // Data
    updates: filteredUpdates,
    allUpdates: regulatoryUpdates,
    metrics,
    filters,
    bookmarkedUpdates,
    activeTab,

    // Filter handlers
    setSearchQuery,
    setSelectedAgency,
    setSelectedSeverity,
    setSortBy,
    setActiveTab,

    // Action handlers
    toggleBookmark,
    handleExport,
    handleRefresh,
    handleShare,
    handleView,
  }
}

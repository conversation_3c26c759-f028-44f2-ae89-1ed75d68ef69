import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { ComplianceInfo } from "./ComplianceInfo";
import {
  Shield,
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
  Download,
  Eye,
  BarChart3,
  TrendingUp,
  AlertCircle,
  Settings,
  Play,
  Pause,
  RefreshCw,
  HelpCircle,
  ArrowRight,
  FileUp,
  Zap,
  Brain,
  Search,
  Target,
  X,
  Plus,
} from "lucide-react";

interface UploadedDocument {
  id: string;
  name: string;
  type: string;
  size: string;
  status: "uploaded" | "processing" | "analyzed" | "error";
  complianceScore?: number;
  issues?: number;
  recommendations?: number;
  riskLevel?: "low" | "medium" | "high" | "critical";
  uploadDate: string;
}

interface ComplianceResult {
  documentId: string;
  framework: string;
  score: number;
  issues: Array<{
    severity: "low" | "medium" | "high" | "critical";
    title: string;
    description: string;
    location: string;
    recommendation: string;
  }>;
  recommendations: Array<{
    priority: "low" | "medium" | "high";
    title: string;
    description: string;
    implementation: string;
  }>;
  insights: Array<{
    category: string;
    finding: string;
    impact: string;
    action: string;
  }>;
}

export default function ComplianceCheck() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedDocuments, setUploadedDocuments] = useState<
    UploadedDocument[]
  >([]);
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([
    "fda-cgmp",
  ]);
  const [analysisType, setAnalysisType] = useState("comprehensive");
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [results, setResults] = useState<ComplianceResult[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const complianceFrameworks = [
    {
      id: "fda-cgmp",
      name: "FDA cGMP",
      description: "Current Good Manufacturing Practice",
      regulations: ["21 CFR 210", "21 CFR 211"],
      color: "bg-blue-500",
    },
    {
      id: "ich-q7",
      name: "ICH Q7",
      description: "Active Pharmaceutical Ingredients",
      regulations: ["ICH Q7"],
      color: "bg-green-500",
    },
    {
      id: "iso-13485",
      name: "ISO 13485",
      description: "Medical Devices Quality Management",
      regulations: ["ISO 13485:2016"],
      color: "bg-purple-500",
    },
    {
      id: "eu-gmp",
      name: "EU GMP",
      description: "European Good Manufacturing Practice",
      regulations: ["EudraLex Volume 4"],
      color: "bg-orange-500",
    },
    {
      id: "ich-q9",
      name: "ICH Q9",
      description: "Quality Risk Management",
      regulations: ["ICH Q9 R1"],
      color: "bg-red-500",
    },
  ];

  const analysisOptions = [
    {
      id: "quick",
      name: "Quick Scan",
      description: "Basic compliance check and gap identification",
      duration: "2-5 minutes",
      features: [
        "Document structure analysis",
        "Key compliance points",
        "Basic gap identification",
      ],
    },
    {
      id: "standard",
      name: "Standard Analysis",
      description: "Comprehensive validation with recommendations",
      duration: "5-10 minutes",
      features: [
        "Full regulatory compliance",
        "Detailed gap analysis",
        "Remediation suggestions",
        "Risk assessment",
      ],
    },
    {
      id: "comprehensive",
      name: "Deep Analysis",
      description: "AI-powered insights with best practices comparison",
      duration: "10-15 minutes",
      features: [
        "AI-powered analysis",
        "Industry benchmarking",
        "Best practices comparison",
        "Predictive insights",
        "Custom recommendations",
      ],
    },
  ];

  const complianceMetrics = [
    { label: "Documents Processed", value: "342", trend: "+12%" },
    { label: "Average Compliance", value: "89%", trend: "+3%" },
    { label: "Issues Resolved", value: "47", trend: "+8%" },
    { label: "Time Saved", value: "120h", trend: "+15%" },
  ];

  const handleFileUpload = (files: FileList) => {
    Array.from(files).forEach((file) => {
      const newDoc: UploadedDocument = {
        id: `doc_${Date.now()}_${Math.random()}`,
        name: file.name,
        type: file.name.split(".").pop()?.toUpperCase() || "FILE",
        size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
        status: "uploaded",
        uploadDate: new Date().toISOString().split("T")[0],
      };
      setUploadedDocuments((prev) => [...prev, newDoc]);
    });

    if (currentStep === 1) {
      setCurrentStep(2);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const removeDocument = (docId: string) => {
    setUploadedDocuments((prev) => prev.filter((doc) => doc.id !== docId));
  };

  const handleFrameworkToggle = (frameworkId: string) => {
    setSelectedFrameworks((prev) =>
      prev.includes(frameworkId)
        ? prev.filter((id) => id !== frameworkId)
        : [...prev, frameworkId],
    );
  };

  const startAnalysis = async () => {
    setIsProcessing(true);
    setProcessingProgress(0);
    setCurrentStep(4);

    // Simulate processing with progress updates
    const progressSteps = [
      { progress: 20, status: "Uploading documents..." },
      { progress: 40, status: "Extracting document content..." },
      { progress: 60, status: "Running compliance checks..." },
      { progress: 80, status: "Performing deep analysis..." },
      { progress: 100, status: "Generating insights..." },
    ];

    for (const step of progressSteps) {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      setProcessingProgress(step.progress);
    }

    // Generate mock results
    const mockResults: ComplianceResult[] = uploadedDocuments.map((doc) => ({
      documentId: doc.id,
      framework: selectedFrameworks[0],
      score: Math.floor(Math.random() * 20) + 80,
      issues: [
        {
          severity: "medium" as const,
          title: "Documentation Gap Identified",
          description: "Missing quality control procedures section",
          location: "Section 4.2",
          recommendation:
            "Add detailed QC procedures according to 21 CFR 211.22",
        },
        {
          severity: "low" as const,
          title: "Minor Formatting Issue",
          description: "Inconsistent date formatting throughout document",
          location: "Multiple sections",
          recommendation: "Standardize date format to ISO 8601",
        },
      ],
      recommendations: [
        {
          priority: "high" as const,
          title: "Implement Change Control System",
          description: "Establish robust change control procedures",
          implementation: "Create change control SOP with approval workflow",
        },
      ],
      insights: [
        {
          category: "Risk Management",
          finding: "Document lacks comprehensive risk assessment",
          impact: "May lead to regulatory scrutiny",
          action: "Implement ICH Q9 risk management principles",
        },
      ],
    }));

    setResults(mockResults);
    setIsProcessing(false);
    setCurrentStep(5);
  };

  const getStepStatus = (step: number) => {
    if (step < currentStep) return "completed";
    if (step === currentStep) return "current";
    return "upcoming";
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Smart Compliance Validation
            </h1>
            <p className="text-muted-foreground mt-1">
              Upload, validate, and analyze documents with AI-powered compliance
              insights
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                // Mock export functionality
                const report = {
                  timestamp: new Date().toISOString(),
                  documents: uploadedDocuments.length,
                  frameworks: selectedFrameworks,
                  results: results,
                };
                const dataStr = JSON.stringify(report, null, 2);
                const dataBlob = new Blob([dataStr], {
                  type: "application/json",
                });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement("a");
                link.href = url;
                link.download = `compliance-report-${new Date().toISOString().split("T")[0]}.json`;
                link.click();
                URL.revokeObjectURL(url);
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline" onClick={() => navigate("/settings")}>
              <Settings className="mr-2 h-4 w-4" />
              Configure
            </Button>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {complianceMetrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold text-foreground">
                      {metric.value}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {metric.label}
                    </p>
                  </div>
                  <div className="flex items-center text-sm text-success">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    {metric.trend}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Workflow */}
        <Tabs
          value={
            currentStep <= 3
              ? "workflow"
              : currentStep === 4
                ? "processing"
                : "results"
          }
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="workflow" className="flex items-center">
              <Shield className="mr-1 h-4 w-4" />
              Smart Validation
            </TabsTrigger>
            <TabsTrigger
              value="processing"
              disabled={!isProcessing && currentStep !== 4}
            >
              <RefreshCw className="mr-1 h-4 w-4" />
              Processing
            </TabsTrigger>
            <TabsTrigger value="results" disabled={currentStep < 5}>
              <BarChart3 className="mr-1 h-4 w-4" />
              Results & Insights
            </TabsTrigger>
            <TabsTrigger value="how-it-works">
              <HelpCircle className="mr-1 h-4 w-4" />
              How it Works
            </TabsTrigger>
          </TabsList>

          {/* Progressive Workflow */}
          <TabsContent value="workflow" className="space-y-6">
            {/* Progress Steps */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center mb-6">
                  {[1, 2, 3].map((step, index) => (
                    <React.Fragment key={step}>
                      <div
                        className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${getStepStatus(step) === "completed"
                          ? "bg-primary border-primary text-primary-foreground"
                          : getStepStatus(step) === "current"
                            ? "border-primary text-primary bg-primary/10"
                            : "border-muted-foreground text-muted-foreground"
                          }`}
                      >
                        {getStepStatus(step) === "completed" ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <span className="font-medium">{step}</span>
                        )}
                      </div>
                      {index < 2 && (
                        <div
                          className={`h-0.5 w-24 mx-4 rounded-full ${getStepStatus(step + 1) === "completed" ||
                            getStepStatus(step + 1) === "current"
                            ? "bg-primary"
                            : "bg-muted-foreground/30"
                            }`}
                        />
                      )}
                    </React.Fragment>
                  ))}
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">
                    {currentStep === 1 && "Upload Documents"}
                    {currentStep === 2 && "Select Frameworks"}
                    {currentStep === 3 && "Configure Analysis"}
                  </h3>
                  <p className="text-muted-foreground">
                    {currentStep === 1 &&
                      "Upload your compliance documents for analysis"}
                    {currentStep === 2 &&
                      "Choose the regulatory frameworks to validate against"}
                    {currentStep === 3 &&
                      "Select analysis depth and review settings"}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Step 1: Document Upload */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Upload className="mr-2 h-5 w-5" />
                    Upload Documents
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div
                    className="border-2 border-dashed border-border rounded-lg p-12 text-center hover:border-primary/50 transition-colors cursor-pointer"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-medium mb-2">
                      Drag and drop files here
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Supports PDF, DOC, DOCX, XLS, XLSX and TXT formats
                    </p>
                    <Button className="bg-primary hover:bg-primary/90">
                      <Plus className="mr-2 h-4 w-4" />
                      Browse Files
                    </Button>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                    onChange={(e) =>
                      e.target.files && handleFileUpload(e.target.files)
                    }
                    className="hidden"
                  />

                  {/* Security Features */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-5 w-5 text-success" />
                      <span className="text-sm">End-to-end encryption</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-success" />
                      <span className="text-sm">GDPR compliant</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-5 w-5 text-success" />
                      <span className="text-sm">Auto-delete after 30 days</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Uploaded Documents List */}
            {uploadedDocuments.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    Uploaded Documents ({uploadedDocuments.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {uploadedDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-primary" />
                          <div>
                            <p className="font-medium">{doc.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {doc.type} • {doc.size} • Uploaded{" "}
                              {doc.uploadDate}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{doc.status}</Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeDocument(doc.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  {currentStep === 1 && (
                    <div className="mt-4 text-center">
                      <Button
                        onClick={() => setCurrentStep(2)}
                        className="bg-primary hover:bg-primary/90"
                      >
                        Continue to Framework Selection
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 2: Framework Selection */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="mr-2 h-5 w-5" />
                    Select Compliance Frameworks
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {complianceFrameworks.map((framework) => (
                      <div
                        key={framework.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${selectedFrameworks.includes(framework.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                          }`}
                        onClick={() => handleFrameworkToggle(framework.id)}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div
                            className={`w-3 h-3 rounded-full ${framework.color}`}
                          />
                          <Checkbox
                            checked={selectedFrameworks.includes(framework.id)}
                          />
                        </div>
                        <h4 className="font-medium mb-2">{framework.name}</h4>
                        <p className="text-sm text-muted-foreground mb-3">
                          {framework.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {framework.regulations.map((reg, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {reg}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>
                      Back to Upload
                    </Button>
                    <Button
                      onClick={() => setCurrentStep(3)}
                      disabled={selectedFrameworks.length === 0}
                      className="bg-primary hover:bg-primary/90"
                    >
                      Continue to Analysis Setup
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Analysis Configuration */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Brain className="mr-2 h-5 w-5" />
                    Configure Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    {analysisOptions.map((option) => (
                      <div
                        key={option.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${analysisType === option.id
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                          }`}
                        onClick={() => setAnalysisType(option.id)}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            {option.id === "quick" && (
                              <Zap className="h-4 w-4 text-yellow-500" />
                            )}
                            {option.id === "standard" && (
                              <CheckCircle className="h-4 w-4 text-blue-500" />
                            )}
                            {option.id === "comprehensive" && (
                              <Brain className="h-4 w-4 text-purple-500" />
                            )}
                            <span className="font-medium">{option.name}</span>
                          </div>
                          <div
                            className={`w-4 h-4 rounded-full border-2 ${analysisType === option.id
                              ? "border-primary bg-primary"
                              : "border-muted"
                              }`}
                          />
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {option.description}
                        </p>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">
                              Duration:
                            </span>
                            <span className="font-medium">
                              {option.duration}
                            </span>
                          </div>
                          <div className="space-y-1">
                            {option.features.map((feature, index) => (
                              <div
                                key={index}
                                className="flex items-center space-x-2 text-xs"
                              >
                                <CheckCircle className="h-3 w-3 text-success" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>
                      Back to Frameworks
                    </Button>
                    <Button
                      onClick={startAnalysis}
                      className="bg-primary hover:bg-primary/90"
                    >
                      <Play className="mr-2 h-4 w-4" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Processing Tab */}
          <TabsContent value="processing" className="space-y-6">
            <Card>
              <CardContent className="p-8">
                <div className="text-center space-y-6">
                  <div className="flex justify-center">
                    <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">
                      Analysis in Progress
                    </h3>
                    <p className="text-muted-foreground">
                      Running comprehensive compliance validation across{" "}
                      {uploadedDocuments.length} documents
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Progress value={processingProgress} className="h-3" />
                    <p className="text-sm text-muted-foreground">
                      {processingProgress}% complete
                    </p>
                  </div>
                  <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-foreground">
                        {uploadedDocuments.length}
                      </p>
                      <p className="text-sm text-muted-foreground">Documents</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-foreground">
                        {selectedFrameworks.length}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Frameworks
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-foreground">
                        {analysisType === "comprehensive"
                          ? "15"
                          : analysisType === "standard"
                            ? "8"
                            : "3"}
                      </p>
                      <p className="text-sm text-muted-foreground">Checks</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Results Tab */}
          <TabsContent value="results" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Overall Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center space-y-4">
                      <div className="text-4xl font-bold text-success">
                        {results.length > 0
                          ? Math.round(
                            results.reduce((acc, r) => acc + r.score, 0) /
                            results.length,
                          )
                          : 0}
                        %
                      </div>
                      <Progress
                        value={
                          results.length > 0
                            ? Math.round(
                              results.reduce((acc, r) => acc + r.score, 0) /
                              results.length,
                            )
                            : 0
                        }
                        className="h-2"
                      />
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Issues Found:</span>
                          <span className="font-medium">
                            {results.reduce(
                              (acc, r) => acc + r.issues.length,
                              0,
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Recommendations:</span>
                          <span className="font-medium">
                            {results.reduce(
                              (acc, r) => acc + r.recommendations.length,
                              0,
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-3">
                <Card>
                  <CardHeader>
                    <CardTitle>Document Analysis Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {results.map((result, index) => {
                        const doc = uploadedDocuments.find(
                          (d) => d.id === result.documentId,
                        );
                        return (
                          <div
                            key={result.documentId}
                            className="border rounded-lg p-4"
                          >
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <FileText className="h-5 w-5 text-primary" />
                                <div>
                                  <h4 className="font-medium">{doc?.name}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {result.framework} • Score: {result.score}%
                                  </p>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button variant="outline" size="sm">
                                  <Eye className="mr-2 h-3 w-3" />
                                  View Details
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Download className="mr-2 h-3 w-3" />
                                  Report
                                </Button>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <h5 className="font-medium text-sm mb-2">
                                  Issues ({result.issues.length})
                                </h5>
                                <div className="space-y-1">
                                  {result.issues
                                    .slice(0, 2)
                                    .map((issue, idx) => (
                                      <div
                                        key={idx}
                                        className="text-xs p-2 bg-muted rounded"
                                      >
                                        <div className="flex items-center space-x-1 mb-1">
                                          <div
                                            className={`w-2 h-2 rounded-full ${issue.severity === "critical"
                                              ? "bg-red-500"
                                              : issue.severity === "high"
                                                ? "bg-orange-500"
                                                : issue.severity === "medium"
                                                  ? "bg-yellow-500"
                                                  : "bg-blue-500"
                                              }`}
                                          />
                                          <span className="font-medium">
                                            {issue.title}
                                          </span>
                                        </div>
                                        <p className="text-muted-foreground">
                                          {issue.description}
                                        </p>
                                      </div>
                                    ))}
                                </div>
                              </div>

                              <div>
                                <h5 className="font-medium text-sm mb-2">
                                  Recommendations
                                </h5>
                                <div className="space-y-1">
                                  {result.recommendations
                                    .slice(0, 1)
                                    .map((rec, idx) => (
                                      <div
                                        key={idx}
                                        className="text-xs p-2 bg-muted rounded"
                                      >
                                        <p className="font-medium mb-1">
                                          {rec.title}
                                        </p>
                                        <p className="text-muted-foreground">
                                          {rec.description}
                                        </p>
                                      </div>
                                    ))}
                                </div>
                              </div>

                              <div>
                                <h5 className="font-medium text-sm mb-2">
                                  AI Insights
                                </h5>
                                <div className="space-y-1">
                                  {result.insights
                                    .slice(0, 1)
                                    .map((insight, idx) => (
                                      <div
                                        key={idx}
                                        className="text-xs p-2 bg-muted rounded"
                                      >
                                        <p className="font-medium mb-1">
                                          {insight.category}
                                        </p>
                                        <p className="text-muted-foreground">
                                          {insight.finding}
                                        </p>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* How it Works */}
          <TabsContent value="how-it-works">
            <ComplianceInfo />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

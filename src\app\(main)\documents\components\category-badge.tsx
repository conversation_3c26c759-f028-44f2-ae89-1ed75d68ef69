import { Badge } from '@/components/ui-radix/badge';
import { cn } from '@/lib/utils';
import {
    Archive,
    CheckCircle2,
    Copy,
    Edit,
    Eye
} from 'lucide-react';
import * as React from 'react';

interface CategoryBadgeProps {
  category: 'Approved' | 'Under Review' | 'Draft' | 'Template' | 'Archived';
  size?: 'sm' | 'md';
  showIcon?: boolean;
}

/**
 * CategoryBadge Component - Display document categories with icons
 *
 * Shows appropriate colors and icons based on document category
 */
export const CategoryBadge: React.FC<CategoryBadgeProps> = ({
  category,
  size = 'sm',
  showIcon = false
}) => {
  const config = {
    'Approved': {
      className: 'bg-success/10 text-success border-success/20 hover:bg-success/10',
      icon: CheckCircle2,
      label: 'Approved'
    },
    'Under Review': {
      className: 'bg-warning/10 text-warning border-warning/20 hover:bg-warning/10',
      icon: Eye,
      label: 'Under Review'
    },
    'Draft': {
      className: 'bg-muted text-muted-foreground border-muted-foreground/20 hover:bg-muted',
      icon: Edit,
      label: 'Draft'
    },
    'Template': {
      className: 'bg-info/10 text-info border-info/20 hover:bg-info/10',
      icon: Copy,
      label: 'Template'
    },
    'Archived': {
      className: 'bg-destructive/10 text-destructive border-destructive/20 hover:bg-destructive/10',
      icon: Archive,
      label: 'Archived'
    }
  };

  const categoryConfig = config[category];
  const Icon = categoryConfig.icon;

  return (
    <Badge
      className={cn(
        categoryConfig.className,
        size === 'sm' ? 'text-xs px-2 py-0.5' : 'text-sm px-3 py-1'
      )}
    >
      {showIcon && <Icon className={cn("mr-1", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />}
      {categoryConfig.label}
    </Badge>
  );
};

CategoryBadge.displayName = 'CategoryBadge';

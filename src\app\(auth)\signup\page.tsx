'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'
import { SuperAdminOnlyRoute } from '@/components/auth/protected-route'

import { AuthHeader } from '../login/components/auth-header'
import { SignupForm } from './components/signup-form'

/**
 * Signup Page - AI Compliance Platform (Super Admin Only)
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper Supabase authentication integration
 * - Super Admin role protection for pharmaceutical compliance
 */
export default function SignupPage() {
  usePageMetadata('Sign Up', 'Create your AI Compliance account')

  return (
    <SuperAdminOnlyRoute>
      <AuthHeader />
      <SignupForm />
    </SuperAdminOnlyRoute>
  )
}

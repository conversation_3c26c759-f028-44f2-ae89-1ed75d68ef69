/**
 * User Settings Type Definitions
 * Comprehensive type definitions for all user settings stored in JSONB columns
 * Following DEVELOPMENT_RULES.md and DEVELOPMENT_RULES_2.md for type safety
 */

// User Preferences Interface
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'en' | 'es' | 'fr' | 'de' | 'zh'
  timezone: string
  date_format: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD'
  time_format: '12h' | '24h'
  sidebar_collapsed: boolean
  dashboard_layout: 'grid' | 'list' | 'compact'
  items_per_page: 10 | 25 | 50 | 100
  auto_save: boolean
  keyboard_shortcuts: boolean
}

// Notification Preferences Interface
export interface NotificationPreferences {
  // General notification toggles
  email_notifications: boolean
  push_notifications: boolean

  // Specific email notification categories
  regulatory_updates: boolean
  document_processing_alerts: boolean
  weekly_compliance_reports: boolean

  // Specific push notification categories
  critical_compliance_issues: boolean
  processing_complete: boolean

  // Other notifications
  compliance_alerts: boolean
  document_updates: boolean
  system_announcements: boolean
  audit_notifications: boolean
  deadline_reminders: boolean
  training_reminders: boolean
  security_alerts: boolean

  digest_frequency: 'immediate' | 'daily' | 'weekly' | 'monthly'
  quiet_hours: {
    enabled: boolean
    start_time: string // HH:MM format
    end_time: string   // HH:MM format
  }
}

// Security Settings Interface
export interface SecuritySettings {
  two_factor_enabled: boolean
  session_timeout: number // minutes
  login_notifications: boolean
  password_change_notifications: boolean
  suspicious_activity_alerts: boolean
  device_management: boolean
  ip_whitelist: string[]
  password_requirements: {
    min_length: number
    require_uppercase: boolean
    require_lowercase: boolean
    require_numbers: boolean
    require_symbols: boolean
    expiry_days: number
  }
}

// Compliance Settings Interface
export interface ComplianceSettings {
  gxp_training_completed: boolean
  gxp_training_date: string | null
  gxp_training_expiry: string | null
  certifications: string[]
  audit_trail_level: 'basic' | 'detailed' | 'comprehensive'
  electronic_signature_required: boolean
  document_review_reminders: boolean
  compliance_framework: string[]
  regulatory_notifications: boolean
  validation_requirements: {
    document_approval: boolean
    change_control: boolean
    deviation_reporting: boolean
    capa_management: boolean
  }
}

// Activity Log Entry Interface
export interface ActivityLogEntry {
  id: string
  timestamp: string
  action: string
  resource: string
  resource_id: string | null
  details: Record<string, any>
  ip_address: string | null
  user_agent: string | null
  session_id: string | null
}

// Achievement Interface
export interface Achievement {
  id: string
  type: 'certification' | 'training' | 'milestone' | 'compliance'
  title: string
  description: string
  earned_date: string
  expiry_date: string | null
  issuer: string
  verification_url: string | null
  badge_url: string | null
  metadata: Record<string, any>
}

// Combined User Settings Interface
export interface UserSettings {
  preferences: UserPreferences
  notification_preferences: NotificationPreferences
  security_settings: SecuritySettings
  compliance_settings: ComplianceSettings
  activity_log: ActivityLogEntry[]
  achievements: Achievement[]
  bio: string | null
}

// Default values for new users
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'en',
  timezone: 'UTC',
  date_format: 'MM/DD/YYYY',
  time_format: '12h',
  sidebar_collapsed: false,
  dashboard_layout: 'grid',
  items_per_page: 25,
  auto_save: true,
  keyboard_shortcuts: true
}

export const DEFAULT_NOTIFICATION_PREFERENCES: NotificationPreferences = {
  // General notification toggles
  email_notifications: true,
  push_notifications: true,

  // Specific email notification categories
  regulatory_updates: true,
  document_processing_alerts: true,
  weekly_compliance_reports: true,

  // Specific push notification categories
  critical_compliance_issues: true,
  processing_complete: true,

  // Other notifications
  compliance_alerts: true,
  document_updates: true,
  system_announcements: true,
  audit_notifications: true,
  deadline_reminders: true,
  training_reminders: true,
  security_alerts: true,

  digest_frequency: 'daily',
  quiet_hours: {
    enabled: false,
    start_time: '22:00',
    end_time: '08:00'
  }
}

export const DEFAULT_SECURITY_SETTINGS: SecuritySettings = {
  two_factor_enabled: false,
  session_timeout: 30,
  login_notifications: true,
  password_change_notifications: true,
  suspicious_activity_alerts: true,
  device_management: true,
  ip_whitelist: [],
  password_requirements: {
    min_length: 12,
    require_uppercase: true,
    require_lowercase: true,
    require_numbers: true,
    require_symbols: true,
    expiry_days: 90
  }
}

export const DEFAULT_COMPLIANCE_SETTINGS: ComplianceSettings = {
  gxp_training_completed: false,
  gxp_training_date: null,
  gxp_training_expiry: null,
  certifications: [],
  audit_trail_level: 'detailed',
  electronic_signature_required: true,
  document_review_reminders: true,
  compliance_framework: [],
  regulatory_notifications: true,
  validation_requirements: {
    document_approval: true,
    change_control: true,
    deviation_reporting: true,
    capa_management: true
  }
}

// Type guards for runtime validation
export const isUserPreferences = (obj: any): obj is UserPreferences => {
  return obj && typeof obj === 'object' && 'theme' in obj
}

export const isNotificationPreferences = (obj: any): obj is NotificationPreferences => {
  return obj && typeof obj === 'object' && 'email_notifications' in obj
}

export const isSecuritySettings = (obj: any): obj is SecuritySettings => {
  return obj && typeof obj === 'object' && 'two_factor_enabled' in obj
}

export const isComplianceSettings = (obj: any): obj is ComplianceSettings => {
  return obj && typeof obj === 'object' && 'gxp_training_completed' in obj
}

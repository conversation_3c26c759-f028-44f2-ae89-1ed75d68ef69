'use client'

import { CheckCircle } from 'lucide-react'

import { Card, CardContent } from '@/components/ui-radix/card'

interface WorkflowStepsProps {
  readonly currentStep: number;
}

export function WorkflowSteps({ currentStep }: WorkflowStepsProps) {
  const getStepStatus = (step: number) => {
    if (step < currentStep) {
return 'completed'
}
    if (step === currentStep) {
return 'current'
}
    return 'upcoming'
  }

  const stepTitles = {
    1: 'Upload Documents',
    2: 'Select Frameworks',
    3: 'Configure Analysis',
  }

  const stepDescriptions = {
    1: 'Upload your compliance documents for analysis',
    2: 'Choose the regulatory frameworks to validate against',
    3: 'Select analysis depth and review settings',
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-center mb-6">
          {[1, 2, 3].map((step, index) => (
            <div key={step} className="flex items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  getStepStatus(step) === 'completed'
                    ? 'bg-primary border-primary text-primary-foreground'
                    : getStepStatus(step) === 'current'
                      ? 'border-primary text-primary bg-primary/10'
                      : 'border-muted-foreground text-muted-foreground'
                }`}
              >
                {getStepStatus(step) === 'completed' ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <span className="font-medium">{step}</span>
                )}
              </div>
              {index < 2 && (
                <div
                  className={`h-0.5 w-24 mx-4 rounded-full ${
                    getStepStatus(step + 1) === 'completed' ||
                    getStepStatus(step + 1) === 'current'
                      ? 'bg-primary'
                      : 'bg-muted-foreground/30'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">
            {stepTitles[currentStep as keyof typeof stepTitles]}
          </h3>
          <p className="text-muted-foreground">
            {stepDescriptions[currentStep as keyof typeof stepDescriptions]}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

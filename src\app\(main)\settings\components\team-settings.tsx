'use client'

import { Plus, Settings, Users } from 'lucide-react'

import { Avatar, AvatarFallback } from '@/components/ui-radix/avatar'
import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card'


import type { TeamMember } from '../types'

interface TeamSettingsProps {
  readonly teamMembers: readonly TeamMember[];
  readonly onInviteMember?: () => void;
  readonly onManageMember?: (memberId: string) => void;
}

export function TeamSettingsComponent({
  teamMembers,
  onInviteMember,
  onManageMember,
}: TeamSettingsProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success text-success-foreground'
      case 'pending':
        return 'bg-warning text-warning-foreground'
      case 'inactive':
        return 'bg-muted text-muted-foreground'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-foreground flex items-center justify-between">
          <span className="flex items-center">
            <Users className="mr-2 h-6 w-6" />
            Team Management
          </span>
          <Button onClick={onInviteMember}>
            <Plus className="mr-2 h-4 w-4" />
            Invite Member
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {teamMembers.map((member) => (
            <div
              key={member.id}
              className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/30 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarFallback>
                    {member.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-medium">{member.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {member.email}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-muted-foreground">
                      {member.role}
                    </span>
                    <span className="text-xs text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground">
                      Last login: {member.lastLogin}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(member.status)}>
                  {member.status}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:bg-muted hover:text-foreground"
                  onClick={() => onManageMember?.(member.id)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

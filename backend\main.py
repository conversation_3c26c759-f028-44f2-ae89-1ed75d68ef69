#!/usr/bin/env python3
"""VigiLens Pharmaceutical Compliance Platform - AI-Focused FastAPI Application.

VCP_023: Python Backend Refactoring to AI-Only Services
Main application entry point for AI processing services.
"""

import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional

from fastapi import FastAPI, HTTPException, Request, status, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import routers
try:
    from routers import health, auth
    ROUTERS_AVAILABLE = True
    AUTH_ROUTER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Could not import routers: {e}")
    ROUTERS_AVAILABLE = False
    AUTH_ROUTER_AVAILABLE = False
    try:
        from routers import health
        ROUTERS_AVAILABLE = True
    except ImportError:
        pass

# Import new integrated services
try:
    from services.integration.orchestrator_rag_integration import (
        OrchestratorRAGIntegration,
        IntegratedResponse,
        IntegrationMode,
        ComplianceQueryType
    )
    from services.orchestrator.multi_agent_orchestrator import MultiAgentOrchestrator
    from services.knowledge.fda_knowledge_populator import FDAKnowledgePopulator
    from services.analysis.document_analysis_pipeline import (
        DocumentAnalysisPipeline,
        DocumentAnalysisResult,
        DocumentType,
        AnalysisType
    )
    INTEGRATED_SERVICES_AVAILABLE = True
    logger.info("Integrated multi-agent services loaded successfully")
except ImportError as e:
    logger.warning(f"Could not import integrated services: {e}")
    INTEGRATED_SERVICES_AVAILABLE = False

# Application configuration
class Settings(BaseModel):
    """Application settings from environment variables."""

    # Basic app config
    project_name: str = os.getenv('PROJECT_NAME', 'VigiLens AI Processing Engine')
    project_version: str = os.getenv('PROJECT_VERSION', '1.0.0')
    api_v1_str: str = os.getenv('API_V1_STR', '/api/v1')
    environment: str = os.getenv('ENVIRONMENT', 'development')

    # Server config
    host: str = os.getenv('HOST', '0.0.0.0')
    port: int = int(os.getenv('PORT', '8000'))
    debug: bool = os.getenv('DEBUG', 'true').lower() == 'true'

    # Security config
    secret_key: str = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')

    # CORS config
    backend_cors_origins: list = [
        origin.strip() for origin in
        os.getenv('BACKEND_CORS_ORIGINS', 'http://localhost:3000,http://localhost:3001').split(',')
    ]

    # AI Service config
    openrouter_api_key: str = os.getenv('OPENROUTER_API_KEY', '')
    openrouter_base_url: str = os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
    default_model: str = os.getenv('OPENROUTER_MODEL', 'moonshotai/kimi-k2:free')
    max_tokens: int = int(os.getenv('MAX_TOKENS', '4096'))
    temperature: float = float(os.getenv('TEMPERATURE', '0.1'))
    ai_timeout: int = int(os.getenv('AI_TIMEOUT', '30'))

    # Vector DB config
    chromadb_path: str = os.getenv('CHROMADB_PATH', './data/chromadb')
    chromadb_collection: str = os.getenv('CHROMADB_COLLECTION', 'pharmaceutical_kb')
    embedding_model: str = os.getenv('EMBEDDING_MODEL', 'all-MiniLM-L6-v2')

    # Knowledge Base config
    knowledge_base_path: str = os.getenv('KNOWLEDGE_BASE_PATH', './data/knowledge_base')
    knowledge_base_version: str = os.getenv('KNOWLEDGE_BASE_VERSION', 'v1.0.0')

    # Feature flags
    enable_docs: bool = os.getenv('ENABLE_DOCS', 'true').lower() == 'true'
    enable_redoc: bool = os.getenv('ENABLE_REDOC', 'true').lower() == 'true'
    enable_openapi: bool = os.getenv('ENABLE_OPENAPI', 'true').lower() == 'true'


settings = Settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info(f"Starting {settings.project_name} v{settings.project_version}")
    logger.info(f"Environment: {settings.environment}")

    # Initialize AI services
    try:
        # Initialize ChromaDB
        from langchain_community.vectorstores import Chroma
        from langchain_community.embeddings import HuggingFaceEmbeddings

        # Ensure ChromaDB directory exists
        os.makedirs(settings.chromadb_path, exist_ok=True)

        # Initialize integrated multi-agent services
        if INTEGRATED_SERVICES_AVAILABLE:
            logger.info("Initializing integrated multi-agent orchestrator...")
            app.state.integration_service = OrchestratorRAGIntegration(
                knowledge_base_path=settings.chromadb_path,
                default_mode=IntegrationMode.HYBRID
            )

            # Initialize document analysis pipeline
            logger.info("Initializing document analysis pipeline...")
            app.state.document_analysis_pipeline = DocumentAnalysisPipeline(
                knowledge_base_path=settings.chromadb_path
            )

            logger.info("✅ Multi-agent orchestrator and document analysis pipeline initialized successfully")

        # Log AI service initialization
        logger.info("✅ AI services initialized successfully")
        logger.info(f"ChromaDB path: {settings.chromadb_path}")
        logger.info(f"Default LLM model: {settings.default_model}")

    except Exception as e:
        logger.warning(f"⚠️ AI services initialization warning: {e}")
        logger.info("Continuing with limited AI functionality...")

    yield

    # Shutdown
    logger.info("Shutting down application")
    logger.info("AI services shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=settings.project_name,
    version=settings.project_version,
    description="AI Processing Engine for Pharmaceutical Compliance Analysis",
    docs_url="/docs" if settings.enable_docs else None,
    redoc_url="/redoc" if settings.enable_redoc else None,
    openapi_url="/openapi.json" if settings.enable_openapi else None,
    lifespan=lifespan
)

# Security
security = HTTPBearer(auto_error=False)

# Middleware configuration
if settings.backend_cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.backend_cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Trusted host middleware for production
if settings.environment == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.vigilens.ai", "localhost", "127.0.0.1"]
    )

# Include routers
if ROUTERS_AVAILABLE:
    app.include_router(health.router, prefix=settings.api_v1_str)
    logger.info("Health router included successfully")

    if AUTH_ROUTER_AVAILABLE:
        app.include_router(auth.router, prefix=settings.api_v1_str)
        logger.info("Authentication router included successfully")
    else:
        logger.warning("Authentication router not available")

    logger.info("API routers included successfully")
else:
    logger.warning("API routers not available, using fallback endpoints")


# Response models
class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    message: str
    version: str
    environment: str
    ai_services: str
    timestamp: str


class AIStatsResponse(BaseModel):
    """AI service statistics response model."""
    models_loaded: int
    vector_db_size: str
    processing_queue: int
    total_processed: int
    average_processing_time: float
    last_updated: str


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    status_code: int
    timestamp: str


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with consistent error format."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Exception",
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions with logging."""
    # Acknowledge request parameter to avoid warning
    _ = request

    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred" if settings.environment == "production" else str(exc),
            "status_code": 500,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    )


# Utility functions
async def get_ai_client():
    """Get AI client for language model operations."""
    try:
        from services.ai.client import get_ai_client as get_client
        return await get_client()
    except ImportError as e:
        logger.error(f"AI client module not available: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI client module not available: {str(e)}"
        )
    except Exception as e:
        logger.error(f"AI service initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI service not available: {str(e)}"
        )


async def get_vector_store():
    """Get vector store for knowledge base operations."""
    try:
        from services.ai.vector_store import get_vector_store as get_store
        return await get_store()
    except ImportError:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Vector store service not available"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Vector store not available: {str(e)}"
        )


# Root endpoints
@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root endpoint with basic application information."""
    return {
        "name": settings.project_name,
        "version": settings.project_version,
        "description": "AI Processing Engine for Pharmaceutical Compliance Analysis",
        "environment": settings.environment,
        "docs_url": "/docs" if settings.enable_docs else None,
        "redoc_url": "/redoc" if settings.enable_redoc else None,
        "api_v1": settings.api_v1_str,
        "status": "operational"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for monitoring and load balancers."""
    from datetime import datetime, timezone

    # Check AI services
    ai_status = "healthy"
    try:
        await get_ai_client()
    except Exception as e:
        logger.error(f"AI service health check failed: {e}")
        ai_status = "degraded"

    return HealthResponse(
        status=ai_status,
        message="AI service is operational" if ai_status == "healthy" else "AI service issues",
        version=settings.project_version,
        environment=settings.environment,
        ai_services=ai_status,
        timestamp=datetime.now(timezone.utc).isoformat()
    )


@app.get("/health/ai", response_model=AIStatsResponse)
async def ai_health():
    """Detailed AI service health and statistics."""
    from datetime import datetime, timezone

    try:
        # Get AI service statistics
        # These will be implemented with actual AI services in VCP_023_3
        models_loaded = 1  # Will be dynamic
        vector_db_size = "0 MB"  # Will check ChromaDB size
        processing_queue = 0  # Will check actual queue
        total_processed = 0  # Will track from metrics
        avg_processing_time = 0.0  # Will calculate from metrics

        return AIStatsResponse(
            models_loaded=models_loaded,
            vector_db_size=vector_db_size,
            processing_queue=processing_queue,
            total_processed=total_processed,
            average_processing_time=avg_processing_time,
            last_updated=datetime.now(timezone.utc).isoformat()
        )

    except Exception as e:
        logger.error(f"AI stats query failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI stats query failed: {str(e)}"
        )


@app.get("/version")
async def version():
    """Get application version information."""
    return {
        "version": settings.project_version,
        "name": settings.project_name,
        "environment": settings.environment,
        "python_version": sys.version,
        "fastapi_version": "0.115.5",
        "langchain_version": "0.3.26",
        "chromadb_version": "1.0.15"
    }


# VCP_023 AI Service endpoints
@app.get("/ai/models")
async def ai_models():
    """Get available AI models and their status."""
    try:
        return {
            "available_models": [
                {
                    "name": "moonshot/kimi-k2",
                    "provider": "openrouter",
                    "status": "available",
                    "type": "text-generation",
                    "context_length": 4096,
                    "cost": "free"
                }
            ],
            "default_model": settings.default_model,
            "embedding_model": "all-MiniLM-L6-v2",
            "vector_db": {
                "type": "chromadb",
                "path": settings.chromadb_path,
                "status": "available"
            }
        }
    except Exception as e:
        logger.error(f"AI models query failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI models query failed: {str(e)}"
        )


@app.get("/ai/validate")
async def validate_ai_services():
    """Validate AI services and dependencies."""
    from datetime import datetime, timezone

    try:
        # Define expected AI components for validation
        required_packages = ["langchain", "chromadb", "numpy", "pandas"]

        # Placeholder for actual validation logic
        # This will be implemented in VCP_023_3
        validation_results = {
            "ai_services_valid": True,
            "missing_components": [],
            "validation_timestamp": datetime.now(timezone.utc).isoformat()
        }

        # Check ChromaDB directory
        if not os.path.exists(settings.chromadb_path):
            validation_results["missing_components"].append("chromadb_directory")
            validation_results["ai_services_valid"] = False

        # Check OpenRouter API key
        if not settings.openrouter_api_key:
            validation_results["missing_components"].append("openrouter_api_key")
            validation_results["ai_services_valid"] = False

        # Try importing required packages
        try:
            import importlib

            for package in required_packages:
                try:
                    importlib.import_module(package)
                except ImportError:
                    validation_results["missing_components"].append(package)
                    validation_results["ai_services_valid"] = False
        except Exception as e:
            logger.warning(f"Package validation error: {e}")

        return validation_results

    except ImportError as e:
        logger.error(f"AI service validation failed - missing dependency: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI service validation failed - missing dependency: {str(e)}"
        )
    except Exception as e:
        logger.error(f"AI service validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"AI service validation failed: {str(e)}"
        )


# AI Endpoint placeholders for VCP_023_2
# These will be implemented in the next subtask

class DocumentAnalysisRequest(BaseModel):
    """Document analysis request model."""
    document_id: str
    content: str
    document_type: str
    analysis_type: str = "compliance"  # compliance, summary, risk
    context: Optional[Dict[str, Any]] = None  # Additional context for analysis


class DocumentAnalysisResponse(BaseModel):
    """Document analysis response model."""
    document_id: str
    analysis_id: str
    analysis_type: str
    results: Dict[str, Any]
    confidence_score: float
    processing_time: float
    timestamp: str


@app.post("/api/v1/ai/analyze-document", response_model=DocumentAnalysisResponse)
async def analyze_document(request: DocumentAnalysisRequest):
    """
    Analyze a document for compliance, summarization, or risk assessment.
    Uses OpenRouter AI with MoonshotAI: Kimi K2 model.
    """
    from datetime import datetime, timezone
    import uuid
    import time

    start_time = time.time()
    analysis_id = str(uuid.uuid4())

    try:
        # Get AI client
        ai_client = await get_ai_client()

        # Perform document analysis
        result = await ai_client.analyze_document(
            document_content=request.content,
            document_type=request.document_type,
            analysis_type=request.analysis_type,
            context=getattr(request, 'context', None)
        )

        processing_time = time.time() - start_time

        if result["status"] == "success":
            analysis = result.get("analysis", {})

            return DocumentAnalysisResponse(
                document_id=request.document_id,
                analysis_id=analysis_id,
                analysis_type=request.analysis_type,
                results={
                    "status": "success",
                    "compliance_score": analysis.get("compliance_score", 0),
                    "risk_level": analysis.get("risk_level", "Unknown"),
                    "key_findings": analysis.get("key_findings", []),
                    "recommendations": analysis.get("recommendations", []),
                    "regulatory_frameworks": analysis.get("regulatory_frameworks", []),
                    "summary": analysis.get("raw_response", "Analysis completed successfully")
                },
                confidence_score=analysis.get("confidence", 0) / 100.0,  # Convert to 0-1 range
                processing_time=processing_time,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
        else:
            # Return error response
            logger.error(f"Document analysis failed: {result.get('error')}")
            return DocumentAnalysisResponse(
                document_id=request.document_id,
                analysis_id=analysis_id,
                analysis_type=request.analysis_type,
                results={
                    "status": "error",
                    "error": result.get("error", "Unknown error"),
                    "details": result.get("details", "")
                },
                confidence_score=0.0,
                processing_time=processing_time,
                timestamp=datetime.now(timezone.utc).isoformat()
            )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Document analysis endpoint failed: {str(e)}")

        return DocumentAnalysisResponse(
            document_id=request.document_id,
            analysis_id=analysis_id,
            analysis_type=request.analysis_type,
            results={
                "status": "system_error",
                "error": f"System error during analysis: {str(e)}"
            },
            confidence_score=0.0,
            processing_time=processing_time,
            timestamp=datetime.now(timezone.utc).isoformat()
        )


@app.post("/api/v1/ai/summarize")
async def summarize_document(request: Dict[str, Any]):
    """
    Summarize a document using AI.
    Uses OpenRouter AI with MoonshotAI: Kimi K2 model.
    """
    from datetime import datetime, timezone
    import time

    start_time = time.time()

    try:
        # Extract request data
        content = request.get("content", "")
        document_type = request.get("document_type", "document")
        max_length = request.get("max_length", 500)

        if not content:
            return {
                "status": "error",
                "error": "Content is required for summarization"
            }

        # Get AI client
        ai_client = await get_ai_client()

        # Create summarization prompt
        system_prompt = f"""You are a pharmaceutical compliance expert. Summarize the following {document_type} in approximately {max_length} words.

Focus on:
1. Key regulatory requirements
2. Compliance implications
3. Critical findings or recommendations
4. Risk factors

Provide a clear, concise summary suitable for pharmaceutical professionals."""

        user_prompt = f"Please summarize this {document_type}:\n\n{content}"

        # Generate summary
        result = await ai_client.generate_text(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3,  # Slightly higher for more natural summaries
            max_tokens=max_length * 2  # Allow some buffer for token estimation
        )

        processing_time = time.time() - start_time

        if result["status"] == "success":
            return {
                "status": "success",
                "summary": result["content"],
                "document_type": document_type,
                "processing_time": processing_time,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "model_info": {
                    "model": result.get("model"),
                    "usage": result.get("usage", {})
                }
            }
        else:
            return {
                "status": "error",
                "error": result.get("error", "Summarization failed"),
                "details": result.get("details", ""),
                "processing_time": processing_time,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Summarization endpoint failed: {str(e)}")

        return {
            "status": "system_error",
            "error": f"System error during summarization: {str(e)}",
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/compliance-score")
async def calculate_compliance_score(request: Dict[str, Any]):
    """
    Calculate compliance score for a document.
    Uses OpenRouter AI with MoonshotAI: Kimi K2 model.
    """
    from datetime import datetime, timezone
    import time

    start_time = time.time()

    try:
        # Extract request data
        content = request.get("content", "")
        document_type = request.get("document_type", "document")
        frameworks = request.get("frameworks", ["21_cfr_part_11", "eu_gmp"])

        if not content:
            return {
                "status": "error",
                "error": "Content is required for compliance scoring"
            }

        # Get AI client
        ai_client = await get_ai_client()

        # Create compliance scoring prompt
        frameworks_text = ", ".join(frameworks)
        system_prompt = f"""You are a pharmaceutical compliance expert. Analyze the following {document_type} for compliance with these regulatory frameworks: {frameworks_text}.

Provide a detailed compliance assessment with:
1. Overall compliance score (0-100)
2. Framework-specific scores
3. Critical violations (if any)
4. Recommendations for improvement
5. Risk level assessment

Return your response in JSON format:
{{
    "overall_score": <0-100>,
    "framework_scores": {{"framework": score, ...}},
    "critical_violations": ["violation1", "violation2", ...],
    "recommendations": ["rec1", "rec2", ...],
    "risk_level": "<High|Medium|Low>",
    "confidence": <0-100>
}}"""

        user_prompt = f"Please assess compliance for this {document_type}:\n\n{content}"

        # Generate compliance score
        result = await ai_client.generate_text(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.1,  # Low temperature for consistent scoring
            max_tokens=1500
        )

        processing_time = time.time() - start_time

        if result["status"] == "success":
            try:
                # Try to parse JSON response
                import json
                compliance_data = json.loads(result["content"])

                return {
                    "status": "success",
                    "compliance_score": compliance_data.get("overall_score", 0),
                    "framework_scores": compliance_data.get("framework_scores", {}),
                    "critical_violations": compliance_data.get("critical_violations", []),
                    "recommendations": compliance_data.get("recommendations", []),
                    "risk_level": compliance_data.get("risk_level", "Unknown"),
                    "confidence": compliance_data.get("confidence", 0),
                    "frameworks_assessed": frameworks,
                    "document_type": document_type,
                    "processing_time": processing_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "model_info": {
                        "model": result.get("model"),
                        "usage": result.get("usage", {})
                    }
                }
            except json.JSONDecodeError:
                # If JSON parsing fails, return basic response
                return {
                    "status": "success",
                    "compliance_score": 50,  # Default score
                    "raw_response": result["content"],
                    "frameworks_assessed": frameworks,
                    "document_type": document_type,
                    "processing_time": processing_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "note": "Response was not in expected JSON format"
                }
        else:
            return {
                "status": "error",
                "error": result.get("error", "Compliance scoring failed"),
                "details": result.get("details", ""),
                "processing_time": processing_time,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Compliance scoring endpoint failed: {str(e)}")

        return {
            "status": "system_error",
            "error": f"System error during compliance scoring: {str(e)}",
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/knowledge-search")
async def search_knowledge_base(request: Dict[str, Any]):
    """
    Search the pharmaceutical knowledge base using vector similarity.
    Uses ChromaDB for semantic search.
    """
    from datetime import datetime, timezone
    import time

    start_time = time.time()

    try:
        # Extract request data
        query = request.get("query", "")
        n_results = min(request.get("n_results", 5), 20)  # Limit to 20 max
        filters = request.get("filters", {})

        if not query:
            return {
                "status": "error",
                "error": "Query is required for knowledge base search"
            }

        # Get vector store
        vector_store = await get_vector_store()

        # Perform search
        results = await vector_store.search(
            query=query,
            n_results=n_results,
            filters=filters
        )

        processing_time = time.time() - start_time

        return {
            "status": "success",
            "query": query,
            "results": results,
            "result_count": len(results),
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Knowledge search endpoint failed: {str(e)}")

        return {
            "status": "system_error",
            "error": f"System error during knowledge search: {str(e)}",
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/populate-knowledge-base")
async def populate_knowledge_base(request: Dict[str, Any]):
    """
    Populate the pharmaceutical knowledge base with regulatory data.
    Enhanced to process local FDA CFR documents and gather data from FDA, EMA, ICH APIs.
    """
    from datetime import datetime, timezone
    import time

    start_time = time.time()

    try:
        # Extract request parameters
        sources = request.get("sources", ["local_cfr", "fda", "ema", "ich"])
        force_refresh = request.get("force_refresh", False)

        logger.info(f"Starting knowledge base population with sources: {sources}")

        results = {
            "status": "success",
            "sources_processed": [],
            "total_documents_added": 0,
            "processing_details": {}
        }

        # Process local FDA CFR documents if requested
        if "local_cfr" in sources:
            try:
                logger.info("Processing local FDA CFR documents...")

                # Import FDA processing services
                from services.ai.pdf_processor import get_fda_pdf_processor
                from services.ai.fda_chunker import get_fda_chunker
                from services.ai.fda_metadata import get_fda_metadata_manager
                from services.ai.vector_store import add_fda_documents

                # Initialize FDA processing pipeline
                pdf_processor = get_fda_pdf_processor()
                metadata_manager = get_fda_metadata_manager()

                # Process all FDA PDF documents
                processed_documents = await pdf_processor.process_all_fda_documents()

                if processed_documents:
                    # Load FDA hierarchy for intelligent chunking
                    hierarchy_data = await pdf_processor.load_fda_hierarchy()
                    chunker = get_fda_chunker(hierarchy_data)

                    # Process each document through the pipeline
                    all_chunks = []

                    for doc in processed_documents:
                        # Create intelligent chunks
                        chunks = chunker.chunk_fda_document(doc)

                        # Enrich chunks with FDA metadata
                        enriched_chunks = []
                        for chunk in chunks:
                            enriched_chunk = metadata_manager.enrich_chunk_metadata(chunk)
                            enriched_chunks.append(enriched_chunk)

                        all_chunks.extend(enriched_chunks)

                    # Add to vector store
                    if all_chunks:
                        vector_result = await add_fda_documents(all_chunks)

                        results["sources_processed"].append("local_cfr")
                        results["total_documents_added"] += vector_result.get("documents_added", 0)
                        results["processing_details"]["local_cfr"] = {
                            "pdf_documents_processed": len(processed_documents),
                            "chunks_created": len(all_chunks),
                            "chunks_added_to_vector_store": vector_result.get("documents_added", 0),
                            "processing_stats": pdf_processor.get_processing_stats()
                        }

                        logger.info(f"✅ Successfully processed {len(processed_documents)} FDA CFR documents into {len(all_chunks)} chunks")
                    else:
                        logger.warning("No chunks created from FDA documents")
                else:
                    logger.warning("No FDA documents found to process")

            except Exception as e:
                logger.error(f"Failed to process local FDA CFR documents: {str(e)}")
                results["processing_details"]["local_cfr"] = {
                    "error": str(e),
                    "status": "failed"
                }

        # Process API-based sources (existing functionality)
        api_sources = [s for s in sources if s != "local_cfr"]
        if api_sources:
            try:
                # Get data gathering orchestrator for API sources
                from services.data_gathering import get_data_orchestrator
                orchestrator = await get_data_orchestrator()

                try:
                    # Populate knowledge base from APIs
                    api_result = await orchestrator.populate_knowledge_base(sources=api_sources)

                    # Merge API results
                    if api_result.get("status") == "success":
                        results["sources_processed"].extend(api_result.get("sources_processed", []))
                        results["total_documents_added"] += api_result.get("total_documents_added", 0)
                        results["processing_details"].update(api_result.get("processing_details", {}))

                finally:
                    # Always close orchestrator
                    await orchestrator.close()

            except Exception as e:
                logger.error(f"Failed to process API sources: {str(e)}")
                results["processing_details"]["api_sources"] = {
                    "error": str(e),
                    "status": "failed"
                }

        processing_time = time.time() - start_time
        results["processing_time"] = processing_time
        results["timestamp"] = datetime.now(timezone.utc).isoformat()

        logger.info(f"🎯 Knowledge base population completed in {processing_time:.2f}s")

        return results

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Knowledge base population endpoint failed: {str(e)}")

        return {
            "status": "system_error",
            "error": f"System error during knowledge base population: {str(e)}",
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.get("/api/v1/ai/knowledge-base-stats")
async def get_knowledge_base_stats():
    """
    Get statistics about the pharmaceutical knowledge base.
    """
    try:
        # Get vector store
        vector_store = await get_vector_store()

        # Get collection stats
        stats = await vector_store.get_collection_stats()

        return {
            "status": "success",
            "stats": stats,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Knowledge base stats endpoint failed: {str(e)}")

        return {
            "status": "error",
            "error": f"Failed to get knowledge base stats: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/clean-knowledge-base")
async def clean_knowledge_base(request: Dict[str, Any]):
    """
    Clean the knowledge base by removing documents based on filters.
    Specifically designed to remove ICH and EMA documents while keeping FDA data.
    """
    from datetime import datetime, timezone
    import time

    start_time = time.time()

    try:
        # Extract request parameters
        remove_sources = request.get("remove_sources", ["ich", "ema"])
        confirm_deletion = request.get("confirm_deletion", False)

        if not confirm_deletion:
            return {
                "status": "confirmation_required",
                "message": "Please set 'confirm_deletion': true to proceed with deletion",
                "remove_sources": remove_sources,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        logger.info(f"Starting knowledge base cleanup - removing sources: {remove_sources}")

        # Get vector store
        from services.ai.vector_store import get_vector_store
        vector_store = await get_vector_store()

        results = {
            "status": "success",
            "sources_removed": [],
            "total_documents_deleted": 0,
            "deletion_details": {}
        }

        # Remove ICH documents
        if "ich" in remove_sources:
            try:
                logger.info("Removing ICH documents...")
                ich_filter = {
                    "$or": [
                        {"source": {"$eq": "ich"}},
                        {"document_type": {"$eq": "ich_guideline"}},
                        {"compliance_frameworks": {"$like": "%ich%"}}
                    ]
                }

                ich_result = await vector_store.delete_documents_by_filter(ich_filter)

                if ich_result["success"]:
                    results["sources_removed"].append("ich")
                    results["total_documents_deleted"] += ich_result["documents_deleted"]
                    results["deletion_details"]["ich"] = ich_result
                    logger.info(f"✅ Removed {ich_result['documents_deleted']} ICH documents")
                else:
                    results["deletion_details"]["ich"] = {"error": ich_result.get("error", "Unknown error")}

            except Exception as e:
                logger.error(f"Failed to remove ICH documents: {str(e)}")
                results["deletion_details"]["ich"] = {"error": str(e)}

        # Remove EMA documents
        if "ema" in remove_sources:
            try:
                logger.info("Removing EMA documents...")
                ema_filter = {
                    "$or": [
                        {"source": {"$eq": "ema"}},
                        {"document_type": {"$eq": "ema_guideline"}},
                        {"compliance_frameworks": {"$like": "%ema%"}},
                        {"compliance_frameworks": {"$like": "%eu_gmp%"}}
                    ]
                }

                ema_result = await vector_store.delete_documents_by_filter(ema_filter)

                if ema_result["success"]:
                    results["sources_removed"].append("ema")
                    results["total_documents_deleted"] += ema_result["documents_deleted"]
                    results["deletion_details"]["ema"] = ema_result
                    logger.info(f"✅ Removed {ema_result['documents_deleted']} EMA documents")
                else:
                    results["deletion_details"]["ema"] = {"error": ema_result.get("error", "Unknown error")}

            except Exception as e:
                logger.error(f"Failed to remove EMA documents: {str(e)}")
                results["deletion_details"]["ema"] = {"error": str(e)}

        processing_time = time.time() - start_time
        results["processing_time"] = processing_time
        results["timestamp"] = datetime.now(timezone.utc).isoformat()

        logger.info(f"🎯 Knowledge base cleanup completed in {processing_time:.2f}s")
        logger.info(f"Total documents deleted: {results['total_documents_deleted']}")

        return results

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Knowledge base cleanup endpoint failed: {str(e)}")

        return {
            "status": "system_error",
            "error": f"System error during knowledge base cleanup: {str(e)}",
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/regulatory-monitor")
async def regulatory_monitor(request: Dict[str, Any]):
    """
    Monitor regulatory changes from FDA, EMA, etc.
    This is a placeholder that will be implemented in future versions.
    """
    # Acknowledge request parameter to avoid warning
    _ = request
    return {
        "status": "placeholder",
        "message": "Regulatory monitoring will be implemented in future versions"
    }


# VCP_024 Enhanced Multi-Agent Orchestrator Endpoints
class IntegratedComplianceRequest(BaseModel):
    """Request model for integrated compliance queries."""
    query: str
    context: Optional[str] = None
    mode: Optional[str] = None  # "rag_first", "orchestrator_first", "parallel", "hybrid"


@app.post("/api/v1/ai/integrated-compliance", response_model=Dict[str, Any])
async def integrated_compliance_query(request: IntegratedComplianceRequest):
    """
    Process pharmaceutical compliance query using integrated multi-agent orchestrator.

    Combines RAG pipeline with multi-agent orchestration for comprehensive analysis.
    Supports multiple processing modes for optimal results.
    """
    if not INTEGRATED_SERVICES_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Integrated multi-agent services not available"
        )

    try:
        start_time = datetime.now(timezone.utc)

        # Get integration service from app state
        integration_service = getattr(app.state, 'integration_service', None)
        if not integration_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Integration service not initialized"
            )

        # Parse integration mode
        mode = None
        if request.mode:
            try:
                mode = IntegrationMode(request.mode)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid mode: {request.mode}. Valid modes: rag_first, orchestrator_first, parallel, hybrid"
                )

        # Process query
        result = await integration_service.process_compliance_query(
            query=request.query,
            context=request.context,
            mode=mode
        )

        processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        return {
            "status": "success",
            "result": result.dict(),
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Integrated compliance query failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Integrated compliance query failed: {str(e)}"
        )


@app.post("/api/v1/ai/populate-fda-knowledge")
async def populate_fda_knowledge():
    """
    Populate knowledge base with FDA CFR Title 21 documents.

    Uses the new FDA Knowledge Populator for 21 CFR Part 11 compliant processing.
    """
    if not INTEGRATED_SERVICES_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Integrated services not available"
        )

    try:
        start_time = datetime.now(timezone.utc)

        # Initialize FDA knowledge populator
        populator = FDAKnowledgePopulator(qdrant_path="./qdrant_fda_db", collection_name="fda_compliance_docs")

        # Populate FDA documents
        result = await populator.populate_fda_documents()

        processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        return {
            "status": "success",
            "result": result,
            "processing_time": processing_time,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"FDA knowledge population failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"FDA knowledge population failed: {str(e)}"
        )


@app.get("/api/v1/ai/orchestrator-status")
async def get_orchestrator_status():
    """
    Get status of the multi-agent orchestrator system.
    """
    if not INTEGRATED_SERVICES_AVAILABLE:
        return {
            "status": "unavailable",
            "message": "Integrated services not available",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    try:
        integration_service = getattr(app.state, 'integration_service', None)

        if not integration_service:
            return {
                "status": "not_initialized",
                "message": "Integration service not initialized",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Get knowledge base status
        kb_status = await integration_service.knowledge_populator.get_population_status()

        return {
            "status": "operational",
            "integration_service": "initialized",
            "knowledge_base": kb_status,
            "available_modes": ["rag_first", "orchestrator_first", "parallel", "hybrid"],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Orchestrator status check failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@app.post("/api/v1/ai/analyze-document-advanced")
async def analyze_document_advanced(
    file: UploadFile = File(..., description="Document to analyze (PDF format)"),
    analysis_type: str = "full_analysis"
):
    """
    Advanced document analysis using multi-agent orchestrator pipeline.

    Supports comprehensive pharmaceutical compliance document analysis with:
    - Executive summary generation
    - Key regulatory changes identification
    - Compliance impact assessment
    - Risk assessment
    - Actionable recommendations
    """
    if not INTEGRATED_SERVICES_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Advanced document analysis services not available"
        )

    # Validate file type
    if not file.filename or not file.filename.lower().endswith('.pdf'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only PDF files are supported for document analysis"
        )

    # Validate file size (max 50MB for security)
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="File size exceeds maximum limit of 50MB"
        )

    try:
        start_time = datetime.now(timezone.utc)

        # Get document analysis pipeline from app state
        pipeline = getattr(app.state, 'document_analysis_pipeline', None)
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Document analysis pipeline not initialized"
            )

        # Save uploaded file temporarily
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Parse analysis type
            try:
                analysis_type_enum = AnalysisType(analysis_type)
            except ValueError:
                analysis_type_enum = AnalysisType.FULL_ANALYSIS

            # Perform document analysis
            result = await pipeline.analyze_document(
                document_path=temp_file_path,
                analysis_type=analysis_type_enum
            )

            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            return {
                "status": "success",
                "filename": file.filename,
                "file_size": len(file_content),
                "analysis_type": analysis_type,
                "result": result.dict(),
                "processing_time": processing_time,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        finally:
            # Clean up temporary file
            import os
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass  # File already deleted or doesn't exist

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Advanced document analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Advanced document analysis failed: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug and settings.environment == "development",
        log_level=os.getenv('LOG_LEVEL', 'info').lower()
    )

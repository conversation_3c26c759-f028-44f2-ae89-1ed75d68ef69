#!/usr/bin/env python3

import sys
from pathlib import Path

print("=== FDA Knowledge Base Status Check ===")
print(f"Python version: {sys.version}")
print(f"Current directory: {Path.cwd()}")

# Check if Qdrant database directory exists
qdrant_path = Path("./qdrant_fda_db")
print(f"\nChecking Qdrant database path: {qdrant_path.absolute()}")
print(f"Directory exists: {qdrant_path.exists()}")

if qdrant_path.exists():
    print(f"Directory contents:")
    for item in qdrant_path.iterdir():
        print(f"  - {item.name} ({'dir' if item.is_dir() else 'file'})")
else:
    print("Qdrant database directory not found!")

# Check audit file
audit_file = Path("./fda_kb_setup_audit.json")
print(f"\nAudit file exists: {audit_file.exists()}")

if audit_file.exists():
    import json
    try:
        with open(audit_file, 'r') as f:
            audit_data = json.load(f)
        print(f"Audit entries: {len(audit_data)}")
        if audit_data:
            last_entry = audit_data[-1]
            print(f"Last audit action: {last_entry.get('action', 'unknown')}")
            print(f"Last audit timestamp: {last_entry.get('timestamp', 'unknown')}")
    except Exception as e:
        print(f"Error reading audit file: {e}")

print("\n=== Status Check Complete ===")
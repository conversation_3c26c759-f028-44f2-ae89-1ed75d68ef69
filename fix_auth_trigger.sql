-- COMPREHENSIVE FIX for VigiLens Authentication Issue
-- Run this SQL in Supabase SQL Editor: https://supabase.com/dashboard/project/esgciouphhajolkojipw/sql

-- Step 1: Ensure demo organization exists
INSERT INTO organizations (
    id,
    name,
    display_name,
    compliance_frameworks,
    industry,
    country,
    is_active,
    created_at,
    updated_at
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    'demo-pharma-corp',
    'Demo Pharmaceutical Corporation',
    ARRAY['fda_cgmp', 'ich_q7', 'gmp']::compliance_framework[],
    'pharmaceutical',
    'US',
    true,
    NOW(),
    NOW()
) ON CONFLICT (name) DO NOTHING;

-- Step 2: Fix RLS policies that block user registration
-- Drop the problematic RLS policy that blocks trigger inserts
DROP POLICY IF EXISTS "Controlled profile creation" ON user_profiles;

-- Drop existing policy if it exists and recreate with correct settings
DROP POLICY IF EXISTS "Allow trigger user creation" ON user_profiles;

-- Create a new policy that allows trigger-based user creation
CREATE POLICY "Allow trigger user creation" ON user_profiles
    FOR INSERT WITH CHECK (
        -- Allow inserts during user registration (when no session exists)
        auth.uid() IS NULL
        OR
        -- Allow users to create their own profile
        id = auth.uid()
    );

-- Step 3: Create enhanced function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    demo_org_id UUID;
    user_full_name TEXT;
BEGIN
    -- Get demo organization ID
    SELECT id INTO demo_org_id
    FROM organizations
    WHERE name = 'demo-pharma-corp'
    LIMIT 1;

    -- If demo org doesn't exist, create it
    IF demo_org_id IS NULL THEN
        INSERT INTO organizations (
            id, name, display_name, compliance_frameworks,
            industry, country, is_active, created_at, updated_at
        ) VALUES (
            '550e8400-e29b-41d4-a716-************',
            'demo-pharma-corp',
            'Demo Pharmaceutical Corporation',
            ARRAY['fda_cgmp', 'ich_q7', 'gmp']::compliance_framework[],
            'pharmaceutical', 'US', true, NOW(), NOW()
        ) RETURNING id INTO demo_org_id;
    END IF;

    -- Extract user name from metadata or email
    user_full_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Create user profile automatically when user signs up
    INSERT INTO user_profiles (
        id,
        email,
        full_name,
        organization_id,
        role,
        is_active,
        email_verified_at,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        user_full_name,
        demo_org_id,
        'read_only',
        true,
        CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN NEW.email_confirmed_at ELSE NULL END,
        NOW(),
        NOW()
    );

    -- Log the user creation event (only if audit function exists)
    BEGIN
        PERFORM log_audit_event(
            demo_org_id,
            NEW.id,
            'create',
            'New user profile created automatically',
            'user',
            NEW.id,
            user_full_name,
            NULL,
            jsonb_build_object(
                'registration_method', 'supabase_auth',
                'email_verified', COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
                'trigger_version', '2.0'
            )
        );
    EXCEPTION
        WHEN undefined_function THEN
            -- Audit function doesn't exist, skip logging
            NULL;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Drop existing trigger and create new one
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;

CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Step 5: Verify the setup
-- Check if demo organization exists
DO $$
DECLARE
    org_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO org_count FROM organizations WHERE name = 'demo-pharma-corp';
    IF org_count = 0 THEN
        RAISE NOTICE 'ERROR: Demo organization not found!';
    ELSE
        RAISE NOTICE 'SUCCESS: Demo organization exists (% records)', org_count;
    END IF;
END $$;

-- Check if trigger exists
DO $$
DECLARE
    trigger_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers
    WHERE trigger_name = 'handle_new_user_trigger';

    IF trigger_count = 0 THEN
        RAISE NOTICE 'ERROR: Trigger not found!';
    ELSE
        RAISE NOTICE 'SUCCESS: Trigger exists and is active';
    END IF;
END $$;

-- Verify the trigger was created
SELECT
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'handle_new_user_trigger';

/**
 * Role-Based Navigation Component for VigiLens
 *
 * Provides navigation with pharmaceutical compliance role-based access control
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, accessibility
 * Follows DEVELOPMENT_RULES_2.md: Production-first, runtime type safety
 */

'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAuth } from '@/contexts/auth-context'
import { LogoutButton } from '@/components/auth/logout-button'
import {
  LayoutDashboard,
  FileText,
  Search,
  Bot,
  Upload,
  Shield,
  Bell,
  Settings,
  User,
  Users,
  Building,
  TrendingUp,
  CheckCircle,
  type LucideIcon
} from 'lucide-react'

// Navigation item interface
interface NavItem {
  href: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description?: string
  requiredPermissions?: string[]
  requiredRoles?: string[]
  badge?: string | number
  isExternal?: boolean
}

// Navigation sections for pharmaceutical compliance platform
const NAVIGATION_SECTIONS: Record<string, NavItem[]> = {
  main: [
    {
      href: '/dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
      description: 'Overview and metrics'
    },
    {
      href: '/documents',
      label: 'Documents',
      icon: FileText,
      description: 'Regulatory document management',
      requiredPermissions: ['canViewCompliance']
    },
    {
      href: '/search',
      label: 'Search',
      icon: Search,
      description: 'Search and discovery'
    },
    {
      href: '/compliance-check',
      label: 'Compliance Check',
      icon: CheckCircle,
      description: 'Compliance validation',
      requiredPermissions: ['canViewCompliance']
    }
  ],
  tools: [
    {
      href: '/ai-assistant',
      label: 'AI Assistant',
      icon: Bot,
      description: 'AI-powered compliance assistance',
      requiredPermissions: ['canAccessAI']
    },
    {
      href: '/upload',
      label: 'Upload',
      icon: Upload,
      description: 'Document upload and processing',
      requiredPermissions: ['canCreateDocuments']
    },
    {
      href: '/updates',
      label: 'Regulatory Updates',
      icon: TrendingUp,
      description: 'Latest regulatory changes'
    }
  ],

  management: [
    {
      href: '/admin',
      label: 'Admin Panel',
      icon: Shield,
      description: 'System administration',
      requiredRoles: ['super_admin', 'admin']
    },
    {
      href: '/signup',
      label: 'User Registration',
      icon: Users,
      description: 'Register new users',
      requiredRoles: ['super_admin']
    }
  ],
  personal: [
    {
      href: '/notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Alerts and notifications'
    },
    {
      href: '/profile',
      label: 'Profile',
      icon: User,
      description: 'User profile and preferences'
    },
    {
      href: '/settings',
      label: 'Settings',
      icon: Settings,
      description: 'Application settings'
    }
  ]
}

interface RoleBasedNavProps {
  className?: string
  showSections?: boolean
  compact?: boolean
}

export function RoleBasedNav({
  className = '',
  showSections = true,
  compact = false
}: RoleBasedNavProps) {
  const { userProfile, hasPermission, hasRole, loading } = useAuth()
  const pathname = usePathname()

  // Filter navigation items based on user permissions and roles
  const filterNavItems = (items: NavItem[]): NavItem[] => {
    if (!userProfile) return []

    return items.filter(item => {
      // Check role requirements
      if (item.requiredRoles && !hasRole(item.requiredRoles)) {
        return false
      }

      // Check permission requirements
      if (item.requiredPermissions) {
        return item.requiredPermissions.every(permission =>
          hasPermission(permission as any)
        )
      }

      return true
    })
  }

  // Get filtered sections
  const filteredSections = React.useMemo(() => {
    const sections: Record<string, NavItem[]> = {}

    Object.entries(NAVIGATION_SECTIONS).forEach(([sectionKey, items]) => {
      const filteredItems = filterNavItems(items)
      if (filteredItems.length > 0) {
        sections[sectionKey] = filteredItems
      }
    })

    return sections
  }, [userProfile, hasPermission, hasRole])

  // Loading state
  if (loading) {
    return (
      <nav className={cn('space-y-2', className)}>
        <div className="animate-pulse space-y-2">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-10 bg-muted rounded-md" />
          ))}
        </div>
      </nav>
    )
  }

  // Not authenticated
  if (!userProfile) {
    return null
  }

  const NavLink = ({ item }: { item: NavItem }) => {
    const isActive = pathname === item.href || (pathname?.startsWith(item.href + '/') ?? false)
    const Icon = item.icon

    return (
      <Link
        href={item.href}
        className={cn(
          'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
          'hover:bg-accent hover:text-accent-foreground',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
          isActive && 'bg-accent text-accent-foreground',
          compact && 'px-2 py-1.5'
        )}
        aria-current={isActive ? 'page' : undefined}
      >
        <Icon className={cn('h-4 w-4', compact && 'h-3.5 w-3.5')} />
        {!compact && (
          <span className="flex-1">{item.label}</span>
        )}
        {item.badge && !compact && (
          <span className="ml-auto rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
            {item.badge}
          </span>
        )}
      </Link>
    )
  }

  const SectionHeader = ({ title }: { title: string }) => (
    <h3 className={cn(
      'px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider',
      compact && 'px-2'
    )}>
      {title}
    </h3>
  )

  return (
    <nav className={cn('space-y-4', className)} role="navigation" aria-label="Main navigation">
      {/* User info section */}
      {!compact && (
        <div className="px-3 py-2 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
              <span className="text-xs font-semibold text-primary-foreground">
                {userProfile.full_name?.charAt(0) || userProfile.email.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {userProfile.full_name || userProfile.email}
              </p>
              <p className="text-xs text-muted-foreground capitalize">
                {userProfile.role.replace('_', ' ')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation sections */}
      {Object.entries(filteredSections).map(([sectionKey, items]) => (
        <div key={sectionKey} className="space-y-2">
          {showSections && !compact && (
            <SectionHeader
              title={sectionKey === 'main' ? 'Main' :
                     sectionKey === 'tools' ? 'Tools' :
                     sectionKey === 'management' ? 'Management' :
                     'Personal'}
            />
          )}
          <div className="space-y-1">
            {items.map((item) => (
              <NavLink key={item.href} item={item} />
            ))}
          </div>
        </div>
      ))}

      {/* Logout section */}
      {!compact && (
        <div className="pt-4 border-t">
          <LogoutButton
            variant="ghost"
            size="sm"
            showIcon={true}
            showText={true}
            confirmLogout={true}
            className="w-full justify-start"
          />
        </div>
      )}
    </nav>
  )
}

// Compact navigation for mobile or sidebar
export function CompactNav({ className }: { className?: string }) {
  return (
    <RoleBasedNav
      className={className || ''}
      showSections={false}
      compact={true}
    />
  )
}

// Full navigation for desktop sidebar
export function FullNav({ className }: { className?: string }) {
  return (
    <RoleBasedNav
      className={className || ''}
      showSections={true}
      compact={false}
    />
  )
}

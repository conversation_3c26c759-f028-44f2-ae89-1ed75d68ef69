/**
 * Test Script for VCP_001 Edge Functions
 * 
 * Tests the calculate-compliance-score and setup-realtime-channels Edge Functions
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, comprehensive testing
 */

const SUPABASE_URL = 'https://esgciouphhajolkojipw.supabase.co'
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Test data
const TEST_ORG_ID = '987fcdeb-51a2-43d7-8f9e-123456789abc'
const TEST_USER_ID = '456e7890-e12b-34d5-a678-901234567def'
const TEST_DOCUMENT_ID = '123e4567-e89b-12d3-a456-426614174000'

async function testCalculateComplianceScore() {
  console.log('🧪 Testing calculate-compliance-score Edge Function...')
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/calculate-compliance-score`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        documentId: TEST_DOCUMENT_ID,
        organizationId: TEST_ORG_ID,
        userId: TEST_USER_ID,
        frameworkId: 'fda_cgmp_2025'
      })
    })

    console.log('📊 Response Status:', response.status)
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()))

    if (response.ok) {
      const data = await response.json()
      console.log('✅ Success! Response:', JSON.stringify(data, null, 2))
      
      // Validate response structure
      if (data.success && data.data) {
        console.log('✅ Response structure is valid')
        console.log('📈 Compliance Score:', data.data.overall_score)
        console.log('📋 Compliance Status:', data.data.compliance_status)
        console.log('⚠️  Critical Violations:', data.data.critical_violations?.length || 0)
      } else {
        console.log('❌ Invalid response structure')
      }
    } else {
      const errorText = await response.text()
      console.log('❌ Error Response:', errorText)
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
  
  console.log('---')
}

async function testSetupRealtimeChannels() {
  console.log('🧪 Testing setup-realtime-channels Edge Function...')
  
  try {
    // Test channel setup
    const setupResponse = await fetch(`${SUPABASE_URL}/functions/v1/setup-realtime-channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        organizationId: TEST_ORG_ID,
        userId: TEST_USER_ID,
        action: 'setup',
        channels: [
          {
            name: 'compliance_alerts',
            type: 'compliance_alerts',
            options: {
              auto_reconnect: true,
              retry_attempts: 3
            }
          },
          {
            name: 'document_processing',
            type: 'document_processing'
          }
        ]
      })
    })

    console.log('📊 Setup Response Status:', setupResponse.status)
    
    if (setupResponse.ok) {
      const setupData = await setupResponse.json()
      console.log('✅ Setup Success! Response:', JSON.stringify(setupData, null, 2))
    } else {
      const errorText = await setupResponse.text()
      console.log('❌ Setup Error Response:', errorText)
    }

    // Test channel status
    const statusResponse = await fetch(
      `${SUPABASE_URL}/functions/v1/setup-realtime-channels?organizationId=${TEST_ORG_ID}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        }
      }
    )

    console.log('📊 Status Response Status:', statusResponse.status)
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json()
      console.log('✅ Status Success! Response:', JSON.stringify(statusData, null, 2))
    } else {
      const errorText = await statusResponse.text()
      console.log('❌ Status Error Response:', errorText)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
  
  console.log('---')
}

async function testEdgeFunctionsList() {
  console.log('🧪 Testing Edge Functions List...')
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      }
    })

    console.log('📊 Functions List Status:', response.status)
    
    if (response.ok) {
      const data = await response.text()
      console.log('✅ Functions available:', data)
    } else {
      console.log('❌ Could not list functions')
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
  
  console.log('---')
}

async function testCORSHeaders() {
  console.log('🧪 Testing CORS Headers...')
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/calculate-compliance-score`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'authorization, content-type',
      }
    })

    console.log('📊 CORS Response Status:', response.status)
    console.log('📊 CORS Headers:', Object.fromEntries(response.headers.entries()))
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
    }
    
    console.log('✅ CORS Configuration:', corsHeaders)
  } catch (error) {
    console.error('❌ CORS test failed:', error.message)
  }
  
  console.log('---')
}

async function runAllTests() {
  console.log('🚀 Starting VCP_001 Edge Functions Tests')
  console.log('=' .repeat(50))
  
  // Check environment
  if (!SUPABASE_ANON_KEY) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable not set')
    return
  }
  
  console.log('🔧 Test Configuration:')
  console.log('   Supabase URL:', SUPABASE_URL)
  console.log('   Test Org ID:', TEST_ORG_ID)
  console.log('   Test User ID:', TEST_USER_ID)
  console.log('   Test Document ID:', TEST_DOCUMENT_ID)
  console.log('')
  
  // Run tests
  await testEdgeFunctionsList()
  await testCORSHeaders()
  await testCalculateComplianceScore()
  await testSetupRealtimeChannels()
  
  console.log('🏁 All tests completed!')
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  runAllTests().catch(console.error)
} else {
  // Browser environment
  window.runVCP001Tests = runAllTests
  console.log('VCP_001 Edge Functions test suite loaded. Run window.runVCP001Tests() to execute.')
}

module.exports = {
  testCalculateComplianceScore,
  testSetupRealtimeChannels,
  testEdgeFunctionsList,
  testCORSHeaders,
  runAllTests
}

'use client'

import { Download, CheckCircle } from 'lucide-react'

import { Button } from '@/components/ui-radix/button'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui-radix/tabs'
import { usePageMetadata } from '@/hooks/use-page-metadata'

import { ComplianceSettingsComponent } from './components/compliance-settings'
import { NotificationSettingsComponent } from './components/notification-settings'
import { ProfileSettings } from './components/profile-settings'
import { SecuritySettingsComponent } from './components/security-settings'
import { TeamSettingsComponent } from './components/team-settings'

import type { TeamMember } from './types'

/**
 * Settings Page - AI Compliance Platform (Migration Plan Phase 3.3 Complete)
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper state management with custom hooks
 *
 * Migrated from: src/pages/Settings.tsx (730 lines)
 * Broken down into 5+ components for maintainability:
 * - ProfileSettings: User profile and company information
 * - NotificationSettings: Email and push notification preferences
 * - SecuritySettings: Password, 2FA, and session management
 * - ComplianceSettings: Compliance frameworks management
 * - TeamSettings: Team member management
 */
export default function SettingsPage() {
  usePageMetadata(
    'Settings & Configuration',
    'Manage your account preferences, compliance frameworks, and system settings',
  )

  const handleExportSettings = () => {
    console.log('Export settings')
  }

  const handleInviteMember = () => {
    console.log('Invite new team member')
  }

  const handleManageMember = (memberId: string) => {
    console.log(`Manage member: ${memberId}`)
  }

  // Minimal team data for team component
  const teamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Current User',
      email: '<EMAIL>',
      role: 'Admin',
      status: 'active',
      lastLogin: new Date().toISOString(),
    },
  ]




  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Settings & Configuration
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your account preferences, compliance frameworks, and system
            settings
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleExportSettings}>
            <Download className="mr-2 h-4 w-4" />
            Export Settings
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        {/* Profile Information */}
        <TabsContent value="profile" className="space-y-6">
          <ProfileSettings />
        </TabsContent>

        {/* Compliance Frameworks */}
        <TabsContent value="compliance" className="space-y-6">
          <ComplianceSettingsComponent />
        </TabsContent>

        {/* Notification Preferences */}
        <TabsContent value="notifications" className="space-y-6">
          <NotificationSettingsComponent />
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <SecuritySettingsComponent />
        </TabsContent>

        {/* Team Management */}
        <TabsContent value="team" className="space-y-6">
          <TeamSettingsComponent
            teamMembers={teamMembers}
            onInviteMember={handleInviteMember}
            onManageMember={handleManageMember}
          />
        </TabsContent>

        {/* Billing (Placeholder) */}
        <TabsContent value="billing" className="space-y-6">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium">Billing Settings</h3>
            <p className="text-muted-foreground">Coming soon...</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

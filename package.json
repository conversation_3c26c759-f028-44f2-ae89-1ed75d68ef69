{"name": "fusion-starter", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build --turbo", "start": "next start", "test": "vitest --run", "format.fix": "prettier --write .", "typecheck": "tsc", "type-check": "tsc --noEmit", "type-check:all": "tsc --noEmit && tsc --project tsconfig.app.json --noEmit", "verify:all": "npm run type-check:all && npm run lint", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@react-three/drei": "10.4.4", "@react-three/fiber": "9.1.4", "@stagewise/toolbar": "latest", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@swc/core": "1.12.7", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "5.81.5", "@types/three": "0.178.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "dotenv": "^17.2.0", "embla-carousel-react": "8.6.0", "framer-motion": "12.23.0", "input-otp": "1.4.2", "lucide-react": "0.525.0", "next": "15.3.5", "next-themes": "0.4.6", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "7.60.0", "react-is": "19.1.0", "react-resizable-panels": "3.0.3", "recharts": "3.0.2", "sonner": "^2.0.6", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "three": "0.178.0", "vaul": "1.1.2", "zod": "3.25.74"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@tailwindcss/typography": "0.5.16", "@types/node": "24.0.10", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react-swc": "3.10.2", "autoprefixer": "10.4.21", "eslint": "9.30.1", "eslint-config-next": "15.3.5", "globals": "16.3.0", "postcss": "8.5.6", "prettier": "3.6.2", "supabase": "^2.30.4", "tailwindcss": "4.1.11", "tw-animate-css": "1.3.5", "typescript": "5.8.3", "vite": "7.0.2", "vitest": "3.2.4"}}
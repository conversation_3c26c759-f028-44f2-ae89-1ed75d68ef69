-- Fix electronic_signatures table - Add missing columns and complete setup
-- Run this to fix the incomplete table structure

-- Add missing columns if they don't exist
DO $$ 
BEGIN
    -- Add signature_metadata column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_metadata') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signature_metadata JSONB DEFAULT '{}';
        RAISE NOTICE 'Added signature_metadata column';
    ELSE
        RAISE NOTICE 'signature_metadata column already exists';
    END IF;
    
    -- Add ip_address column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'ip_address') THEN
        ALTER TABLE electronic_signatures ADD COLUMN ip_address INET;
        RAISE NOTICE 'Added ip_address column';
    ELSE
        RAISE NOTICE 'ip_address column already exists';
    END IF;
    
    -- Add user_agent column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'user_agent') THEN
        ALTER TABLE electronic_signatures ADD COLUMN user_agent TEXT;
        RAISE NOTICE 'Added user_agent column';
    ELSE
        RAISE NOTICE 'user_agent column already exists';
    END IF;
    
    -- Add signer_title column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signer_title') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signer_title VARCHAR(255);
        RAISE NOTICE 'Added signer_title column';
    ELSE
        RAISE NOTICE 'signer_title column already exists';
    END IF;
    
    -- Add signer_department column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signer_department') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signer_department VARCHAR(100);
        RAISE NOTICE 'Added signer_department column';
    ELSE
        RAISE NOTICE 'signer_department column already exists';
    END IF;
    
    -- Add signature_reason column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'signature_reason') THEN
        ALTER TABLE electronic_signatures ADD COLUMN signature_reason TEXT;
        RAISE NOTICE 'Added signature_reason column';
    ELSE
        RAISE NOTICE 'signature_reason column already exists';
    END IF;
    
    -- Add authentication_timestamp column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'authentication_timestamp') THEN
        ALTER TABLE electronic_signatures ADD COLUMN authentication_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW();
        RAISE NOTICE 'Added authentication_timestamp column';
    ELSE
        RAISE NOTICE 'authentication_timestamp column already exists';
    END IF;
    
    -- Add document_hash_at_signing column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'document_hash_at_signing') THEN
        ALTER TABLE electronic_signatures ADD COLUMN document_hash_at_signing VARCHAR(512);
        RAISE NOTICE 'Added document_hash_at_signing column';
    ELSE
        RAISE NOTICE 'document_hash_at_signing column already exists';
    END IF;
    
    -- Add document_version_id column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'document_version_id') THEN
        ALTER TABLE electronic_signatures ADD COLUMN document_version_id UUID REFERENCES document_versions(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added document_version_id column';
    ELSE
        RAISE NOTICE 'document_version_id column already exists';
    END IF;
    
    -- Add audit_trail_id column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'electronic_signatures' AND column_name = 'audit_trail_id') THEN
        ALTER TABLE electronic_signatures ADD COLUMN audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added audit_trail_id column';
    ELSE
        RAISE NOTICE 'audit_trail_id column already exists';
    END IF;
END $$;

-- Add constraints if they don't exist
DO $$ 
BEGIN
    -- Add signature hash length constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'valid_signature_hash') THEN
        ALTER TABLE electronic_signatures ADD CONSTRAINT valid_signature_hash CHECK (LENGTH(signature_hash) >= 64);
        RAISE NOTICE 'Added signature hash constraint';
    ELSE
        RAISE NOTICE 'Signature hash constraint already exists';
    END IF;
    
    -- Add authentication timestamp constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'valid_authentication_timestamp') THEN
        ALTER TABLE electronic_signatures ADD CONSTRAINT valid_authentication_timestamp CHECK (authentication_timestamp <= signed_at);
        RAISE NOTICE 'Added authentication timestamp constraint';
    ELSE
        RAISE NOTICE 'Authentication timestamp constraint already exists';
    END IF;
END $$;

-- Create missing indexes
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_organization') THEN
        CREATE INDEX idx_electronic_signatures_organization ON electronic_signatures(organization_id);
        RAISE NOTICE 'Created organization index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_document') THEN
        CREATE INDEX idx_electronic_signatures_document ON electronic_signatures(document_id);
        RAISE NOTICE 'Created document index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signer') THEN
        CREATE INDEX idx_electronic_signatures_signer ON electronic_signatures(signer_id);
        RAISE NOTICE 'Created signer index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signed_at') THEN
        CREATE INDEX idx_electronic_signatures_signed_at ON electronic_signatures(signed_at);
        RAISE NOTICE 'Created signed_at index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_type') THEN
        CREATE INDEX idx_electronic_signatures_type ON electronic_signatures(signature_type);
        RAISE NOTICE 'Created signature_type index';
    END IF;
    
    -- Now create the metadata index (after ensuring column exists)
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_metadata') THEN
        CREATE INDEX idx_electronic_signatures_metadata ON electronic_signatures USING GIN (signature_metadata);
        RAISE NOTICE 'Created metadata GIN index';
    END IF;
END $$;

-- Create RLS policies if they don't exist
DO $$ 
BEGIN
    -- Policy 1: Users can view signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can view signatures in their organization') THEN
        CREATE POLICY "Users can view signatures in their organization"
            ON electronic_signatures FOR SELECT
            USING (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
            );
        RAISE NOTICE 'Created view policy for electronic_signatures';
    END IF;

    -- Policy 2: Users can create signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can create signatures in their organization') THEN
        CREATE POLICY "Users can create signatures in their organization"
            ON electronic_signatures FOR INSERT
            WITH CHECK (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
                AND signer_id = auth.uid()
            );
        RAISE NOTICE 'Created insert policy for electronic_signatures';
    END IF;

    -- Policy 3: Electronic signatures are immutable
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures are immutable') THEN
        CREATE POLICY "Electronic signatures are immutable"
            ON electronic_signatures FOR UPDATE
            USING (false);
        RAISE NOTICE 'Created update policy for electronic_signatures';
    END IF;

    -- Policy 4: Electronic signatures cannot be deleted
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures cannot be deleted') THEN
        CREATE POLICY "Electronic signatures cannot be deleted"
            ON electronic_signatures FOR DELETE
            USING (false);
        RAISE NOTICE 'Created delete policy for electronic_signatures';
    END IF;
END $$;

-- Recreate the functions with proper error handling
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(version_number::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Create electronic signature
    INSERT INTO electronic_signatures (
        id,
        organization_id,
        document_id,
        signer_id,
        signer_name,
        signature_type,
        signature_meaning,
        signature_reason,
        authentication_method,
        signature_hash,
        document_hash_at_signing,
        signature_metadata
    ) VALUES (
        gen_random_uuid(),
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        p_signature_reason,
        p_authentication_method,
        signature_hash,
        document_hash,
        p_signature_metadata
    ) RETURNING id INTO signature_id;
    
    -- Log audit event if log_audit_event function exists
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'log_audit_event') THEN
        PERFORM log_audit_event(
            p_organization_id,
            p_signer_id,
            'sign'::audit_action_type,
            'Electronic signature created: ' || p_signature_meaning,
            'signature',
            signature_id,
            'Electronic Signature',
            NULL,
            jsonb_build_object(
                'signature_type', p_signature_type,
                'authentication_method', p_authentication_method,
                'document_id', p_document_id
            )
        );
    END IF;
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT, INSERT ON electronic_signatures TO authenticated;
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;
GRANT EXECUTE ON FUNCTION verify_signature_integrity TO authenticated;

-- Final verification
SELECT 
    'ELECTRONIC SIGNATURES FIX' as status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'electronic_signatures')
        THEN '✅ TABLE EXISTS'
        ELSE '❌ TABLE MISSING'
    END as table_status,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'electronic_signatures') as column_count,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'electronic_signatures') as policy_count,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'electronic_signatures') as index_count;

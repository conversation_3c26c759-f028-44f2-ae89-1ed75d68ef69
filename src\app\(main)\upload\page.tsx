'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'

export default function UploadPage() {
  usePageMetadata(
    'Upload',
    'Upload documents and files for compliance analysis',
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Upload</h1>
          <p className="text-muted-foreground mt-1">
            Upload documents and files for compliance analysis
          </p>
        </div>
      </div>

      <div className="text-center py-12">
        <p className="text-muted-foreground">
          Upload page migration in progress...
        </p>
      </div>
    </div>
  )
}

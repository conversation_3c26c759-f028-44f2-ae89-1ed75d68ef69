-- VigiLens Authentication System Test Suite
-- Run this after deploying the clean schema to verify authentication works
-- Tests user registration, profile creation, and RLS policies

-- =====================================================
-- AUTHENTICATION SYSTEM VERIFICATION TESTS
-- =====================================================

-- Test 1: Verify demo organization exists
SELECT 
    'TEST 1: DEMO ORGANIZATION' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as org_count,
    MAX(name) as org_name,
    MAX(display_name) as display_name
FROM organizations 
WHERE name = 'demo-pharma-corp';

-- Test 2: Verify authentication trigger exists
SELECT 
    'TEST 2: AUTH TRIGGER' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as trigger_count,
    MAX(trigger_name) as trigger_name
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger'
AND event_object_table = 'users'
AND event_object_schema = 'auth';

-- Test 3: Verify handle_new_user function exists
SELECT 
    'TEST 3: AUTH FUNCTION' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as function_count,
    MAX(routine_name) as function_name
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user'
AND routine_schema = 'public';

-- Test 4: Verify RLS policies on user_profiles
SELECT 
    'TEST 4: USER PROFILES RLS' as test_name,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as policy_count,
    array_agg(policyname ORDER BY policyname) as policies
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename = 'user_profiles';

-- Test 5: Verify role permissions exist
SELECT 
    'TEST 5: ROLE PERMISSIONS' as test_name,
    CASE 
        WHEN COUNT(*) > 20 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as permission_count,
    COUNT(DISTINCT role) as role_count
FROM role_permissions rp
JOIN organizations o ON rp.organization_id = o.id
WHERE o.name = 'demo-pharma-corp';

-- Test 6: Verify audit trail function exists
SELECT 
    'TEST 6: AUDIT FUNCTION' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as function_count
FROM information_schema.routines 
WHERE routine_name = 'log_audit_event'
AND routine_schema = 'public';

-- Test 7: Check table structure integrity
SELECT 
    'TEST 7: TABLE STRUCTURE' as test_name,
    CASE 
        WHEN COUNT(*) = 8 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as table_count,
    array_agg(table_name ORDER BY table_name) as tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'organizations', 'user_profiles', 'role_permissions', 
    'user_role_assignments', 'regulatory_documents', 
    'document_versions', 'audit_trail', 'electronic_signatures'
);

-- Test 8: Verify enum types
SELECT 
    'TEST 8: ENUM TYPES' as test_name,
    CASE 
        WHEN COUNT(*) = 9 THEN '✅ PASS'
        ELSE '❌ FAIL'
    END as status,
    COUNT(*) as enum_count,
    array_agg(typname ORDER BY typname) as enums
FROM pg_type 
WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND typtype = 'e'
AND typname IN (
    'user_role', 'document_type', 'document_status', 
    'processing_status', 'risk_level', 'audit_action_type',
    'signature_type', 'compliance_framework', 'regulatory_agency'
);

-- =====================================================
-- AUTHENTICATION READINESS SUMMARY
-- =====================================================

SELECT 
    'AUTHENTICATION SYSTEM STATUS' as summary,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM organizations WHERE name = 'demo-pharma-corp'
        ) = 1 
        AND (
            SELECT COUNT(*) FROM information_schema.triggers 
            WHERE trigger_name = 'handle_new_user_trigger'
        ) = 1
        AND (
            SELECT COUNT(*) FROM pg_policies 
            WHERE tablename = 'user_profiles' AND schemaname = 'public'
        ) >= 3
        THEN '🎉 READY FOR USER REGISTRATION TESTING'
        ELSE '⚠️ ISSUES DETECTED - CHECK INDIVIDUAL TESTS'
    END as status,
    NOW() as checked_at;

-- =====================================================
-- NEXT STEPS INSTRUCTIONS
-- =====================================================

SELECT 
    'NEXT STEPS' as instruction_type,
    'If all tests pass, proceed to test user registration at http://localhost:3000/signup' as instruction,
    'Expected: User should be able to register and receive email confirmation' as expected_result,
    'No more "An authentication error occurred" messages' as success_criteria;

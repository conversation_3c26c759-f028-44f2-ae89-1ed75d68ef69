# VigiLens Development Setup Guide

## 🚀 Quick Start Checklist

### Prerequisites
- [ ] Node.js 18+ installed
- [ ] npm or pnpm package manager
- [ ] Git configured
- [ ] Supabase account created

### 1. Repository Setup
```bash
# Clone the repository
git clone <repository-url>
cd vigilen-complianceai/app

# Install dependencies
npm install
# or
pnpm install
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.local.example .env.local

# Edit .env.local with your Supabase credentials
# Get these from: https://supabase.com/dashboard/project/[your-project]/settings/api
```

Required variables in `.env.local`:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 3. Validate Configuration
```bash
# Test environment setup
node scripts/test-env.cjs

# Expected output:
# ✅ Environment validation passed!
```

### 4. Start Development Server
```bash
npm run dev
# or
pnpm dev

# Server will start at http://localhost:3000
```

### 5. Test Supabase Connection
Navigate to: `http://localhost:3000/test-supabase`

Expected result: ✅ Connected to Supabase successfully

## 🔧 Development Workflow

### Code Quality Checks
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Format code
npm run format.fix

# Run all checks
npm run verify:all
```

### Building for Production
```bash
# Build the application
npm run build

# Start production server
npm run start
```

## 📁 Project Structure

```
app/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # React components
│   ├── lib/                 # Utility libraries
│   ├── utils/
│   │   └── supabase/        # Supabase client configuration
│   │       ├── client.ts    # Browser client
│   │       ├── server.ts    # Server client
│   │       └── middleware.ts # Auth middleware
│   └── types/               # TypeScript type definitions
├── docs/                    # Documentation
├── scripts/                 # Utility scripts
├── middleware.ts            # Next.js middleware
├── .env.local.example       # Environment template
└── package.json
```

## 🌍 Environment Variables Reference

### Frontend (.env.local)
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Development Configuration
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

### Backend (.env)
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database
DATABASE_URL=postgresql://postgres.your-project:<EMAIL>:6543/postgres

# API Configuration
BACKEND_CORS_ORIGINS=http://localhost:3000
```

## 🧪 Testing

### Environment Validation
```bash
# Validate environment configuration
node scripts/test-env.cjs
```

### Supabase Connection Test
```bash
# Start dev server
npm run dev

# Test connection
curl http://localhost:3000/test-supabase
# or visit in browser
```

### Type Checking
```bash
# Check TypeScript types
npm run type-check

# Check all TypeScript configurations
npm run type-check:all
```

## 🚨 Common Issues & Solutions

### 1. Environment Variables Not Loading
**Problem**: Variables not available in application

**Solutions**:
- Ensure `.env.local` exists in project root
- Restart development server after changes
- Check variable names match exactly (case-sensitive)
- Verify no extra spaces around `=`

### 2. Supabase Connection Failed
**Problem**: Cannot connect to Supabase

**Solutions**:
- Verify URL format: `https://project-id.supabase.co`
- Check anon key is correct (208 characters, JWT format)
- Ensure project is not paused in Supabase dashboard
- Run validation script: `node scripts/test-env.cjs`

### 3. TypeScript Errors
**Problem**: Type errors in Supabase clients

**Solutions**:
- Use bracket notation: `process.env['VARIABLE_NAME']`
- Ensure all imports use correct paths
- Run type check: `npm run type-check`

### 4. Middleware Issues
**Problem**: Authentication not working

**Solutions**:
- Ensure `middleware.ts` exists in project root
- Check middleware matcher configuration
- Verify `updateSession` import path
- Clear browser cookies and try again

### 5. Build Failures
**Problem**: Production build fails

**Solutions**:
- Run type check: `npm run type-check:all`
- Fix all linting errors: `npm run lint`
- Ensure all environment variables are set
- Check for unused imports or variables

## 📞 Getting Help

### Documentation
- [Supabase Configuration Guide](./supabase-configuration.md)
- [Technical Specification](./technical_specification.md)
- [Official Supabase Docs](https://supabase.com/docs)

### Debugging Steps
1. Run environment validation: `node scripts/test-env.cjs`
2. Check browser console for errors
3. Verify Supabase dashboard shows active project
4. Test with minimal example from documentation
5. Check network tab for failed requests

### Support Channels
- Check existing documentation first
- Review error messages carefully
- Test with official Supabase examples
- Consult team members for project-specific issues

## 🔄 Updates & Maintenance

### Keeping Dependencies Updated
```bash
# Check for updates
npm outdated

# Update dependencies
npm update

# Update Supabase packages specifically
npm install @supabase/supabase-js@latest @supabase/ssr@latest
```

### Environment Changes
- Always update `.env.local.example` when adding new variables
- Document new variables in this guide
- Test changes with validation script
- Notify team of required environment updates

### Configuration Changes
- Follow official Supabase migration guides
- Test changes in development first
- Update documentation accordingly
- Maintain backward compatibility when possible

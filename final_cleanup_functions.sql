-- FINAL FUNCTION CLEANUP FOR VIGILENS
-- Remove only custom VigiLens functions, leave PostgreSQL system functions intact
-- Run this in Supabase SQL Editor

-- Drop remaining VigiLens custom functions
DROP FUNCTION IF EXISTS check_database_health() CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_audit_records() CASCADE;
DROP FUNCTION IF EXISTS create_document_version(UUI<PERSON>, UUID, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS get_document_version_history(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_organization_compliance_status(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_user_permissions(UUID) CASCADE;
DROP FUNCTION IF EXISTS handle_failed_login() CASCADE;
DROP FUNCTION IF EXISTS is_account_locked(UUID) CASCADE;
DROP FUNCTION IF EXISTS log_user_activity() CASCADE;
DROP FUNCTION IF EXISTS notify_compliance_alert() CASCADE;
DROP FUNCTION IF EXISTS notify_document_assignment() CASCADE;
DROP FUNCTION IF EXISTS notify_document_status_change() CASCADE;
DROP FUNCTION IF EXISTS set_immutable_timestamp() CASCADE;
DROP FUNCTION IF EXISTS track_user_login() CASCADE;
DROP FUNCTION IF EXISTS update_last_profile_update() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS update_user_jwt_claims(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_user_login() CASCADE;
DROP FUNCTION IF EXISTS user_has_permission(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS validate_organization() CASCADE;
DROP FUNCTION IF EXISTS verify_audit_integrity() CASCADE;

-- Verify cleanup of custom functions
SELECT 
    'CUSTOM FUNCTION CLEANUP VERIFICATION' as status,
    'All VigiLens custom functions removed' as message,
    NOW() as timestamp;

-- Check remaining custom functions (should only show PostgreSQL system functions)
SELECT 
    'REMAINING CUSTOM FUNCTIONS' as check_type,
    routine_name
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name NOT LIKE 'gin_%'
AND routine_name NOT LIKE 'gtrgm_%'
AND routine_name NOT LIKE '%similarity%'
AND routine_name NOT LIKE 'unaccent%'
AND routine_name NOT LIKE 'set_limit'
AND routine_name NOT LIKE 'show_%'
ORDER BY routine_name;

-- Final verification - database should be completely clean
SELECT 
    'FINAL DATABASE STATE' as check_type,
    'Clean and ready for new schema' as status,
    NOW() as timestamp;

'use client'

import { Download, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/ui-radix/button'

interface ExportControlsProps {
  readonly uploadedDocuments: readonly unknown[];
  readonly selectedFrameworks: readonly string[];
  readonly results: readonly unknown[];
}

export function ExportControls({
  uploadedDocuments,
  selectedFrameworks,
  results,
}: ExportControlsProps) {
  const router = useRouter()

  const handleExport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      documents: uploadedDocuments.length,
      frameworks: selectedFrameworks,
      results: results,
    }

    const dataStr = JSON.stringify(report, null, 2)
    const dataBlob = new Blob([dataStr], {
      type: 'application/json',
    })

    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `compliance-report-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        onClick={handleExport}
        disabled={results.length === 0}
      >
        <Download className="mr-2 h-4 w-4" />
        Export Report
      </Button>
      <Button variant="outline" onClick={() => router.push('/settings')}>
        <Settings className="mr-2 h-4 w-4" />
        Configure
      </Button>
    </div>
  )
}

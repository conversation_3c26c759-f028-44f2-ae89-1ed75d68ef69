#!/usr/bin/env python3
"""
FDA Knowledge Base Status Checker

This script checks the status of the FDA knowledge base vector stores
with proper error handling and None value checks.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from services.ai.qdrant_store import QdrantVectorStore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_vector_store_status(path: str, collection_name: str) -> Dict[str, Any]:
    """
    Check the status of a specific vector store with proper error handling.
    
    Args:
        path: Path to the vector store
        collection_name: Name of the collection
        
    Returns:
        Dictionary with status information
    """
    status = {
        "path": path,
        "collection_name": collection_name,
        "exists": False,
        "accessible": False,
        "vectors_count": 0,
        "size_mb": 0.0,
        "error": None,
        "search_test_passed": False
    }
    
    try:
        # Check if path exists
        path_obj = Path(path)
        if not path_obj.exists():
            status["error"] = "Path does not exist"
            return status
        
        status["exists"] = True
        
        # Calculate size
        total_size = sum(f.stat().st_size for f in path_obj.rglob('*') if f.is_file())
        status["size_mb"] = round(total_size / (1024 * 1024), 2)
        
        # Try to connect to vector store
        store = QdrantVectorStore(
            path=path,
            collection_name=collection_name
        )
        
        await store.initialize()
        status["accessible"] = True
        
        # Get collection info with proper None handling
        collection_info = await store.get_collection_info()
        
        # Use the fixed method that handles None values
        vectors_count = collection_info.get('vectors_count', 0)
        status["vectors_count"] = vectors_count if vectors_count is not None else 0
        
        # Test search if we have vectors
        if status["vectors_count"] > 0:
            try:
                search_results = await store.search_documents(
                    query='FDA pharmaceutical manufacturing',
                    limit=2
                )
                status["search_test_passed"] = True
                status["search_results_count"] = len(search_results.results)
                
                # Add sample results
                status["sample_results"] = []
                for i, result in enumerate(search_results.results[:2]):
                    status["sample_results"].append({
                        "score": round(result.score, 3),
                        "preview": result.content[:80] + "..." if len(result.content) > 80 else result.content
                    })
                    
            except Exception as e:
                status["search_error"] = str(e)
        
    except Exception as e:
        status["error"] = str(e)
        logger.error(f"Error checking vector store {path}: {e}")
    
    return status


async def check_fda_documents_status() -> Dict[str, Any]:
    """Check the status of FDA documents."""
    fda_status = {
        "documents_found": False,
        "total_files": 0,
        "total_size_mb": 0.0,
        "files": []
    }
    
    try:
        fda_docs_path = Path('./fda_docs')
        if not fda_docs_path.exists():
            fda_status["error"] = "FDA documents directory not found"
            return fda_status
        
        pdf_files = list(fda_docs_path.glob('*.pdf'))
        fda_status["documents_found"] = len(pdf_files) > 0
        fda_status["total_files"] = len(pdf_files)
        
        total_size = 0
        for pdf_file in pdf_files:
            size_bytes = pdf_file.stat().st_size
            size_mb = round(size_bytes / (1024 * 1024), 2)
            total_size += size_bytes
            
            fda_status["files"].append({
                "name": pdf_file.name,
                "size_mb": size_mb
            })
        
        fda_status["total_size_mb"] = round(total_size / (1024 * 1024), 1)
        
        # Sort files by size for better display
        fda_status["files"].sort(key=lambda x: x["size_mb"])
        
    except Exception as e:
        fda_status["error"] = str(e)
    
    return fda_status


async def main():
    """Main status check function."""
    print('📊 FDA KNOWLEDGE BASE STATUS CHECK')
    print('=' * 50)
    
    # Check vector stores
    vector_stores = [
        ('./data/qdrant_test', 'fda_regulatory_documents'),
        ('./data/qdrant_fda', 'fda_regulatory_documents'),
        ('./data/qdrant_fda_full', 'fda_regulatory_documents')
    ]
    
    print('\n🗄️ VECTOR STORES STATUS:')
    print('-' * 30)
    
    for path, collection_name in vector_stores:
        status = await check_vector_store_status(path, collection_name)
        
        print(f'\n📍 {path}')
        if status["exists"]:
            print(f'   ✅ Exists: {status["size_mb"]} MB')
            
            if status["accessible"]:
                print(f'   ✅ Accessible: {status["vectors_count"]} vectors')
                
                if status["vectors_count"] > 0:
                    if status["search_test_passed"]:
                        print(f'   ✅ Search test: {status.get("search_results_count", 0)} results')
                        
                        # Show sample results
                        for i, result in enumerate(status.get("sample_results", [])):
                            print(f'      Result {i+1}: Score {result["score"]} - {result["preview"]}')
                    else:
                        search_error = status.get("search_error", "Unknown error")
                        print(f'   ❌ Search test failed: {search_error}')
                else:
                    print(f'   ⚠️ Empty collection (no vectors)')
            else:
                print(f'   ❌ Not accessible: {status.get("error", "Unknown error")}')
        else:
            print(f'   ❌ Does not exist')
    
    # Check FDA documents
    print('\n📄 FDA DOCUMENTS STATUS:')
    print('-' * 30)
    
    fda_status = await check_fda_documents_status()
    
    if fda_status["documents_found"]:
        print(f'✅ Found {fda_status["total_files"]} CFR documents ({fda_status["total_size_mb"]} MB total)')
        
        print(f'\n📋 Document List (sorted by size):')
        for file_info in fda_status["files"]:
            print(f'   📄 {file_info["name"]} ({file_info["size_mb"]} MB)')
    else:
        error = fda_status.get("error", "No PDF files found")
        print(f'❌ {error}')
    
    # Provide recommendations
    print('\n🎯 RECOMMENDATIONS:')
    print('-' * 30)
    
    # Check if any vector store has data
    has_data = False
    for path, collection_name in vector_stores:
        status = await check_vector_store_status(path, collection_name)
        if status["vectors_count"] > 0:
            has_data = True
            break
    
    if not has_data:
        print('📝 No vector data found. Next steps:')
        print('   1. Run: python populate_sample.py (process 3 smallest documents)')
        print('   2. Run: python populate_full_knowledge_base.py (process all documents)')
    else:
        print('✅ Vector data found. Knowledge base is operational!')
        print('   • You can now use the search functionality')
        print('   • Consider processing more documents if needed')
    
    if fda_status["documents_found"]:
        smallest_files = fda_status["files"][:3]
        print(f'\n💡 For testing, start with smallest files:')
        for file_info in smallest_files:
            print(f'   📄 {file_info["name"]} ({file_info["size_mb"]} MB)')


if __name__ == "__main__":
    asyncio.run(main())

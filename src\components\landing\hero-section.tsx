import { <PERSON>R<PERSON>, ChevronRight, Play, Shield, Sparkles } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'

export function HeroSection() {
  const handleScheduleDemo = () => {
    window.open(
      'mailto:<EMAIL>?subject=Schedule Demo Request&body=I would like to schedule a demo of the AI Compliance Platform.',
      '_blank',
    )
  }

  const handleGetStarted = () => {
    // TODO: Replace with Next.js navigation to login
    console.log('Navigate to login - placeholder for Next.js router')
  }

  return (
    <>
      {/* Floating Particles Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-20 left-40 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="relative border-b border-border/20 bg-card/80 dark:bg-card/60 backdrop-blur-xl supports-[backdrop-filter]:bg-card/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-20 items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-600 to-purple-800 shadow-lg">
                  <Shield className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                  AI Compliance
                </h1>
                <p className="text-xs text-purple-600/70 font-medium">
                  Pharmaceutical Intelligence
                </p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Features
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
              <a
                href="#benefits"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Benefits
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
              <a
                href="#testimonials"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Testimonials
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                className="hidden sm:inline-flex font-medium"
                onClick={handleGetStarted}
              >
                Sign In
              </Button>
              <Button
                onClick={handleGetStarted}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-purple-500/25 transition-all duration-300 font-medium"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full px-6 py-3 mb-8 border border-purple-200/50">
              <Sparkles className="w-5 h-5 text-purple-600" />
              <span className="text-purple-700 font-semibold text-sm">
                AI-Powered Regulatory Intelligence Platform
              </span>
              <Badge
                variant="secondary"
                className="ml-2 bg-purple-600 text-white"
              >
                NEW
              </Badge>
            </div>

            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 bg-clip-text text-transparent">
                Regulatory
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 bg-clip-text text-transparent">
                Autopilot
              </span>
              <br />
              <span className="text-slate-700 text-4xl md:text-5xl lg:text-6xl">
                for Pharma
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transform your pharmaceutical compliance with AI-powered
              regulatory intelligence. Automate document analysis, stay ahead of
              regulatory changes, and ensure
              <span className="font-semibold text-purple-600">
                {' '}
                100% compliance{' '}
              </span>
              with industry standards.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button
                size="lg"
                className="text-lg px-10 py-6 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-2xl hover:shadow-purple-500/30 transition-all duration-300 transform hover:scale-105"
                onClick={handleGetStarted}
              >
                Start Free Trial
                <ChevronRight className="ml-2 h-6 w-6" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-10 py-6 border-2 border-purple-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300 group"
                onClick={handleScheduleDemo}
              >
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Schedule Demo
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="p-4 rounded-2xl bg-card/60 dark:bg-card/70 backdrop-blur-sm border border-border/20">
                <div className="text-2xl font-bold text-purple-600">500+</div>
                <div className="text-sm text-slate-600 font-medium">
                  Companies Trust Us
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-card/60 dark:bg-card/70 backdrop-blur-sm border border-border/20">
                <div className="text-2xl font-bold text-purple-600">99.7%</div>
                <div className="text-sm text-slate-600 font-medium">
                  Accuracy Rate
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-card/60 dark:bg-card/70 backdrop-blur-sm border border-border/20">
                <div className="text-2xl font-bold text-purple-600">85%</div>
                <div className="text-sm text-slate-600 font-medium">
                  Time Reduction
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-card/60 dark:bg-card/70 backdrop-blur-sm border border-border/20">
                <div className="text-2xl font-bold text-purple-600">24/7</div>
                <div className="text-sm text-slate-600 font-medium">
                  Monitoring
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

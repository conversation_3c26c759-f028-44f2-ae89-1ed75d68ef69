#!/usr/bin/env python3
"""Setup database schema in Supabase for VigiLens."""

import asyncio
import logging
import os
from pathlib import Path

from config.supabase import supabase

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def setup_database():
    """Set up the database schema in Supabase."""
    try:
        # Read the schema SQL file
        schema_file = Path(__file__).parent / "sql" / "schema.sql"
        
        if not schema_file.exists():
            logger.error(f"Schema file not found: {schema_file}")
            return False
        
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        logger.info("Setting up database schema in Supabase...")
        
        # Split the SQL into individual statements
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        success_count = 0
        error_count = 0
        
        for i, statement in enumerate(statements, 1):
            if not statement:
                continue
                
            try:
                logger.info(f"Executing statement {i}/{len(statements)}")
                
                # Use Supabase RPC to execute raw SQL
                # Note: This requires a custom function in Supabase or direct database access
                # For now, we'll use a simple approach
                
                # Try to execute using Supabase client
                # This is a simplified approach - in production you'd use proper migrations
                result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                
                success_count += 1
                logger.info(f"✅ Statement {i} executed successfully")
                
            except Exception as e:
                error_count += 1
                logger.error(f"❌ Error executing statement {i}: {e}")
                # Continue with other statements
                continue
        
        logger.info(f"Database setup completed: {success_count} successful, {error_count} errors")
        
        if error_count == 0:
            logger.info("🎉 Database schema setup completed successfully!")
            return True
        else:
            logger.warning(f"⚠️ Database setup completed with {error_count} errors")
            return False
            
    except Exception as e:
        logger.error(f"Failed to setup database: {e}")
        return False


async def test_database_connection():
    """Test the database connection and basic operations."""
    try:
        logger.info("Testing database connection...")
        
        # Test basic query
        result = supabase.table('organizations').select('*').limit(1).execute()
        
        if result.data is not None:
            logger.info("✅ Database connection test successful")
            logger.info(f"Found {len(result.data)} organizations")
            return True
        else:
            logger.error("❌ Database connection test failed - no data returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False


async def create_sample_data():
    """Create sample data for testing."""
    try:
        logger.info("Creating sample data...")
        
        # Check if sample organization already exists
        result = supabase.table('organizations').select('*').eq('name', 'Demo Pharmaceutical Corp').execute()
        
        if result.data and len(result.data) > 0:
            logger.info("Sample data already exists")
            return True
        
        # Create sample organization
        org_data = {
            'name': 'Demo Pharmaceutical Corp',
            'description': 'Sample pharmaceutical company for testing',
            'industry': 'Pharmaceuticals',
            'country': 'United States'
        }
        
        org_result = supabase.table('organizations').insert(org_data).execute()
        
        if org_result.data:
            org_id = org_result.data[0]['id']
            logger.info(f"✅ Created sample organization: {org_id}")
            
            # Create sample user
            user_data = {
                'organization_id': org_id,
                'email': '<EMAIL>',
                'full_name': 'Demo Admin',
                'role': 'Administrator',
                'department': 'Compliance'
            }
            
            user_result = supabase.table('user_profiles').insert(user_data).execute()
            
            if user_result.data:
                logger.info(f"✅ Created sample user: {user_result.data[0]['id']}")
                return True
            else:
                logger.error("Failed to create sample user")
                return False
        else:
            logger.error("Failed to create sample organization")
            return False
            
    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")
        return False


async def main():
    """Main setup function."""
    logger.info("🚀 Starting VigiLens database setup...")
    
    # Test connection first
    if not await test_database_connection():
        logger.error("Cannot proceed without database connection")
        return
    
    # Setup schema
    schema_success = await setup_database()
    
    # Create sample data
    if schema_success:
        await create_sample_data()
    
    logger.info("✅ Database setup process completed!")


if __name__ == "__main__":
    asyncio.run(main())

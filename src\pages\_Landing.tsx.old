import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Shield,
  ChevronRight,
  CheckCircle,
  BarChart3,
  Bell,
  FileCheck,
  Users,
  Globe,
  ArrowRight,
  Star,
  Zap,
  Lock,
  TrendingUp,
  Award,
  Clock,
  MessageSquare,
  Mail,
  Phone,
  MapPin,
  Play,
  Sparkles,
  Target,
  Database,
  Brain,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";

export default function Landing() {
  const navigate = useNavigate();
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description:
        "Advanced machine learning algorithms analyze regulatory documents with 99.7% accuracy, identifying compliance gaps instantly.",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: Bell,
      title: "Real-time Monitoring",
      description:
        "Continuous surveillance of FDA, EMA, ICH, and CDSCO updates ensures you never miss critical regulatory changes.",
      gradient: "from-orange-500 to-red-500",
    },
    {
      icon: Target,
      title: "Risk Assessment",
      description:
        "Proactive risk identification and mitigation strategies keep your pharmaceutical operations compliant and secure.",
      gradient: "from-green-500 to-emerald-500",
    },
    {
      icon: Database,
      title: "Document Management",
      description:
        "Centralized repository with version control, automated workflows, and seamless collaboration across teams.",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description:
        "Comprehensive insights and reporting tools provide clear visibility into your compliance posture and trends.",
      gradient: "from-indigo-500 to-blue-500",
    },
    {
      icon: MessageSquare,
      title: "Expert AI Assistant",
      description:
        "Specialized pharmaceutical AI assistant provides instant answers to complex regulatory questions and guidance.",
      gradient: "from-teal-500 to-cyan-500",
    },
  ];

  const benefits = [
    {
      icon: Clock,
      stat: "85%",
      title: "Faster Reviews",
      description: "Reduce compliance documentation time from weeks to days",
      color: "text-blue-600",
      bg: "bg-blue-50",
    },
    {
      icon: TrendingUp,
      stat: "99.7%",
      title: "Accuracy Rate",
      description:
        "Industry-leading precision in regulatory compliance detection",
      color: "text-green-600",
      bg: "bg-green-50",
    },
    {
      icon: Award,
      stat: "500+",
      title: "Global Companies",
      description: "Trusted by pharmaceutical leaders worldwide",
      color: "text-purple-600",
      bg: "bg-purple-50",
    },
    {
      icon: Zap,
      stat: "24/7",
      title: "Active Monitoring",
      description: "Continuous regulatory landscape surveillance",
      color: "text-orange-600",
      bg: "bg-orange-50",
    },
  ];

  const testimonials = [
    {
      quote:
        "AI Compliance has revolutionized our regulatory workflow. The AI-powered analysis caught compliance issues we would have missed, potentially saving us millions in regulatory delays.",
      author: "Dr. Sarah Chen",
      role: "Head of Quality Assurance",
      company: "PharmaCore International",
      avatar: "SC",
      rating: 5,
    },
    {
      quote:
        "The real-time regulatory updates are a game-changer. We're always ahead of new requirements, and the AI assistant has become our go-to resource for complex queries.",
      author: "Michael Rodriguez",
      role: "VP of Regulatory Affairs",
      company: "BioTech Solutions",
      avatar: "MR",
      rating: 5,
    },
    {
      quote:
        "Implementation was seamless, and the ROI was immediate. Our compliance review time dropped by 80%, and our audit success rate is now 100%.",
      author: "Dr. Emily Watson",
      role: "Chief Compliance Officer",
      company: "Global Pharma Dynamics",
      avatar: "EW",
      rating: 5,
    },
  ];

  const handleScheduleDemo = () => {
    // In a real app, this would open a calendar booking widget or form
    window.open(
      "mailto:<EMAIL>?subject=Schedule Demo Request&body=I would like to schedule a demo of the AI Compliance Platform.",
      "_blank",
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      {/* Floating Particles Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-20 left-40 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="relative border-b border-white/20 bg-white/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-20 items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-600 to-purple-800 shadow-lg">
                  <Shield className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                  AI Compliance
                </h1>
                <p className="text-xs text-purple-600/70 font-medium">
                  Pharmaceutical Intelligence
                </p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Features
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
              <a
                href="#benefits"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Benefits
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
              <a
                href="#testimonials"
                className="text-slate-600 hover:text-purple-600 transition-all duration-300 font-medium relative group"
              >
                Testimonials
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></div>
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                asChild
                className="hidden sm:inline-flex font-medium"
              >
                <Link to="/login">Sign In</Link>
              </Button>
              <Button
                onClick={() => navigate("/login")}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-purple-500/25 transition-all duration-300 font-medium"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full px-6 py-3 mb-8 border border-purple-200/50">
              <Sparkles className="w-5 h-5 text-purple-600" />
              <span className="text-purple-700 font-semibold text-sm">
                AI-Powered Regulatory Intelligence Platform
              </span>
              <Badge
                variant="secondary"
                className="ml-2 bg-purple-600 text-white"
              >
                NEW
              </Badge>
            </div>

            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 bg-clip-text text-transparent">
                Regulatory
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 bg-clip-text text-transparent">
                Autopilot
              </span>
              <br />
              <span className="text-slate-700 text-4xl md:text-5xl lg:text-6xl">
                for Pharma
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transform your pharmaceutical compliance with AI-powered
              regulatory intelligence. Automate document analysis, stay ahead of
              regulatory changes, and ensure
              <span className="font-semibold text-purple-600">
                {" "}
                100% compliance{" "}
              </span>
              with industry standards.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button
                size="lg"
                className="text-lg px-10 py-6 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-2xl hover:shadow-purple-500/30 transition-all duration-300 transform hover:scale-105"
                onClick={() => navigate("/login")}
              >
                Start Free Trial
                <ChevronRight className="ml-2 h-6 w-6" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-10 py-6 border-2 border-purple-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300 group"
                onClick={handleScheduleDemo}
              >
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Schedule Demo
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-white/20">
                <div className="text-2xl font-bold text-purple-600">500+</div>
                <div className="text-sm text-slate-600 font-medium">
                  Companies Trust Us
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-white/20">
                <div className="text-2xl font-bold text-purple-600">99.7%</div>
                <div className="text-sm text-slate-600 font-medium">
                  Accuracy Rate
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-white/20">
                <div className="text-2xl font-bold text-purple-600">85%</div>
                <div className="text-sm text-slate-600 font-medium">
                  Time Reduction
                </div>
              </div>
              <div className="p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-white/20">
                <div className="text-2xl font-bold text-purple-600">24/7</div>
                <div className="text-sm text-slate-600 font-medium">
                  Monitoring
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <Badge
              variant="outline"
              className="mb-6 border-purple-200 text-purple-700"
            >
              <Zap className="w-4 h-4 mr-2" />
              Powerful Features
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Everything You Need for
              <br />
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Regulatory Excellence
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Comprehensive AI-powered tools designed specifically for
              pharmaceutical manufacturing compliance requirements and
              regulatory management.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group border-0 bg-white/80 backdrop-blur-sm hover:bg-white transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 overflow-hidden"
              >
                <CardHeader className="relative">
                  <div
                    className={`flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br ${feature.gradient} shadow-lg mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-xl font-bold text-slate-900 group-hover:text-purple-600 transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
                <div
                  className={`absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r ${feature.gradient} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}
                ></div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section
        id="benefits"
        className="py-24 bg-gradient-to-br from-slate-50 to-purple-50 relative"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <Badge
              variant="outline"
              className="mb-6 border-purple-200 text-purple-700"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Proven Results
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Transforming Pharmaceutical
              <br />
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Compliance Operations
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Join hundreds of pharmaceutical companies that have revolutionized
              their compliance processes with measurable results and
              industry-leading performance.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card
                key={index}
                className="text-center border-0 bg-white/80 backdrop-blur-sm hover:bg-white transition-all duration-300 hover:shadow-xl group"
              >
                <CardHeader className="pb-4">
                  <div
                    className={`flex h-16 w-16 items-center justify-center rounded-2xl ${benefit.bg} mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <benefit.icon className={`h-8 w-8 ${benefit.color}`} />
                  </div>
                  <div className={`text-4xl font-bold ${benefit.color} mb-2`}>
                    {benefit.stat}
                  </div>
                  <CardTitle className="text-lg font-bold text-slate-900">
                    {benefit.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <Badge
              variant="outline"
              className="mb-6 border-purple-200 text-purple-700"
            >
              <Users className="w-4 h-4 mr-2" />
              Customer Success
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Trusted by Industry
              <br />
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Leaders Worldwide
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              See what pharmaceutical professionals are saying about the AI
              Compliance Platform and how it's transformed their regulatory
              operations.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="border-0 bg-white/80 backdrop-blur-sm hover:bg-white transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group"
              >
                <CardHeader>
                  <div className="flex items-center space-x-1 mb-6">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <p className="text-slate-700 italic leading-relaxed font-medium">
                    "{testimonial.quote}"
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-600 to-purple-700 text-white font-bold text-sm">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-bold text-slate-900">
                        {testimonial.author}
                      </div>
                      <div className="text-sm text-purple-600 font-medium">
                        {testimonial.role}
                      </div>
                      <div className="text-xs text-slate-500">
                        {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-700 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-8 leading-tight">
              Ready to Transform Your
              <br />
              <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Compliance Process?
              </span>
            </h2>
            <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
              Join the pharmaceutical revolution. Start your free trial today
              and experience the future of regulatory compliance management with
              AI-powered intelligence.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Button
                size="lg"
                variant="secondary"
                className="text-lg px-10 py-6 bg-white text-purple-700 hover:bg-white/90 shadow-2xl hover:shadow-white/20 transition-all duration-300 transform hover:scale-105 font-bold"
                onClick={() => navigate("/login")}
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-6 w-6" />
              </Button>
              <div
                className="inline-flex items-center justify-center text-lg px-10 py-6 bg-transparent border-2 border-white text-white hover:bg-white/10 backdrop-blur-sm font-medium transition-all duration-200 rounded-lg cursor-pointer"
                onClick={handleScheduleDemo}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === "Enter" && handleScheduleDemo()}
              >
                <Play className="mr-2 h-5 w-5" style={{ color: "white" }} />
                <span style={{ color: "white" }}>Schedule Demo</span>
              </div>
            </div>

            <div className="inline-flex items-center space-x-6 text-white/80 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>14-day free trial</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>No credit card required</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>Full feature access</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-16 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-600 to-purple-700">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">AI Compliance</h3>
                  <p className="text-xs text-purple-400">
                    Pharmaceutical Intelligence
                  </p>
                </div>
              </div>
              <p className="text-slate-400 text-sm mb-6 leading-relaxed">
                Empowering pharmaceutical manufacturers with AI-driven
                regulatory compliance solutions for a safer, more efficient
                future.
              </p>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-white">Product</h4>
              <ul className="space-y-3 text-sm text-slate-400">
                <li>
                  <a
                    href="#features"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Features
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    API Documentation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Integrations
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Security
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-white">Company</h4>
              <ul className="space-y-3 text-sm text-slate-400">
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    About Us
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Careers
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Blog
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-purple-400 transition-colors"
                  >
                    Press Kit
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-white">Contact</h4>
              <ul className="space-y-3 text-sm text-slate-400">
                <li className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-purple-400" />
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-purple-400" />
                  <span>+****************</span>
                </li>
                <li className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-purple-400" />
                  <span>San Francisco, CA</span>
                </li>
              </ul>
            </div>
          </div>

          <Separator className="mb-8 bg-slate-800" />

          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-slate-400">
            <div className="flex space-x-6 mb-4 md:mb-0">
              <a href="#" className="hover:text-purple-400 transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="hover:text-purple-400 transition-colors">
                Terms of Service
              </a>
              <a href="#" className="hover:text-purple-400 transition-colors">
                Cookie Policy
              </a>
            </div>
            <div className="text-center md:text-right">
              <p>© 2024 AI Compliance Platform. All rights reserved.</p>
              <p className="text-xs text-slate-500 mt-1">
                Trusted by 500+ pharmaceutical organizations worldwide
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

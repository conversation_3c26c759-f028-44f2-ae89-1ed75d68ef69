/**
 * Protected Route Component for VigiLens
 * 
 * Provides client-side route protection with role-based access control
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, accessibility
 * Follows DEVELOPMENT_RULES_2.md: Production-first, proper error handling
 */

'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Loader2, Shield, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/auth-context'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string | string[]
  requiredPermissions?: string[]
  fallbackPath?: string
  showFallback?: boolean
  loadingComponent?: React.ReactNode
  unauthorizedComponent?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermissions = [],
  fallbackPath = '/dashboard',
  showFallback = true,
  loadingComponent,
  unauthorizedComponent
}: ProtectedRouteProps) {
  const { user, userProfile, hasRole, hasPermission, loading } = useAuth()
  const router = useRouter()

  // Loading state
  if (loading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-sm text-muted-foreground">
            Verifying authentication...
          </p>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!user || !userProfile) {
    if (showFallback) {
      return (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                <Shield className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>
                You must be logged in to access this page.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={() => router.push('/login')}
                className="w-full"
              >
                Sign In
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.push('/')}
                className="w-full"
              >
                Go Home
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    }

    // Redirect to login
    router.push('/login')
    return null
  }

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    if (unauthorizedComponent) {
      return <>{unauthorizedComponent}</>
    }

    if (showFallback) {
      return (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                You don't have the required role to access this page.
                <br />
                <br />
                <strong>Your role:</strong> {userProfile.role}
                <br />
                <strong>Required role:</strong> {Array.isArray(requiredRole) ? requiredRole.join(' or ') : requiredRole}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={() => router.push(fallbackPath)}
                className="w-full"
              >
                Go to Dashboard
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    }

    // Redirect to fallback
    router.push(fallbackPath)
    return null
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission as any)
    )

    if (!hasAllPermissions) {
      if (unauthorizedComponent) {
        return <>{unauthorizedComponent}</>
      }

      if (showFallback) {
        return (
          <div className="flex items-center justify-center min-h-screen p-4">
            <Card className="w-full max-w-md">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <CardTitle>Insufficient Permissions</CardTitle>
                <CardDescription>
                  You don't have the required permissions to access this page.
                  <br />
                  <br />
                  <strong>Your role:</strong> {userProfile.role}
                  <br />
                  <strong>Required permissions:</strong> {requiredPermissions.join(', ')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  onClick={() => router.push(fallbackPath)}
                  className="w-full"
                >
                  Go to Dashboard
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => router.back()}
                  className="w-full"
                >
                  Go Back
                </Button>
              </CardContent>
            </Card>
          </div>
        )
      }

      // Redirect to fallback
      router.push(fallbackPath)
      return null
    }
  }

  // User is authenticated and authorized
  return <>{children}</>
}

// Higher-order component for protecting pages
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}

// Specific protection components for common use cases
export function AdminOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole={['super_admin', 'admin']}>
      {children}
    </ProtectedRoute>
  )
}

export function SuperAdminOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="super_admin">
      {children}
    </ProtectedRoute>
  )
}

export function ComplianceOfficerRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute 
      requiredRole={['super_admin', 'admin', 'compliance_officer']}
      requiredPermissions={['canViewCompliance']}
    >
      {children}
    </ProtectedRoute>
  )
}

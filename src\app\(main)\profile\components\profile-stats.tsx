'use client'

import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Activity, CheckCircle, Clock, FileText } from 'lucide-react'

interface StatCardProps {
  readonly label: string
  readonly value: string
  readonly change: string
  readonly icon: React.ComponentType<{ className?: string }>
  readonly color: string
}

function StatCard({ label, value, change, icon: Icon, color }: StatCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{label}</p>
            <p className="text-2xl font-bold text-foreground">{value}</p>
          </div>
          <div className="flex flex-col items-end space-y-1">
            <Icon className={cn('h-5 w-5', color)} />
            <span className="text-xs text-success">{change}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface ProfileStatsProps {
  readonly className?: string
}

export function ProfileStats({ className }: ProfileStatsProps) {
  const activityStats = [
    {
      label: 'Documents Analyzed',
      value: '127',
      change: '+23%',
      icon: FileText,
      color: 'text-blue-500',
    },
    {
      label: 'Compliance Checks',
      value: '89',
      change: '+15%',
      icon: CheckCircle,
      color: 'text-green-500',
    },
    {
      label: 'AI Interactions',
      value: '342',
      change: '+45%',
      icon: Activity,
      color: 'text-purple-500',
    },
    {
      label: 'Hours Saved',
      value: '156',
      change: '+28%',
      icon: Clock,
      color: 'text-orange-500',
    },
  ]

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4', className)}>
      {activityStats.map((stat, index) => (
        <StatCard
          key={index}
          label={stat.label}
          value={stat.value}
          change={stat.change}
          icon={stat.icon}
          color={stat.color}
        />
      ))}
    </div>
  )
}

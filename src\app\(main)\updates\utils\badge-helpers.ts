export function getAgencyBadgeStyle(agency: string): string {
  switch (agency) {
    case 'FDA':
      return 'bg-info/10 text-info border-info/20'
    case 'EMA':
      return 'bg-success/10 text-success border-success/20'
    case 'ICH':
      return 'bg-primary/10 text-primary border-primary/20'
    case 'CDSCO':
      return 'bg-warning/10 text-warning border-warning/20'
    default:
      return 'bg-muted/50 text-muted-foreground border-border'
  }
}

export function getSeverityBadgeProps(severity: string) {
  switch (severity) {
    case 'critical':
      return {
        className: 'bg-destructive text-destructive-foreground',
        iconName: 'alert-triangle',
      }
    case 'high':
      return {
        className: 'bg-warning text-white',
        iconName: 'clock',
      }
    case 'medium':
      return {
        className: 'bg-info text-info-foreground',
        iconName: 'bell',
      }
    case 'low':
      return {
        className: 'bg-success text-success-foreground',
        iconName: 'check-circle',
      }
    default:
      return {
        className: 'bg-muted text-muted-foreground',
        iconName: null,
      }
  }
}

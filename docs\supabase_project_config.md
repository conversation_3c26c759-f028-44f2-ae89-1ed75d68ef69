# VigiLens Supabase Project Configuration

**Project Name:** VigiLens
**Region:** ap-south-1 (Asia Pacific - Mumbai)
**Created:** 2025-07-11
**Status:** Active and Ready for Development

---

## 🔧 **Project Details**

### Supabase Project Information
```
Project Name: VigiLens
Project URL: https://esgciouphhajolkojipw.supabase.co
Region: ap-south-1 (Asia Pacific - Mumbai)
Database: PostgreSQL 15
```

### API Keys & Configuration
```env
# Frontend Environment Variables (.env.local)
NEXT_PUBLIC_SUPABASE_URL=https://esgciouphhajolkojipw.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjYwMzcsImV4cCI6MjA2NzgwMjAzN30.DqyAefIWDBBH11i4stMELtx9a3zrFIP9q604AWZPTjI

# Backend Environment Variables (.env)
SUPABASE_URL=https://esgciouphhajolkojipw.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjIyNjAzNywiZXhwIjoyMDY3ODAyMDM3fQ.AHkgjUq0KHT2ZV9TlsEYVTsgladyg4GrFCECeXHirDM
DATABASE_URL=************************************************************/postgres
```

---

## 🚀 **Setup Instructions**

### 1. Local Development Setup
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Initialize local project
supabase init

# Link to existing VigiLens project
supabase link --project-ref esgciouphhajolkojipw

# Start local development
supabase start
```

### 2. Frontend Integration (Next.js 15.1.5)
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### 3. Backend Integration (Python 3.13.5 + FastAPI)
```python
# requirements.txt
supabase==2.3.4
asyncpg==0.29.0

# config/database.py
from supabase import create_client, Client
import os

supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

supabase: Client = create_client(supabase_url, supabase_key)
```

---

## 📊 **Database Configuration**

### Current Database Status
- **Database Type:** PostgreSQL 15
- **Storage:** 500MB (free tier)
- **Connections:** Up to 60 concurrent connections
- **Row Level Security:** Enabled and ready for multi-tenant setup

### Planned Schema Structure
```sql
-- Core Tables (to be implemented)
organizations          -- Multi-tenant organization data
user_profiles          -- User management with RBAC
regulatory_documents   -- Document storage and metadata
document_analysis      -- AI analysis results
compliance_frameworks  -- FDA cGMP, ICH Q7, ISO 13485 data
audit_logs            -- 21 CFR Part 11 compliance tracking
notifications         -- Real-time notification system
```

### Row Level Security (RLS) Strategy
```sql
-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE regulatory_documents ENABLE ROW LEVEL SECURITY;

-- Example RLS Policy
CREATE POLICY "Users can only see their organization data" ON user_profiles
  FOR ALL USING (organization_id = (auth.jwt() ->> 'organization_id')::uuid);
```

---

## 🔐 **Authentication Configuration**

### Supabase Auth Settings
- **Email/Password:** Enabled
- **Email Confirmation:** Required
- **Password Requirements:** 8+ characters (will be enhanced to pharmaceutical standards)
- **Session Timeout:** 24 hours (will be reduced to 8 hours for pharmaceutical compliance)

### Planned Authentication Features
- **Multi-Factor Authentication (MFA):** TOTP and SMS
- **Single Sign-On (SSO):** Google OAuth, Azure AD for enterprise
- **Role-Based Access Control:** Admin, Manager, User roles
- **Pharmaceutical Security Standards:** Enhanced password policies, session management

---

## 📁 **Storage Configuration**

### Supabase Storage Buckets (to be created)
```
regulatory-documents/   -- Main document storage
├── org_id/
│   ├── year/
│   │   └── month/
│   │       └── documents/
user-uploads/          -- Temporary upload storage
processed-documents/   -- AI-processed document cache
```

### Storage Policies
- **File Size Limit:** 100MB per file
- **Allowed Types:** PDF, DOC, DOCX, XLS, XLSX, TXT
- **Access Control:** Organization-based with RLS
- **Total Storage:** 1GB (free tier)

---

## 🔄 **Real-time Features**

### Supabase Realtime Configuration
- **WebSocket Endpoint:** wss://esgciouphhajolkojipw.supabase.co/realtime/v1/websocket
- **Planned Channels:**
  - `regulatory_updates` - New document notifications
  - `document_analysis` - AI processing status updates
  - `compliance_alerts` - Critical compliance notifications

### Real-time Integration Example
```typescript
// Real-time subscription setup
const subscription = supabase
  .channel('regulatory_updates')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'regulatory_documents',
    filter: `organization_id=eq.${organizationId}`
  }, (payload) => {
    // Handle new document notification
    console.log('New regulatory document:', payload.new)
  })
  .subscribe()
```

---

## 📈 **Monitoring & Analytics**

### Supabase Analytics (Built-in)
- **API Usage:** Request count, response times
- **Database Performance:** Query performance, connection usage
- **Authentication:** Login attempts, user activity
- **Storage:** File upload/download metrics

### Additional Monitoring (to be implemented)
- **LangSmith 2025:** AI model performance and token usage (free tier)
- **FastAPI Logging:** Application-level logging and error tracking
- **Supabase Analytics 2025:** Built-in analytics and monitoring
- **Custom Metrics:** Pharmaceutical compliance-specific KPIs

---

## ✅ **Next Steps**

### Immediate Actions (Week 1)
1. **Verify Project Access:** Confirm all team members have access to VigiLens project
2. **Environment Setup:** Configure local development environments with provided credentials
3. **Database Schema:** Begin implementing the planned database schema
4. **Authentication Testing:** Test basic authentication flows

### Development Sequence
1. **VCP_001_1:** Configure local development environment (2 hours)
2. **VCP_001_2:** Design and implement database schema (4 hours)
3. **VCP_001_3:** Set up authentication and user management (3 hours)
4. **VCP_002:** Implement FastAPI backend with Supabase integration (12 hours)

---

**Project Status:** Ready for Development
**Last Updated:** 2025-07-11
**Next Review:** After database schema implementation

'use client'

import { Calendar } from 'lucide-react'

import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'

interface Activity {
  readonly type: 'success' | 'warning' | 'info' | 'error';
  readonly message: string;
  readonly time: string;
}

export function RecentActivity() {
  const recentActivities: readonly Activity[] = [
    {
      type: 'success',
      message: 'Compliance check completed for Q2 2023 Manufacturing Report',
      time: '2 hours ago',
    },
    {
      type: 'warning',
      message: 'New regulatory alert for Batch Release Procedures',
      time: 'Yesterday, 01:00 PM',
    },
    {
      type: 'info',
      message: 'New document uploaded: Annual Quality Review Template',
      time: 'June 16, 2023, 9:00 AM',
    },
    {
      type: 'error',
      message:
        'Critical compliance issue identified in Stability Testing Protocol',
      time: 'June 16, 2023, 02:15 PM',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Recent Activities */}
      <Card className="card-subtle">
        <CardHeader>
          <CardTitle className="text-[18px] font-bold">
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {recentActivities.map((activity, index) => (
            <div
              key={index}
              className="flex items-start space-x-3 p-2 -m-2 rounded-lg hover:bg-muted/50 transition-colors duration-150 cursor-pointer"
            >
              <div
                className={`flex h-2 w-2 rounded-full mt-2 ${
                  activity.type === 'success'
                    ? 'bg-success'
                    : activity.type === 'warning'
                      ? 'bg-warning'
                      : activity.type === 'info'
                        ? 'bg-info'
                        : 'bg-destructive'
                }`}
              />
              <div className="flex-1 min-w-0">
                <p className="text-[14px] text-foreground">
                  {activity.message}
                </p>
                <p className="text-[12px] text-muted-foreground">
                  {activity.time}
                </p>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Compliance Calendar */}
      <Card className="card-subtle">
        <CardHeader>
          <CardTitle className="text-[18px] font-bold flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Compliance Calendar
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground text-[14px]">
            <p className="font-medium">June 2023</p>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-[12px] p-2 -m-2 rounded-lg hover:bg-muted/50 transition-colors duration-150 cursor-pointer">
                <span>Q2 Audit Review</span>
                <span>June 30</span>
              </div>
              <div className="flex justify-between text-[12px] p-2 -m-2 rounded-lg hover:bg-muted/50 transition-colors duration-150 cursor-pointer">
                <span>GMP Training Due</span>
                <span>July 15</span>
              </div>
              <div className="flex justify-between text-[12px] p-2 -m-2 rounded-lg hover:bg-muted/50 transition-colors duration-150 cursor-pointer">
                <span>Annual Report</span>
                <span>July 31</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

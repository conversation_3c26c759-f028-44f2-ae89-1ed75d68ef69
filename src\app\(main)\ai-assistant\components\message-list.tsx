'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>, FileTex<PERSON>, ThumbsDown, ThumbsUp, User } from 'lucide-react'
import { useEffect, useRef } from 'react'

import { Avatar, AvatarFallback } from '@/components/ui-radix/avatar'
import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'

import type { Message } from '../types'

interface MessageListProps {
  readonly messages: readonly Message[];
  readonly isTyping: boolean;
  readonly onCopyMessage?: ((message: string) => void) | undefined;
  readonly onFeedback?:
    | ((messageId: string, feedback: 'positive' | 'negative') => void)
    | undefined;
}

export function MessageList({
  messages,
  isTyping = false,
  onCopyMessage,
  onFeedback,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex gap-3 ${
            message.sender === 'user' ? 'justify-end' : 'justify-start'
          }`}
        >
          {message.sender === 'ai' && (
            <Avatar className="w-7 h-7 rounded-full bg-primary">
              <AvatarFallback className="bg-primary text-primary-foreground">
                <Bot className="h-3 w-3" />
              </AvatarFallback>
            </Avatar>
          )}

          <div
            className={`max-w-[80%] rounded-lg p-3 transition-colors ${
              message.sender === 'user'
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'bg-muted text-foreground border border-border'
            }`}
          >
            {/* Show attachments if present */}
            {message.attachments && message.attachments.length > 0 && (
              <div className="mb-3 space-y-2">
                {message.attachments.map((doc) => (
                  <div
                    key={doc.id}
                    className={`flex items-center space-x-2 p-2 rounded border transition-colors ${
                      message.sender === 'user'
                        ? 'border-primary-foreground/20 bg-card/10'
                        : 'border-border bg-card'
                    }`}
                  >
                    <FileText className="h-4 w-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium truncate">{doc.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {doc.type} • {doc.size}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {doc.category}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>

            <div className="flex items-center justify-between mt-3 pt-2">
              <span className="text-xs opacity-70">
                {message.timestamp.toLocaleTimeString()}
              </span>
              {message.sender === 'ai' && (
                <div className="flex space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 rounded-md hover:bg-muted focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1"
                    onClick={() => onCopyMessage?.(message.content)}
                    aria-label="Copy message"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 rounded-md hover:bg-muted focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1"
                    onClick={() => onFeedback?.(message.id, 'positive')}
                    aria-label="Positive feedback"
                  >
                    <ThumbsUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 rounded-md hover:bg-muted focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1"
                    onClick={() => onFeedback?.(message.id, 'negative')}
                    aria-label="Negative feedback"
                  >
                    <ThumbsDown className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {message.sender === 'user' && (
            <Avatar className="w-7 h-7 rounded-full bg-primary">
              <AvatarFallback className="bg-muted text-muted-foreground">
                <User className="h-3 w-3" />
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      ))}

      {isTyping && (
        <div className="flex gap-3 justify-start">
          <Avatar className="w-7 h-7 rounded-full bg-primary">
            <AvatarFallback className="bg-primary text-primary-foreground">
              <Bot className="h-3 w-3" />
            </AvatarFallback>
          </Avatar>
          <div className="bg-muted text-foreground rounded-lg p-3 border border-border">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
              <div
                className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                style={{ animationDelay: '0.1s' }}
              />
              <div
                className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"
                style={{ animationDelay: '0.2s' }}
              />
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  )
}

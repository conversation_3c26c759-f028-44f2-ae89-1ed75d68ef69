-- VigiLens Database Schema - Migration 004
-- Create User Roles and Permissions System - RBAC for Pharmaceutical Compliance
-- Granular role-based access control with time-based assignments

-- User role assignments table - Supports multiple roles per user with time-based expiration
CREATE TABLE user_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- User and organization relationship
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- Role assignment details
    role user_role NOT NULL,
    assigned_by UUID NOT NULL REFERENCES user_profiles(id),

    -- Time-based role management
    effective_date TIMESTAMPTZ DEFAULT NOW(),
    expiry_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,

    -- Assignment context and justification
    assignment_reason TEXT,
    approval_required BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ,

    -- Scope and limitations (JSONB for flexibility)
    scope_limitations JSONB DEFAULT '{
        "departments": [],
        "document_types": [],
        "compliance_frameworks": [],
        "max_document_value": null,
        "read_only_mode": false
    }'::JSONB,

    -- Audit trail
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),

    -- Constraints
    CONSTRAINT user_role_assignments_valid_dates CHECK (
        expiry_date IS NULL OR expiry_date > effective_date
    ),
    CONSTRAINT user_role_assignments_approval_logic CHECK (
        (approval_required = false) OR
        (approval_required = true AND approved_by IS NOT NULL AND approved_at IS NOT NULL)
    )
);

-- Role permissions table - Defines what each role can do
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Role and permission details
    role user_role NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    permission_category VARCHAR(50) NOT NULL, -- 'documents', 'users', 'compliance', 'system'

    -- Permission configuration
    can_create BOOLEAN DEFAULT false,
    can_read BOOLEAN DEFAULT false,
    can_update BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    can_approve BOOLEAN DEFAULT false,
    can_export BOOLEAN DEFAULT false,

    -- Scope and conditions (JSONB for complex rules)
    conditions JSONB DEFAULT '{}'::JSONB,

    -- Metadata
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Unique constraint to prevent duplicate permissions
    UNIQUE(role, permission_name)
);

-- Permission audit log - Track permission changes for compliance
CREATE TABLE permission_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- User and organization context
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),

    -- Permission change details
    action VARCHAR(50) NOT NULL, -- 'granted', 'revoked', 'modified', 'expired'
    role_assignment_id UUID REFERENCES user_role_assignments(id),
    old_permissions JSONB,
    new_permissions JSONB,

    -- Change context
    changed_by UUID NOT NULL REFERENCES user_profiles(id),
    change_reason TEXT,
    approval_workflow_id UUID, -- For future workflow integration

    -- Audit metadata
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),

    -- 21 CFR Part 11 compliance
    electronic_signature JSONB,
    checksum VARCHAR(64)
);

-- Create indexes for performance
CREATE INDEX idx_user_role_assignments_user_id ON user_role_assignments(user_id);
CREATE INDEX idx_user_role_assignments_organization_id ON user_role_assignments(organization_id);
CREATE INDEX idx_user_role_assignments_role ON user_role_assignments(role);
CREATE INDEX idx_user_role_assignments_active ON user_role_assignments(user_id, is_active) WHERE is_active = true;
CREATE INDEX idx_user_role_assignments_effective ON user_role_assignments(effective_date, expiry_date);

CREATE INDEX idx_role_permissions_role ON role_permissions(role);
CREATE INDEX idx_role_permissions_category ON role_permissions(permission_category);
CREATE INDEX idx_role_permissions_active ON role_permissions(role, permission_name) WHERE is_active = true;

CREATE INDEX idx_permission_audit_log_user_id ON permission_audit_log(user_id);
CREATE INDEX idx_permission_audit_log_organization_id ON permission_audit_log(organization_id);
CREATE INDEX idx_permission_audit_log_timestamp ON permission_audit_log(timestamp);

-- GIN indexes for JSONB columns
CREATE INDEX idx_user_role_assignments_scope ON user_role_assignments USING GIN(scope_limitations);
CREATE INDEX idx_role_permissions_conditions ON role_permissions USING GIN(conditions);

-- Add updated_at triggers
CREATE TRIGGER update_user_role_assignments_updated_at
    BEFORE UPDATE ON user_role_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at
    BEFORE UPDATE ON role_permissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE user_role_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- User role assignments policies
CREATE POLICY "Users can view role assignments in org" ON user_role_assignments
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

CREATE POLICY "Admins can manage role assignments" ON user_role_assignments
    FOR ALL USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (auth.jwt() ->> 'role') IN ('admin', 'compliance_officer')
    );

-- Role permissions policies (read-only for most users)
CREATE POLICY "Users can view role permissions" ON role_permissions
    FOR SELECT USING (true); -- Global read access to understand permissions

CREATE POLICY "Only system admins can modify permissions" ON role_permissions
    FOR ALL USING (
        (auth.jwt() ->> 'role') = 'admin'
        AND (auth.jwt() ->> 'email') LIKE '%@vigilens.com' -- System admin check
    );

-- Permission audit log policies
CREATE POLICY "Users can view permission audit in org" ON permission_audit_log
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

CREATE POLICY "System logs permission changes" ON permission_audit_log
    FOR INSERT WITH CHECK (true); -- Allow system to log changes

-- Create function to get user's effective permissions
CREATE OR REPLACE FUNCTION get_user_permissions(user_id UUID)
RETURNS JSONB AS $$
DECLARE
    user_permissions JSONB := '[]'::JSONB;
    role_record RECORD;
BEGIN
    -- Get all active role assignments for the user
    FOR role_record IN
        SELECT DISTINCT ura.role
        FROM user_role_assignments ura
        WHERE ura.user_id = get_user_permissions.user_id
        AND ura.is_active = true
        AND ura.effective_date <= NOW()
        AND (ura.expiry_date IS NULL OR ura.expiry_date > NOW())
    LOOP
        -- Get permissions for each role
        user_permissions := user_permissions || (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'permission_name', permission_name,
                    'category', permission_category,
                    'can_create', can_create,
                    'can_read', can_read,
                    'can_update', can_update,
                    'can_delete', can_delete,
                    'can_approve', can_approve,
                    'can_export', can_export,
                    'conditions', conditions
                )
            )
            FROM role_permissions
            WHERE role = role_record.role
            AND is_active = true
        );
    END LOOP;

    RETURN COALESCE(user_permissions, '[]'::JSONB);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(
    user_id UUID,
    permission_name TEXT,
    action TEXT -- 'create', 'read', 'update', 'delete', 'approve', 'export'
)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := false;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM user_role_assignments ura
        JOIN role_permissions rp ON rp.role = ura.role
        WHERE ura.user_id = user_has_permission.user_id
        AND ura.is_active = true
        AND ura.effective_date <= NOW()
        AND (ura.expiry_date IS NULL OR ura.expiry_date > NOW())
        AND rp.permission_name = user_has_permission.permission_name
        AND rp.is_active = true
        AND (
            (action = 'create' AND rp.can_create = true) OR
            (action = 'read' AND rp.can_read = true) OR
            (action = 'update' AND rp.can_update = true) OR
            (action = 'delete' AND rp.can_delete = true) OR
            (action = 'approve' AND rp.can_approve = true) OR
            (action = 'export' AND rp.can_export = true)
        )
    ) INTO has_permission;

    RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to assign role to user
CREATE OR REPLACE FUNCTION assign_user_role(
    target_user_id UUID,
    target_role user_role,
    assigned_by_user_id UUID,
    assignment_reason TEXT DEFAULT NULL,
    expiry_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    assignment_id UUID;
    org_id UUID;
BEGIN
    -- Get organization ID from the assigning user
    SELECT organization_id INTO org_id
    FROM user_profiles
    WHERE id = assigned_by_user_id;

    -- Create role assignment
    INSERT INTO user_role_assignments (
        user_id,
        organization_id,
        role,
        assigned_by,
        assignment_reason,
        expiry_date
    ) VALUES (
        target_user_id,
        org_id,
        target_role,
        assigned_by_user_id,
        assignment_reason,
        expiry_date
    ) RETURNING id INTO assignment_id;

    -- Log the permission change
    INSERT INTO permission_audit_log (
        user_id,
        organization_id,
        action,
        role_assignment_id,
        new_permissions,
        changed_by,
        change_reason
    ) VALUES (
        target_user_id,
        org_id,
        'granted',
        assignment_id,
        jsonb_build_object('role', target_role, 'expiry_date', expiry_date),
        assigned_by_user_id,
        assignment_reason
    );

    RETURN assignment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default role permissions for pharmaceutical compliance

-- Admin role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('admin', 'manage_users', 'users', true, true, true, true, true, true, 'Full user management capabilities'),
('admin', 'manage_documents', 'documents', true, true, true, true, true, true, 'Full document management capabilities'),
('admin', 'manage_compliance', 'compliance', true, true, true, true, true, true, 'Full compliance management capabilities'),
('admin', 'system_configuration', 'system', true, true, true, true, true, true, 'System configuration and settings'),
('admin', 'audit_access', 'system', false, true, false, false, false, true, 'Access to audit trails and logs');

-- Compliance Officer role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('compliance_officer', 'manage_documents', 'documents', true, true, true, false, true, true, 'Document management with approval rights'),
('compliance_officer', 'compliance_assessment', 'compliance', true, true, true, false, true, true, 'Compliance assessments and scoring'),
('compliance_officer', 'regulatory_monitoring', 'compliance', false, true, true, false, false, true, 'Monitor regulatory updates'),
('compliance_officer', 'audit_access', 'system', false, true, false, false, false, true, 'Read-only audit access');

-- QA Manager role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('qa_manager', 'quality_documents', 'documents', true, true, true, false, true, true, 'Quality-related document management'),
('qa_manager', 'compliance_review', 'compliance', false, true, true, false, true, true, 'Review and approve compliance assessments'),
('qa_manager', 'training_records', 'users', true, true, true, false, false, true, 'Manage training records');

-- Regulatory Affairs role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('regulatory_affairs', 'regulatory_documents', 'documents', true, true, true, false, false, true, 'Regulatory document management'),
('regulatory_affairs', 'submission_preparation', 'compliance', true, true, true, false, false, true, 'Prepare regulatory submissions'),
('regulatory_affairs', 'agency_correspondence', 'compliance', true, true, true, false, false, true, 'Manage agency communications');

-- Auditor role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('auditor', 'audit_documents', 'documents', false, true, false, false, false, true, 'Read-only access to documents for auditing'),
('auditor', 'audit_trails', 'system', false, true, false, false, false, true, 'Full audit trail access'),
('auditor', 'compliance_reports', 'compliance', true, true, false, false, false, true, 'Generate compliance reports');

-- Quality Assurance role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('quality_assurance', 'qa_documents', 'documents', true, true, true, false, true, true, 'QA document management'),
('quality_assurance', 'deviation_management', 'compliance', true, true, true, false, true, true, 'Manage deviations and CAPAs'),
('quality_assurance', 'validation_protocols', 'compliance', true, true, true, false, true, true, 'Validation protocol management');

-- Document Reviewer role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('document_reviewer', 'review_documents', 'documents', false, true, true, false, false, false, 'Review and comment on documents'),
('document_reviewer', 'compliance_input', 'compliance', false, true, true, false, false, false, 'Provide compliance input');

-- Read Only role permissions
INSERT INTO role_permissions (role, permission_name, permission_category, can_create, can_read, can_update, can_delete, can_approve, can_export, description) VALUES
('read_only', 'view_documents', 'documents', false, true, false, false, false, false, 'Read-only document access'),
('read_only', 'view_compliance', 'compliance', false, true, false, false, false, false, 'Read-only compliance information');

-- Comments for documentation
COMMENT ON TABLE user_role_assignments IS 'Time-based role assignments with approval workflows for pharmaceutical compliance';
COMMENT ON TABLE role_permissions IS 'Granular permission definitions for each pharmaceutical role';
COMMENT ON TABLE permission_audit_log IS '21 CFR Part 11 compliant audit log for permission changes';
COMMENT ON FUNCTION get_user_permissions(UUID) IS 'Returns effective permissions for a user based on active role assignments';
COMMENT ON FUNCTION user_has_permission(UUID, TEXT, TEXT) IS 'Checks if user has specific permission for given action';
COMMENT ON FUNCTION assign_user_role(UUID, user_role, UUID, TEXT, TIMESTAMPTZ) IS 'Assigns role to user with audit logging';

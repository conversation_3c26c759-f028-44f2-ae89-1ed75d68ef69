'use client'

import { TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { But<PERSON> } from '@/components/ui-radix/button'

export function DashboardHeader() {
  const router = useRouter()

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-[30px] font-bold text-foreground leading-[36px] mb-1">
          Regulatory Autopilot
        </h1>
        <p className="text-[14px] text-muted-foreground">
          Welcome back, your compliance dashboard is up to date
        </p>
      </div>
      <Button
        className="h-9 px-5 bg-primary hover:bg-primary/90 text-primary-foreground text-[14px] font-semibold transition-colors duration-150"
        onClick={() => router.push('/updates')}
      >
        <TrendingUp className="mr-2 h-4 w-4" />
        View all updates
      </Button>
    </div>
  )
}

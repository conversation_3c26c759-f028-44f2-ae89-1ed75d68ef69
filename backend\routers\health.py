"""Health check endpoints for VigiLens AI API."""

from datetime import datetime
import os
import platform
import sys
from fastapi import APIRouter, HTTPException
from models.base import HealthCheckResponse

router = APIRouter(prefix="/health", tags=["health"])


@router.get("/", response_model=HealthCheckResponse)
async def health_check():
    """
    Health check endpoint to verify AI API connectivity.

    Returns:
        HealthCheckResponse: System health status
    """
    try:
        # Check AI service health
        ai_status = "healthy"  # Will be enhanced with actual AI service checks

        return HealthCheckResponse(
            status=ai_status,
            timestamp=datetime.utcnow(),
            version="1.0.0",
            database="not_applicable",  # Database operations moved to Supabase
            dependencies={
                "fastapi": "running",
                "langchain": "available",
                "openrouter": "available"  # Will be enhanced with actual checks
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/ai")
async def ai_health():
    """
    Detailed AI service health check.

    Returns:
        dict: AI service status and details
    """
    try:
        # System information
        system_info = {
            "python_version": sys.version,
            "platform": platform.platform(),
            "cpu_count": os.cpu_count(),
            "memory_available": "Check implemented in production"
        }

        # AI service status
        ai_status = {
            "langchain_version": "0.3.26",  # Will be dynamic in production
            "openrouter_status": "available",  # Will check actual connection
            "chromadb_status": "available",  # Will check actual connection
            "models_loaded": ["llama-3.1-8b-instruct"],  # Will be dynamic
            "embedding_model": "all-MiniLM-L6-v2"  # Will be dynamic
        }

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "system": system_info,
            "ai_services": ai_status
        }
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"AI health check failed: {str(e)}"
        )

# VigiLens Authentication Setup Guide

## 🔐 Supabase Email Template Configuration

To ensure proper authentication redirect flows, you must configure the email templates in your Supabase dashboard.

### 1. Access Email Templates

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your VigiLens project
3. Navigate to **Authentication** → **Email Templates**

### 2. Configure Confirm Signup Template

**Template:** `Confirm signup`

**Update the confirmation URL:**

**❌ Default (incorrect):**
```html
<a href="{{ .ConfirmationURL }}">Confirm your signup</a>
```

**✅ Correct for VigiLens:**
```html
<a href="{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=email&next=/dashboard">Confirm your signup</a>
```

### 3. Configure Reset Password Template

**Template:** `Reset Password`

**Update the reset URL:**

**❌ Default (incorrect):**
```html
<a href="{{ .ConfirmationURL }}">Reset your password</a>
```

**✅ Correct for VigiLens:**
```html
<a href="{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=recovery&next=/dashboard">Reset your password</a>
```

### 4. Environment Variables Required

Ensure these environment variables are set in your `.env.local`:

```bash
# Your site URL (critical for email redirects)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# For production:
# NEXT_PUBLIC_SITE_URL=https://your-domain.com

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## 🔄 Authentication Flow

### Signup Flow
1. User fills signup form → `signup` Server Action
2. Supabase sends confirmation email with custom template
3. User clicks email link → `/auth/confirm?token_hash=...&type=email&next=/dashboard`
4. Token verified → User redirected to `/dashboard`

### Login Flow
1. User fills login form → `login` Server Action
2. Credentials verified → Direct redirect to `/dashboard`

### Password Reset Flow
1. User requests reset → `resetPassword` Server Action
2. Supabase sends reset email with custom template
3. User clicks email link → `/auth/confirm?token_hash=...&type=recovery&next=/dashboard`
4. Token verified → User redirected to password update form

## 🛠️ Troubleshooting

### Issue: Users redirected to homepage instead of dashboard
**Solution:** Verify email templates use the correct URLs with `&next=/dashboard` parameter

### Issue: "Invalid token" errors
**Solution:** Ensure `NEXT_PUBLIC_SITE_URL` matches your actual domain exactly

### Issue: Email confirmation not working
**Solution:** Check that email templates use `{{ .TokenHash }}` and `type=email` parameters

## 🔍 Testing Authentication

### 1. Test Signup Flow
```bash
# 1. Fill signup form
# 2. Check email for confirmation link
# 3. Verify link format: /auth/confirm?token_hash=...&type=email&next=/dashboard
# 4. Click link and verify redirect to dashboard
```

### 2. Test Login Flow
```bash
# 1. Use confirmed account credentials
# 2. Verify direct redirect to /dashboard
```

### 3. Test Password Reset
```bash
# 1. Request password reset
# 2. Check email for reset link
# 3. Verify link format: /auth/confirm?token_hash=...&type=recovery&next=/dashboard
# 4. Complete password reset flow
```

## 📋 Checklist

- [ ] Email templates updated in Supabase dashboard
- [ ] `NEXT_PUBLIC_SITE_URL` environment variable set
- [ ] Signup flow tested end-to-end
- [ ] Login flow tested
- [ ] Password reset flow tested
- [ ] All redirects go to `/dashboard`

## 🚨 Security Notes

- Never expose service role keys in frontend code
- Always use `NEXT_PUBLIC_SUPABASE_ANON_KEY` for client-side operations
- Email templates should only use `{{ .SiteURL }}`, `{{ .TokenHash }}`, and approved variables
- Validate all redirects to prevent open redirect vulnerabilities

## 📞 Support

If authentication issues persist:
1. Check browser developer console for errors
2. Verify environment variables are loaded correctly
3. Test with a fresh incognito browser session
4. Check Supabase Auth logs in the dashboard

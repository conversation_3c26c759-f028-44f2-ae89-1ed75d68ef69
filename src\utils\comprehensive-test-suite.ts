/**
 * Comprehensive Test Suite for VigiLens Critical Issues
 * Tests database integration, logout functionality, and auth context fixes
 */

import { createClient } from '@/utils/supabase/client'

interface TestResult {
  test: string
  success: boolean
  error?: string
  data?: any
  timestamp: string
}

export class ComprehensiveTestSuite {
  private supabase = createClient()
  private results: TestResult[] = []

  /**
   * Run all critical tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Comprehensive Test Suite for Critical Issues...')

    this.results = []

    // Test 1: Environment Configuration
    await this.testEnvironmentConfiguration()

    // Test 2: Auth Context Profile Fetching
    await this.testAuthContextProfileFetching()

    // Test 3: Database Update Integration
    await this.testDatabaseUpdateIntegration()

    // Test 4: Notification Preferences Update
    await this.testNotificationPreferencesUpdate()

    // Test 5: Logout Functionality
    await this.testLogoutFunctionality()

    // Test 6: Real-time Profile Updates
    await this.testRealTimeProfileUpdates()

    console.log('🧪 Comprehensive Test Suite Complete:', this.results)
    return this.results
  }

  /**
   * Test 1: Environment Configuration
   */
  private async testEnvironmentConfiguration(): Promise<void> {
    try {
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const currentPort = window.location.port

      const isCorrectPort = siteUrl?.includes(currentPort) || siteUrl?.includes('3001')

      this.results.push({
        test: 'Environment Configuration',
        success: isCorrectPort,
        data: {
          siteUrl,
          supabaseUrl,
          currentPort,
          windowLocation: window.location.href
        },
        timestamp: new Date().toISOString(),
        error: isCorrectPort ? undefined : 'Site URL port mismatch with current port'
      })

    } catch (error) {
      this.results.push({
        test: 'Environment Configuration',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Test 2: Auth Context Profile Fetching
   */
  private async testAuthContextProfileFetching(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()

      if (!user) {
        this.results.push({
          test: 'Auth Context Profile Fetching',
          success: false,
          error: 'No authenticated user found',
          timestamp: new Date().toISOString()
        })
        return
      }

      // Test if we can fetch profile with all JSONB columns
      const { data: profile, error } = await this.supabase
        .from('user_profiles')
        .select(`
          id,
          email,
          full_name,
          role,
          organization_id,
          department,
          phone,
          bio,
          preferences,
          notification_preferences,
          security_settings,
          compliance_settings,
          activity_log,
          achievements,
          created_at,
          updated_at
        `)
        .eq('id', user.id)
        .single()

      if (error) {
        this.results.push({
          test: 'Auth Context Profile Fetching',
          success: false,
          error: error.message,
          data: { error },
          timestamp: new Date().toISOString()
        })
        return
      }

      const hasJsonbFields = !!(
        profile.preferences !== undefined ||
        profile.notification_preferences !== undefined ||
        profile.security_settings !== undefined ||
        profile.compliance_settings !== undefined
      )

      this.results.push({
        test: 'Auth Context Profile Fetching',
        success: hasJsonbFields,
        data: {
          profileId: profile.id,
          hasPreferences: profile.preferences !== undefined,
          hasNotificationPrefs: profile.notification_preferences !== undefined,
          hasSecuritySettings: profile.security_settings !== undefined,
          hasComplianceSettings: profile.compliance_settings !== undefined,
          hasBio: profile.bio !== undefined,
          hasActivityLog: profile.activity_log !== undefined,
          hasAchievements: profile.achievements !== undefined
        },
        timestamp: new Date().toISOString(),
        error: hasJsonbFields ? undefined : 'JSONB fields not being fetched correctly'
      })

    } catch (error) {
      this.results.push({
        test: 'Auth Context Profile Fetching',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Test 3: Database Update Integration
   */
  private async testDatabaseUpdateIntegration(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()

      if (!user) {
        this.results.push({
          test: 'Database Update Integration',
          success: false,
          error: 'No authenticated user found',
          timestamp: new Date().toISOString()
        })
        return
      }

      // Test profile update
      const testData = {
        full_name: `Test User ${Date.now()}`,
        department: 'Quality Assurance Test',
        phone: '******-TEST'
      }

      console.log('🔄 Testing database update with:', testData)

      const { data: updatedProfile, error } = await this.supabase
        .from('user_profiles')
        .update(testData)
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        this.results.push({
          test: 'Database Update Integration',
          success: false,
          error: error.message,
          data: { testData, error },
          timestamp: new Date().toISOString()
        })
        return
      }

      // Verify the update worked
      const updateWorked = updatedProfile.full_name === testData.full_name &&
                          updatedProfile.department === testData.department &&
                          updatedProfile.phone === testData.phone

      this.results.push({
        test: 'Database Update Integration',
        success: updateWorked,
        data: { testData, updatedProfile, updateWorked },
        timestamp: new Date().toISOString(),
        error: updateWorked ? undefined : 'Database update did not persist correctly'
      })

    } catch (error) {
      this.results.push({
        test: 'Database Update Integration',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Test 4: Notification Preferences Update
   */
  private async testNotificationPreferencesUpdate(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()

      if (!user) {
        this.results.push({
          test: 'Notification Preferences Update',
          success: false,
          error: 'No authenticated user found',
          timestamp: new Date().toISOString()
        })
        return
      }

      // Test notification preferences update
      const testNotificationPrefs = {
        email_notifications: true,
        push_notifications: false,
        regulatory_updates: true,
        document_processing_alerts: true,
        weekly_compliance_reports: false,
        critical_compliance_issues: true,
        processing_complete: true,
        digest_frequency: 'daily' as const,
        test_timestamp: Date.now()
      }

      console.log('🔄 Testing notification preferences update:', testNotificationPrefs)

      const { data: updatedProfile, error } = await this.supabase
        .from('user_profiles')
        .update({ notification_preferences: testNotificationPrefs })
        .eq('id', user.id)
        .select('notification_preferences')
        .single()

      if (error) {
        this.results.push({
          test: 'Notification Preferences Update',
          success: false,
          error: error.message,
          data: { testNotificationPrefs, error },
          timestamp: new Date().toISOString()
        })
        return
      }

      // Verify the JSONB update worked
      const savedPrefs = updatedProfile.notification_preferences as any
      const updateWorked = savedPrefs?.test_timestamp === testNotificationPrefs.test_timestamp

      this.results.push({
        test: 'Notification Preferences Update',
        success: updateWorked,
        data: { testNotificationPrefs, savedPrefs, updateWorked },
        timestamp: new Date().toISOString(),
        error: updateWorked ? undefined : 'Notification preferences JSONB update failed'
      })

    } catch (error) {
      this.results.push({
        test: 'Notification Preferences Update',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Test 5: Logout Functionality (simulation)
   */
  private async testLogoutFunctionality(): Promise<void> {
    try {
      // Test if signOut method exists and is callable
      const { data: { user } } = await this.supabase.auth.getUser()

      if (!user) {
        this.results.push({
          test: 'Logout Functionality',
          success: false,
          error: 'No authenticated user to test logout',
          timestamp: new Date().toISOString()
        })
        return
      }

      // Test that signOut method exists (don't actually call it)
      const signOutExists = typeof this.supabase.auth.signOut === 'function'

      this.results.push({
        test: 'Logout Functionality',
        success: signOutExists,
        data: {
          signOutMethodExists: signOutExists,
          userLoggedIn: !!user,
          note: 'Logout method tested for existence only (not executed to avoid logging out)'
        },
        timestamp: new Date().toISOString(),
        error: signOutExists ? undefined : 'signOut method not available'
      })

    } catch (error) {
      this.results.push({
        test: 'Logout Functionality',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Test 6: Real-time Profile Updates
   */
  private async testRealTimeProfileUpdates(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()

      if (!user) {
        this.results.push({
          test: 'Real-time Profile Updates',
          success: false,
          error: 'No authenticated user found',
          timestamp: new Date().toISOString()
        })
        return
      }

      // Get current profile
      const { data: beforeProfile } = await this.supabase
        .from('user_profiles')
        .select('updated_at')
        .eq('id', user.id)
        .single()

      // Make a small update
      const { data: afterProfile, error } = await this.supabase
        .from('user_profiles')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', user.id)
        .select('updated_at')
        .single()

      if (error) {
        this.results.push({
          test: 'Real-time Profile Updates',
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        })
        return
      }

      const timestampChanged = beforeProfile?.updated_at !== afterProfile?.updated_at

      this.results.push({
        test: 'Real-time Profile Updates',
        success: timestampChanged,
        data: {
          beforeTimestamp: beforeProfile?.updated_at,
          afterTimestamp: afterProfile?.updated_at,
          timestampChanged
        },
        timestamp: new Date().toISOString(),
        error: timestampChanged ? undefined : 'Profile timestamp did not update'
      })

    } catch (error) {
      this.results.push({
        test: 'Real-time Profile Updates',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Get test results summary
   */
  getTestSummary(): { total: number; passed: number; failed: number; results: TestResult[] } {
    const total = this.results.length
    const passed = this.results.filter(r => r.success).length
    const failed = total - passed

    return { total, passed, failed, results: this.results }
  }
}

// Export function to run tests from browser console
export async function runComprehensiveTests(): Promise<any> {
  console.log('🧪 Starting Comprehensive Test Suite...')

  const testSuite = new ComprehensiveTestSuite()
  const results = await testSuite.runAllTests()
  const summary = testSuite.getTestSummary()

  console.log('📊 Comprehensive Test Summary:', summary)

  if (summary.failed > 0) {
    console.error('❌ Some critical tests failed. Check the results above for details.')
    console.table(summary.results.filter(r => !r.success))
  } else {
    console.log('✅ All critical tests passed!')
  }

  return summary
}

// Immediately make it available globally when this module loads
if (typeof window !== 'undefined') {
  (window as any).runComprehensiveTests = runComprehensiveTests
  (window as any).ComprehensiveTestSuite = ComprehensiveTestSuite
  console.log('🔧 Test functions loaded and available globally!')
}

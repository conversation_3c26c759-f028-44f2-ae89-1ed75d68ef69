export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      audit_trail: {
        Row: {
          action_category: string
          action_description: string
          action_type: Database["public"]["Enums"]["audit_action_type"]
          change_summary: string | null
          compliance_metadata: Json | null
          context: Json | null
          correlation_id: string | null
          data_integrity_hash: string | null
          electronic_signature: Json | null
          gxp_relevant: boolean | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          organization_id: string
          request_id: string | null
          resource_id: string | null
          resource_name: string | null
          resource_type: string
          risk_level: Database["public"]["Enums"]["risk_level"] | null
          session_id: string | null
          severity: string | null
          timestamp: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action_category: string
          action_description: string
          action_type: Database["public"]["Enums"]["audit_action_type"]
          change_summary?: string | null
          compliance_metadata?: Json | null
          context?: Json | null
          correlation_id?: string | null
          data_integrity_hash?: string | null
          electronic_signature?: Json | null
          gxp_relevant?: boolean | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          organization_id: string
          request_id?: string | null
          resource_id?: string | null
          resource_name?: string | null
          resource_type: string
          risk_level?: Database["public"]["Enums"]["risk_level"] | null
          session_id?: string | null
          severity?: string | null
          timestamp?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action_category?: string
          action_description?: string
          action_type?: Database["public"]["Enums"]["audit_action_type"]
          change_summary?: string | null
          compliance_metadata?: Json | null
          context?: Json | null
          correlation_id?: string | null
          data_integrity_hash?: string | null
          electronic_signature?: Json | null
          gxp_relevant?: boolean | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          organization_id?: string
          request_id?: string | null
          resource_id?: string | null
          resource_name?: string | null
          resource_type?: string
          risk_level?: Database["public"]["Enums"]["risk_level"] | null
          session_id?: string | null
          severity?: string | null
          timestamp?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_trail_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_trail_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      document_compliance_assessments: {
        Row: {
          assessed_at: string
          assessed_by: string
          category_scores: Json
          compliance_status: string
          created_at: string
          document_id: string
          framework_id: string
          id: string
          organization_id: string
          overall_score: number
          recommendations: string[] | null
          updated_at: string
          violations: Json | null
        }
        Insert: {
          assessed_at?: string
          assessed_by: string
          category_scores?: Json
          compliance_status: string
          created_at?: string
          document_id: string
          framework_id: string
          id?: string
          organization_id: string
          overall_score: number
          recommendations?: string[] | null
          updated_at?: string
          violations?: Json | null
        }
        Update: {
          assessed_at?: string
          assessed_by?: string
          category_scores?: Json
          compliance_status?: string
          created_at?: string
          document_id?: string
          framework_id?: string
          id?: string
          organization_id?: string
          overall_score?: number
          recommendations?: string[] | null
          updated_at?: string
          violations?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "document_compliance_assessments_assessed_by_fkey"
            columns: ["assessed_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_compliance_assessments_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "regulatory_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_compliance_assessments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      document_versions: {
        Row: {
          change_reason: string | null
          change_summary: string
          change_type: string | null
          content: string | null
          created_at: string | null
          created_by: string
          document_id: string
          electronic_signature: Json | null
          file_hash: string | null
          file_path: string | null
          id: string
          is_current: boolean | null
          organization_id: string
          status: Database["public"]["Enums"]["document_status"]
          title: string
          version_number: string
          version_type: string | null
        }
        Insert: {
          change_reason?: string | null
          change_summary: string
          change_type?: string | null
          content?: string | null
          created_at?: string | null
          created_by: string
          document_id: string
          electronic_signature?: Json | null
          file_hash?: string | null
          file_path?: string | null
          id?: string
          is_current?: boolean | null
          organization_id: string
          status: Database["public"]["Enums"]["document_status"]
          title: string
          version_number: string
          version_type?: string | null
        }
        Update: {
          change_reason?: string | null
          change_summary?: string
          change_type?: string | null
          content?: string | null
          created_at?: string | null
          created_by?: string
          document_id?: string
          electronic_signature?: Json | null
          file_hash?: string | null
          file_path?: string | null
          id?: string
          is_current?: boolean | null
          organization_id?: string
          status?: Database["public"]["Enums"]["document_status"]
          title?: string
          version_number?: string
          version_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_versions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_versions_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "regulatory_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_versions_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      electronic_signatures: {
        Row: {
          audit_trail_id: string | null
          authentication_method: Database["public"]["Enums"]["authentication_method"]
          authentication_timestamp: string
          document_hash_at_signing: string
          document_id: string | null
          document_version_id: string | null
          id: string
          ip_address: unknown | null
          is_valid: boolean | null
          organization_id: string
          public_key_fingerprint: string | null
          signature_algorithm: string | null
          signature_hash: string
          signature_meaning: string
          signature_metadata: Json | null
          signature_reason: string | null
          signature_type: Database["public"]["Enums"]["signature_type"]
          signed_at: string
          signer_department: string | null
          signer_id: string
          signer_name: string
          signer_title: string | null
          user_agent: string | null
          verification_notes: string | null
          witness_signature_hash: string | null
          witnessed_by: string | null
        }
        Insert: {
          audit_trail_id?: string | null
          authentication_method: Database["public"]["Enums"]["authentication_method"]
          authentication_timestamp: string
          document_hash_at_signing: string
          document_id?: string | null
          document_version_id?: string | null
          id?: string
          ip_address?: unknown | null
          is_valid?: boolean | null
          organization_id: string
          public_key_fingerprint?: string | null
          signature_algorithm?: string | null
          signature_hash: string
          signature_meaning: string
          signature_metadata?: Json | null
          signature_reason?: string | null
          signature_type: Database["public"]["Enums"]["signature_type"]
          signed_at?: string
          signer_department?: string | null
          signer_id: string
          signer_name: string
          signer_title?: string | null
          user_agent?: string | null
          verification_notes?: string | null
          witness_signature_hash?: string | null
          witnessed_by?: string | null
        }
        Update: {
          audit_trail_id?: string | null
          authentication_method?: Database["public"]["Enums"]["authentication_method"]
          authentication_timestamp?: string
          document_hash_at_signing?: string
          document_id?: string | null
          document_version_id?: string | null
          id?: string
          ip_address?: unknown | null
          is_valid?: boolean | null
          organization_id?: string
          public_key_fingerprint?: string | null
          signature_algorithm?: string | null
          signature_hash?: string
          signature_meaning?: string
          signature_metadata?: Json | null
          signature_reason?: string | null
          signature_type?: Database["public"]["Enums"]["signature_type"]
          signed_at?: string
          signer_department?: string | null
          signer_id?: string
          signer_name?: string
          signer_title?: string | null
          user_agent?: string | null
          verification_notes?: string | null
          witness_signature_hash?: string | null
          witnessed_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "electronic_signatures_audit_trail_id_fkey"
            columns: ["audit_trail_id"]
            isOneToOne: false
            referencedRelation: "audit_trail"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "electronic_signatures_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "regulatory_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "electronic_signatures_document_version_id_fkey"
            columns: ["document_version_id"]
            isOneToOne: false
            referencedRelation: "document_versions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "electronic_signatures_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "electronic_signatures_signer_id_fkey"
            columns: ["signer_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "electronic_signatures_witnessed_by_fkey"
            columns: ["witnessed_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          address: Json | null
          company_type: string | null
          compliance_frameworks:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          created_at: string | null
          created_by: string | null
          description: string | null
          display_name: string
          gmp_license_number: string | null
          id: string
          is_active: boolean | null
          locale: string | null
          name: string
          phone: string | null
          primary_contact_email: string
          regulatory_agencies:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          regulatory_id: string | null
          settings: Json | null
          timezone: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          address?: Json | null
          company_type?: string | null
          compliance_frameworks?:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          display_name: string
          gmp_license_number?: string | null
          id?: string
          is_active?: boolean | null
          locale?: string | null
          name: string
          phone?: string | null
          primary_contact_email: string
          regulatory_agencies?:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          regulatory_id?: string | null
          settings?: Json | null
          timezone?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          address?: Json | null
          company_type?: string | null
          compliance_frameworks?:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          display_name?: string
          gmp_license_number?: string | null
          id?: string
          is_active?: boolean | null
          locale?: string | null
          name?: string
          phone?: string | null
          primary_contact_email?: string
          regulatory_agencies?:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          regulatory_id?: string | null
          settings?: Json | null
          timezone?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      regulatory_documents: {
        Row: {
          ai_analysis_results: Json | null
          approval_comments: string | null
          approved_at: string | null
          approved_by: string | null
          approver_id: string | null
          assigned_to: string[] | null
          compliance_frameworks:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          compliance_score: number | null
          content: string | null
          created_at: string | null
          created_by: string
          deadline: string | null
          description: string | null
          document_number: string | null
          document_type: Database["public"]["Enums"]["document_type"]
          effective_date: string | null
          expiry_date: string | null
          file_hash: string | null
          file_path: string | null
          file_size: number | null
          gxp_relevant: boolean | null
          id: string
          keywords: string[] | null
          mime_type: string | null
          next_review_date: string | null
          organization_id: string
          parent_document_id: string | null
          priority: string | null
          processing_status:
            | Database["public"]["Enums"]["processing_status"]
            | null
          regulatory_agencies:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          related_documents: string[] | null
          review_due_date: string | null
          reviewer_id: string | null
          risk_level: Database["public"]["Enums"]["risk_level"] | null
          status: Database["public"]["Enums"]["document_status"]
          supersedes_document_id: string | null
          title: string
          updated_at: string | null
          updated_by: string | null
          version: string
        }
        Insert: {
          ai_analysis_results?: Json | null
          approval_comments?: string | null
          approved_at?: string | null
          approved_by?: string | null
          approver_id?: string | null
          assigned_to?: string[] | null
          compliance_frameworks?:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          compliance_score?: number | null
          content?: string | null
          created_at?: string | null
          created_by: string
          deadline?: string | null
          description?: string | null
          document_number?: string | null
          document_type: Database["public"]["Enums"]["document_type"]
          effective_date?: string | null
          expiry_date?: string | null
          file_hash?: string | null
          file_path?: string | null
          file_size?: number | null
          gxp_relevant?: boolean | null
          id?: string
          keywords?: string[] | null
          mime_type?: string | null
          next_review_date?: string | null
          organization_id: string
          parent_document_id?: string | null
          priority?: string | null
          processing_status?:
            | Database["public"]["Enums"]["processing_status"]
            | null
          regulatory_agencies?:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          related_documents?: string[] | null
          review_due_date?: string | null
          reviewer_id?: string | null
          risk_level?: Database["public"]["Enums"]["risk_level"] | null
          status?: Database["public"]["Enums"]["document_status"]
          supersedes_document_id?: string | null
          title: string
          updated_at?: string | null
          updated_by?: string | null
          version?: string
        }
        Update: {
          ai_analysis_results?: Json | null
          approval_comments?: string | null
          approved_at?: string | null
          approved_by?: string | null
          approver_id?: string | null
          assigned_to?: string[] | null
          compliance_frameworks?:
            | Database["public"]["Enums"]["compliance_framework"][]
            | null
          compliance_score?: number | null
          content?: string | null
          created_at?: string | null
          created_by?: string
          deadline?: string | null
          description?: string | null
          document_number?: string | null
          document_type?: Database["public"]["Enums"]["document_type"]
          effective_date?: string | null
          expiry_date?: string | null
          file_hash?: string | null
          file_path?: string | null
          file_size?: number | null
          gxp_relevant?: boolean | null
          id?: string
          keywords?: string[] | null
          mime_type?: string | null
          next_review_date?: string | null
          organization_id?: string
          parent_document_id?: string | null
          priority?: string | null
          processing_status?:
            | Database["public"]["Enums"]["processing_status"]
            | null
          regulatory_agencies?:
            | Database["public"]["Enums"]["regulatory_agency"][]
            | null
          related_documents?: string[] | null
          review_due_date?: string | null
          reviewer_id?: string | null
          risk_level?: Database["public"]["Enums"]["risk_level"] | null
          status?: Database["public"]["Enums"]["document_status"]
          supersedes_document_id?: string | null
          title?: string
          updated_at?: string | null
          updated_by?: string | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "regulatory_documents_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_approver_id_fkey"
            columns: ["approver_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_parent_document_id_fkey"
            columns: ["parent_document_id"]
            isOneToOne: false
            referencedRelation: "regulatory_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_reviewer_id_fkey"
            columns: ["reviewer_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_supersedes_document_id_fkey"
            columns: ["supersedes_document_id"]
            isOneToOne: false
            referencedRelation: "regulatory_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "regulatory_documents_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          action: string
          created_at: string | null
          created_by: string | null
          id: string
          organization_id: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
          scope: Json | null
        }
        Insert: {
          action: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          organization_id: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
          scope?: Json | null
        }
        Update: {
          action?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          organization_id?: string
          resource?: string
          role?: Database["public"]["Enums"]["user_role"]
          scope?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          account_locked_until: string | null
          achievements: Json | null
          activity_log: Json | null
          bio: string | null
          compliance_settings: Json | null
          created_at: string | null
          created_by: string | null
          department: string | null
          electronic_signature_enabled: boolean | null
          email: string
          email_verified: boolean | null
          emergency_contact: Json | null
          employee_id: string | null
          failed_login_attempts: number | null
          full_name: string
          gxp_training_completed: boolean | null
          gxp_training_date: string | null
          gxp_training_expiry: string | null
          id: string
          is_active: boolean | null
          job_title: string | null
          last_login: string | null
          login_count: number | null
          notification_preferences: Json | null
          notification_settings: Json | null
          organization_id: string
          password_changed_at: string | null
          phone: string | null
          phone_verified: boolean | null
          preferences: Json | null
          role: Database["public"]["Enums"]["user_role"]
          security_settings: Json | null
          signature_meaning: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_locked_until?: string | null
          achievements?: Json | null
          activity_log?: Json | null
          bio?: string | null
          compliance_settings?: Json | null
          created_at?: string | null
          created_by?: string | null
          department?: string | null
          electronic_signature_enabled?: boolean | null
          email: string
          email_verified?: boolean | null
          emergency_contact?: Json | null
          employee_id?: string | null
          failed_login_attempts?: number | null
          full_name: string
          gxp_training_completed?: boolean | null
          gxp_training_date?: string | null
          gxp_training_expiry?: string | null
          id: string
          is_active?: boolean | null
          job_title?: string | null
          last_login?: string | null
          login_count?: number | null
          notification_preferences?: Json | null
          notification_settings?: Json | null
          organization_id: string
          password_changed_at?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          preferences?: Json | null
          role?: Database["public"]["Enums"]["user_role"]
          security_settings?: Json | null
          signature_meaning?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_locked_until?: string | null
          achievements?: Json | null
          activity_log?: Json | null
          bio?: string | null
          compliance_settings?: Json | null
          created_at?: string | null
          created_by?: string | null
          department?: string | null
          electronic_signature_enabled?: boolean | null
          email?: string
          email_verified?: boolean | null
          emergency_contact?: Json | null
          employee_id?: string | null
          failed_login_attempts?: number | null
          full_name?: string
          gxp_training_completed?: boolean | null
          gxp_training_date?: string | null
          gxp_training_expiry?: string | null
          id?: string
          is_active?: boolean | null
          job_title?: string | null
          last_login?: string | null
          login_count?: number | null
          notification_preferences?: Json | null
          notification_settings?: Json | null
          organization_id?: string
          password_changed_at?: string | null
          phone?: string | null
          phone_verified?: boolean | null
          preferences?: Json | null
          role?: Database["public"]["Enums"]["user_role"]
          security_settings?: Json | null
          signature_meaning?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_role_assignments: {
        Row: {
          assigned_at: string | null
          assigned_by: string
          expires_at: string | null
          id: string
          is_active: boolean | null
          justification: string
          organization_id: string
          role: Database["public"]["Enums"]["user_role"]
          scope: Json | null
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          justification: string
          organization_id: string
          role: Database["public"]["Enums"]["user_role"]
          scope?: Json | null
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          justification?: string
          organization_id?: string
          role?: Database["public"]["Enums"]["user_role"]
          scope?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_role_assignments_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_role_assignments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_role_assignments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_electronic_signature: {
        Args: {
          p_organization_id: string
          p_document_id: string
          p_signer_id: string
          p_signature_type: Database["public"]["Enums"]["signature_type"]
          p_signature_meaning: string
          p_signature_reason?: string
          p_authentication_method?: Database["public"]["Enums"]["authentication_method"]
          p_signature_metadata?: Json
        }
        Returns: string
      }
      generate_data_integrity_hash: {
        Args: { data: Json }
        Returns: string
      }
      get_signature_details: {
        Args: { signature_id: string }
        Returns: {
          id: string
          organization_name: string
          signer_name: string
          signature_type: Database["public"]["Enums"]["signature_type"]
          signature_meaning: string
          authentication_method: Database["public"]["Enums"]["authentication_method"]
          signed_at: string
          document_title: string
          is_valid: boolean
        }[]
      }
      gtrgm_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      gtrgm_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      handle_failed_login: {
        Args: { user_email: string }
        Returns: undefined
      }
      is_account_locked: {
        Args: { user_email: string }
        Returns: boolean
      }
      log_audit_event: {
        Args: {
          p_organization_id: string
          p_user_id: string
          p_action_type: Database["public"]["Enums"]["audit_action_type"]
          p_action_description: string
          p_resource_type: string
          p_resource_id?: string
          p_resource_name?: string
          p_old_values?: Json
          p_new_values?: Json
          p_context?: Json
        }
        Returns: string
      }
      set_limit: {
        Args: { "": number }
        Returns: number
      }
      setup_compliance_alert_trigger: {
        Args: { org_id: string; channel_name: string; alert_thresholds?: Json }
        Returns: boolean
      }
      setup_document_processing_trigger: {
        Args: {
          org_id: string
          channel_name: string
          processing_stages?: string[]
        }
        Returns: boolean
      }
      show_limit: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      show_trgm: {
        Args: { "": string }
        Returns: string[]
      }
      unaccent: {
        Args: { "": string }
        Returns: string
      }
      unaccent_init: {
        Args: { "": unknown }
        Returns: unknown
      }
      update_user_login: {
        Args: { user_id: string }
        Returns: undefined
      }
      user_has_permission: {
        Args: { user_id: string; permission_name: string; action: string }
        Returns: boolean
      }
      validate_organization: {
        Args: { org_id: string }
        Returns: boolean
      }
      verify_audit_integrity: {
        Args: { start_date?: string; end_date?: string }
        Returns: {
          total_records: number
          integrity_violations: number
          verification_status: string
        }[]
      }
      verify_signature_integrity: {
        Args: { signature_id: string }
        Returns: boolean
      }
    }
    Enums: {
      audit_action_type:
        | "create"
        | "read"
        | "update"
        | "delete"
        | "approve"
        | "reject"
        | "sign"
        | "login"
        | "logout"
        | "export"
        | "print"
        | "system_event"
        | "security_event"
      authentication_method:
        | "password"
        | "biometric"
        | "token"
        | "certificate"
        | "multi_factor"
      compliance_framework:
        | "fda_21_cfr_part_11"
        | "eu_gmp"
        | "ich_q7"
        | "iso_13485"
        | "hipaa"
        | "gdpr"
        | "gxp"
        | "cdsco"
        | "who_gmp"
      document_status:
        | "draft"
        | "under_review"
        | "pending_approval"
        | "approved"
        | "effective"
        | "superseded"
        | "obsolete"
        | "withdrawn"
        | "archived"
      document_type:
        | "sop"
        | "protocol"
        | "report"
        | "specification"
        | "validation"
        | "deviation"
        | "capa"
        | "change_control"
        | "training"
        | "audit"
        | "regulatory_filing"
        | "batch_record"
        | "investigation"
        | "risk_assessment"
      processing_status:
        | "pending"
        | "processing"
        | "completed"
        | "failed"
        | "requires_review"
        | "on_hold"
      regulatory_agency:
        | "fda"
        | "ema"
        | "cdsco"
        | "health_canada"
        | "tga"
        | "pmda"
        | "who"
        | "ich"
      risk_level: "low" | "medium" | "high" | "critical"
      signature_type:
        | "approval"
        | "review"
        | "witness"
        | "author"
        | "quality_approval"
        | "regulatory_approval"
      user_role:
        | "super_admin"
        | "org_admin"
        | "quality_manager"
        | "regulatory_lead"
        | "compliance_officer"
        | "document_reviewer"
        | "analyst"
        | "auditor"
        | "viewer"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      audit_action_type: [
        "create",
        "read",
        "update",
        "delete",
        "approve",
        "reject",
        "sign",
        "login",
        "logout",
        "export",
        "print",
        "system_event",
        "security_event",
      ],
      authentication_method: [
        "password",
        "biometric",
        "token",
        "certificate",
        "multi_factor",
      ],
      compliance_framework: [
        "fda_21_cfr_part_11",
        "eu_gmp",
        "ich_q7",
        "iso_13485",
        "hipaa",
        "gdpr",
        "gxp",
        "cdsco",
        "who_gmp",
      ],
      document_status: [
        "draft",
        "under_review",
        "pending_approval",
        "approved",
        "effective",
        "superseded",
        "obsolete",
        "withdrawn",
        "archived",
      ],
      document_type: [
        "sop",
        "protocol",
        "report",
        "specification",
        "validation",
        "deviation",
        "capa",
        "change_control",
        "training",
        "audit",
        "regulatory_filing",
        "batch_record",
        "investigation",
        "risk_assessment",
      ],
      processing_status: [
        "pending",
        "processing",
        "completed",
        "failed",
        "requires_review",
        "on_hold",
      ],
      regulatory_agency: [
        "fda",
        "ema",
        "cdsco",
        "health_canada",
        "tga",
        "pmda",
        "who",
        "ich",
      ],
      risk_level: ["low", "medium", "high", "critical"],
      signature_type: [
        "approval",
        "review",
        "witness",
        "author",
        "quality_approval",
        "regulatory_approval",
      ],
      user_role: [
        "super_admin",
        "org_admin",
        "quality_manager",
        "regulatory_lead",
        "compliance_officer",
        "document_reviewer",
        "analyst",
        "auditor",
        "viewer",
      ],
    },
  },
} as const

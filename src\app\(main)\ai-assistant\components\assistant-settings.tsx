'use client'

import { <PERSON><PERSON> } from 'lucide-react'

import { Avatar, AvatarFallback } from '@/components/ui-radix/avatar'
import { Button } from '@/components/ui-radix/button'

import type { QuickAction } from '../types'

interface AssistantSettingsProps {
  readonly isOnline?: boolean;
  readonly onNewChat?: (() => void) | undefined;
  readonly quickActions?: readonly QuickAction[];
  readonly onQuickAction?: ((action: string) => void) | undefined;
}

export function AssistantSettings({
  isOnline = true,
  onNewChat,
  quickActions = [],
  onQuickAction,
}: AssistantSettingsProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <Avatar className="rounded-full h-8 w-8 overflow-hidden bg-primary text-primary-foreground">
          <AvatarFallback className="bg-primary text-primary-foreground">
            <Bot className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="font-semibold text-base">Compliance AI Assistant</h3>
          <p className="text-xs text-muted-foreground">
            {isOnline ? 'Ready to help with regulatory compliance questions' : 'Currently offline'}
          </p>
        </div>
      </div>

      <div className="flex space-x-1">
        {/* Quick Actions in Header - Show first 2 actions */}
        {quickActions.slice(0, 2).map((action, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            className="inline-flex items-center gap-2 text-sm font-medium h-8 px-2"
            onClick={() => onQuickAction?.(action.action)}
            aria-label={action.description}
          >
            <action.icon className="h-4 w-4" />
            <span className="text-xs">{action.label}</span>
          </Button>
        ))}

        {/* New Chat Button */}
        {onNewChat && (
          <Button
            variant="outline"
            size="sm"
            className="inline-flex items-center gap-2 text-sm font-medium h-8 px-2"
            onClick={onNewChat}
            aria-label="Start new chat"
          >
            New Chat
          </Button>
        )}
      </div>
    </div>
  )
}

-- Safe deployment of electronic_signatures table
-- Checks for existing components before creating

-- Check and create signature_type enum if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'signature_type') THEN
        CREATE TYPE signature_type AS ENUM (
            'approval',         -- Document approval signature
            'review',           -- Document review signature
            'witness',          -- Witness signature
            'author',           -- Author signature
            'quality_approval', -- Quality department approval
            'regulatory_approval' -- Regulatory approval
        );
        RAISE NOTICE 'Created signature_type enum';
    ELSE
        RAISE NOTICE 'signature_type enum already exists';
    END IF;
END $$;

-- Check and create authentication_method enum if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'authentication_method') THEN
        CREATE TYPE authentication_method AS ENUM (
            'password',         -- Password-based authentication
            'biometric',        -- Biometric authentication
            'token',           -- Hardware/software token
            'certificate',     -- Digital certificate
            'multi_factor'     -- Multi-factor authentication
        );
        RAISE NOTICE 'Created authentication_method enum';
    ELSE
        RAISE NOTICE 'authentication_method enum already exists';
    END IF;
END $$;

-- Check and create electronic_signatures table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'electronic_signatures' AND table_schema = 'public') THEN
        CREATE TABLE electronic_signatures (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
            
            -- Signature identification
            document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE,
            document_version_id UUID REFERENCES document_versions(id) ON DELETE CASCADE,
            audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE,
            
            -- Signer information
            signer_id UUID NOT NULL REFERENCES user_profiles(id),
            signer_name VARCHAR(255) NOT NULL,
            signer_title VARCHAR(255),
            signer_department VARCHAR(100),
            
            -- Signature details
            signature_type signature_type NOT NULL,
            signature_meaning TEXT NOT NULL, -- What the signature represents
            signature_reason TEXT, -- Reason for signing
            
            -- Authentication and security
            authentication_method authentication_method NOT NULL,
            authentication_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            signature_hash VARCHAR(512) NOT NULL, -- Cryptographic hash of signature
            document_hash_at_signing VARCHAR(512), -- Document hash when signed
            
            -- Timestamps
            signed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            
            -- Metadata
            signature_metadata JSONB DEFAULT '{}',
            ip_address INET,
            user_agent TEXT,
            
            -- Constraints
            CONSTRAINT valid_signature_hash CHECK (LENGTH(signature_hash) >= 64),
            CONSTRAINT valid_authentication_timestamp CHECK (authentication_timestamp <= signed_at)
        );
        
        -- Enable RLS
        ALTER TABLE electronic_signatures ENABLE ROW LEVEL SECURITY;
        
        RAISE NOTICE 'Created electronic_signatures table';
    ELSE
        RAISE NOTICE 'electronic_signatures table already exists';
    END IF;
END $$;

-- Create RLS policies if they don't exist
DO $$ 
BEGIN
    -- Policy 1: Users can view signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can view signatures in their organization') THEN
        CREATE POLICY "Users can view signatures in their organization"
            ON electronic_signatures FOR SELECT
            USING (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
            );
        RAISE NOTICE 'Created view policy for electronic_signatures';
    END IF;

    -- Policy 2: Users can create signatures in their organization
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Users can create signatures in their organization') THEN
        CREATE POLICY "Users can create signatures in their organization"
            ON electronic_signatures FOR INSERT
            WITH CHECK (
                organization_id IN (
                    SELECT organization_id 
                    FROM user_profiles 
                    WHERE id = auth.uid()
                )
                AND signer_id = auth.uid()
            );
        RAISE NOTICE 'Created insert policy for electronic_signatures';
    END IF;

    -- Policy 3: Electronic signatures are immutable
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures are immutable') THEN
        CREATE POLICY "Electronic signatures are immutable"
            ON electronic_signatures FOR UPDATE
            USING (false);
        RAISE NOTICE 'Created update policy for electronic_signatures';
    END IF;

    -- Policy 4: Electronic signatures cannot be deleted
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'electronic_signatures' AND policyname = 'Electronic signatures cannot be deleted') THEN
        CREATE POLICY "Electronic signatures cannot be deleted"
            ON electronic_signatures FOR DELETE
            USING (false);
        RAISE NOTICE 'Created delete policy for electronic_signatures';
    END IF;
END $$;

-- Create indexes if they don't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_organization') THEN
        CREATE INDEX idx_electronic_signatures_organization ON electronic_signatures(organization_id);
        RAISE NOTICE 'Created organization index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_document') THEN
        CREATE INDEX idx_electronic_signatures_document ON electronic_signatures(document_id);
        RAISE NOTICE 'Created document index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signer') THEN
        CREATE INDEX idx_electronic_signatures_signer ON electronic_signatures(signer_id);
        RAISE NOTICE 'Created signer index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_signed_at') THEN
        CREATE INDEX idx_electronic_signatures_signed_at ON electronic_signatures(signed_at);
        RAISE NOTICE 'Created signed_at index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_type') THEN
        CREATE INDEX idx_electronic_signatures_type ON electronic_signatures(signature_type);
        RAISE NOTICE 'Created signature_type index';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_electronic_signatures_metadata') THEN
        CREATE INDEX idx_electronic_signatures_metadata ON electronic_signatures USING GIN (signature_metadata);
        RAISE NOTICE 'Created metadata GIN index';
    END IF;
END $$;

-- Create functions if they don't exist
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(version_number::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Create electronic signature
    INSERT INTO electronic_signatures (
        id,
        organization_id,
        document_id,
        signer_id,
        signer_name,
        signature_type,
        signature_meaning,
        signature_reason,
        authentication_method,
        signature_hash,
        document_hash_at_signing,
        signature_metadata
    ) VALUES (
        gen_random_uuid(),
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        p_signature_reason,
        p_authentication_method,
        signature_hash,
        document_hash,
        p_signature_metadata
    ) RETURNING id INTO signature_id;
    
    -- Log audit event if log_audit_event function exists
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'log_audit_event') THEN
        PERFORM log_audit_event(
            p_organization_id,
            p_signer_id,
            'sign'::audit_action_type,
            'Electronic signature created: ' || p_signature_meaning,
            'signature',
            signature_id,
            'Electronic Signature',
            NULL,
            jsonb_build_object(
                'signature_type', p_signature_type,
                'authentication_method', p_authentication_method,
                'document_id', p_document_id
            )
        );
    END IF;
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create signature verification function
CREATE OR REPLACE FUNCTION verify_signature_integrity(signature_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    sig_record RECORD;
BEGIN
    -- Get signature record
    SELECT * INTO sig_record
    FROM electronic_signatures
    WHERE id = signature_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Verify signature hash format
    IF LENGTH(sig_record.signature_hash) < 64 THEN
        RETURN FALSE;
    END IF;
    
    -- Additional integrity checks can be added here
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT, INSERT ON electronic_signatures TO authenticated;
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;
GRANT EXECUTE ON FUNCTION verify_signature_integrity TO authenticated;

-- Final verification
SELECT 
    'ELECTRONIC SIGNATURES DEPLOYMENT' as status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'electronic_signatures')
        THEN '✅ COMPLETE'
        ELSE '❌ FAILED'
    END as table_status,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'electronic_signatures') as column_count,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'electronic_signatures') as policy_count;

export interface RegulatoryUpdate {
  readonly id: number;
  readonly title: string;
  readonly agency: string;
  readonly category: string;
  readonly publishedDate: string;
  readonly severity: 'critical' | 'high' | 'medium' | 'low';
  readonly summary: string;
  readonly url: string;
  readonly tags: readonly string[];
  readonly estimatedImpact: string;
  readonly deadline?: string;
}

export interface UpdateMetric {
  readonly label: string;
  readonly value: string;
  readonly color: string;
}

export interface SearchFilters {
  readonly searchQuery: string;
  readonly selectedAgency: string;
  readonly selectedSeverity: string;
  readonly sortBy: string;
}

export interface UpdateActions {
  readonly onBookmark: (updateId: number) => void;
  readonly onShare: (update: RegulatoryUpdate) => void;
  readonly onView: (update: RegulatoryUpdate) => void;
  readonly onExport: (updates: readonly RegulatoryUpdate[]) => void;
}

export type TabValue = 'all' | 'critical' | 'action-required' | 'bookmarked';

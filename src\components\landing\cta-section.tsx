import { <PERSON><PERSON><PERSON>, CheckCircle, Play } from 'lucide-react'

import { Button } from '@/components/ui-radix/button'

export function CtaSection() {
  const handleGetStarted = () => {
    // TODO: Replace with Next.js navigation to login
    console.log('Navigate to login - placeholder for Next.js router')
  }

  const handleScheduleDemo = () => {
    window.open(
      'mailto:<EMAIL>?subject=Schedule Demo Request&body=I would like to schedule a demo of the AI Compliance Platform.',
      '_blank',
    )
  }

  return (
    <section className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-700 to-blue-700"></div>
      <div className="absolute inset-0 bg-black/20"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8 leading-tight">
            Ready to Transform Your
            <br />
            <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              Compliance Process?
            </span>
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
            Join the pharmaceutical revolution. Start your free trial today and
            experience the future of regulatory compliance management with
            AI-powered intelligence.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Button
              size="lg"
              variant="secondary"
              className="text-lg px-10 py-6 bg-card text-purple-700 hover:bg-card/90 shadow-2xl hover:shadow-purple-200/20 transition-all duration-300 transform hover:scale-105 font-bold"
              onClick={handleGetStarted}
            >
              Start Free Trial
              <ArrowRight className="ml-2 h-6 w-6" />
            </Button>
            <div
              className="inline-flex items-center justify-center text-lg px-10 py-6 bg-transparent border-2 border-border text-white hover:bg-white/10 backdrop-blur-sm font-medium transition-all duration-200 rounded-lg cursor-pointer"
              onClick={handleScheduleDemo}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => e.key === 'Enter' && handleScheduleDemo()}
            >
              <Play className="mr-2 h-5 w-5" style={{ color: 'white' }} />
              <span style={{ color: 'white' }}>Schedule Demo</span>
            </div>
          </div>

          <div className="inline-flex items-center space-x-6 text-white/80 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>14-day free trial</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>No credit card required</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>Full feature access</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

#!/usr/bin/env python3
"""VCP_001 Implementation Validation Script.

This script validates that VCP_001: Database Schema Design & Implementation
is correctly implemented and all components are working as expected.

Usage:
    python validate_vcp001.py [--verbose] [--fix-issues]

Options:
    --verbose: Enable detailed logging
    --fix-issues: Attempt to fix detected issues automatically
"""

import argparse
import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL: str = os.getenv('DATABASE_URL') or ""
if not DATABASE_URL:
    logger.error("DATABASE_URL environment variable is required")
    sys.exit(1)


class VCP001Validator:
    """VCP_001 implementation validator."""

    def __init__(self, database_url: str, verbose: bool = False, fix_issues: bool = False):
        self.database_url = database_url
        self.verbose = verbose
        self.fix_issues = fix_issues
        self.connection: Optional[asyncpg.Connection] = None
        self.validation_results = {
            "overall_status": "UNKNOWN",
            "timestamp": datetime.utcnow().isoformat(),
            "tests_passed": 0,
            "tests_failed": 0,
            "issues_found": [],
            "issues_fixed": [],
            "recommendations": []
        }

        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)

    async def connect(self) -> None:
        """Establish database connection."""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logger.info("Connected to database successfully")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    async def disconnect(self) -> None:
        """Close database connection."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from database")

    def _ensure_connection(self) -> asyncpg.Connection:
        """Ensure connection is available, raise error if not."""
        if not self.connection:
            raise RuntimeError("Database connection not available")
        return self.connection

    def log_test_result(self, test_name: str, passed: bool, message: str = "", issue: str = ""):
        """Log test result and update validation results."""
        if passed:
            self.validation_results["tests_passed"] += 1
            logger.info(f"✅ {test_name}: PASSED {message}")
        else:
            self.validation_results["tests_failed"] += 1
            logger.error(f"❌ {test_name}: FAILED {message}")
            if issue:
                self.validation_results["issues_found"].append({
                    "test": test_name,
                    "issue": issue,
                    "message": message
                })

    async def test_database_connection(self) -> bool:
        """Test basic database connectivity."""
        if not self.connection:
            self.log_test_result("Database Connection", False, "No connection available", "Connection not established")
            return False

        try:
            result = await self.connection.fetchval("SELECT 1")
            self.log_test_result("Database Connection", result == 1)
            return result == 1
        except Exception as e:
            self.log_test_result("Database Connection", False, str(e), "Connection failed")
            return False

    async def test_required_tables(self) -> bool:
        """Test that all required tables exist."""
        required_tables = [
            'organizations',
            'user_profiles',
            'user_roles',
            'regulatory_documents',
            'document_versions',
            'compliance_frameworks',
            'document_compliance_assessments',
            'audit_trail',
            'compliance_log',
            'schema_migrations'
        ]

        missing_tables = []

        try:
            conn = self._ensure_connection()
            for table in required_tables:
                exists = await conn.fetchval(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1",
                    table
                )
                if not exists:
                    missing_tables.append(table)

            if missing_tables:
                self.log_test_result(
                    "Required Tables",
                    False,
                    f"Missing tables: {', '.join(missing_tables)}",
                    "Missing required tables"
                )
                return False
            else:
                self.log_test_result("Required Tables", True, f"All {len(required_tables)} tables found")
                return True

        except Exception as e:
            self.log_test_result("Required Tables", False, str(e), "Query failed")
            return False

    async def test_required_functions(self) -> bool:
        """Test that all required functions exist."""
        required_functions = [
            'update_updated_at_column',
            'handle_new_user',
            'update_last_login',
            'check_role_expiration',
            'get_user_permissions',
            'verify_user_permission',
            'calculate_compliance_score',
            'auto_calculate_risk_level',
            'update_content_vector',
            'create_document_version',
            'get_document_version_history',
            'restore_document_version',
            'assess_document_compliance',
            'log_audit_event',
            'log_compliance_event',
            'cleanup_audit_trail',
            'trigger_audit_document_changes'
        ]

        missing_functions = []

        try:
            for function in required_functions:
                exists = await self.connection.fetchval(
                    "SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = $1",
                    function
                )
                if not exists:
                    missing_functions.append(function)

            if missing_functions:
                self.log_test_result(
                    "Required Functions",
                    False,
                    f"Missing functions: {', '.join(missing_functions)}",
                    "Missing required functions"
                )
                return False
            else:
                self.log_test_result("Required Functions", True, f"All {len(required_functions)} functions found")
                return True

        except Exception as e:
            self.log_test_result("Required Functions", False, str(e), "Query failed")
            return False

    async def test_required_views(self) -> bool:
        """Test that all required views exist."""
        required_views = [
            'user_permissions_view',
            'document_analytics_view',
            'document_version_comparison_view',
            'compliance_dashboard_view',
            'audit_trail_report_view',
            'compliance_report_view'
        ]

        missing_views = []

        try:
            for view in required_views:
                exists = await self.connection.fetchval(
                    "SELECT COUNT(*) FROM information_schema.views WHERE table_name = $1",
                    view
                )
                if not exists:
                    missing_views.append(view)

            if missing_views:
                self.log_test_result(
                    "Required Views",
                    False,
                    f"Missing views: {', '.join(missing_views)}",
                    "Missing required views"
                )
                return False
            else:
                self.log_test_result("Required Views", True, f"All {len(required_views)} views found")
                return True

        except Exception as e:
            self.log_test_result("Required Views", False, str(e), "Query failed")
            return False

    async def test_rls_policies(self) -> bool:
        """Test that Row Level Security policies are enabled."""
        tables_with_rls = [
            'organizations',
            'user_profiles',
            'user_roles',
            'regulatory_documents',
            'document_versions',
            'compliance_frameworks',
            'document_compliance_assessments',
            'audit_trail',
            'compliance_log'
        ]

        missing_rls = []

        try:
            for table in tables_with_rls:
                rls_enabled = await self.connection.fetchval(
                    "SELECT relrowsecurity FROM pg_class WHERE relname = $1",
                    table
                )
                if not rls_enabled:
                    missing_rls.append(table)

            if missing_rls:
                self.log_test_result(
                    "RLS Policies",
                    False,
                    f"RLS not enabled on: {', '.join(missing_rls)}",
                    "Missing RLS policies"
                )
                return False
            else:
                self.log_test_result("RLS Policies", True, f"RLS enabled on all {len(tables_with_rls)} tables")
                return True

        except Exception as e:
            self.log_test_result("RLS Policies", False, str(e), "Query failed")
            return False

    async def test_triggers(self) -> bool:
        """Test that required triggers exist."""
        required_triggers = [
            ('organizations', 'update_organizations_updated_at'),
            ('user_profiles', 'update_user_profiles_updated_at'),
            ('user_profiles', 'on_auth_user_created'),
            ('user_roles', 'update_user_roles_updated_at'),
            ('regulatory_documents', 'update_regulatory_documents_updated_at'),
            ('regulatory_documents', 'update_document_content_vector'),
            ('regulatory_documents', 'audit_document_changes'),
            ('document_versions', 'update_document_versions_updated_at'),
            ('document_versions', 'create_initial_version'),
            ('compliance_frameworks', 'update_compliance_frameworks_updated_at'),
            ('document_compliance_assessments', 'update_document_compliance_assessments_updated_at')
        ]

        missing_triggers = []

        try:
            for table, trigger in required_triggers:
                exists = await self.connection.fetchval(
                    """
                    SELECT COUNT(*) FROM information_schema.triggers
                    WHERE event_object_table = $1 AND trigger_name = $2
                    """,
                    table, trigger
                )
                if not exists:
                    missing_triggers.append(f"{table}.{trigger}")

            if missing_triggers:
                self.log_test_result(
                    "Required Triggers",
                    False,
                    f"Missing triggers: {', '.join(missing_triggers)}",
                    "Missing required triggers"
                )
                return False
            else:
                self.log_test_result("Required Triggers", True, f"All {len(required_triggers)} triggers found")
                return True

        except Exception as e:
            self.log_test_result("Required Triggers", False, str(e), "Query failed")
            return False

    async def test_initial_data(self) -> bool:
        """Test that initial data is properly inserted."""
        try:
            # Check for development organization
            org_count = await self.connection.fetchval(
                "SELECT COUNT(*) FROM organizations WHERE domain = 'dev.vigilens.ai'"
            )

            # Check for compliance frameworks
            framework_count = await self.connection.fetchval(
                "SELECT COUNT(*) FROM compliance_frameworks"
            )

            issues = []
            if org_count == 0:
                issues.append("No development organization found")
            if framework_count == 0:
                issues.append("No compliance frameworks found")

            if issues:
                self.log_test_result(
                    "Initial Data",
                    False,
                    f"Issues: {', '.join(issues)}",
                    "Missing initial data"
                )
                return False
            else:
                self.log_test_result(
                    "Initial Data",
                    True,
                    f"Found {org_count} org(s), {framework_count} framework(s)"
                )
                return True

        except Exception as e:
            self.log_test_result("Initial Data", False, str(e), "Query failed")
            return False

    async def test_migration_tracking(self) -> bool:
        """Test that migration tracking is working."""
        try:
            migration_count = await self.connection.fetchval(
                "SELECT COUNT(*) FROM schema_migrations WHERE success = true"
            )

            if migration_count < 7:  # Should have at least 7 migrations for VCP_001
                self.log_test_result(
                    "Migration Tracking",
                    False,
                    f"Only {migration_count} successful migrations found",
                    "Incomplete migrations"
                )
                return False
            else:
                self.log_test_result(
                    "Migration Tracking",
                    True,
                    f"Found {migration_count} successful migrations"
                )
                return True

        except Exception as e:
            self.log_test_result("Migration Tracking", False, str(e), "Query failed")
            return False

    async def test_data_integrity(self) -> bool:
        """Test basic data integrity constraints."""
        try:
            # Test foreign key constraints
            constraints = await self.connection.fetch(
                """
                SELECT conname, contype FROM pg_constraint
                WHERE contype IN ('f', 'p', 'u', 'c')
                ORDER BY conname
                """
            )

            constraint_count = len(constraints)

            if constraint_count < 20:  # Should have many constraints
                self.log_test_result(
                    "Data Integrity",
                    False,
                    f"Only {constraint_count} constraints found",
                    "Insufficient constraints"
                )
                return False
            else:
                self.log_test_result(
                    "Data Integrity",
                    True,
                    f"Found {constraint_count} integrity constraints"
                )
                return True

        except Exception as e:
            self.log_test_result("Data Integrity", False, str(e), "Query failed")
            return False

    async def test_performance_indexes(self) -> bool:
        """Test that performance indexes exist."""
        try:
            indexes = await self.connection.fetch(
                """
                SELECT indexname FROM pg_indexes
                WHERE schemaname = 'public'
                ORDER BY indexname
                """
            )

            index_count = len(indexes)

            if index_count < 15:  # Should have many indexes for performance
                self.log_test_result(
                    "Performance Indexes",
                    False,
                    f"Only {index_count} indexes found",
                    "Insufficient indexes"
                )
                return False
            else:
                self.log_test_result(
                    "Performance Indexes",
                    True,
                    f"Found {index_count} performance indexes"
                )
                return True

        except Exception as e:
            self.log_test_result("Performance Indexes", False, str(e), "Query failed")
            return False

    async def test_file_structure(self) -> bool:
        """Test that required files exist in the backend directory."""
        backend_dir = Path(__file__).parent

        required_files = [
            'main.py',
            'init_database.py',
            'requirements.txt',
            '.env.example',
            'README.md',
            'models/compliance.py',
            'models/audit.py'
        ]

        required_dirs = [
            'migrations',
            'models'
        ]

        missing_files = []
        missing_dirs = []

        # Check files
        for file_path in required_files:
            if not (backend_dir / file_path).exists():
                missing_files.append(file_path)

        # Check directories
        for dir_path in required_dirs:
            if not (backend_dir / dir_path).is_dir():
                missing_dirs.append(dir_path)

        issues = []
        if missing_files:
            issues.append(f"Missing files: {', '.join(missing_files)}")
        if missing_dirs:
            issues.append(f"Missing directories: {', '.join(missing_dirs)}")

        if issues:
            self.log_test_result(
                "File Structure",
                False,
                '; '.join(issues),
                "Missing required files/directories"
            )
            return False
        else:
            self.log_test_result(
                "File Structure",
                True,
                f"All {len(required_files)} files and {len(required_dirs)} directories found"
            )
            return True

    async def run_all_tests(self) -> Dict:
        """Run all validation tests."""
        logger.info("Starting VCP_001 validation tests...")
        logger.info("=" * 60)

        # List of all test methods
        tests = [
            self.test_database_connection,
            self.test_required_tables,
            self.test_required_functions,
            self.test_required_views,
            self.test_rls_policies,
            self.test_triggers,
            self.test_initial_data,
            self.test_migration_tracking,
            self.test_data_integrity,
            self.test_performance_indexes,
            self.test_file_structure
        ]

        # Run all tests
        for test in tests:
            try:
                await test()
            except Exception as e:
                logger.error(f"Test {test.__name__} failed with exception: {e}")
                self.validation_results["tests_failed"] += 1

        # Determine overall status
        total_tests = self.validation_results["tests_passed"] + self.validation_results["tests_failed"]
        success_rate = self.validation_results["tests_passed"] / total_tests if total_tests > 0 else 0

        if success_rate == 1.0:
            self.validation_results["overall_status"] = "PASSED"
        elif success_rate >= 0.8:
            self.validation_results["overall_status"] = "MOSTLY_PASSED"
        else:
            self.validation_results["overall_status"] = "FAILED"

        # Add recommendations
        if self.validation_results["issues_found"]:
            self.validation_results["recommendations"].extend([
                "Review failed tests and fix identified issues",
                "Run 'python init_database.py --verify' for detailed schema validation",
                "Check migration files in migrations/ directory",
                "Verify environment variables in .env file"
            ])

        logger.info("=" * 60)
        logger.info(f"VCP_001 Validation Complete: {self.validation_results['overall_status']}")
        logger.info(f"Tests Passed: {self.validation_results['tests_passed']}")
        logger.info(f"Tests Failed: {self.validation_results['tests_failed']}")
        logger.info(f"Success Rate: {success_rate:.1%}")

        return self.validation_results


async def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(description='Validate VCP_001 implementation')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--fix-issues', action='store_true', help='Attempt to fix issues automatically')
    parser.add_argument('--output', help='Output results to JSON file')

    args = parser.parse_args()

    # Initialize validator
    validator = VCP001Validator(DATABASE_URL, args.verbose, args.fix_issues)

    try:
        # Connect to database
        await validator.connect()

        # Run validation tests
        results = await validator.run_all_tests()

        # Output results to file if requested
        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Results written to {args.output}")

        # Return appropriate exit code
        if results["overall_status"] == "PASSED":
            return 0
        elif results["overall_status"] == "MOSTLY_PASSED":
            return 1
        else:
            return 2

    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return 3

    finally:
        await validator.disconnect()


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))

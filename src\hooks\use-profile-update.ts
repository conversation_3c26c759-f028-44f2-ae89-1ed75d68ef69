'use client'

import { useState, useCallback } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { UserProfileService } from '@/lib/supabase-services'
import { toast } from 'sonner'

interface ProfileUpdateData {
  full_name?: string
  department?: string
  phone?: string
  job_title?: string
  first_name?: string
  last_name?: string
  email?: string
  role?: string
  bio?: string
  preferences?: any
  notification_preferences?: any
  security_settings?: any
  compliance_settings?: any
}

interface ValidationError {
  field: string
  message: string
}

interface UseProfileUpdateReturn {
  updateProfile: (data: ProfileUpdateData) => Promise<boolean>
  isUpdating: boolean
  validationErrors: ValidationError[]
  clearErrors: () => void
}

/**
 * Custom hook for updating user profiles with validation and error handling
 * Includes pharmaceutical compliance audit trail and proper error management
 */
export function useProfileUpdate(): UseProfileUpdateReturn {
  const { userProfile, refreshUserProfile } = useAuth()
  const [isUpdating, setIsUpdating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])

  const profileService = new UserProfileService()

  /**
   * Validate profile data before submission
   */
  const validateProfileData = useCallback((data: ProfileUpdateData): ValidationError[] => {
    const errors: ValidationError[] = []

    // Validate full name
    if (data.full_name !== undefined) {
      if (!data.full_name.trim()) {
        errors.push({ field: 'full_name', message: 'Full name is required' })
      } else if (data.full_name.trim().length < 2) {
        errors.push({ field: 'full_name', message: 'Full name must be at least 2 characters' })
      } else if (data.full_name.trim().length > 255) {
        errors.push({ field: 'full_name', message: 'Full name must be less than 255 characters' })
      }
    }

    // Validate department
    if (data.department !== undefined && data.department.trim().length > 100) {
      errors.push({ field: 'department', message: 'Department must be less than 100 characters' })
    }

    // Validate phone number
    if (data.phone !== undefined && data.phone.trim()) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.push({ field: 'phone', message: 'Please enter a valid phone number' })
      }
    }

    // Validate job title
    if (data.job_title !== undefined && data.job_title.trim().length > 200) {
      errors.push({ field: 'job_title', message: 'Job title must be less than 200 characters' })
    }

    return errors
  }, [])

  /**
   * Update user profile with validation and error handling
   */
  const updateProfile = useCallback(async (data: ProfileUpdateData): Promise<boolean> => {
    if (!userProfile) {
      toast.error('User profile not loaded')
      return false
    }

    // Clear previous errors
    setValidationErrors([])

    // Validate input data
    const errors = validateProfileData(data)
    if (errors.length > 0) {
      setValidationErrors(errors)
      toast.error('Please fix validation errors before saving')
      return false
    }

    setIsUpdating(true)

    try {
      // Clean up data - remove empty strings and trim values
      const cleanData: ProfileUpdateData = {}
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const trimmedValue = typeof value === 'string' ? value.trim() : value
          if (trimmedValue !== '') {
            cleanData[key as keyof ProfileUpdateData] = trimmedValue
          }
        }
      })

      console.log('🔄 Profile Update - Starting update:', {
        userId: userProfile.id,
        updateData: cleanData,
        timestamp: new Date().toISOString()
      })

      // Update profile via service
      const { data: updatedProfile, error } = await profileService.updateUserProfile(
        userProfile.id,
        cleanData
      )

      console.log('📊 Profile Update - Service response:', {
        success: !error,
        error: error,
        updatedProfile: updatedProfile,
        timestamp: new Date().toISOString()
      })

      if (error) {
        console.error('Profile update failed:', error)
        toast.error(error.message || 'Failed to update profile')
        return false
      }

      if (!updatedProfile) {
        toast.error('No data returned from profile update')
        return false
      }

      // Refresh user profile in auth context
      await refreshUserProfile()

      // Show success message
      toast.success('Profile updated successfully')

      return true
    } catch (error) {
      console.error('Unexpected error during profile update:', error)
      toast.error('An unexpected error occurred while updating your profile')
      return false
    } finally {
      setIsUpdating(false)
    }
  }, [userProfile, profileService, validateProfileData, refreshUserProfile])

  /**
   * Clear validation errors
   */
  const clearErrors = useCallback(() => {
    setValidationErrors([])
  }, [])

  return {
    updateProfile,
    isUpdating,
    validationErrors,
    clearErrors
  }
}

[pytest]
# Pytest configuration for VCP_024 RAG Pipeline tests

# Enable asyncio support
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Test discovery
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warning filters
filterwarnings =
    ignore::DeprecationWarning:pydantic.*
    ignore:Support for class-based.*:DeprecationWarning:pydantic.*
    ignore::UserWarning

# Test output
addopts = -v --tb=short --strict-markers

# Markers
markers =
    asyncio: marks tests as async
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    slow: marks tests as slow running
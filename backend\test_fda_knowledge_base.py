#!/usr/bin/env python3
"""
Test script for FDA Knowledge Base Enhancement - VigiLens Pharmaceutical Compliance Platform.
Tests the complete FDA CFR processing pipeline with enterprise validation.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_fda_pdf_processor():
    """Test FDA PDF processor with local CFR documents."""
    logger.info("🧪 Testing FDA PDF Processor...")
    
    try:
        from services.ai.pdf_processor import get_fda_pdf_processor
        
        processor = get_fda_pdf_processor()
        
        # Test hierarchy loading
        hierarchy = await processor.load_fda_hierarchy()
        if hierarchy:
            logger.info(f"✅ FDA hierarchy loaded: {len(hierarchy.get('children', []))} top-level sections")
        else:
            logger.warning("⚠️ FDA hierarchy not found or empty")
        
        # Test document processing
        documents = await processor.process_all_fda_documents()
        
        if documents:
            logger.info(f"✅ Processed {len(documents)} FDA CFR documents")
            
            # Show sample document info
            sample_doc = documents[0]
            logger.info(f"Sample document: {sample_doc['title']}")
            logger.info(f"Content length: {len(sample_doc['content'])} characters")
            logger.info(f"CFR Volume: {sample_doc['metadata']['cfr_volume']}")
            
            # Get processing stats
            stats = processor.get_processing_stats()
            logger.info(f"Processing stats: {stats}")
            
            return True
        else:
            logger.error("❌ No FDA documents processed")
            return False
            
    except Exception as e:
        logger.error(f"❌ FDA PDF processor test failed: {str(e)}")
        return False


async def test_fda_chunker():
    """Test FDA hierarchy-based chunker."""
    logger.info("🧪 Testing FDA Chunker...")
    
    try:
        from services.ai.pdf_processor import get_fda_pdf_processor
        from services.ai.fda_chunker import get_fda_chunker
        
        # Get sample document
        processor = get_fda_pdf_processor()
        documents = await processor.process_all_fda_documents()
        
        if not documents:
            logger.error("❌ No documents available for chunking test")
            return False
        
        # Load hierarchy and create chunker
        hierarchy = await processor.load_fda_hierarchy()
        chunker = get_fda_chunker(hierarchy)
        
        # Test chunking
        sample_doc = documents[0]
        chunks = chunker.chunk_fda_document(sample_doc)
        
        if chunks:
            logger.info(f"✅ Created {len(chunks)} chunks from sample document")
            
            # Show sample chunk info
            sample_chunk = chunks[0]
            logger.info(f"Sample chunk ID: {sample_chunk['chunk_id']}")
            logger.info(f"Chunk size: {len(sample_chunk['content'])} characters")
            logger.info(f"CFR section: {sample_chunk['metadata'].get('cfr_full_identifier', 'N/A')}")
            
            return True
        else:
            logger.error("❌ No chunks created")
            return False
            
    except Exception as e:
        logger.error(f"❌ FDA chunker test failed: {str(e)}")
        return False


async def test_fda_metadata_manager():
    """Test FDA metadata enrichment."""
    logger.info("🧪 Testing FDA Metadata Manager...")
    
    try:
        from services.ai.fda_metadata import get_fda_metadata_manager
        
        manager = get_fda_metadata_manager()
        
        # Test with sample chunk
        sample_chunk = {
            "chunk_id": "test_chunk",
            "content": """
            § 11.10 Controls for closed systems.
            Persons who use closed systems to create, modify, maintain, or transmit electronic records shall employ procedures and controls designed to ensure the authenticity, integrity, and, when appropriate, the confidentiality of electronic records, and to ensure that the signer cannot readily repudiate the signed record as not genuine.
            """,
            "metadata": {
                "cfr_volume": "vol1",
                "source": "FDA_CFR_LOCAL"
            }
        }
        
        # Enrich metadata
        enriched_chunk = manager.enrich_chunk_metadata(sample_chunk)
        
        metadata = enriched_chunk["metadata"]
        
        logger.info(f"✅ Metadata enrichment completed")
        logger.info(f"CFR Parts found: {metadata.get('cfr_parts', [])}")
        logger.info(f"Compliance frameworks: {metadata.get('compliance_frameworks', [])}")
        logger.info(f"Regulatory keywords: {list(metadata.get('regulatory_keywords', {}).keys())}")
        logger.info(f"Importance score: {metadata.get('importance_score', 0.0)}")
        logger.info(f"Categories: {metadata.get('regulatory_categories', [])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FDA metadata manager test failed: {str(e)}")
        return False


async def test_vector_store_integration():
    """Test vector store integration with FDA documents."""
    logger.info("🧪 Testing Vector Store Integration...")
    
    try:
        from services.ai.vector_store import get_vector_store, add_fda_documents
        
        # Get vector store
        vector_store = await get_vector_store()
        
        # Test with sample FDA chunks
        sample_chunks = [
            {
                "chunk_id": "test_fda_chunk_1",
                "content": "Electronic records and electronic signatures requirements under 21 CFR Part 11.",
                "metadata": {
                    "source": "FDA_CFR_LOCAL",
                    "cfr_volume": "vol1",
                    "cfr_parts": [{"part_number": "11", "title": "Electronic Records; Electronic Signatures"}],
                    "compliance_frameworks": ["fda_21_cfr_part_11"],
                    "importance_score": 0.9
                }
            }
        ]
        
        # Add to vector store
        result = await add_fda_documents(sample_chunks)
        
        if result["success"]:
            logger.info(f"✅ Added {result['documents_added']} test documents to vector store")
            
            # Test search
            search_results = await vector_store.search("electronic records validation", limit=5)
            logger.info(f"✅ Search returned {len(search_results)} results")
            
            return True
        else:
            logger.error(f"❌ Failed to add documents: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Vector store integration test failed: {str(e)}")
        return False


async def test_knowledge_base_endpoint():
    """Test the enhanced knowledge base population endpoint."""
    logger.info("🧪 Testing Knowledge Base Population Endpoint...")
    
    try:
        import httpx
        
        # Test the endpoint
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/ai/populate-knowledge-base",
                json={
                    "sources": ["local_cfr"],
                    "force_refresh": False
                },
                timeout=300.0  # 5 minutes timeout for large processing
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Endpoint test successful")
                logger.info(f"Status: {result.get('status', 'unknown')}")
                logger.info(f"Sources processed: {result.get('sources_processed', [])}")
                logger.info(f"Total documents added: {result.get('total_documents_added', 0)}")
                logger.info(f"Processing time: {result.get('processing_time', 0):.2f}s")
                
                return True
            else:
                logger.error(f"❌ Endpoint returned status {response.status_code}: {response.text}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Knowledge base endpoint test failed: {str(e)}")
        return False


async def run_comprehensive_test():
    """Run comprehensive FDA knowledge base test suite."""
    logger.info("🚀 Starting Comprehensive FDA Knowledge Base Test Suite")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Test 1: FDA PDF Processor
    test_results["pdf_processor"] = await test_fda_pdf_processor()
    
    # Test 2: FDA Chunker
    test_results["chunker"] = await test_fda_chunker()
    
    # Test 3: FDA Metadata Manager
    test_results["metadata_manager"] = await test_fda_metadata_manager()
    
    # Test 4: Vector Store Integration
    test_results["vector_store"] = await test_vector_store_integration()
    
    # Test 5: Knowledge Base Endpoint (requires running server)
    logger.info("⚠️ Skipping endpoint test - requires running FastAPI server")
    test_results["endpoint"] = None
    
    # Summary
    logger.info("=" * 80)
    logger.info("🎯 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    total = 0
    
    for test_name, result in test_results.items():
        if result is not None:
            total += 1
            if result:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        else:
            logger.info(f"⏭️ {test_name}: SKIPPED")
    
    logger.info("=" * 80)
    logger.info(f"🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! FDA Knowledge Base Enhancement is ready for production.")
        return True
    else:
        logger.error("❌ Some tests failed. Please review and fix issues before deployment.")
        return False


if __name__ == "__main__":
    # Run the comprehensive test
    success = asyncio.run(run_comprehensive_test())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

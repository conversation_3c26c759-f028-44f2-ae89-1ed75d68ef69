/**
 * Sidebar Navigation Component for VigiLens
 *
 * Provides role-based navigation with pharmaceutical compliance focus
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, accessibility
 * Follows DEVELOPMENT_RULES_2.md: Production-first, runtime type safety
 * Follows CSS-Rules.md: Semantic color tokens, component classes
 * Based on reference code structure with full accessibility
 */

'use client'

import {
    Bell,
    Bot,
    FileText,
    LayoutDashboard,
    Search,
    Settings,
    Shield,
    User,
    HelpCircle,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { cn } from '@/lib/utils'
import { useAuth } from '@/contexts/auth-context'

// Navigation configuration with prefetch optimization
const navigationConfig = {
  main: [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      prefetch: true,
    },
    { name: 'Updates', href: '/updates', icon: Bell, prefetch: true },
    { name: 'Documents', href: '/documents', icon: FileText, prefetch: false },
    { name: 'Search', href: '/search', icon: Search, prefetch: false },
    { name: 'AI Assistant', href: '/ai-assistant', icon: Bo<PERSON>, prefetch: false },
    {
      name: 'Compliance Check',
      href: '/compliance-check',
      icon: Shield,
      prefetch: false,
    },
  ],
  account: [
    {
      name: 'Notifications',
      href: '/notifications',
      icon: Bell,
      prefetch: false,
    },
    { name: 'Profile', href: '/profile', icon: User, prefetch: false },
    { name: 'Settings', href: '/settings', icon: Settings, prefetch: false },
    { name: 'Help', href: '/help', icon: HelpCircle, prefetch: false },
  ],
} as const

// Improved active link detection
function isActivePath(pathname: string, href: string): boolean {
  if (href === '/dashboard') {
    return pathname === '/dashboard' || pathname === '/'
  }
  return pathname.startsWith(href)
}

// Navigation link component with better accessibility
interface NavLinkProps {
  readonly item: {
    readonly name: string;
    readonly href: string;
    readonly icon: React.ComponentType<{ className?: string }>;
    readonly prefetch?: boolean;
  };
  readonly isActive: boolean;
}

function NavLink({ item, isActive }: NavLinkProps) {
  const Icon = item.icon

  return (
    <Link
      href={item.href}
      prefetch={item.prefetch ?? false}
      className={cn(
        'flex items-center px-3 py-2 text-[14px] font-medium rounded-lg transition-all duration-[150ms] ease-[cubic-bezier(0.4,0,0.2,1)] group cursor-pointer',
        'focus:outline-none',
        isActive
          ? 'bg-sidebar-accent text-sidebar-accent-foreground shadow-sm'
          : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
      )}
      style={{
        marginTop: 'calc(0.25rem * calc(1 - var(--tw-space-y-reverse)))',
      }}
      aria-current={isActive ? 'page' : undefined}
      role="menuitem"
      tabIndex={0}
    >
      <Icon
        className={cn(
          'h-5 w-5 mr-3 transition-colors',
          isActive
            ? 'text-sidebar-accent-foreground'
            : 'text-sidebar-foreground/70 group-hover:text-sidebar-accent-foreground',
        )}
        aria-hidden="true"
      />
      <span className="truncate">{item.name}</span>
    </Link>
  )
}

export function Sidebar() {
  const { userProfile, hasRole } = useAuth()
  const pathname = usePathname()

  return (
    <aside
      className="flex w-[256px] h-[100vh] flex-col bg-sidebar z-10 overflow-visible"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Logo Section */}
      <header className="h-16 flex items-center pl-6 gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
          <Shield className="h-5 w-5 text-white" aria-hidden="true" />
        </div>
        <h1 className="text-[18px] font-semibold text-sidebar-foreground/90 ml-2">
          VigiLens
        </h1>
      </header>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-8" role="menubar">
        {/* Main Navigation */}
        <div className="space-y-1">
          <h2 className="px-3 text-xs font-medium text-sidebar-foreground/70 uppercase tracking-[0.08em] mt-2 mb-1">
            Main
          </h2>
          <div role="menu" aria-label="Main navigation">
            {navigationConfig.main.map((item) => (
              <NavLink
                key={item.name}
                item={item}
                isActive={isActivePath(pathname || '', item.href)}
              />
            ))}
          </div>
        </div>

        {/* Admin Navigation - Only for super_admin users */}
        {hasRole(['super_admin', 'admin']) && (
          <div className="space-y-1">
            <h2 className="px-3 text-xs font-medium text-sidebar-foreground/70 uppercase tracking-[0.08em] mt-2 mb-1">
              Admin
            </h2>
            <div role="menu" aria-label="Admin navigation">
              <NavLink
                item={{
                  name: 'Admin Panel',
                  href: '/admin',
                  icon: Shield,
                  prefetch: false,
                }}
                isActive={isActivePath(pathname || '', '/admin')}
              />
              <NavLink
                item={{
                  name: 'User Registration',
                  href: '/signup',
                  icon: User,
                  prefetch: false,
                }}
                isActive={isActivePath(pathname || '', '/signup')}
              />
            </div>
          </div>
        )}

        {/* Account Navigation */}
        <div className="space-y-1">
          <h2 className="px-3 text-xs font-medium text-sidebar-foreground/70 uppercase tracking-[0.08em] mt-2 mb-1">
            Account
          </h2>
          <div role="menu" aria-label="Account navigation">
            {navigationConfig.account.map((item) => (
              <NavLink
                key={item.name}
                item={item}
                isActive={isActivePath(pathname || '', item.href)}
              />
            ))}
          </div>
        </div>
      </nav>

      {/* User Profile */}
      <footer className="border-t border-sidebar-border p-4">
        <Link
          href="/profile"
          className="flex items-center space-x-3 rounded-lg p-2 -m-2 hover:bg-sidebar-accent transition-colors duration-[150ms] ease-[cubic-bezier(0.4,0,0.2,1)] focus:outline-none"
          aria-label={`View profile for ${userProfile?.full_name || userProfile?.email || 'User'}`}
        >
          <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center overflow-hidden">
            <span
              className="text-[14px] font-medium text-white"
              aria-hidden="true"
            >
              {userProfile?.full_name?.split(' ').map(n => n[0]).join('').toUpperCase() ||
               userProfile?.email?.[0]?.toUpperCase() || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-[14px] font-medium text-sidebar-foreground truncate">
              {userProfile?.full_name || userProfile?.email || 'User'}
            </p>
            <p className="text-[12px] text-sidebar-foreground/70 truncate">
              {userProfile?.role?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Role'}
            </p>
          </div>
        </Link>
      </footer>
    </aside>
  )
}

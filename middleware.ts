/**
 * Enhanced Middleware for VigiLens Pharmaceutical Compliance Platform
 *
 * Implements comprehensive route protection with role-based access control
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, pharmaceutical compliance
 * Follows DEVELOPMENT_RULES_2.md: Production-first, input sanitization, security
 */

import { type NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'
import { createServerClient } from '@supabase/ssr'

// Route configuration for pharmaceutical compliance platform
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/about',
  '/contact',
  '/auth/confirm',
  '/auth/callback'
]

const PROTECTED_ROUTES = [
  '/dashboard',
  '/documents',
  '/profile',
  '/settings',
  '/compliance-check',
  '/ai-assistant',
  '/search',
  '/updates',
  '/upload',
  '/notifications'
]

const ADMIN_ONLY_ROUTES = [
  '/signup',
  '/admin',
  '/user-management'
]

// Helper function to check if route matches pattern
function matchesRoute(pathname: string, routes: string[]): boolean {
  return routes.some(route => {
    if (route === pathname) return true
    if (route.endsWith('*')) {
      const baseRoute = route.slice(0, -1)
      return pathname.startsWith(baseRoute)
    }
    return pathname.startsWith(route + '/')
  })
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // First, update the session (existing Supabase middleware)
  const supabaseResponse = await updateSession(request)

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return supabaseResponse
  }

  // Create Supabase client for user verification
  const supabase = createServerClient(
    process.env['NEXT_PUBLIC_SUPABASE_URL']!,
    process.env['NEXT_PUBLIC_SUPABASE_ANON_KEY']!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          // Handle cookie setting for auth
          cookiesToSet.forEach(({ name, value, options }) => {
            supabaseResponse.cookies.set(name, value, {
              ...options,
              httpOnly: false,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax'
            })
          })
        },
      },
    }
  )

  // Get current user and session
  const { data: { user }, error: userError } = await supabase.auth.getUser()

  // Handle authentication errors
  if (userError && !matchesRoute(pathname, PUBLIC_ROUTES)) {
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // Check if route is public (allow access without authentication)
  if (matchesRoute(pathname, PUBLIC_ROUTES)) {
    // If user is authenticated and trying to access login, redirect to dashboard
    if (user && pathname === '/login') {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    return supabaseResponse
  }

  // Check if user is authenticated for protected routes
  if (!user && matchesRoute(pathname, [...PROTECTED_ROUTES, ...ADMIN_ONLY_ROUTES])) {
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // Check admin-only routes
  if (matchesRoute(pathname, ADMIN_ONLY_ROUTES)) {
    try {
      // Get user profile with role information
      const { data: userProfile, error: profileError } = await supabase
        .from('user_profiles')
        .select('role, organization_id')
        .eq('id', user?.id)
        .single()

      if (profileError || !userProfile) {
        console.error('Failed to fetch user profile:', profileError)
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }

      // Check if user has super_admin role
      if (userProfile.role !== 'super_admin') {
        // Redirect non-admin users to dashboard
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    } catch (error) {
      console.error('Error checking admin permissions:', error)
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  // Add security headers for pharmaceutical compliance
  const response = NextResponse.next()

  // Copy cookies from supabase response
  supabaseResponse.cookies.getAll().forEach(cookie => {
    response.cookies.set(cookie.name, cookie.value, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
  })

  // Add pharmaceutical compliance security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

  // HIPAA compliance headers
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
    response.headers.set('X-XSS-Protection', '1; mode=block')
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - API routes
     */
    '/((?!_next/static|_next/image|favicon.ico|api/|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

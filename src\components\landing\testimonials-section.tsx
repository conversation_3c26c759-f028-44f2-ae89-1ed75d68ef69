'use client'

import { Star } from 'lucide-react'

import { Avatar, AvatarFallback } from '@/components/ui-radix/avatar'
import { Badge } from '@/components/ui-radix/badge'
import { Card, CardContent } from '@/components/ui-radix/card'


const testimonials = [
  {
    id: 1,
    content:
      'AI Compliance has transformed our regulatory workflow. What used to take weeks now takes days, and we have complete confidence in our compliance status.',
    author: 'Dr. <PERSON>',
    role: 'VP of Quality Assurance',
    company: 'BioTech Innovations',
    rating: 5,
    industry: 'Biotechnology',
  },
  {
    id: 2,
    content:
      'The AI-powered analysis catches compliance gaps we would have missed. It&apos;s like having a compliance expert working 24/7.',
    author: '<PERSON>',
    role: 'Regulatory Affairs Director',
    company: 'PharmaCorp Global',
    rating: 5,
    industry: 'Pharmaceutical',
  },
  {
    id: 3,
    content:
      'Implementation was seamless and the ROI was immediate. Our audit preparation time has been cut by 60% and we&apos;ve had zero compliance violations since adopting the platform.',
    author: '<PERSON>',
    role: 'Chief Compliance Officer',
    company: 'MedDevice Solutions',
    rating: 5,
    industry: 'Medical Devices',
  },
  {
    id: 4,
    content:
      'The real-time regulatory updates feature keeps us ahead of changing requirements. We&apos;re always compliant, never reactive.',
    author: 'Dr. James Thompson',
    role: 'Head of Regulatory Science',
    company: 'Global Pharma Inc',
    rating: 5,
    industry: 'Pharmaceutical',
  },
]

export function TestimonialsSection() {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Trusted by Industry Leaders
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            See how organizations worldwide are transforming their compliance
            operations with our AI-powered platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto">
          {testimonials.map((testimonial) => (
            <Card
              key={testimonial.id}
              className="bg-background border hover:shadow-lg transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>

                <blockquote className="text-foreground mb-6 leading-relaxed">
                  &ldquo;{testimonial.content}&rdquo;
                </blockquote>

                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {testimonial.author
                        .split(' ')
                        .map((n: string) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-semibold text-foreground">
                      {testimonial.author}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.role}
                    </p>
                    <p className="text-sm font-medium text-primary">
                      {testimonial.company}
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-background">
                    {testimonial.industry}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

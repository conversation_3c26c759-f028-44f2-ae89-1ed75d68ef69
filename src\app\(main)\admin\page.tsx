/**
 * Admin Panel for VigiLens Pharmaceutical Compliance Platform
 *
 * Administrative interface for system management
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, pharmaceutical compliance
 * Follows DEVELOPMENT_RULES_2.md: Production-first, runtime type safety
 * Follows CSS-Rules.md: Semantic color tokens, component classes
 */

'use client'

import { useState } from 'react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { usePageMetadata } from '@/hooks/use-page-metadata'
import { useAuth } from '@/contexts/auth-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Shield,
  Users,
  Building,
  Settings,
  Activity,
  UserPlus,
  Database,
  AlertTriangle,
  CheckCircle,
  Clock,
  User
} from 'lucide-react'

interface SystemMetric {
  label: string
  value: string | number
  change?: string
  status: 'success' | 'warning' | 'error' | 'info'
  icon: React.ComponentType<{ className?: string }>
}

const SYSTEM_METRICS: SystemMetric[] = [
  {
    label: 'Total Users',
    value: 24,
    change: '+3 this month',
    status: 'success',
    icon: Users
  },
  {
    label: 'Active Sessions',
    value: 8,
    change: 'Currently online',
    status: 'info',
    icon: Activity
  },
  {
    label: 'System Status',
    value: 'Operational',
    change: '99.9% uptime',
    status: 'success',
    icon: CheckCircle
  },
  {
    label: 'Database Size',
    value: '2.4 GB',
    change: '+120 MB this week',
    status: 'info',
    icon: Database
  }
]

export default function AdminPage() {
  usePageMetadata(
    'Admin Panel',
    'System administration and management for VigiLens platform'
  )

  const { userProfile } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300'
      case 'warning': return 'bg-amber-50 text-amber-700 dark:bg-amber-950 dark:text-amber-300'
      case 'error': return 'bg-rose-50 text-rose-700 dark:bg-rose-950 dark:text-rose-300'
      case 'info': return 'bg-sky-50 text-sky-700 dark:bg-sky-950 dark:text-sky-300'
      default: return 'bg-muted text-muted-foreground'
    }
  }

  return (
    <ProtectedRoute
      requiredRoles={['super_admin', 'admin']}
      fallbackPath="/dashboard"
    >
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary">
              <Shield className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">
                Admin Panel
              </h1>
              <p className="text-muted-foreground">
                System administration for VigiLens pharmaceutical compliance platform
              </p>
            </div>
          </div>
        </div>

        {/* Admin Info */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-foreground">
              <User className="h-5 w-5" />
              Administrator Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-foreground">Current Admin</p>
                <p className="text-sm text-muted-foreground">{userProfile?.email}</p>
                <Badge className={getStatusColor('success')}>
                  {userProfile?.role}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-foreground">Organization</p>
                <p className="text-sm text-muted-foreground">
                  {userProfile?.organization_name || 'No organization'}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-foreground">Department</p>
                <p className="text-sm text-muted-foreground">
                  {userProfile?.department || 'No department'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {SYSTEM_METRICS.map((metric) => {
            const Icon = metric.icon
            return (
              <Card key={metric.label} className="bg-card border-border">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">
                        {metric.label}
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {metric.value}
                      </p>
                      {metric.change && (
                        <p className="text-xs text-muted-foreground">
                          {metric.change}
                        </p>
                      )}
                    </div>
                    <div className={`p-2 rounded-lg ${getStatusColor(metric.status)}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Admin Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-muted">
            <TabsTrigger value="overview" className="data-[state=active]:bg-background">
              Overview
            </TabsTrigger>
            <TabsTrigger value="users" className="data-[state=active]:bg-background">
              User Management
            </TabsTrigger>
            <TabsTrigger value="system" className="data-[state=active]:bg-background">
              System Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">Quick Actions</CardTitle>
                  <CardDescription className="text-muted-foreground">
                    Common administrative tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => window.location.href = '/signup'}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Register New User
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    disabled
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Manage User Roles
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    disabled
                  >
                    <Building className="mr-2 h-4 w-4" />
                    Organization Settings
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    disabled
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    System Configuration
                  </Button>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">Recent Activity</CardTitle>
                  <CardDescription className="text-muted-foreground">
                    Latest system events and user actions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                      <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground">
                          User profile updated
                        </p>
                        <p className="text-xs text-muted-foreground">
                          <EMAIL> • 2 minutes ago
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                      <Clock className="h-4 w-4 text-sky-600 dark:text-sky-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground">
                          System backup completed
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Automated backup • 1 hour ago
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                      <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground">
                          Authentication timeout detected
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Login system • 30 minutes ago
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-foreground">User Management</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Manage user accounts and permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    User Management Coming Soon
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Advanced user management features will be available in the next update.
                  </p>
                  <Button
                    onClick={() => window.location.href = '/signup'}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Register New User
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-foreground">System Settings</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Configure system-wide settings and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    System Settings Coming Soon
                  </h3>
                  <p className="text-muted-foreground">
                    Advanced system configuration options will be available in the next update.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  )
}

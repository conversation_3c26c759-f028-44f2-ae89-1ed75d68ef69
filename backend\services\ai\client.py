"""
AI Client Service

OpenRouter AI client integration for pharmaceutical compliance applications.
Provides chat completion, text generation, and model management.

Features:
- OpenRouter API integration
- Multiple model support (Moonshot Kimi K2, etc.)
- Async operations with proper error handling
- Type safety with Pydantic validation
- Rate limiting and retry logic
- Comprehensive logging and monitoring
"""

import asyncio
import logging
import os
from typing import List, Optional, Dict, Any, Union
import httpx
from pydantic import BaseModel, Field, field_validator

# Configure logging
logger = logging.getLogger(__name__)

class ChatMessage(BaseModel):
    """Chat message model."""
    role: str = Field(..., description="Message role (system, user, assistant)")
    content: str = Field(..., description="Message content")

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        """Validate message role."""
        if v not in ["system", "user", "assistant"]:
            raise ValueError("Role must be 'system', 'user', or 'assistant'")
        return v

    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        """Validate message content."""
        if not v or not v.strip():
            raise ValueError("Message content cannot be empty")
        return v.strip()

class ChatRequest(BaseModel):
    """Request model for chat completion."""
    messages: List[ChatMessage] = Field(..., min_items=1, description="List of chat messages")
    model: str = Field(default="moonshot/kimi-k2", description="Model to use for completion")
    max_tokens: int = Field(default=1000, ge=1, le=4000, description="Maximum tokens to generate")
    temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(default=1.0, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    stream: bool = Field(default=False, description="Whether to stream the response")

    @field_validator('messages')
    @classmethod
    def validate_messages(cls, v):
        """Validate messages list."""
        if not v:
            raise ValueError("Messages list cannot be empty")

        # Check for system message
        has_system = any(msg.role == "system" for msg in v)
        if not has_system:
            logger.warning("No system message found in chat request")

        return v

class ChatResponse(BaseModel):
    """Response model for chat completion."""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float

class AIClientError(Exception):
    """Custom exception for AI client errors."""
    pass

class OpenRouterClient:
    """
    OpenRouter AI Client

    Provides integration with OpenRouter API for various AI models.
    Supports chat completion, text generation, and model management.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: str = "https://openrouter.ai/api/v1",
        timeout: int = 60,
        max_retries: int = 3
    ):
        """
        Initialize OpenRouter client.

        Args:
            api_key: OpenRouter API key (or from environment)
            base_url: OpenRouter API base URL
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.client: Optional[httpx.AsyncClient] = None
        self._is_initialized = False

        if not self.api_key:
            raise AIClientError("OpenRouter API key not provided")

        logger.info("Initializing OpenRouter AI client")

    async def initialize(self) -> None:
        """Initialize the HTTP client."""
        if self._is_initialized:
            return

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://vigilens.ai",
                "X-Title": "VigiLens Pharmaceutical Compliance"
            }

            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                headers=headers,
                timeout=self.timeout
            )

            self._is_initialized = True
            logger.info("OpenRouter client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize OpenRouter client: {e}")
            raise AIClientError(f"Client initialization failed: {e}")

    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion.

        Args:
            request: Chat completion request

        Returns:
            ChatResponse with generated content

        Raises:
            AIClientError: If completion fails
        """
        if not self._is_initialized:
            await self.initialize()

        if not self.client:
            raise AIClientError("Client not initialized")

        try:
            import time
            start_time = time.time()

            # Prepare request payload
            payload = {
                "model": request.model,
                "messages": [msg.dict() for msg in request.messages],
                "max_tokens": request.max_tokens,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }

            # Make API request with retries
            response_data = await self._make_request_with_retries(
                "POST",
                "/chat/completions",
                json=payload
            )

            response_time = time.time() - start_time

            # Extract response content
            choice = response_data["choices"][0]
            content = choice["message"]["content"]
            finish_reason = choice["finish_reason"]
            usage = response_data.get("usage", {})

            logger.info(f"Chat completion generated in {response_time:.2f}s")

            return ChatResponse(
                content=content,
                model=request.model,
                usage=usage,
                finish_reason=finish_reason,
                response_time=response_time
            )

        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise AIClientError(f"Chat completion failed: {e}")

    async def _make_request_with_retries(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Make HTTP request with retry logic."""
        if not self.client:
            raise AIClientError("Client not initialized")

        last_exception = None

        for attempt in range(self.max_retries):
            try:
                response = await self.client.request(method, endpoint, **kwargs)
                response.raise_for_status()
                return response.json()

            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt
                    logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                elif e.response.status_code >= 500:  # Server error
                    wait_time = 2 ** attempt
                    logger.warning(f"Server error, waiting {wait_time}s before retry {attempt + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # Client error, don't retry
                    break

            except (httpx.RequestError, httpx.TimeoutException) as e:
                last_exception = e
                wait_time = 2 ** attempt
                logger.warning(f"Request error, waiting {wait_time}s before retry {attempt + 1}")
                await asyncio.sleep(wait_time)
                continue

        # All retries failed
        raise AIClientError(f"Request failed after {self.max_retries} retries: {last_exception}")

    async def list_models(self) -> List[Dict[str, Any]]:
        """List available models."""
        if not self._is_initialized:
            await self.initialize()

        try:
            response_data = await self._make_request_with_retries("GET", "/models")
            return response_data.get("data", [])

        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            raise AIClientError(f"Model listing failed: {e}")

    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get information about a specific model."""
        models = await self.list_models()

        for model in models:
            if model.get("id") == model_name:
                return model

        raise AIClientError(f"Model not found: {model_name}")

    async def health_check(self) -> bool:
        """Check if the AI client is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()

            # Test with a simple chat completion
            test_request = ChatRequest(
                messages=[
                    ChatMessage(role="system", content="You are a helpful assistant."),
                    ChatMessage(role="user", content="Hello")
                ],
                model="moonshot/kimi-k2",
                max_tokens=10
            )

            await self.chat_completion(test_request)
            return True

        except Exception as e:
            logger.error(f"AI client health check failed: {e}")
            return False

    async def close(self) -> None:
        """Close the HTTP client."""
        if self.client:
            await self.client.aclose()
            self._is_initialized = False
            logger.info("OpenRouter client closed")

# Global client instance
_ai_client: Optional[OpenRouterClient] = None

async def get_ai_client() -> OpenRouterClient:
    """Get or create the global AI client instance."""
    global _ai_client

    if _ai_client is None:
        _ai_client = OpenRouterClient()
        await _ai_client.initialize()

    return _ai_client

async def close_ai_client() -> None:
    """Close the global AI client."""
    global _ai_client

    if _ai_client:
        await _ai_client.close()
        _ai_client = None

"""Pydantic models for compliance frameworks and assessments."""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, ConfigDict

from .base import TimestampedModel
from .documents import RegulatoryAgency


class AssessmentType(str, Enum):
    """Assessment type enumeration."""
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    HYBRID = "hybrid"


class RiskLevel(str, Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceStatus(str, Enum):
    """Compliance status enumeration."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    UNDER_REVIEW = "under_review"
    NOT_ASSESSED = "not_assessed"


class ComplianceRequirement(BaseModel):
    """Individual compliance requirement."""
    id: str = Field(..., description="Unique requirement identifier")
    title: str = Field(..., description="Requirement title")
    description: str = Field(..., description="Detailed requirement description")
    weight: int = Field(..., ge=1, le=100, description="Weight for scoring (1-100)")
    mandatory: bool = Field(default=True, description="Whether requirement is mandatory")
    category: Optional[str] = Field(None, description="Requirement category")
    reference: Optional[str] = Field(None, description="Reference to source document")


class ValidationRules(BaseModel):
    """Validation rules for compliance checking."""
    document_types: List[str] = Field(default_factory=list, description="Applicable document types")
    required_sections: List[str] = Field(default_factory=list, description="Required document sections")
    critical_keywords: List[str] = Field(default_factory=list, description="Critical keywords to check")
    minimum_content_length: Optional[int] = Field(None, description="Minimum content length")
    exclusion_keywords: List[str] = Field(default_factory=list, description="Keywords that indicate non-compliance")
    scoring_rules: Dict[str, Any] = Field(default_factory=dict, description="Custom scoring rules")


class ScoringWeights(BaseModel):
    """Scoring weights for different compliance areas."""
    quality_system: Optional[float] = Field(None, ge=0, le=1)
    documentation: Optional[float] = Field(None, ge=0, le=1)
    validation: Optional[float] = Field(None, ge=0, le=1)
    personnel: Optional[float] = Field(None, ge=0, le=1)
    facilities: Optional[float] = Field(None, ge=0, le=1)
    equipment: Optional[float] = Field(None, ge=0, le=1)
    risk_management: Optional[float] = Field(None, ge=0, le=1)
    materials: Optional[float] = Field(None, ge=0, le=1)
    production: Optional[float] = Field(None, ge=0, le=1)

    @field_validator('*', mode='before')
    @classmethod
    def validate_weights(cls, v):
        """Ensure weights are between 0 and 1."""
        if v is not None and (v < 0 or v > 1):
            raise ValueError("Weight must be between 0 and 1")
        return v


class ComplianceFrameworkBase(BaseModel):
    """Base compliance framework model."""
    name: str = Field(..., max_length=255, description="Framework name")
    code: str = Field(..., max_length=50, description="Framework code")
    description: Optional[str] = Field(None, description="Framework description")
    regulatory_agency: RegulatoryAgency = Field(..., description="Regulatory agency")
    version: Optional[str] = Field(None, max_length=50, description="Framework version")
    effective_date: Optional[date] = Field(None, description="Effective date")
    expiration_date: Optional[date] = Field(None, description="Expiration date")
    jurisdiction: Optional[str] = Field(None, max_length=100, description="Jurisdiction")
    category: Optional[str] = Field(None, max_length=100, description="Framework category")
    requirements: List[ComplianceRequirement] = Field(default_factory=list, description="Framework requirements")
    validation_rules: ValidationRules = Field(default_factory=lambda: ValidationRules(), description="Validation rules")
    scoring_weights: ScoringWeights = Field(default_factory=lambda: ScoringWeights(), description="Scoring weights")
    is_active: bool = Field(default=True, description="Whether framework is active")

    @field_validator('expiration_date')
    @classmethod
    def validate_expiration_date(cls, v, info):
        """Ensure expiration date is after effective date."""
        if v and info.data.get('effective_date'):
            if v <= info.data['effective_date']:
                raise ValueError("Expiration date must be after effective date")
        return v


class ComplianceFrameworkCreate(ComplianceFrameworkBase):
    """Model for creating compliance frameworks."""
    pass


class ComplianceFrameworkUpdate(BaseModel):
    """Model for updating compliance frameworks."""
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    version: Optional[str] = Field(None, max_length=50)
    effective_date: Optional[date] = None
    expiration_date: Optional[date] = None
    jurisdiction: Optional[str] = Field(None, max_length=100)
    category: Optional[str] = Field(None, max_length=100)
    requirements: Optional[List[ComplianceRequirement]] = None
    validation_rules: Optional[ValidationRules] = None
    scoring_weights: Optional[ScoringWeights] = None
    is_active: Optional[bool] = None


class ComplianceFramework(ComplianceFrameworkBase, TimestampedModel):
    """Complete compliance framework model."""
    id: UUID = Field(..., description="Framework ID")

    model_config = ConfigDict(from_attributes=True)


class ComplianceFinding(BaseModel):
    """Individual compliance finding."""
    requirement_id: str = Field(..., description="Related requirement ID")
    finding_type: str = Field(..., description="Type of finding (gap, observation, non-compliance)")
    severity: str = Field(..., description="Severity level (low, medium, high, critical)")
    description: str = Field(..., description="Finding description")
    evidence: Optional[str] = Field(None, description="Supporting evidence")
    location: Optional[str] = Field(None, description="Location in document")
    recommendation: Optional[str] = Field(None, description="Recommended action")


class ComplianceRecommendation(BaseModel):
    """Compliance recommendation."""
    priority: str = Field(..., description="Priority level (low, medium, high, critical)")
    category: str = Field(..., description="Recommendation category")
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Detailed recommendation")
    effort_estimate: Optional[str] = Field(None, description="Estimated effort")
    timeline: Optional[str] = Field(None, description="Recommended timeline")
    resources_required: List[str] = Field(default_factory=list, description="Required resources")


class ComplianceGap(BaseModel):
    """Identified compliance gap."""
    requirement_id: str = Field(..., description="Related requirement ID")
    gap_type: str = Field(..., description="Type of gap")
    severity: str = Field(..., description="Gap severity")
    description: str = Field(..., description="Gap description")
    impact: Optional[str] = Field(None, description="Potential impact")
    mitigation: Optional[str] = Field(None, description="Mitigation strategy")


class EvidenceReference(BaseModel):
    """Reference to supporting evidence."""
    type: str = Field(..., description="Evidence type (document, section, external)")
    reference: str = Field(..., description="Evidence reference")
    description: Optional[str] = Field(None, description="Evidence description")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Confidence level")


class DocumentComplianceAssessmentBase(BaseModel):
    """Base document compliance assessment model."""
    document_id: UUID = Field(..., description="Document ID")
    framework_id: UUID = Field(..., description="Framework ID")
    assessment_type: AssessmentType = Field(default=AssessmentType.AUTOMATIC, description="Assessment type")
    overall_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Overall compliance score")
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    compliance_status: ComplianceStatus = Field(default=ComplianceStatus.NOT_ASSESSED, description="Compliance status")
    findings: List[ComplianceFinding] = Field(default_factory=list, description="Assessment findings")
    recommendations: List[ComplianceRecommendation] = Field(default_factory=list, description="Recommendations")
    gaps_identified: List[ComplianceGap] = Field(default_factory=list, description="Identified gaps")
    evidence_references: List[EvidenceReference] = Field(default_factory=list, description="Evidence references")
    assessed_by: Optional[UUID] = Field(None, description="Assessor user ID")
    reviewed_by: Optional[UUID] = Field(None, description="Reviewer user ID")
    assessment_date: datetime = Field(default_factory=datetime.utcnow, description="Assessment date")
    review_date: Optional[datetime] = Field(None, description="Review date")
    next_review_date: Optional[datetime] = Field(None, description="Next review date")
    is_current: bool = Field(default=True, description="Whether this is the current assessment")


class DocumentComplianceAssessmentCreate(DocumentComplianceAssessmentBase):
    """Model for creating compliance assessments."""
    pass


class DocumentComplianceAssessmentUpdate(BaseModel):
    """Model for updating compliance assessments."""
    overall_score: Optional[Decimal] = Field(None, ge=0, le=100)
    risk_level: Optional[RiskLevel] = None
    compliance_status: Optional[ComplianceStatus] = None
    findings: Optional[List[ComplianceFinding]] = None
    recommendations: Optional[List[ComplianceRecommendation]] = None
    gaps_identified: Optional[List[ComplianceGap]] = None
    evidence_references: Optional[List[EvidenceReference]] = None
    reviewed_by: Optional[UUID] = None
    review_date: Optional[datetime] = None
    next_review_date: Optional[datetime] = None


class DocumentComplianceAssessment(DocumentComplianceAssessmentBase, TimestampedModel):
    """Complete document compliance assessment model."""
    id: UUID = Field(..., description="Assessment ID")

    model_config = ConfigDict(from_attributes=True)


class ComplianceDashboardMetrics(BaseModel):
    """Compliance dashboard metrics."""
    organization_name: str = Field(..., description="Organization name")
    framework_name: str = Field(..., description="Framework name")
    framework_code: str = Field(..., description="Framework code")
    category: Optional[str] = Field(None, description="Framework category")
    total_assessments: int = Field(..., description="Total number of assessments")
    compliant_count: int = Field(..., description="Number of compliant assessments")
    partially_compliant_count: int = Field(..., description="Number of partially compliant assessments")
    non_compliant_count: int = Field(..., description="Number of non-compliant assessments")
    critical_risk_count: int = Field(..., description="Number of critical risk assessments")
    average_score: Optional[float] = Field(None, description="Average compliance score")
    upcoming_reviews: int = Field(..., description="Number of upcoming reviews")

    @property
    def compliance_rate(self) -> float:
        """Calculate compliance rate percentage."""
        if self.total_assessments == 0:
            return 0.0
        return (self.compliant_count / self.total_assessments) * 100

    @property
    def risk_distribution(self) -> Dict[str, float]:
        """Calculate risk distribution percentages."""
        if self.total_assessments == 0:
            return {"low": 0.0, "medium": 0.0, "high": 0.0, "critical": 0.0}

        # This would need to be calculated from actual data
        # For now, return a placeholder
        return {
            "critical": (self.critical_risk_count / self.total_assessments) * 100,
            "high": 0.0,  # Would need actual data
            "medium": 0.0,  # Would need actual data
            "low": 0.0  # Would need actual data
        }


class ComplianceAssessmentRequest(BaseModel):
    """Request model for compliance assessment."""
    document_id: UUID = Field(..., description="Document ID to assess")
    framework_id: UUID = Field(..., description="Framework ID to assess against")
    assessment_type: AssessmentType = Field(default=AssessmentType.AUTOMATIC, description="Assessment type")
    force_reassessment: bool = Field(default=False, description="Force new assessment even if current exists")


class ComplianceAssessmentResponse(BaseModel):
    """Response model for compliance assessment."""
    assessment_id: UUID = Field(..., description="Created assessment ID")
    document_id: UUID = Field(..., description="Document ID")
    framework_id: UUID = Field(..., description="Framework ID")
    overall_score: Optional[Decimal] = Field(None, description="Overall compliance score")
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    compliance_status: ComplianceStatus = Field(..., description="Compliance status")
    assessment_date: datetime = Field(..., description="Assessment date")
    next_review_date: Optional[datetime] = Field(None, description="Next review date")
    findings_count: int = Field(..., description="Number of findings")
    recommendations_count: int = Field(..., description="Number of recommendations")
    gaps_count: int = Field(..., description="Number of gaps identified")


class BulkComplianceAssessmentRequest(BaseModel):
    """Request model for bulk compliance assessment."""
    document_ids: List[UUID] = Field(..., description="Document IDs to assess")
    framework_ids: List[UUID] = Field(..., description="Framework IDs to assess against")
    assessment_type: AssessmentType = Field(default=AssessmentType.AUTOMATIC, description="Assessment type")
    force_reassessment: bool = Field(default=False, description="Force new assessments")


class BulkComplianceAssessmentResponse(BaseModel):
    """Response model for bulk compliance assessment."""
    total_requested: int = Field(..., description="Total assessments requested")
    successful_assessments: List[ComplianceAssessmentResponse] = Field(..., description="Successful assessments")
    failed_assessments: List[Dict[str, Any]] = Field(..., description="Failed assessments with errors")
    summary: Dict[str, Any] = Field(..., description="Assessment summary statistics")


class ComplianceFrameworkSummary(BaseModel):
    """Summary model for compliance frameworks."""
    id: UUID = Field(..., description="Framework ID")
    name: str = Field(..., description="Framework name")
    code: str = Field(..., description="Framework code")
    regulatory_agency: RegulatoryAgency = Field(..., description="Regulatory agency")
    category: Optional[str] = Field(None, description="Framework category")
    jurisdiction: Optional[str] = Field(None, description="Jurisdiction")
    requirements_count: int = Field(..., description="Number of requirements")
    is_active: bool = Field(..., description="Whether framework is active")
    assessments_count: Optional[int] = Field(None, description="Number of assessments using this framework")


class ComplianceReportRequest(BaseModel):
    """Request model for compliance reports."""
    organization_id: Optional[UUID] = Field(None, description="Organization ID filter")
    framework_ids: Optional[List[UUID]] = Field(None, description="Framework IDs filter")
    document_ids: Optional[List[UUID]] = Field(None, description="Document IDs filter")
    compliance_status: Optional[List[ComplianceStatus]] = Field(None, description="Compliance status filter")
    risk_levels: Optional[List[RiskLevel]] = Field(None, description="Risk levels filter")
    date_from: Optional[datetime] = Field(None, description="Start date filter")
    date_to: Optional[datetime] = Field(None, description="End date filter")
    include_details: bool = Field(default=False, description="Include detailed findings")
    format: str = Field(default="json", description="Report format (json, csv, pdf)")


class ComplianceReportResponse(BaseModel):
    """Response model for compliance reports."""
    report_id: UUID = Field(..., description="Report ID")
    generated_at: datetime = Field(..., description="Report generation timestamp")
    filters_applied: Dict[str, Any] = Field(..., description="Applied filters")
    summary: Dict[str, Any] = Field(..., description="Report summary")
    data: List[Dict[str, Any]] = Field(..., description="Report data")
    total_records: int = Field(..., description="Total number of records")
    export_url: Optional[str] = Field(None, description="URL for downloading report file")

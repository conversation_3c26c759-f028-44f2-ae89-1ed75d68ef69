-- VigiLens Database Schema - Migration 009
-- Create Electronic Signatures Table for 21 CFR Part 11 Compliance
-- Optimized for Supabase-First Architecture with Direct Frontend Integration

-- Electronic signature types
CREATE TYPE signature_type AS ENUM (
    'approval',         -- Document approval signature
    'review',           -- Document review signature
    'witness',          -- Witness signature
    'author',           -- Author signature
    'quality_approval', -- Quality department approval
    'regulatory_approval' -- Regulatory approval
);

-- Authentication methods for electronic signatures
CREATE TYPE authentication_method AS ENUM (
    'password',         -- Password-based authentication
    'biometric',        -- Biometric authentication
    'token',           -- Hardware/software token
    'certificate',     -- Digital certificate
    'multi_factor'     -- Multi-factor authentication
);

-- Electronic signatures table - 21 CFR Part 11 compliant signatures
CREATE TABLE electronic_signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Signature identification
    document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE,
    document_version_id UUID REFERENCES document_versions(id) ON DELETE CASCADE,
    audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE,
    
    -- Signer information
    signer_id UUID NOT NULL REFERENCES user_profiles(id),
    signer_name VARCHAR(255) NOT NULL,
    signer_title VARCHAR(255),
    signer_department VARCHAR(100),
    
    -- Signature details
    signature_type signature_type NOT NULL,
    signature_meaning TEXT NOT NULL, -- What the signature represents
    signature_reason TEXT, -- Reason for signing
    
    -- Authentication and security
    authentication_method authentication_method NOT NULL,
    authentication_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    signature_hash VARCHAR(512) NOT NULL, -- Cryptographic hash of signature
    document_hash_at_signing VARCHAR(512), -- Document hash when signed
    
    -- Timestamps
    signed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    signature_metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    
    -- Constraints
    CONSTRAINT valid_signature_hash CHECK (LENGTH(signature_hash) >= 64),
    CONSTRAINT valid_authentication_timestamp CHECK (authentication_timestamp <= signed_at)
);

-- Enable RLS
ALTER TABLE electronic_signatures ENABLE ROW LEVEL SECURITY;

-- RLS Policies for electronic_signatures
CREATE POLICY "Users can view signatures in their organization"
    ON electronic_signatures FOR SELECT
    USING (
        organization_id IN (
            SELECT organization_id 
            FROM user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can create signatures in their organization"
    ON electronic_signatures FOR INSERT
    WITH CHECK (
        organization_id IN (
            SELECT organization_id 
            FROM user_profiles 
            WHERE id = auth.uid()
        )
        AND signer_id = auth.uid()
    );

-- No UPDATE or DELETE allowed for electronic signatures (immutable for compliance)
CREATE POLICY "Electronic signatures are immutable"
    ON electronic_signatures FOR UPDATE
    USING (false);

CREATE POLICY "Electronic signatures cannot be deleted"
    ON electronic_signatures FOR DELETE
    USING (false);

-- Indexes for performance
CREATE INDEX idx_electronic_signatures_organization ON electronic_signatures(organization_id);
CREATE INDEX idx_electronic_signatures_document ON electronic_signatures(document_id);
CREATE INDEX idx_electronic_signatures_signer ON electronic_signatures(signer_id);
CREATE INDEX idx_electronic_signatures_signed_at ON electronic_signatures(signed_at);
CREATE INDEX idx_electronic_signatures_type ON electronic_signatures(signature_type);

-- GIN index for metadata search
CREATE INDEX idx_electronic_signatures_metadata ON electronic_signatures USING GIN (signature_metadata);

-- Function to create electronic signature with validation
CREATE OR REPLACE FUNCTION create_electronic_signature(
    p_organization_id UUID,
    p_document_id UUID,
    p_signer_id UUID,
    p_signature_type signature_type,
    p_signature_meaning TEXT,
    p_signature_reason TEXT DEFAULT NULL,
    p_authentication_method authentication_method DEFAULT 'password',
    p_signature_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    signature_id UUID;
    signer_info RECORD;
    document_hash VARCHAR(512);
    signature_hash VARCHAR(512);
BEGIN
    -- Get signer information
    SELECT full_name, role INTO signer_info
    FROM user_profiles
    WHERE id = p_signer_id AND organization_id = p_organization_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Signer not found in organization';
    END IF;
    
    -- Generate document hash at time of signing
    SELECT encode(digest(
        CONCAT(
            COALESCE(title, ''),
            COALESCE(content, ''),
            COALESCE(version_number::text, ''),
            NOW()::text
        ), 'sha256'
    ), 'hex') INTO document_hash
    FROM regulatory_documents
    WHERE id = p_document_id;
    
    -- Generate signature hash
    signature_hash := encode(digest(
        CONCAT(
            p_signer_id::text,
            p_document_id::text,
            p_signature_meaning,
            NOW()::text,
            gen_random_uuid()::text
        ), 'sha256'
    ), 'hex');
    
    -- Create electronic signature
    INSERT INTO electronic_signatures (
        id,
        organization_id,
        document_id,
        signer_id,
        signer_name,
        signature_type,
        signature_meaning,
        signature_reason,
        authentication_method,
        signature_hash,
        document_hash_at_signing,
        signature_metadata
    ) VALUES (
        gen_random_uuid(),
        p_organization_id,
        p_document_id,
        p_signer_id,
        signer_info.full_name,
        p_signature_type,
        p_signature_meaning,
        p_signature_reason,
        p_authentication_method,
        signature_hash,
        document_hash,
        p_signature_metadata
    ) RETURNING id INTO signature_id;
    
    -- Log audit event
    PERFORM log_audit_event(
        p_organization_id,
        p_signer_id,
        'sign'::audit_action_type,
        'Electronic signature created: ' || p_signature_meaning,
        'signature',
        signature_id,
        'Electronic Signature',
        NULL,
        jsonb_build_object(
            'signature_type', p_signature_type,
            'authentication_method', p_authentication_method,
            'document_id', p_document_id
        )
    );
    
    RETURN signature_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify signature integrity
CREATE OR REPLACE FUNCTION verify_signature_integrity(signature_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    sig_record RECORD;
    current_doc_hash VARCHAR(512);
BEGIN
    -- Get signature record
    SELECT * INTO sig_record
    FROM electronic_signatures
    WHERE id = signature_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Verify signature hash format
    IF LENGTH(sig_record.signature_hash) < 64 THEN
        RETURN FALSE;
    END IF;
    
    -- Additional integrity checks can be added here
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT, INSERT ON electronic_signatures TO authenticated;
GRANT EXECUTE ON FUNCTION create_electronic_signature TO authenticated;
GRANT EXECUTE ON FUNCTION verify_signature_integrity TO authenticated;

-- Comments for documentation
COMMENT ON TABLE electronic_signatures IS 'Electronic signatures for 21 CFR Part 11 compliance';
COMMENT ON COLUMN electronic_signatures.signature_hash IS 'Cryptographic hash ensuring signature integrity';
COMMENT ON COLUMN electronic_signatures.document_hash_at_signing IS 'Document hash at time of signing for tamper detection';
COMMENT ON FUNCTION create_electronic_signature IS 'Creates compliant electronic signature with audit trail';

-- Verify table creation
SELECT 
    'ELECTRONIC SIGNATURES TABLE CREATED' as status,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND table_schema = 'public';

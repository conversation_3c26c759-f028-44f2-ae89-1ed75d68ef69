"""
Role-Based Access Control (RBAC) for VigiLens Pharmaceutical Compliance

VCP_002_3: Role-Based Access Control (RBAC) System
Implements comprehensive RBAC with pharmaceutical compliance permissions.

Following PRD.md specifications for 9 pharmaceutical roles:
- super_admin, org_admin, quality_manager, regulatory_lead, compliance_officer
- document_reviewer, analyst, auditor, viewer

Following DEVELOPMENT_RULES.md for pharmaceutical compliance standards.
"""

from enum import Enum
from typing import Dict, List, Set
from functools import lru_cache

from .models import PharmaceuticalRole


class PharmaceuticalPermissions(str, Enum):
    """
    Pharmaceutical compliance permissions following 21 CFR Part 11 and GxP standards.
    
    Organized by functional areas:
    - Document Management
    - Quality Control
    - Regulatory Compliance
    - User Management
    - System Administration
    - Audit & Reporting
    """
    
    # Document Management Permissions
    DOCUMENT_CREATE = "document:create"
    DOCUMENT_READ = "document:read"
    DOCUMENT_UPDATE = "document:update"
    DOCUMENT_DELETE = "document:delete"
    DOCUMENT_APPROVE = "document:approve"
    DOCUMENT_REVIEW = "document:review"
    DOCUMENT_SIGN = "document:sign"
    DOCUMENT_ARCHIVE = "document:archive"
    
    # Quality Control Permissions
    QUALITY_CREATE_BATCH = "quality:create_batch"
    QUALITY_REVIEW_BATCH = "quality:review_batch"
    QUALITY_APPROVE_BATCH = "quality:approve_batch"
    QUALITY_REJECT_BATCH = "quality:reject_batch"
    QUALITY_VIEW_REPORTS = "quality:view_reports"
    QUALITY_MANAGE_SPECS = "quality:manage_specs"
    
    # Regulatory Compliance Permissions
    REGULATORY_CREATE_SUBMISSION = "regulatory:create_submission"
    REGULATORY_REVIEW_SUBMISSION = "regulatory:review_submission"
    REGULATORY_APPROVE_SUBMISSION = "regulatory:approve_submission"
    REGULATORY_SUBMIT_TO_AGENCY = "regulatory:submit_to_agency"
    REGULATORY_VIEW_CORRESPONDENCE = "regulatory:view_correspondence"
    REGULATORY_MANAGE_REGISTRATIONS = "regulatory:manage_registrations"
    
    # Compliance Monitoring Permissions
    COMPLIANCE_VIEW_DASHBOARD = "compliance:view_dashboard"
    COMPLIANCE_CREATE_DEVIATION = "compliance:create_deviation"
    COMPLIANCE_INVESTIGATE_DEVIATION = "compliance:investigate_deviation"
    COMPLIANCE_APPROVE_CAPA = "compliance:approve_capa"
    COMPLIANCE_MANAGE_TRAINING = "compliance:manage_training"
    
    # User Management Permissions
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_ASSIGN_ROLES = "user:assign_roles"
    USER_MANAGE_PERMISSIONS = "user:manage_permissions"
    USER_VIEW_AUDIT_TRAIL = "user:view_audit_trail"
    
    # Organization Management Permissions
    ORG_CREATE = "org:create"
    ORG_UPDATE = "org:update"
    ORG_DELETE = "org:delete"
    ORG_MANAGE_SETTINGS = "org:manage_settings"
    ORG_VIEW_ANALYTICS = "org:view_analytics"
    
    # System Administration Permissions
    SYSTEM_CONFIGURE = "system:configure"
    SYSTEM_BACKUP = "system:backup"
    SYSTEM_RESTORE = "system:restore"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_MANAGE_INTEGRATIONS = "system:manage_integrations"
    
    # Audit & Reporting Permissions
    AUDIT_VIEW_LOGS = "audit:view_logs"
    AUDIT_EXPORT_LOGS = "audit:export_logs"
    AUDIT_CREATE_REPORTS = "audit:create_reports"
    AUDIT_SCHEDULE_REPORTS = "audit:schedule_reports"
    AUDIT_MANAGE_RETENTION = "audit:manage_retention"
    
    # Electronic Signature Permissions (21 CFR Part 11)
    ESIG_CREATE = "esig:create"
    ESIG_VERIFY = "esig:verify"
    ESIG_MANAGE_CERTIFICATES = "esig:manage_certificates"
    ESIG_VIEW_HISTORY = "esig:view_history"


class RolePermissionMatrix:
    """
    Pharmaceutical role-permission matrix following PRD.md specifications.
    
    Implements least-privilege principle with hierarchical permissions.
    """
    
    # Define role hierarchy (higher roles inherit lower role permissions)
    ROLE_HIERARCHY = {
        PharmaceuticalRole.SUPER_ADMIN: 9,
        PharmaceuticalRole.ORG_ADMIN: 8,
        PharmaceuticalRole.QUALITY_MANAGER: 7,
        PharmaceuticalRole.REGULATORY_LEAD: 6,
        PharmaceuticalRole.COMPLIANCE_OFFICER: 5,
        PharmaceuticalRole.DOCUMENT_REVIEWER: 4,
        PharmaceuticalRole.ANALYST: 3,
        PharmaceuticalRole.AUDITOR: 2,
        PharmaceuticalRole.VIEWER: 1,
    }
    
    # Base permissions for each role
    ROLE_PERMISSIONS: Dict[PharmaceuticalRole, Set[PharmaceuticalPermissions]] = {
        
        PharmaceuticalRole.VIEWER: {
            # Basic read-only access
            PharmaceuticalPermissions.DOCUMENT_READ,
            PharmaceuticalPermissions.QUALITY_VIEW_REPORTS,
            PharmaceuticalPermissions.COMPLIANCE_VIEW_DASHBOARD,
            PharmaceuticalPermissions.USER_READ,
        },
        
        PharmaceuticalRole.AUDITOR: {
            # Audit and compliance monitoring
            PharmaceuticalPermissions.AUDIT_VIEW_LOGS,
            PharmaceuticalPermissions.AUDIT_CREATE_REPORTS,
            PharmaceuticalPermissions.COMPLIANCE_VIEW_DASHBOARD,
            PharmaceuticalPermissions.COMPLIANCE_CREATE_DEVIATION,
            PharmaceuticalPermissions.USER_VIEW_AUDIT_TRAIL,
            PharmaceuticalPermissions.ESIG_VIEW_HISTORY,
        },
        
        PharmaceuticalRole.ANALYST: {
            # Data analysis and reporting
            PharmaceuticalPermissions.QUALITY_VIEW_REPORTS,
            PharmaceuticalPermissions.COMPLIANCE_VIEW_DASHBOARD,
            PharmaceuticalPermissions.AUDIT_CREATE_REPORTS,
            PharmaceuticalPermissions.ORG_VIEW_ANALYTICS,
        },
        
        PharmaceuticalRole.DOCUMENT_REVIEWER: {
            # Document review and approval
            PharmaceuticalPermissions.DOCUMENT_READ,
            PharmaceuticalPermissions.DOCUMENT_REVIEW,
            PharmaceuticalPermissions.DOCUMENT_APPROVE,
            PharmaceuticalPermissions.ESIG_CREATE,
            PharmaceuticalPermissions.ESIG_VERIFY,
        },
        
        PharmaceuticalRole.COMPLIANCE_OFFICER: {
            # Compliance management and monitoring
            PharmaceuticalPermissions.COMPLIANCE_VIEW_DASHBOARD,
            PharmaceuticalPermissions.COMPLIANCE_CREATE_DEVIATION,
            PharmaceuticalPermissions.COMPLIANCE_INVESTIGATE_DEVIATION,
            PharmaceuticalPermissions.COMPLIANCE_APPROVE_CAPA,
            PharmaceuticalPermissions.COMPLIANCE_MANAGE_TRAINING,
            PharmaceuticalPermissions.AUDIT_VIEW_LOGS,
            PharmaceuticalPermissions.AUDIT_EXPORT_LOGS,
            PharmaceuticalPermissions.DOCUMENT_CREATE,
            PharmaceuticalPermissions.DOCUMENT_UPDATE,
        },
        
        PharmaceuticalRole.REGULATORY_LEAD: {
            # Regulatory submissions and correspondence
            PharmaceuticalPermissions.REGULATORY_CREATE_SUBMISSION,
            PharmaceuticalPermissions.REGULATORY_REVIEW_SUBMISSION,
            PharmaceuticalPermissions.REGULATORY_APPROVE_SUBMISSION,
            PharmaceuticalPermissions.REGULATORY_SUBMIT_TO_AGENCY,
            PharmaceuticalPermissions.REGULATORY_VIEW_CORRESPONDENCE,
            PharmaceuticalPermissions.REGULATORY_MANAGE_REGISTRATIONS,
            PharmaceuticalPermissions.DOCUMENT_CREATE,
            PharmaceuticalPermissions.DOCUMENT_UPDATE,
            PharmaceuticalPermissions.DOCUMENT_SIGN,
        },
        
        PharmaceuticalRole.QUALITY_MANAGER: {
            # Quality control and batch management
            PharmaceuticalPermissions.QUALITY_CREATE_BATCH,
            PharmaceuticalPermissions.QUALITY_REVIEW_BATCH,
            PharmaceuticalPermissions.QUALITY_APPROVE_BATCH,
            PharmaceuticalPermissions.QUALITY_REJECT_BATCH,
            PharmaceuticalPermissions.QUALITY_MANAGE_SPECS,
            PharmaceuticalPermissions.DOCUMENT_CREATE,
            PharmaceuticalPermissions.DOCUMENT_UPDATE,
            PharmaceuticalPermissions.DOCUMENT_APPROVE,
            PharmaceuticalPermissions.DOCUMENT_SIGN,
        },
        
        PharmaceuticalRole.ORG_ADMIN: {
            # Organization management
            PharmaceuticalPermissions.ORG_UPDATE,
            PharmaceuticalPermissions.ORG_MANAGE_SETTINGS,
            PharmaceuticalPermissions.ORG_VIEW_ANALYTICS,
            PharmaceuticalPermissions.USER_CREATE,
            PharmaceuticalPermissions.USER_UPDATE,
            PharmaceuticalPermissions.USER_ASSIGN_ROLES,
            PharmaceuticalPermissions.SYSTEM_CONFIGURE,
            PharmaceuticalPermissions.SYSTEM_MANAGE_INTEGRATIONS,
            PharmaceuticalPermissions.AUDIT_SCHEDULE_REPORTS,
            PharmaceuticalPermissions.ESIG_MANAGE_CERTIFICATES,
        },
        
        PharmaceuticalRole.SUPER_ADMIN: {
            # Full system administration
            PharmaceuticalPermissions.ORG_CREATE,
            PharmaceuticalPermissions.ORG_DELETE,
            PharmaceuticalPermissions.USER_DELETE,
            PharmaceuticalPermissions.USER_MANAGE_PERMISSIONS,
            PharmaceuticalPermissions.SYSTEM_BACKUP,
            PharmaceuticalPermissions.SYSTEM_RESTORE,
            PharmaceuticalPermissions.SYSTEM_MONITOR,
            PharmaceuticalPermissions.AUDIT_MANAGE_RETENTION,
            PharmaceuticalPermissions.DOCUMENT_DELETE,
            PharmaceuticalPermissions.DOCUMENT_ARCHIVE,
        },
    }


@lru_cache(maxsize=32)
def get_role_permissions(role: PharmaceuticalRole) -> Set[PharmaceuticalPermissions]:
    """
    Get all permissions for a role including inherited permissions.
    
    Args:
        role: Pharmaceutical role
        
    Returns:
        Set of permissions for the role
    """
    permissions = set()
    role_level = RolePermissionMatrix.ROLE_HIERARCHY[role]
    
    # Add permissions from all roles at or below this level
    for check_role, level in RolePermissionMatrix.ROLE_HIERARCHY.items():
        if level <= role_level:
            permissions.update(RolePermissionMatrix.ROLE_PERMISSIONS.get(check_role, set()))
    
    return permissions


@lru_cache(maxsize=128)
def has_permission(role: PharmaceuticalRole, permission: PharmaceuticalPermissions) -> bool:
    """
    Check if a role has a specific permission.
    
    Args:
        role: Pharmaceutical role
        permission: Permission to check
        
    Returns:
        True if role has permission, False otherwise
    """
    role_permissions = get_role_permissions(role)
    return permission in role_permissions


def can_access_organization(user_role: PharmaceuticalRole, target_org_id: str, user_org_id: str) -> bool:
    """
    Check if user can access resources from target organization.
    
    Args:
        user_role: User's pharmaceutical role
        target_org_id: Target organization ID
        user_org_id: User's organization ID
        
    Returns:
        True if access allowed, False otherwise
    """
    # Super admin can access all organizations
    if user_role == PharmaceuticalRole.SUPER_ADMIN:
        return True
    
    # All other roles can only access their own organization
    return target_org_id == user_org_id


class RoleChecker:
    """Utility class for role-based access control checks."""
    
    @staticmethod
    def is_admin_role(role: PharmaceuticalRole) -> bool:
        """Check if role is an administrative role."""
        return role in {
            PharmaceuticalRole.SUPER_ADMIN,
            PharmaceuticalRole.ORG_ADMIN,
        }
    
    @staticmethod
    def is_quality_role(role: PharmaceuticalRole) -> bool:
        """Check if role has quality management responsibilities."""
        return role in {
            PharmaceuticalRole.SUPER_ADMIN,
            PharmaceuticalRole.ORG_ADMIN,
            PharmaceuticalRole.QUALITY_MANAGER,
        }
    
    @staticmethod
    def is_regulatory_role(role: PharmaceuticalRole) -> bool:
        """Check if role has regulatory responsibilities."""
        return role in {
            PharmaceuticalRole.SUPER_ADMIN,
            PharmaceuticalRole.ORG_ADMIN,
            PharmaceuticalRole.REGULATORY_LEAD,
        }
    
    @staticmethod
    def is_compliance_role(role: PharmaceuticalRole) -> bool:
        """Check if role has compliance responsibilities."""
        return role in {
            PharmaceuticalRole.SUPER_ADMIN,
            PharmaceuticalRole.ORG_ADMIN,
            PharmaceuticalRole.COMPLIANCE_OFFICER,
        }
    
    @staticmethod
    def can_manage_users(role: PharmaceuticalRole) -> bool:
        """Check if role can manage users."""
        return has_permission(role, PharmaceuticalPermissions.USER_CREATE)
    
    @staticmethod
    def can_sign_documents(role: PharmaceuticalRole) -> bool:
        """Check if role can electronically sign documents."""
        return has_permission(role, PharmaceuticalPermissions.DOCUMENT_SIGN)


class PermissionChecker:
    """Utility class for permission-based access control checks."""
    
    @staticmethod
    def check_document_access(role: PharmaceuticalRole, action: str) -> bool:
        """Check document access permissions."""
        permission_map = {
            "create": PharmaceuticalPermissions.DOCUMENT_CREATE,
            "read": PharmaceuticalPermissions.DOCUMENT_READ,
            "update": PharmaceuticalPermissions.DOCUMENT_UPDATE,
            "delete": PharmaceuticalPermissions.DOCUMENT_DELETE,
            "approve": PharmaceuticalPermissions.DOCUMENT_APPROVE,
            "review": PharmaceuticalPermissions.DOCUMENT_REVIEW,
            "sign": PharmaceuticalPermissions.DOCUMENT_SIGN,
            "archive": PharmaceuticalPermissions.DOCUMENT_ARCHIVE,
        }
        
        permission = permission_map.get(action)
        if not permission:
            return False
        
        return has_permission(role, permission)
    
    @staticmethod
    def check_quality_access(role: PharmaceuticalRole, action: str) -> bool:
        """Check quality management permissions."""
        permission_map = {
            "create_batch": PharmaceuticalPermissions.QUALITY_CREATE_BATCH,
            "review_batch": PharmaceuticalPermissions.QUALITY_REVIEW_BATCH,
            "approve_batch": PharmaceuticalPermissions.QUALITY_APPROVE_BATCH,
            "reject_batch": PharmaceuticalPermissions.QUALITY_REJECT_BATCH,
            "view_reports": PharmaceuticalPermissions.QUALITY_VIEW_REPORTS,
            "manage_specs": PharmaceuticalPermissions.QUALITY_MANAGE_SPECS,
        }
        
        permission = permission_map.get(action)
        if not permission:
            return False
        
        return has_permission(role, permission)
    
    @staticmethod
    def check_audit_access(role: PharmaceuticalRole, action: str) -> bool:
        """Check audit and reporting permissions."""
        permission_map = {
            "view_logs": PharmaceuticalPermissions.AUDIT_VIEW_LOGS,
            "export_logs": PharmaceuticalPermissions.AUDIT_EXPORT_LOGS,
            "create_reports": PharmaceuticalPermissions.AUDIT_CREATE_REPORTS,
            "schedule_reports": PharmaceuticalPermissions.AUDIT_SCHEDULE_REPORTS,
            "manage_retention": PharmaceuticalPermissions.AUDIT_MANAGE_RETENTION,
        }
        
        permission = permission_map.get(action)
        if not permission:
            return False
        
        return has_permission(role, permission)

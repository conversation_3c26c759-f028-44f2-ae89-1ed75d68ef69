-- Migration: Add comprehensive user settings columns to user_profiles table
-- Date: 2025-07-14
-- Purpose: Add missing JSONB columns for user preferences, activity, achievements, compliance settings, notifications, and security

-- Check if columns exist before adding them
DO $$
BEGIN
    -- Add preferences column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'preferences') THEN
        ALTER TABLE user_profiles ADD COLUMN preferences JSONB DEFAULT '{}';
    END IF;

    -- Add activity_log column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'activity_log') THEN
        ALTER TABLE user_profiles ADD COLUMN activity_log JSONB DEFAULT '[]';
    END IF;

    -- Add achievements column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'achievements') THEN
        ALTER TABLE user_profiles ADD COLUMN achievements JSONB DEFAULT '[]';
    END IF;

    -- Add compliance_settings column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'compliance_settings') THEN
        ALTER TABLE user_profiles ADD COLUMN compliance_settings JSONB DEFAULT '{}';
    END IF;

    -- Add notification_preferences column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'notification_preferences') THEN
        ALTER TABLE user_profiles ADD COLUMN notification_preferences JSONB DEFAULT '{}';
    END IF;

    -- Add security_settings column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'security_settings') THEN
        ALTER TABLE user_profiles ADD COLUMN security_settings JSONB DEFAULT '{}';
    END IF;

    -- Add bio column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'bio') THEN
        ALTER TABLE user_profiles ADD COLUMN bio TEXT;
    END IF;
END $$;

-- Add comments for documentation
COMMENT ON COLUMN user_profiles.preferences IS 'User interface and application preferences (theme, language, etc.)';
COMMENT ON COLUMN user_profiles.activity_log IS 'Array of user activity entries for audit trail';
COMMENT ON COLUMN user_profiles.achievements IS 'Array of user achievements and certifications';
COMMENT ON COLUMN user_profiles.compliance_settings IS 'Pharmaceutical compliance specific settings';
COMMENT ON COLUMN user_profiles.notification_preferences IS 'User notification and alert preferences';
COMMENT ON COLUMN user_profiles.security_settings IS 'User security preferences and settings';
COMMENT ON COLUMN user_profiles.bio IS 'User biography and professional description';

-- Create indexes for JSONB columns for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_preferences ON user_profiles USING GIN (preferences);
CREATE INDEX IF NOT EXISTS idx_user_profiles_compliance_settings ON user_profiles USING GIN (compliance_settings);
CREATE INDEX IF NOT EXISTS idx_user_profiles_notification_preferences ON user_profiles USING GIN (notification_preferences);

-- Set default values for existing users
UPDATE user_profiles
SET
  preferences = COALESCE(preferences, '{}'),
  activity_log = COALESCE(activity_log, '[]'),
  achievements = COALESCE(achievements, '[]'),
  compliance_settings = COALESCE(compliance_settings, '{}'),
  notification_preferences = COALESCE(notification_preferences, '{
    "email_notifications": true,
    "push_notifications": true,
    "compliance_alerts": true,
    "document_updates": true,
    "system_announcements": true
  }'),
  security_settings = COALESCE(security_settings, '{
    "two_factor_enabled": false,
    "session_timeout": 30,
    "login_notifications": true,
    "password_change_notifications": true
  }')
WHERE
  preferences IS NULL
  OR activity_log IS NULL
  OR achievements IS NULL
  OR compliance_settings IS NULL
  OR notification_preferences IS NULL
  OR security_settings IS NULL;

-- Add audit trigger for settings changes (only if audit_trail table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audit_trail') THEN
        CREATE OR REPLACE FUNCTION audit_user_settings_changes()
        RETURNS TRIGGER AS $func$
        BEGIN
          -- Log settings changes to audit_trail
          IF OLD.preferences IS DISTINCT FROM NEW.preferences
             OR OLD.compliance_settings IS DISTINCT FROM NEW.compliance_settings
             OR OLD.notification_preferences IS DISTINCT FROM NEW.notification_preferences
             OR OLD.security_settings IS DISTINCT FROM NEW.security_settings THEN

            INSERT INTO audit_trail (
              table_name,
              record_id,
              action,
              old_values,
              new_values,
              user_id,
              timestamp,
              ip_address,
              user_agent
            ) VALUES (
              'user_profiles',
              NEW.id,
              'UPDATE_SETTINGS',
              jsonb_build_object(
                'preferences', COALESCE(OLD.preferences, '{}'),
                'compliance_settings', COALESCE(OLD.compliance_settings, '{}'),
                'notification_preferences', COALESCE(OLD.notification_preferences, '{}'),
                'security_settings', COALESCE(OLD.security_settings, '{}')
              ),
              jsonb_build_object(
                'preferences', COALESCE(NEW.preferences, '{}'),
                'compliance_settings', COALESCE(NEW.compliance_settings, '{}'),
                'notification_preferences', COALESCE(NEW.notification_preferences, '{}'),
                'security_settings', COALESCE(NEW.security_settings, '{}')
              ),
              NEW.id,
              NOW(),
              COALESCE(inet_client_addr()::text, 'unknown'),
              COALESCE(current_setting('application_name', true), 'unknown')
            );
          END IF;

          RETURN NEW;
        END;
        $func$ LANGUAGE plpgsql SECURITY DEFINER;
    END IF;
END $$;

-- Create trigger for settings audit
DROP TRIGGER IF EXISTS trigger_audit_user_settings ON user_profiles;
CREATE TRIGGER trigger_audit_user_settings
  AFTER UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION audit_user_settings_changes();

-- Grant necessary permissions
GRANT SELECT, UPDATE ON user_profiles TO authenticated;
GRANT SELECT ON audit_trail TO authenticated;

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui-radix/avatar';
import { Badge } from '@/components/ui-radix/badge';
import { Button } from '@/components/ui-radix/button';
import { Card } from '@/components/ui-radix/card';
import { Checkbox } from '@/components/ui-radix/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui-radix/dropdown-menu';
import { Progress } from '@/components/ui-radix/progress';
import { Skeleton } from '@/components/ui-radix/skeleton';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui-radix/table';
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui-radix/tooltip';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';
import {
    AlertTriangle,
    ChevronDown,
    ChevronUp,
    ClipboardList,
    Copy,
    Download,
    Edit,
    Eye,
    FileText,
    History,
    MoreHorizontal,
    Share,
    Trash
} from 'lucide-react';
import * as React from 'react';
import type { Document } from '../types';
import { CategoryBadge } from './category-badge';
import { FileTypeIcon } from './file-type-icon';
import { StatusBadge } from './status-badge';

interface DocumentTableProps {
  documents: Document[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (column: string) => void;
  onView: (id: number) => void;
  onDownload: (id: number) => void;
  onShare: (id: number) => void;
  onDelete: (id: number) => void;
  selectedDocuments: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  showSelection?: boolean;
  isLoading?: boolean;
}

interface Column {
  key: string;
  title: string;
  width: string;
  minWidth: string;
  sortable: boolean;
  resizable: boolean;
}

/**
 * DocumentTable Component - Comprehensive table view for documents
 *
 * Features:
 * - Sortable columns with visual indicators
 * - Row selection with checkboxes
 * - Rich column content with icons, badges, and progress bars
 * - Responsive behavior with column hiding
 * - Accessibility with proper ARIA labels
 * - Loading and empty states
 */
export const DocumentTable: React.FC<DocumentTableProps> = ({
  documents,
  sortBy,
  sortOrder,
  onSort,
  onView,
  onDownload,
  onShare,
  onDelete,
  selectedDocuments,
  onSelectionChange,
  showSelection = false,
  isLoading = false
}) => {
  const columns: Column[] = [
    ...(showSelection ? [{
      key: 'selection',
      title: '',
      width: '48px',
      minWidth: '48px',
      sortable: false,
      resizable: false
    }] : []),
    {
      key: 'name',
      title: 'Document Name',
      width: '40%',
      minWidth: '300px',
      sortable: true,
      resizable: true
    },
    {
      key: 'type',
      title: 'Type',
      width: '80px',
      minWidth: '80px',
      sortable: true,
      resizable: false
    },
    {
      key: 'category',
      title: 'Category',
      width: '120px',
      minWidth: '120px',
      sortable: true,
      resizable: true
    },
    {
      key: 'status',
      title: 'Status',
      width: '140px',
      minWidth: '140px',
      sortable: true,
      resizable: true
    },
    {
      key: 'complianceScore',
      title: 'Compliance',
      width: '160px',
      minWidth: '160px',
      sortable: true,
      resizable: true
    },
    {
      key: 'author',
      title: 'Author',
      width: '160px',
      minWidth: '160px',
      sortable: true,
      resizable: true
    },
    {
      key: 'uploadDate',
      title: 'Upload Date',
      width: '120px',
      minWidth: '120px',
      sortable: true,
      resizable: true
    },
    {
      key: 'actions',
      title: 'Actions',
      width: '80px',
      minWidth: '80px',
      sortable: false,
      resizable: false
    }
  ];

  // File type color mapping
  const getFileTypeBadgeColor = (type: string) => {
    const colors = {
      'PDF': 'bg-destructive/10 text-destructive border-destructive/20',
      'DOCX': 'bg-primary/10 text-primary border-primary/20',
      'XLSX': 'bg-success/10 text-success border-success/20',
      'TXT': 'bg-muted text-muted-foreground border-border',
      'PPT': 'bg-warning/10 text-warning border-warning/20',
      'CSV': 'bg-secondary text-secondary-foreground border-border'
    };
    return colors[type as keyof typeof colors] || 'bg-muted text-muted-foreground border-border';
  };

  // Handle row click
  const handleRowClick = (document: Document) => {
    onView(document.id);
  };

  // Handle sort
  const handleSort = (columnKey: string) => {
    if (columns.find(col => col.key === columnKey)?.sortable) {
      onSort(columnKey);
    }
  };

  // Render sort indicator
  const renderSortIndicator = (columnKey: string) => {
    if (!columns.find(col => col.key === columnKey)?.sortable) return null;

    return (
      <div className="inline-flex flex-col ml-1">
        <ChevronUp
          className={cn(
            "h-3 w-3 -mb-1",
            sortBy === columnKey && sortOrder === 'asc'
              ? "text-foreground"
              : "text-muted-foreground/50"
          )}
        />
        <ChevronDown
          className={cn(
            "h-3 w-3",
            sortBy === columnKey && sortOrder === 'desc'
              ? "text-foreground"
              : "text-muted-foreground/50"
          )}
        />
      </div>
    );
  };

  // Loading skeleton
  if (isLoading) {
    return (
      <Card className="relative overflow-auto">
        <Table className="text-xs">
          <TableHeader>
            <TableRow>
              {columns.map(column => (
                <TableHead key={column.key} style={{ width: column.width }}>
                  <Skeleton className="h-4 w-3/4" />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                {columns.map(column => (
                  <TableCell key={column.key} style={{ width: column.width }}>
                    <Skeleton className="h-4 w-full" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    );
  }

  // Empty state
  if (documents.length === 0) {
    return (
      <Card className="relative overflow-auto">
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No documents found
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Try adjusting your search criteria or filters to find what you're looking for.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="relative overflow-auto">
      <Table className="text-xs">
        <TableHeader className="sticky top-0 bg-card z-10">
          <TableRow className="border-border bg-muted/50">
            {columns.map((column) => (
              <TableHead
                key={column.key}
                className={cn(
                  "font-medium text-muted-foreground uppercase text-xs tracking-wide h-11 px-4",
                  column.sortable && "cursor-pointer select-none hover:text-foreground transition-colors"
                )}
                style={{ width: column.width, minWidth: column.minWidth }}
                onClick={() => handleSort(column.key)}
              >
                <div className="flex items-center">
                  <span>{column.title}</span>
                  {renderSortIndicator(column.key)}
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document, rowIndex) => (
            <TableRow
              key={document.id}
              className={cn(
                "cursor-pointer transition-colors border-border hover:bg-muted/50",
                "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                selectedDocuments.includes(document.id) && "bg-primary/5 border-primary/20",
                rowIndex % 2 === 1 && "bg-muted/25"
              )}
              onClick={() => handleRowClick(document)}
              tabIndex={0}
              role="button"
              aria-label={`View details for ${document.name}`}
            >
              {/* Selection Column */}
              {showSelection && (
                <TableCell className="px-4 py-3" style={{ width: '48px' }}>
                  <div className="flex items-center justify-center">
                    <Checkbox
                      checked={selectedDocuments.includes(document.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          onSelectionChange([...selectedDocuments, document.id]);
                        } else {
                          onSelectionChange(selectedDocuments.filter(id => id !== document.id));
                        }
                      }}
                      aria-label={`Select ${document.name}`}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                </TableCell>
              )}

              {/* Document Name Column */}
              <TableCell className="px-4 py-3" style={{ width: '40%', minWidth: '300px' }}>
                <div className="flex items-center space-x-3 min-w-0">
                  <div className="flex-shrink-0">
                    <FileTypeIcon
                      type={document.type}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center space-x-2">
                      <p
                        className="font-medium text-foreground truncate cursor-pointer hover:text-primary transition-colors"
                        title={document.name}
                        onClick={(e) => {
                          e.stopPropagation();
                          onView(document.id);
                        }}
                      >
                        {document.name}
                      </p>
                      {document.versions.history.length > 1 && (
                        <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                          v{document.versions.current}
                        </Badge>
                      )}
                    </div>
                    {document.metadata.pageCount && (
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {document.metadata.pageCount} pages
                      </p>
                    )}
                  </div>
                </div>
              </TableCell>

              {/* Type Column */}
              <TableCell className="px-4 py-3" style={{ width: '80px' }}>
                <Badge
                  variant="outline"
                  className={cn(
                    "font-mono text-xs font-medium px-2 py-1",
                    getFileTypeBadgeColor(document.type)
                  )}
                >
                  {document.type}
                </Badge>
              </TableCell>

              {/* Category Column */}
              <TableCell className="px-4 py-3" style={{ width: '120px' }}>
                <CategoryBadge
                  category={document.category}
                  size="sm"
                  showIcon={true}
                />
              </TableCell>

              {/* Status Column */}
              <TableCell className="px-4 py-3" style={{ width: '140px' }}>
                <StatusBadge status={document.status} size="sm" />
              </TableCell>

              {/* Compliance Column */}
              <TableCell className="px-4 py-3" style={{ width: '160px' }}>
                <div className="space-y-2">
                  {/* Progress bar and percentage */}
                  <div className="flex items-center space-x-2">
                    <Progress
                      value={document.complianceScore}
                      className="h-2 flex-1"
                    />
                    <span className={cn(
                      "text-xs font-medium min-w-[2.5rem] text-right",
                      document.complianceScore >= 90 ? "text-success" :
                      document.complianceScore >= 75 ? "text-warning" : "text-destructive"
                    )}>
                      {document.complianceScore}%
                    </span>
                  </div>

                  {/* Findings */}
                  {(document.complianceChecks.criticalIssues > 0 || document.complianceChecks.findings > 0) && (
                    <div className="flex items-center gap-1.5">
                      {document.complianceChecks.criticalIssues > 0 && (
                        <Tooltip>
                          <TooltipTrigger>
                            <Badge className="bg-destructive/10 text-destructive border-destructive/20 text-[10px] px-2 py-1 font-medium shadow-sm hover:bg-destructive/10">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              {document.complianceChecks.criticalIssues}
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent className="bg-destructive text-destructive-foreground border-destructive">
                            <p className="text-xs">{document.complianceChecks.criticalIssues} critical issues</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                      {document.complianceChecks.findings > 0 && (
                        <Tooltip>
                          <TooltipTrigger>
                            <Badge className="bg-warning/10 text-warning border-warning/20 text-[10px] px-2 py-1 font-medium shadow-sm hover:bg-warning/10">
                              <ClipboardList className="h-3 w-3 mr-1" />
                              {document.complianceChecks.findings}
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent className="bg-warning text-warning-foreground border-warning">
                            <p className="text-xs">{document.complianceChecks.findings} findings</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Author Column */}
              <TableCell className="px-4 py-3" style={{ width: '160px' }}>
                <div className="flex items-center space-x-2">
                  <Avatar className="h-7 w-7">
                    <AvatarImage src={document.author.avatar} alt={document.author.name} />
                    <AvatarFallback className="text-xs">
                      {document.author.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <p className="text-xs font-medium text-foreground truncate">
                      {document.author.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(document.uploadDate), { addSuffix: true })}
                    </p>
                  </div>
                </div>
              </TableCell>

              {/* Upload Date Column */}
              <TableCell className="px-4 py-3" style={{ width: '120px' }}>
                <div className="text-xs">
                  <div className="text-foreground font-medium">
                    {format(new Date(document.uploadDate), 'MMM dd, yyyy')}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(document.uploadDate), 'h:mm a')}
                  </div>
                  {document.lastModified !== document.uploadDate && (
                    <div className="text-xs text-muted-foreground mt-0.5">
                      Modified: {formatDistanceToNow(new Date(document.lastModified), { addSuffix: true })}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Actions Column */}
              <TableCell className="px-4 py-3" style={{ width: '80px' }}>
                <div className="flex items-center justify-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu for {document.name}</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[200px]">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>

                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onView(document.id);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        Preview Document
                      </DropdownMenuItem>

                      {document.permissions.canDownload && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onDownload(document.id);
                        }}>
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </DropdownMenuItem>
                      )}

                      {document.permissions.canShare && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onShare(document.id);
                        }}>
                          <Share className="mr-2 h-4 w-4" />
                          Share
                        </DropdownMenuItem>
                      )}

                      {document.permissions.canEdit && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Add edit functionality
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Properties
                          </DropdownMenuItem>

                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Add duplicate functionality
                          }}>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                        </>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Add version history functionality
                      }}>
                        <History className="mr-2 h-4 w-4" />
                        Version History
                      </DropdownMenuItem>

                      {document.permissions.canDelete && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onDelete(document.id);
                            }}
                            className="text-destructive focus:text-destructive focus:bg-destructive/10"
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );
};

DocumentTable.displayName = 'DocumentTable';

-- VigiLens Database Schema - Migration 003
-- Create User Profiles Table - Supabase Auth Integration
-- CRITICAL: Links to auth.users(id) for Supabase Auth integration

-- User profiles table - Links to Supabase Auth
CREATE TABLE user_profiles (
    -- CRITICAL: Must reference auth.users(id) for Supabase Auth integration
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Organization relationship (multi-tenant)
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- Basic user information
    email VARCHAR(255) NOT NULL UNIQUE,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),

    -- Pharmaceutical role and permissions
    role user_role DEFAULT 'read_only',
    department VARCHAR(100),
    job_title VARCHAR(200),
    employee_id VARCHAR(100), -- For pharmaceutical compliance tracking

    -- Contact information
    phone VARCHAR(50),
    mobile VARCHAR(50),
    office_location VARCHAR(200),

    -- Professional information for pharmaceutical compliance
    professional_license_number VARCHAR(100),
    license_expiry_date DATE,
    certifications TEXT[], -- Array of professional certifications
    training_records JSONB DEFAULT '[]'::JSONB, -- Training completion records

    -- User preferences and settings
    preferences JSONB DEFAULT '{
        "language": "en",
        "timezone": "UTC",
        "date_format": "MM/DD/YYYY",
        "notifications": {
            "email_enabled": true,
            "in_app_enabled": true,
            "regulatory_updates": true,
            "document_alerts": true,
            "compliance_reminders": true
        },
        "dashboard": {
            "default_view": "overview",
            "widgets_enabled": ["compliance_score", "recent_documents", "pending_reviews"]
        }
    }'::JSONB,

    -- Security and compliance
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    email_verified_at TIMESTAMPTZ,
    last_login TIMESTAMPTZ,
    login_count INTEGER DEFAULT 0,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMPTZ,

    -- Multi-factor authentication
    mfa_enabled BOOLEAN DEFAULT false,
    mfa_secret VARCHAR(255), -- Encrypted TOTP secret
    backup_codes TEXT[], -- Encrypted backup codes

    -- Pharmaceutical compliance tracking
    gxp_training_completed BOOLEAN DEFAULT false,
    gxp_training_date DATE,
    gxp_training_expiry DATE,
    signature_on_file BOOLEAN DEFAULT false, -- For 21 CFR Part 11
    electronic_signature_id VARCHAR(255), -- Unique signature identifier

    -- Audit and compliance metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_profile_update TIMESTAMPTZ DEFAULT NOW(),

    -- Audit trail
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),

    -- Constraints
    CONSTRAINT user_profiles_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT user_profiles_full_name_length CHECK (char_length(full_name) >= 2),
    CONSTRAINT user_profiles_valid_license_dates CHECK (
        license_expiry_date IS NULL OR license_expiry_date > CURRENT_DATE
    ),
    CONSTRAINT user_profiles_valid_training_dates CHECK (
        gxp_training_date IS NULL OR
        gxp_training_expiry IS NULL OR
        gxp_training_expiry > gxp_training_date
    )
);

-- Create indexes for performance optimization
CREATE INDEX idx_user_profiles_organization_id ON user_profiles(organization_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_department ON user_profiles(department);
CREATE INDEX idx_user_profiles_is_active ON user_profiles(is_active);
CREATE INDEX idx_user_profiles_last_login ON user_profiles(last_login);
CREATE INDEX idx_user_profiles_created_at ON user_profiles(created_at);

-- GIN indexes for JSONB columns
CREATE INDEX idx_user_profiles_preferences ON user_profiles USING GIN(preferences);
CREATE INDEX idx_user_profiles_training_records ON user_profiles USING GIN(training_records);
CREATE INDEX idx_user_profiles_certifications ON user_profiles USING GIN(certifications);

-- Partial indexes for common queries
CREATE INDEX idx_user_profiles_active_users ON user_profiles(organization_id, role) WHERE is_active = true;
CREATE INDEX idx_user_profiles_verified_users ON user_profiles(organization_id) WHERE is_verified = true;

-- Composite index for authentication queries
CREATE INDEX idx_user_profiles_auth ON user_profiles(email, is_active, is_verified);

-- Add updated_at trigger
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to update last_profile_update when profile data changes
CREATE OR REPLACE FUNCTION update_last_profile_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update if actual profile data changed (not just login tracking)
    IF (OLD.full_name, OLD.role, OLD.department, OLD.preferences) IS DISTINCT FROM
       (NEW.full_name, NEW.role, NEW.department, NEW.preferences) THEN
        NEW.last_profile_update = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_profiles_last_profile_update
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_last_profile_update();

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles

-- Policy: Users can view profiles in their organization
CREATE POLICY "Users can view org profiles" ON user_profiles
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- Policy: Users can view and update their own profile
CREATE POLICY "Users can manage own profile" ON user_profiles
    FOR ALL USING (
        id = auth.uid()
    );

-- Policy: Admins can manage all profiles in their organization
CREATE POLICY "Admins can manage org profiles" ON user_profiles
    FOR ALL USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (auth.jwt() ->> 'role') = 'admin'
    );

-- Policy: No direct insert - handled through registration flow
CREATE POLICY "Controlled profile creation" ON user_profiles
    FOR INSERT WITH CHECK (
        id = auth.uid()
        AND organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- Create function to handle user registration and profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    org_id UUID;
    user_email TEXT;
BEGIN
    -- Get user email from auth.users
    SELECT email INTO user_email FROM auth.users WHERE id = NEW.id;

    -- For now, assign to demo organization - in production this would be handled differently
    SELECT id INTO org_id FROM organizations WHERE name = 'demo-pharma-corp' LIMIT 1;

    -- Create user profile
    INSERT INTO user_profiles (
        id,
        organization_id,
        email,
        full_name,
        role,
        is_active,
        is_verified
    ) VALUES (
        NEW.id,
        COALESCE(org_id, '550e8400-e29b-41d4-a716-************'),
        user_email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
        'read_only',
        true,
        false
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic profile creation on user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Create function to update user login tracking
CREATE OR REPLACE FUNCTION update_user_login(user_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE user_profiles
    SET
        last_login = NOW(),
        login_count = login_count + 1,
        failed_login_attempts = 0
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to handle failed login attempts
CREATE OR REPLACE FUNCTION handle_failed_login(user_email TEXT)
RETURNS VOID AS $$
DECLARE
    max_attempts INTEGER := 5;
    lockout_duration INTERVAL := '30 minutes';
BEGIN
    UPDATE user_profiles
    SET
        failed_login_attempts = failed_login_attempts + 1,
        account_locked_until = CASE
            WHEN failed_login_attempts + 1 >= max_attempts
            THEN NOW() + lockout_duration
            ELSE account_locked_until
        END
    WHERE email = user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user account is locked
CREATE OR REPLACE FUNCTION is_account_locked(user_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    locked_until TIMESTAMPTZ;
BEGIN
    SELECT account_locked_until INTO locked_until
    FROM user_profiles
    WHERE email = user_email;

    RETURN locked_until IS NOT NULL AND locked_until > NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Sample user profiles will be created automatically when users register
-- The handle_new_user() trigger will create profiles for new auth.users

-- Comments for documentation
COMMENT ON TABLE user_profiles IS 'User profiles linked to Supabase Auth with pharmaceutical compliance tracking';
COMMENT ON COLUMN user_profiles.id IS 'CRITICAL: References auth.users(id) for Supabase Auth integration';
COMMENT ON COLUMN user_profiles.training_records IS 'JSONB array of pharmaceutical training completion records';
COMMENT ON COLUMN user_profiles.electronic_signature_id IS 'Unique identifier for 21 CFR Part 11 electronic signatures';
COMMENT ON FUNCTION handle_new_user() IS 'Automatically creates user profile when new user registers';
COMMENT ON FUNCTION update_user_login(UUID) IS 'Updates login tracking for security and compliance';
COMMENT ON FUNCTION is_account_locked(TEXT) IS 'Checks if user account is locked due to failed login attempts';

#!/usr/bin/env python3
"""
Qdrant FDA Knowledge Base Setup
Sets up local Qdrant instance and populates with FDA compliance documents
Compliant with 21 CFR Part 11 requirements for electronic records
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Add backend to path for imports
sys.path.append(str(Path(__file__).parent))

from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct,
    Filter, FieldCondition, MatchValue
)

# Configure logging for audit trail (21 CFR Part 11 compliance)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fda_kb_audit.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FDAKnowledgeBaseSetup:
    """FDA Knowledge Base Setup with 21 CFR Part 11 Compliance"""

    def __init__(self, db_path: str = "./qdrant_fda_db"):
        self.db_path = db_path
        self.client = None
        self.collection_name = "fda_compliance_docs"
        self.fda_docs_dir = Path("D:/Buisness/Vigilen-ComplianceAI/app/backend/fda_docs")
        self.fda_rules_file = Path("D:/Buisness/Vigilen-ComplianceAI/app/backend/docs/FDA-Development-Rules.md")

        # Audit trail for compliance
        self.audit_log = []

    def log_action(self, action: str, details: Dict[str, Any]):
        """Log actions for 21 CFR Part 11 audit trail"""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details,
            "user": os.getenv("USERNAME", "system"),
            "system": "VigiLens-FDA-KB"
        }
        self.audit_log.append(audit_entry)
        logger.info(f"AUDIT: {action} - {details}")

    def setup_qdrant_client(self) -> bool:
        """Initialize local Qdrant client with file persistence"""
        try:
            # Create persistent local Qdrant instance
            self.client = QdrantClient(path=self.db_path)
            self.log_action("QDRANT_INIT", {"db_path": self.db_path, "status": "success"})
            logger.info(f"✅ Qdrant client initialized with persistent storage: {self.db_path}")
            return True
        except Exception as e:
            self.log_action("QDRANT_INIT", {"db_path": self.db_path, "status": "failed", "error": str(e)})
            logger.error(f"❌ Failed to initialize Qdrant client: {e}")
            return False

    def create_fda_collection(self) -> bool:
        """Create FDA compliance document collection"""
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            if any(col.name == self.collection_name for col in collections):
                logger.info(f"📁 Collection '{self.collection_name}' already exists")
                self.log_action("COLLECTION_EXISTS", {"collection": self.collection_name})
                return True

            # Create collection with vector configuration
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=1024,  # BGE-M3 embedding dimension
                    distance=Distance.COSINE
                )
            )

            self.log_action("COLLECTION_CREATED", {
                "collection": self.collection_name,
                "vector_size": 1024,
                "distance_metric": "cosine"
            })
            logger.info(f"✅ Created FDA collection: {self.collection_name}")
            return True

        except Exception as e:
            self.log_action("COLLECTION_CREATE_FAILED", {
                "collection": self.collection_name,
                "error": str(e)
            })
            logger.error(f"❌ Failed to create collection: {e}")
            return False

    def scan_fda_documents(self) -> List[Path]:
        """Scan FDA documents directory for processing"""
        fda_files = []

        # Scan PDF files in fda_docs directory
        if self.fda_docs_dir.exists():
            pdf_files = list(self.fda_docs_dir.glob("*.pdf"))
            fda_files.extend(pdf_files)
            logger.info(f"📄 Found {len(pdf_files)} PDF files in {self.fda_docs_dir}")
        else:
            logger.warning(f"⚠️ FDA docs directory not found: {self.fda_docs_dir}")

        # Add FDA Development Rules markdown file
        if self.fda_rules_file.exists():
            fda_files.append(self.fda_rules_file)
            logger.info(f"📋 Found FDA Development Rules: {self.fda_rules_file}")
        else:
            logger.warning(f"⚠️ FDA Development Rules file not found: {self.fda_rules_file}")

        self.log_action("DOCUMENT_SCAN", {
            "total_files": len(fda_files),
            "pdf_count": len([f for f in fda_files if f.suffix == '.pdf']),
            "md_count": len([f for f in fda_files if f.suffix == '.md'])
        })

        return fda_files

    def test_qdrant_operations(self) -> bool:
        """Test basic Qdrant operations"""
        try:
            # Test point insertion
            test_point = PointStruct(
                id=999999,
                vector=[0.1] * 1024,  # Dummy vector
                payload={
                    "document_type": "FDA",
                    "title": "Test Document",
                    "source_file": "test.pdf",
                    "chunk_index": 0,
                    "content": "This is a test document for FDA compliance validation.",
                    "created_at": datetime.now().isoformat(),
                    "compliance_category": "21_CFR_Part_11"
                }
            )

            self.client.upsert(
                collection_name=self.collection_name,
                points=[test_point]
            )

            # Test retrieval
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=[0.1] * 1024,
                limit=1
            )

            # Test filtering
            filter_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="document_type",
                            match=MatchValue(value="FDA")
                        )
                    ]
                ),
                limit=1
            )

            # Clean up test point
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=[999999]
            )

            self.log_action("QDRANT_TEST", {
                "insert": "success",
                "search": "success",
                "filter": "success",
                "cleanup": "success"
            })

            logger.info("✅ Qdrant operations test passed")
            return True

        except Exception as e:
            self.log_action("QDRANT_TEST", {"status": "failed", "error": str(e)})
            logger.error(f"❌ Qdrant operations test failed: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """Get FDA collection information"""
        try:
            collection_info = self.client.get_collection(self.collection_name)
            count_result = self.client.count(self.collection_name)

            info = {
                "collection_name": self.collection_name,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value,
                "points_count": count_result.count,
                "status": collection_info.status.value
            }

            self.log_action("COLLECTION_INFO", info)
            return info

        except Exception as e:
            logger.error(f"❌ Failed to get collection info: {e}")
            return {}

    def save_audit_log(self):
        """Save audit log for 21 CFR Part 11 compliance"""
        audit_file = Path("fda_kb_setup_audit.json")
        try:
            with open(audit_file, 'w') as f:
                json.dump(self.audit_log, f, indent=2)
            logger.info(f"💾 Audit log saved: {audit_file}")
        except Exception as e:
            logger.error(f"❌ Failed to save audit log: {e}")

    def run_setup(self) -> bool:
        """Run complete FDA knowledge base setup"""
        logger.info("🚀 Starting FDA Knowledge Base Setup")
        logger.info("📋 Compliance: 21 CFR Part 11 Electronic Records")

        # Step 1: Initialize Qdrant
        if not self.setup_qdrant_client():
            return False

        # Step 2: Create FDA collection
        if not self.create_fda_collection():
            return False

        # Step 3: Test operations
        if not self.test_qdrant_operations():
            return False

        # Step 4: Scan FDA documents
        fda_files = self.scan_fda_documents()

        # Step 5: Get collection info
        collection_info = self.get_collection_info()

        # Step 6: Save audit log
        self.save_audit_log()

        logger.info("✅ FDA Knowledge Base Setup Complete")
        logger.info(f"📊 Collection Info: {collection_info}")
        logger.info(f"📁 FDA Files Found: {len(fda_files)}")

        return True

if __name__ == "__main__":
    setup = FDAKnowledgeBaseSetup()
    success = setup.run_setup()

    if success:
        print("\n🎉 FDA Knowledge Base Setup Successful!")
        print("📋 Ready for document population and QA testing")
        print("🔒 Audit trail maintained for 21 CFR Part 11 compliance")
    else:
        print("\n❌ FDA Knowledge Base Setup Failed")
        print("📋 Check logs for details")
        sys.exit(1)

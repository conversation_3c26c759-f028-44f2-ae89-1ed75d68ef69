#!/usr/bin/env python3
"""
FDA Knowledge Base Population Script

This script populates the knowledge base with FDA regulatory documents,
specifically the 21 CFR (Code of Federal Regulations) volumes.

Usage:
    python populate_knowledge_base.py [--docs-path PATH] [--vector-path PATH] [--validate-only]

Features:
- Processes all PDF files in the FDA documents directory
- Validates FDA-only content (no ICH/EMA documents)
- Generates progress reports and statistics
- Supports resume functionality for interrupted processing
- Comprehensive error handling and logging
"""

import argparse
import asyncio
import json
import logging
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any

from services.knowledge.fda_document_processor import FDADocumentProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fda_knowledge_population.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class KnowledgeBasePopulator:
    """Main class for populating the FDA knowledge base."""
    
    def __init__(
        self,
        docs_path: str = "./fda_docs",
        vector_path: str = "./data/qdrant",
        validate_only: bool = False
    ):
        """
        Initialize the knowledge base populator.
        
        Args:
            docs_path: Path to FDA documents directory
            vector_path: Path for vector storage
            validate_only: If True, only validate documents without processing
        """
        self.docs_path = Path(docs_path)
        self.vector_path = vector_path
        self.validate_only = validate_only
        
        # Initialize processor
        self.processor = FDADocumentProcessor(
            fda_docs_path=str(self.docs_path),
            vector_store_path=vector_path,
            chunk_size=1000,  # Optimal for BGE-M3
            chunk_overlap=200  # Good overlap for regulatory context
        )
        
        # Results tracking
        self.results = {
            "start_time": None,
            "end_time": None,
            "validation_results": {},
            "processing_results": {},
            "errors": [],
            "warnings": []
        }
    
    async def run(self) -> Dict[str, Any]:
        """
        Run the knowledge base population process.
        
        Returns:
            Dictionary containing results and statistics
        """
        self.results["start_time"] = datetime.now(timezone.utc)
        logger.info("🚀 Starting FDA Knowledge Base Population")
        logger.info(f"📁 Documents path: {self.docs_path}")
        logger.info(f"🗄️ Vector store path: {self.vector_path}")
        
        try:
            # Step 1: Validate FDA documents
            logger.info("\n📋 STEP 1: VALIDATING FDA DOCUMENTS")
            validation_results = await self.validate_fda_documents()
            self.results["validation_results"] = validation_results
            
            if not validation_results["is_valid"]:
                logger.error("❌ Document validation failed. Aborting.")
                return self.results
            
            logger.info("✅ Document validation passed")
            
            if self.validate_only:
                logger.info("🔍 Validation-only mode. Skipping processing.")
                return self.results
            
            # Step 2: Initialize processor
            logger.info("\n📋 STEP 2: INITIALIZING PROCESSOR")
            await self.processor.initialize()
            logger.info("✅ Processor initialized")
            
            # Step 3: Process documents
            logger.info("\n📋 STEP 3: PROCESSING FDA DOCUMENTS")
            processing_results = await self.processor.process_all_documents()
            self.results["processing_results"] = processing_results
            
            # Step 4: Generate summary
            logger.info("\n📋 STEP 4: GENERATING SUMMARY")
            await self.generate_summary()
            
            self.results["end_time"] = datetime.now(timezone.utc)
            duration = (self.results["end_time"] - self.results["start_time"]).total_seconds()
            
            logger.info(f"\n🎉 FDA Knowledge Base Population completed in {duration:.2f} seconds")
            return self.results
            
        except Exception as e:
            logger.error(f"💥 Fatal error in knowledge base population: {e}")
            self.results["errors"].append(str(e))
            raise
    
    async def validate_fda_documents(self) -> Dict[str, Any]:
        """
        Validate that all documents are FDA-related and suitable for processing.
        
        Returns:
            Validation results dictionary
        """
        validation_results = {
            "is_valid": True,
            "total_files": 0,
            "pdf_files": 0,
            "fda_files": 0,
            "non_fda_files": [],
            "invalid_files": [],
            "file_details": []
        }
        
        try:
            if not self.docs_path.exists():
                logger.error(f"❌ Documents directory not found: {self.docs_path}")
                validation_results["is_valid"] = False
                return validation_results
            
            # Get all files
            all_files = list(self.docs_path.iterdir())
            validation_results["total_files"] = len(all_files)
            
            # Filter PDF files
            pdf_files = [f for f in all_files if f.suffix.lower() == '.pdf']
            validation_results["pdf_files"] = len(pdf_files)
            
            logger.info(f"📊 Found {len(all_files)} total files, {len(pdf_files)} PDF files")
            
            if not pdf_files:
                logger.warning("⚠️ No PDF files found in documents directory")
                validation_results["is_valid"] = False
                return validation_results
            
            # Validate each PDF file
            for pdf_file in pdf_files:
                file_info = await self.validate_single_file(pdf_file)
                validation_results["file_details"].append(file_info)
                
                if file_info["is_fda"]:
                    validation_results["fda_files"] += 1
                else:
                    validation_results["non_fda_files"].append(pdf_file.name)
                
                if not file_info["is_valid"]:
                    validation_results["invalid_files"].append(pdf_file.name)
            
            # Check if we have any FDA files
            if validation_results["fda_files"] == 0:
                logger.error("❌ No FDA documents found")
                validation_results["is_valid"] = False
            
            # Warn about non-FDA files
            if validation_results["non_fda_files"]:
                logger.warning(f"⚠️ Found {len(validation_results['non_fda_files'])} non-FDA files:")
                for filename in validation_results["non_fda_files"]:
                    logger.warning(f"   - {filename}")
                self.results["warnings"].append(f"Non-FDA files found: {validation_results['non_fda_files']}")
            
            # Error on invalid files
            if validation_results["invalid_files"]:
                logger.error(f"❌ Found {len(validation_results['invalid_files'])} invalid files:")
                for filename in validation_results["invalid_files"]:
                    logger.error(f"   - {filename}")
                validation_results["is_valid"] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            validation_results["is_valid"] = False
            self.results["errors"].append(f"Validation error: {e}")
            return validation_results
    
    async def validate_single_file(self, pdf_file: Path) -> Dict[str, Any]:
        """
        Validate a single PDF file.
        
        Args:
            pdf_file: Path to PDF file
            
        Returns:
            File validation information
        """
        file_info = {
            "filename": pdf_file.name,
            "size_mb": round(pdf_file.stat().st_size / (1024 * 1024), 2),
            "is_valid": True,
            "is_fda": False,
            "document_type": "unknown",
            "issues": []
        }
        
        try:
            filename_lower = pdf_file.name.lower()
            
            # Check if it's an FDA document
            fda_indicators = [
                "cfr",           # Code of Federal Regulations
                "fda",           # FDA documents
                "title-21",      # Title 21 CFR
                "21cfr",         # 21 CFR variations
                "federal"        # Federal regulations
            ]
            
            if any(indicator in filename_lower for indicator in fda_indicators):
                file_info["is_fda"] = True
                
                # Determine document type
                if "cfr" in filename_lower:
                    file_info["document_type"] = "cfr"
                elif "guidance" in filename_lower:
                    file_info["document_type"] = "guidance"
                elif "policy" in filename_lower:
                    file_info["document_type"] = "policy"
                else:
                    file_info["document_type"] = "regulation"
            
            # Check for non-FDA indicators (ICH, EMA, etc.)
            non_fda_indicators = ["ich", "ema", "who", "pmda", "tga", "health_canada"]
            if any(indicator in filename_lower for indicator in non_fda_indicators):
                file_info["is_fda"] = False
                file_info["issues"].append("Contains non-FDA regulatory agency indicators")
            
            # Check file size (warn if too large)
            if file_info["size_mb"] > 100:
                file_info["issues"].append(f"Large file size: {file_info['size_mb']} MB")
                self.results["warnings"].append(f"Large file: {pdf_file.name} ({file_info['size_mb']} MB)")
            
            # Try to open the PDF to check if it's valid
            try:
                import pymupdf
                doc = pymupdf.open(str(pdf_file))
                page_count = len(doc)
                doc.close()
                
                if page_count == 0:
                    file_info["is_valid"] = False
                    file_info["issues"].append("PDF has no pages")
                
            except Exception as e:
                file_info["is_valid"] = False
                file_info["issues"].append(f"Cannot open PDF: {e}")
            
            return file_info
            
        except Exception as e:
            file_info["is_valid"] = False
            file_info["issues"].append(f"Validation error: {e}")
            return file_info
    
    async def generate_summary(self) -> None:
        """Generate processing summary."""
        logger.info("\n📊 PROCESSING SUMMARY")
        logger.info("=" * 50)
        
        # Validation summary
        val_results = self.results["validation_results"]
        logger.info(f"📁 Total files found: {val_results['total_files']}")
        logger.info(f"📄 PDF files: {val_results['pdf_files']}")
        logger.info(f"🏛️ FDA files: {val_results['fda_files']}")
        
        # Processing summary
        if "processing_results" in self.results:
            proc_results = self.results["processing_results"]
            logger.info(f"📚 Documents processed: {proc_results['documents_processed']}")
            logger.info(f"📝 Chunks created: {proc_results['chunks_created']}")
            logger.info(f"🧠 Embeddings generated: {proc_results['embeddings_generated']}")
            logger.info(f"❌ Errors: {proc_results['errors']}")
            
            if proc_results.get("start_time") and proc_results.get("end_time"):
                duration = (proc_results["end_time"] - proc_results["start_time"]).total_seconds()
                logger.info(f"⏱️ Processing time: {duration:.2f} seconds")
        
        # Warnings and errors
        if self.results["warnings"]:
            logger.info(f"\n⚠️ Warnings: {len(self.results['warnings'])}")
            for warning in self.results["warnings"]:
                logger.warning(f"   - {warning}")
        
        if self.results["errors"]:
            logger.info(f"\n❌ Errors: {len(self.results['errors'])}")
            for error in self.results["errors"]:
                logger.error(f"   - {error}")
        
        # Save results to file
        results_file = Path("fda_knowledge_population_results.json")
        with open(results_file, 'w') as f:
            # Convert datetime objects to strings for JSON serialization
            json_results = self._serialize_results(self.results)
            json.dump(json_results, f, indent=2)
        
        logger.info(f"\n💾 Results saved to: {results_file}")
    
    def _serialize_results(self, obj: Any) -> Any:
        """Serialize datetime objects for JSON."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._serialize_results(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_results(item) for item in obj]
        else:
            return obj


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Populate FDA Knowledge Base with regulatory documents"
    )
    parser.add_argument(
        "--docs-path",
        default="./fda_docs",
        help="Path to FDA documents directory (default: ./fda_docs)"
    )
    parser.add_argument(
        "--vector-path",
        default="./data/qdrant",
        help="Path for vector storage (default: ./data/qdrant)"
    )
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only validate documents without processing"
    )
    
    args = parser.parse_args()
    
    # Create populator
    populator = KnowledgeBasePopulator(
        docs_path=args.docs_path,
        vector_path=args.vector_path,
        validate_only=args.validate_only
    )
    
    try:
        # Run population process
        results = await populator.run()
        
        # Exit with appropriate code
        if results["validation_results"]["is_valid"] and not results["errors"]:
            logger.info("✅ Knowledge base population completed successfully")
            sys.exit(0)
        else:
            logger.error("❌ Knowledge base population completed with errors")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ Process interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

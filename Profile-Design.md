# Profile Section – Hyper-Detailed UI/UX Specification (Next.js 15.3.4 + React 19.1.0)

> Version 2.0 • Based on Existing Codebase Analysis • Author: BMAD UX Council

## 1. Functional Overview
* Single-page hub for users to view and modify their personal information, avatar, security preferences, and account settings.
* Supports editable forms with validation (Valibot), avatar upload w/ preview, password change flow, and session management.
* Client component page `app/(main)/profile/page.tsx` utilizing `usePageMetadata`.

## 2. Page Architecture
```
ProfilePage (Client)
├── PageHeader (title + subtitle)
├── MainGrid (2 cols @lg) gap-6
│   ├── LeftColumn
│   │   ├── AvatarCard
│   │   ├── PersonalInfoCard
│   │   └── SecurityCard
│   └── RightColumn
│       ├── PreferencesCard
│       ├── SessionsCard
│       └── DangerZoneCard
└── Modals (AvatarUploader, PasswordModal)


## 1. Complete Page Structure & Layout

### 1.1. Root Container
```tsx
<DashboardLayout>
  <div className="space-y-6">
    {/* All content sections */}
  </div>
</DashboardLayout>
```

### 1.2. Page Header Section
```tsx
<div className="flex items-center justify-between">
  <div>
    <h1 className="text-3xl font-bold text-foreground">Profile</h1>
    <p className="text-muted-foreground mt-1">
      Manage your account settings and view your activity
    </p>
  </div>
  <div className="flex items-center space-x-2">
    <Button variant="outline">
      <Download className="mr-2 h-4 w-4" />
      Export Data
    </Button>
    <Button variant="outline">
      <Settings className="mr-2 h-4 w-4" />
      Account Settings
    </Button>
  </div>
</div>
```

## 2. Profile Header Card

### 2.1. Profile Header Structure
```tsx
<Card>
  <CardContent className="p-6">
    <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
      {/* Avatar Section */}
      <div className="relative">
        <div className="h-24 w-24 rounded-full bg-gradient-to-br from-primary to-primary/60 flex items-center justify-center text-white text-2xl font-bold">
          {user.name.split(' ').map(n => n[0]).join('')}
        </div>
        <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 border-2 border-background rounded-full"></div>
        {editMode && (
          <Button
            variant="outline"
            size="sm"
            className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 text-xs"
          >
            Change
          </Button>
        )}
      </div>

      {/* User Info Section */}
      <div className="flex-1">
        <div className="flex items-center space-x-3 mb-2">
          {editMode ? (
            <Input
              value={editedUser.name}
              onChange={(e) => setEditedUser({ ...editedUser, name: e.target.value })}
              className="text-2xl font-bold"
            />
          ) : (
            <h2 className="text-2xl font-bold text-foreground">{user.name}</h2>
          )}
          <Badge variant="secondary" className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
            Online
          </Badge>
        </div>

        {editMode ? (
          <div className="space-y-2">
            <Input
              placeholder="Title"
              value={editedUser.title}
              onChange={(e) => setEditedUser({ ...editedUser, title: e.target.value })}
            />
            <Input
              placeholder="Department"
              value={editedUser.department}
              onChange={(e) => setEditedUser({ ...editedUser, department: e.target.value })}
            />
            <Input
              placeholder="Email"
              value={editedUser.email}
              onChange={(e) => setEditedUser({ ...editedUser, email: e.target.value })}
            />
          </div>
        ) : (
          <div className="space-y-1">
            <p className="text-lg text-muted-foreground">{user.title}</p>
            <p className="text-sm text-muted-foreground">{user.department}</p>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        )}

        <div className="flex items-center space-x-4 mt-3 text-sm text-muted-foreground">
          <span className="flex items-center">
            <Calendar className="mr-1 h-4 w-4" />
            Joined {user.joinDate}
          </span>
          <span className="flex items-center">
            <MapPin className="mr-1 h-4 w-4" />
            {user.location}
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        {editMode ? (
          <>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save
            </Button>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button onClick={() => setEditMode(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Profile
            </Button>
            <Button variant="outline">
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
          </>
        )}
      </div>
    </div>
  </CardContent>
</Card>
```

## 3. Activity Statistics Section

### 3.1. Stats Grid Layout
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  {activityStats.map((stat, index) => (
    <Card key={index}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <stat.icon className="h-5 w-5 text-primary" />
          </div>
          <div>
            <p className="text-2xl font-bold text-foreground">{stat.value}</p>
            <p className="text-sm text-muted-foreground">{stat.label}</p>
            {stat.change && (
              <p className={`text-xs flex items-center ${
                stat.change > 0 ? "text-green-600" : "text-red-600"
              }`}>
                {stat.change > 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3" />
                )}
                {Math.abs(stat.change)}% from last month
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### 3.2. Activity Stats Data Structure
```tsx
const activityStats = [
  {
    icon: FileText,
    value: "127",
    label: "Documents Reviewed",
    change: 12
  },
  {
    icon: CheckCircle,
    value: "89",
    label: "Tasks Completed",
    change: -5
  },
  {
    icon: Clock,
    value: "24h",
    label: "Time This Week",
    change: 8
  },
  {
    icon: Award,
    value: "15",
    label: "Achievements",
    change: 0
  }
];
```

## 4. Profile Tabs System

### 4.1. Tabs Structure
```tsx
<Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
  <TabsList className="grid w-full grid-cols-4">
    <TabsTrigger value="overview">Overview</TabsTrigger>
    <TabsTrigger value="activity">Activity</TabsTrigger>
    <TabsTrigger value="achievements">Achievements</TabsTrigger>
    <TabsTrigger value="settings">Settings</TabsTrigger>
  </TabsList>

  <TabsContent value={selectedTab} className="space-y-4">
    {/* Tab content */}
  </TabsContent>
</Tabs>
```

## 5. Overview Tab Content

### 5.1. Recent Activity Section
```tsx
<Card>
  <CardHeader>
    <div className="flex items-center justify-between">
      <CardTitle>Recent Activity</CardTitle>
      <Button variant="outline" size="sm">
        View All
      </Button>
    </div>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      {recentActivity.map((activity, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
            <activity.icon className="h-4 w-4 text-primary" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">{activity.action}</p>
            <p className="text-xs text-muted-foreground">{activity.timestamp}</p>
          </div>
          {activity.badge && (
            <Badge variant="outline" className="text-xs">
              {activity.badge}
            </Badge>
          )}
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

### 5.2. Quick Stats Section
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <Card>
    <CardHeader>
      <CardTitle className="text-lg">This Week</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        {weeklyStats.map((stat, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{stat.label}</span>
            <span className="font-medium">{stat.value}</span>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>

  <Card>
    <CardHeader>
      <CardTitle className="text-lg">Performance</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        {performanceStats.map((stat, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{stat.label}</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary rounded-full"
                  style={{ width: `${stat.percentage}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium">{stat.percentage}%</span>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
</div>
```

## 6. Activity Tab Content

### 6.1. Activity Timeline Structure
```tsx
<Card>
  <CardHeader>
    <div className="flex items-center justify-between">
      <CardTitle>Activity Timeline</CardTitle>
      <Select value={activityFilter} onValueChange={setActivityFilter}>
        <SelectTrigger className="w-[150px]">
          <SelectValue placeholder="Filter" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Activity</SelectItem>
          <SelectItem value="documents">Documents</SelectItem>
          <SelectItem value="tasks">Tasks</SelectItem>
          <SelectItem value="logins">Logins</SelectItem>
        </SelectContent>
      </Select>
    </div>
  </CardHeader>
  <CardContent>
    <div className="space-y-6">
      {filteredActivity.map((day, dayIndex) => (
        <div key={dayIndex}>
          <div className="flex items-center space-x-2 mb-3">
            <h4 className="font-medium text-sm">{day.date}</h4>
            <div className="flex-1 h-px bg-border"></div>
          </div>
          <div className="space-y-3 ml-4">
            {day.activities.map((activity, activityIndex) => (
              <div key={activityIndex} className="flex items-start space-x-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10 mt-1">
                  <activity.icon className="h-3 w-3 text-primary" />
                </div>
                <div className="flex-1">
                  <p className="text-sm">{activity.description}</p>
                  <p className="text-xs text-muted-foreground">{activity.time}</p>
                </div>
                {activity.status && (
                  <Badge variant="outline" className="text-xs">
                    {activity.status}
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

## 7. Achievements Tab Content

### 7.1. Achievements Grid
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {achievements.map((achievement, index) => (
    <Card key={index} className={`transition-all duration-200 ${
      achievement.unlocked ? "border-primary/20 bg-primary/5" : "opacity-60"
    }`}>
      <CardContent className="p-4 text-center">
        <div className={`mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full ${
          achievement.unlocked ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
        }`}>
          <achievement.icon className="h-8 w-8" />
        </div>
        <h4 className="font-medium mb-1">{achievement.title}</h4>
        <p className="text-sm text-muted-foreground mb-2">{achievement.description}</p>
        {achievement.unlocked ? (
          <Badge variant="secondary" className="text-xs">
            Unlocked {achievement.unlockedDate}
          </Badge>
        ) : (
          <div className="space-y-2">
            <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
              <div
                className="h-full bg-primary rounded-full transition-all duration-300"
                style={{ width: `${achievement.progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground">
              {achievement.current}/{achievement.target} {achievement.unit}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  ))}
</div>
```

## 8. Settings Tab Content

### 8.1. Settings Form Structure
```tsx
<div className="space-y-6">
  {/* Personal Information */}
  <Card>
    <CardHeader>
      <CardTitle>Personal Information</CardTitle>
      <CardDescription>
        Update your personal details and contact information
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName">First Name</Label>
          <Input id="firstName" value={settings.firstName} onChange={handleSettingChange} />
        </div>
        <div>
          <Label htmlFor="lastName">Last Name</Label>
          <Input id="lastName" value={settings.lastName} onChange={handleSettingChange} />
        </div>
      </div>
      <div>
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" value={settings.email} onChange={handleSettingChange} />
      </div>
      <div>
        <Label htmlFor="bio">Bio</Label>
        <Textarea id="bio" value={settings.bio} onChange={handleSettingChange} rows={3} />
      </div>
    </CardContent>
  </Card>

  {/* Notification Preferences */}
  <Card>
    <CardHeader>
      <CardTitle>Notification Preferences</CardTitle>
      <CardDescription>
        Choose how you want to be notified about updates
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      {notificationSettings.map((setting, index) => (
        <div key={index} className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium">{setting.label}</Label>
            <p className="text-xs text-muted-foreground">{setting.description}</p>
          </div>
          <Switch
            checked={setting.enabled}
            onCheckedChange={(checked) => handleNotificationToggle(setting.key, checked)}
          />
        </div>
      ))}
    </CardContent>
  </Card>

  {/* Privacy Settings */}
  <Card>
    <CardHeader>
      <CardTitle>Privacy Settings</CardTitle>
      <CardDescription>
        Control who can see your profile and activity
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      <div>
        <Label htmlFor="profileVisibility">Profile Visibility</Label>
        <Select value={settings.profileVisibility} onValueChange={handleVisibilityChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="public">Public</SelectItem>
            <SelectItem value="team">Team Only</SelectItem>
            <SelectItem value="private">Private</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </CardContent>
  </Card>

  {/* Save Button */}
  <div className="flex justify-end">
    <Button onClick={handleSaveSettings} disabled={!hasChanges}>
      Save Changes
    </Button>
  </div>
</div>
```

## 9. State Management & Data Structures

### 9.1. User Interface
```tsx
interface User {
  id: string;
  name: string;
  title: string;
  department: string;
  email: string;
  joinDate: string;
  location: string;
  avatar?: string;
  status: "online" | "away" | "busy" | "offline";
}
```

### 9.2. State Variables
```tsx
const [user, setUser] = useState<User>(mockUser);
const [editMode, setEditMode] = useState(false);
const [editedUser, setEditedUser] = useState<User>(user);
const [selectedTab, setSelectedTab] = useState("overview");
const [activityFilter, setActivityFilter] = useState("all");
const [settings, setSettings] = useState(mockSettings);
const [hasChanges, setHasChanges] = useState(false);
```

## 10. Required Imports

```tsx
import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Edit, Save, Share, Calendar, MapPin, Download, Settings,
  FileText, CheckCircle, Clock, Award, TrendingUp, TrendingDown
} from "lucide-react";
```

---
End of hyper-detailed specification

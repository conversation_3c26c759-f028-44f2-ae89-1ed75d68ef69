/**
 * Quick Test Functions for <PERSON><PERSON><PERSON> Console
 * Simple tests to verify critical fixes are working
 */

// Simple test function that's immediately available
export function quickTest() {
  console.log('🧪 Quick Test - All functions loaded successfully!')
  console.log('✅ Test function is available in browser console')
  
  // Test environment
  console.log('🔧 Environment Check:', {
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL,
    currentUrl: window.location.href,
    port: window.location.port
  })
  
  return {
    status: 'success',
    message: 'Quick test completed successfully',
    timestamp: new Date().toISOString()
  }
}

// Test profile save functionality
export function testProfileSave() {
  console.log('🧪 Testing Profile Save Functionality...')
  
  // Check if we're on the profile page
  if (!window.location.pathname.includes('/profile')) {
    console.warn('⚠️ Navigate to /profile page first')
    return { status: 'warning', message: 'Please navigate to profile page first' }
  }
  
  // Look for save buttons
  const saveButtons = document.querySelectorAll('button:contains("Save")')
  console.log('🔍 Found save buttons:', saveButtons.length)
  
  // Check for edit mode
  const editButtons = document.querySelectorAll('button:contains("Edit")')
  console.log('🔍 Found edit buttons:', editButtons.length)
  
  return {
    status: 'info',
    message: 'Profile page analysis complete',
    saveButtons: saveButtons.length,
    editButtons: editButtons.length
  }
}

// Test settings save functionality
export function testSettingsSave() {
  console.log('🧪 Testing Settings Save Functionality...')
  
  // Check if we're on the settings page
  if (!window.location.pathname.includes('/settings')) {
    console.warn('⚠️ Navigate to /settings page first')
    return { status: 'warning', message: 'Please navigate to settings page first' }
  }
  
  // Look for notification switches
  const switches = document.querySelectorAll('[role="switch"]')
  console.log('🔍 Found switches:', switches.length)
  
  // Look for save buttons
  const saveButtons = document.querySelectorAll('button:contains("Save")')
  console.log('🔍 Found save buttons:', saveButtons.length)
  
  return {
    status: 'info',
    message: 'Settings page analysis complete',
    switches: switches.length,
    saveButtons: saveButtons.length
  }
}

// Make functions available globally immediately
if (typeof window !== 'undefined') {
  (window as any).quickTest = quickTest
  (window as any).testProfileSave = testProfileSave
  (window as any).testSettingsSave = testSettingsSave
  
  console.log('🔧 Quick test functions loaded!')
  console.log('📋 Available functions:')
  console.log('  - quickTest()')
  console.log('  - testProfileSave()')
  console.log('  - testSettingsSave()')
  console.log('  - runComprehensiveTests() (if loaded)')
}

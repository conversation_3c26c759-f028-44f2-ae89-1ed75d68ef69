'use client'

import { usePageMetadata } from '@/hooks/use-page-metadata'

import { AuthHeader } from './components/auth-header'
import { LoginForm } from './components/login-form'

/**
 * Login Page - AI Compliance Platform
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper Next.js navigation
 *
 * Migrated from: src/pages/Login.tsx (225 lines)
 * Broken down into 2 components for maintainability
 */
export default function LoginPage() {
  usePageMetadata('Sign In', 'Sign in to your AI Compliance dashboard')

  return (
    <>
      <AuthHeader />
      <LoginForm />
    </>
  )
}

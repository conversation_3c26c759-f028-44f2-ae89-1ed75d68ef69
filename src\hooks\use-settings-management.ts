'use client'

import { useState, useCallback } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { UserProfileService } from '@/lib/supabase-services'
import { toast } from 'sonner'
import {
  UserPreferences,
  NotificationPreferences,
  SecuritySettings,
  ComplianceSettings,
  DEFAULT_USER_PREFERENCES,
  DEFAULT_NOTIFICATION_PREFERENCES,
  DEFAULT_SECURITY_SETTINGS,
  DEFAULT_COMPLIANCE_SETTINGS,
  isUserPreferences,
  isNotificationPreferences,
  isSecuritySettings,
  isComplianceSettings
} from '@/types/user-settings'

interface ValidationError {
  field: string
  message: string
}

/**
 * Deep merge utility for nested objects
 */
function deepMerge(target: any, source: any): any {
  if (source === null || source === undefined) return target
  if (target === null || target === undefined) return source

  const result = { ...target }

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(target[key], source[key])
      } else {
        result[key] = source[key]
      }
    }
  }

  return result
}

interface UseSettingsManagementReturn {
  // Preferences
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>

  // Notifications
  updateNotificationPreferences: (preferences: Partial<NotificationPreferences>) => Promise<boolean>

  // Security
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => Promise<boolean>

  // Compliance
  updateComplianceSettings: (settings: Partial<ComplianceSettings>) => Promise<boolean>

  // State
  isUpdating: boolean
  validationErrors: ValidationError[]
  clearErrors: () => void

  // Getters with defaults
  getUserPreferences: () => UserPreferences
  getNotificationPreferences: () => NotificationPreferences
  getSecuritySettings: () => SecuritySettings
  getComplianceSettings: () => ComplianceSettings
}

/**
 * Comprehensive settings management hook following DEVELOPMENT_RULES.md
 * Handles all user settings with pharmaceutical compliance audit trail
 */
export function useSettingsManagement(): UseSettingsManagementReturn {
  const { userProfile, refreshUserProfile } = useAuth()
  const [isUpdating, setIsUpdating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])

  const profileService = new UserProfileService()

  /**
   * Get user preferences with fallback to defaults
   */
  const getUserPreferences = useCallback((): UserPreferences => {
    if (!userProfile?.preferences) return DEFAULT_USER_PREFERENCES

    const prefs = typeof userProfile.preferences === 'string'
      ? JSON.parse(userProfile.preferences)
      : userProfile.preferences

    return isUserPreferences(prefs) ? prefs : DEFAULT_USER_PREFERENCES
  }, [userProfile?.preferences])

  /**
   * Get notification preferences with fallback to defaults
   */
  const getNotificationPreferences = useCallback((): NotificationPreferences => {
    if (!userProfile?.notification_preferences) return DEFAULT_NOTIFICATION_PREFERENCES

    const prefs = typeof userProfile.notification_preferences === 'string'
      ? JSON.parse(userProfile.notification_preferences)
      : userProfile.notification_preferences

    return isNotificationPreferences(prefs) ? prefs : DEFAULT_NOTIFICATION_PREFERENCES
  }, [userProfile?.notification_preferences])

  /**
   * Get security settings with fallback to defaults
   */
  const getSecuritySettings = useCallback((): SecuritySettings => {
    if (!userProfile?.security_settings) return DEFAULT_SECURITY_SETTINGS

    const settings = typeof userProfile.security_settings === 'string'
      ? JSON.parse(userProfile.security_settings)
      : userProfile.security_settings

    return isSecuritySettings(settings) ? settings : DEFAULT_SECURITY_SETTINGS
  }, [userProfile?.security_settings])

  /**
   * Get compliance settings with fallback to defaults
   */
  const getComplianceSettings = useCallback((): ComplianceSettings => {
    if (!userProfile?.compliance_settings) return DEFAULT_COMPLIANCE_SETTINGS

    const settings = typeof userProfile.compliance_settings === 'string'
      ? JSON.parse(userProfile.compliance_settings)
      : userProfile.compliance_settings

    return isComplianceSettings(settings) ? settings : DEFAULT_COMPLIANCE_SETTINGS
  }, [userProfile?.compliance_settings])

  /**
   * Validate preferences data
   */
  const validatePreferences = useCallback((preferences: Partial<UserPreferences>): ValidationError[] => {
    const errors: ValidationError[] = []

    if (preferences.items_per_page && ![10, 25, 50, 100].includes(preferences.items_per_page)) {
      errors.push({ field: 'items_per_page', message: 'Items per page must be 10, 25, 50, or 100' })
    }

    if (preferences.theme && !['light', 'dark', 'system'].includes(preferences.theme)) {
      errors.push({ field: 'theme', message: 'Theme must be light, dark, or system' })
    }

    return errors
  }, [])

  /**
   * Validate security settings
   */
  const validateSecuritySettings = useCallback((settings: Partial<SecuritySettings>): ValidationError[] => {
    const errors: ValidationError[] = []

    if (settings.session_timeout && (settings.session_timeout < 5 || settings.session_timeout > 480)) {
      errors.push({ field: 'session_timeout', message: 'Session timeout must be between 5 and 480 minutes' })
    }

    if (settings.password_requirements?.min_length && settings.password_requirements.min_length < 8) {
      errors.push({ field: 'password_min_length', message: 'Minimum password length must be at least 8 characters' })
    }

    return errors
  }, [])

  /**
   * Generic update function with validation and error handling
   */
  const updateSettings = useCallback(async (
    settingsType: 'preferences' | 'notification_preferences' | 'security_settings' | 'compliance_settings',
    newSettings: any,
    validator?: (settings: any) => ValidationError[]
  ): Promise<boolean> => {
    if (!userProfile) {
      console.error('❌ Settings Management - User profile not loaded')
      toast.error('User profile not loaded')
      return false
    }

    // Clear previous errors
    setValidationErrors([])

    console.log('🔄 Settings Management - Starting update process:', {
      settingsType,
      newSettings,
      userProfileId: userProfile.id,
      timestamp: new Date().toISOString()
    })

    // Validate if validator provided
    if (validator) {
      console.log('🔍 Settings Management - Running validation...')
      const errors = validator(newSettings)
      if (errors.length > 0) {
        console.error('❌ Settings Management - Validation errors:', errors)
        setValidationErrors(errors)
        toast.error('Please fix validation errors before saving')
        return false
      }
      console.log('✅ Settings Management - Validation passed')
    }

    setIsUpdating(true)

    try {
      // Get current settings and merge with new ones
      let currentSettings = {}
      switch (settingsType) {
        case 'preferences':
          currentSettings = getUserPreferences()
          break
        case 'notification_preferences':
          currentSettings = getNotificationPreferences()
          break
        case 'security_settings':
          currentSettings = getSecuritySettings()
          break
        case 'compliance_settings':
          currentSettings = getComplianceSettings()
          break
      }

      // Deep merge for nested objects like quiet_hours
      const mergedSettings = deepMerge(currentSettings, newSettings)

      console.log('🔄 Settings Management - Starting update:', {
        settingsType,
        currentSettings,
        newSettings,
        mergedSettings,
        userId: userProfile.id,
        timestamp: new Date().toISOString()
      })

      // Update via service
      const { data: updatedProfile, error } = await profileService.updateUserProfile(
        userProfile.id,
        { [settingsType]: mergedSettings }
      )

      console.log('📊 Settings Management - Service response:', {
        settingsType,
        success: !error,
        error: error,
        updatedProfile: updatedProfile,
        timestamp: new Date().toISOString()
      })

      if (error) {
        console.error(`${settingsType} update failed:`, error)
        toast.error(error.message || `Failed to update ${settingsType.replace('_', ' ')}`)
        return false
      }

      if (!updatedProfile) {
        toast.error('No data returned from settings update')
        return false
      }

      // Refresh user profile in auth context
      await refreshUserProfile()

      // Show success message
      toast.success(`${settingsType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} updated successfully`)

      return true
    } catch (error) {
      console.error(`Unexpected error during ${settingsType} update:`, error)
      toast.error(`An unexpected error occurred while updating ${settingsType.replace('_', ' ')}`)
      return false
    } finally {
      setIsUpdating(false)
    }
  }, [userProfile, profileService, refreshUserProfile, getUserPreferences, getNotificationPreferences, getSecuritySettings, getComplianceSettings])

  /**
   * Update user preferences
   */
  const updatePreferences = useCallback(async (preferences: Partial<UserPreferences>): Promise<boolean> => {
    return updateSettings('preferences', preferences, validatePreferences)
  }, [updateSettings, validatePreferences])

  /**
   * Update notification preferences
   */
  const updateNotificationPreferences = useCallback(async (preferences: Partial<NotificationPreferences>): Promise<boolean> => {
    return updateSettings('notification_preferences', preferences)
  }, [updateSettings])

  /**
   * Update security settings
   */
  const updateSecuritySettings = useCallback(async (settings: Partial<SecuritySettings>): Promise<boolean> => {
    return updateSettings('security_settings', settings, validateSecuritySettings)
  }, [updateSettings, validateSecuritySettings])

  /**
   * Update compliance settings
   */
  const updateComplianceSettings = useCallback(async (settings: Partial<ComplianceSettings>): Promise<boolean> => {
    return updateSettings('compliance_settings', settings)
  }, [updateSettings])

  /**
   * Clear validation errors
   */
  const clearErrors = useCallback(() => {
    setValidationErrors([])
  }, [])

  return {
    updatePreferences,
    updateNotificationPreferences,
    updateSecuritySettings,
    updateComplianceSettings,
    isUpdating,
    validationErrors,
    clearErrors,
    getUserPreferences,
    getNotificationPreferences,
    getSecuritySettings,
    getComplianceSettings
  }
}

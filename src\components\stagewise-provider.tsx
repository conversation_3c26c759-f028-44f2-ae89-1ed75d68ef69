'use client'

import { useEffect } from 'react'

// Stagewise configuration
const stagewiseConfig = {
  plugins: [],
}

export function StagewiseProvider() {
  useEffect(() => {
    // Only initialize in development mode
    if (process.env.NODE_ENV === 'development') {
      import('@stagewise/toolbar').then(({ initToolbar }) => {
        initToolbar(stagewiseConfig)
      }).catch((error) => {
        console.warn('Failed to initialize Stagewise toolbar:', error)
      })
    }
  }, [])

  return null
}

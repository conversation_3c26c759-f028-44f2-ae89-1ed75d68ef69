'use client'

import { Bell, Search, Settings, User, LogOut } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui-radix/dropdown-menu'
import { Input } from '@/components/ui-radix/input'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui-radix/popover'
import { ThemeToggle } from '@/components/ui-radix/theme-toggle'
import { useAuth } from '@/contexts/auth-context'
import { toast } from 'sonner'

export function Header() {
  const router = useRouter()
  const { userProfile, signOut } = useAuth()
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  const handleLogout = async () => {
    console.log('🚨 Header Logout - STARTING IMMEDIATE LOGOUT...')

    if (!window.confirm('Are you sure you want to log out of VigiLens?')) {
      console.log('🔄 Header Logout - User cancelled logout')
      return
    }

    setIsLoggingOut(true)

    try {
      console.log('🧹 Header Logout - STEP 1: Force clearing all storage...')

      // Clear all storage immediately
      localStorage.clear()
      sessionStorage.clear()

      // Clear all cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      })

      console.log('✅ Header Logout - Storage cleared successfully')

      console.log('📤 Header Logout - STEP 2: Calling signOut...')

      // Call signOut but don't wait for it
      signOut().catch(error => {
        console.error('⚠️ Header Logout - SignOut error (ignoring):', error)
      })

      console.log('📱 Header Logout - STEP 3: Showing success message...')

      // Show success message
      toast.success('Logged out successfully', {
        description: 'You have been securely logged out of VigiLens.',
        duration: 1500
      })

      console.log('🔄 Header Logout - STEP 4: IMMEDIATE REDIRECT...')

      // IMMEDIATE redirect
      window.location.href = '/login'

    } catch (error) {
      console.error('💥 Header Logout - EMERGENCY LOGOUT:', error)

      // Force logout anyway
      localStorage.clear()
      sessionStorage.clear()

      toast.error('Emergency logout', {
        description: 'Forced logout due to error.',
        duration: 2000
      })

      window.location.href = '/login'
    } finally {
      setIsLoggingOut(false)
    }
  }

  return (
    <header className="h-16 flex items-center justify-between px-6 border-b border-border bg-card z-20">
      {/* Page Title */}
      <div className="flex items-center">
        <h2 className="text-[20px] font-bold text-foreground mr-8">
          Regulatory Autopilot
        </h2>
      </div>

      {/* Search Bar */}
      <div className="flex-1 max-w-[384px] mx-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Quick search for regulations..."
            className="h-10 w-full pl-10 rounded-lg border border-border bg-background text-[14px] text-foreground placeholder:text-muted-foreground focus:ring-2 focus:ring-primary"
            aria-label="Search regulations"
          />
        </div>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center gap-4">
        {/* Theme Toggle */}
        <ThemeToggle />

        {/* Notifications */}
        <Popover>
          <PopoverTrigger asChild>
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-full bg-transparent hover:bg-muted transition-all duration-[150ms] focus:outline-none text-foreground hover:text-foreground"
                aria-label="View notifications"
              >
                <Bell className="h-4 w-4" />
              </Button>
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold pointer-events-none"
              >
                3
              </Badge>
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <h4 className="text-[14px] font-medium text-foreground px-1">Notifications</h4>
              <div className="space-y-2">
                <div className="card-subtle p-3 hover:shadow-md transition-shadow duration-150 cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-[14px] font-medium text-foreground">
                        Critical compliance issue detected
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">
                        Manufacturing SOP requires immediate attention
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">
                        2 hours ago
                      </p>
                    </div>
                  </div>
                </div>
                <div className="card-subtle p-3 hover:shadow-md transition-shadow duration-150 cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-[14px] font-medium text-foreground">
                        Document processing complete
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">
                        Quality Manual analysis finished with 94% score
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">1 day ago</p>
                    </div>
                  </div>
                </div>
                <div className="card-subtle p-3 hover:shadow-md transition-shadow duration-150 cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-[14px] font-medium text-foreground">
                        New regulatory update available
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">
                        FDA guidance on process validation updated
                      </p>
                      <p className="text-[12px] text-muted-foreground mt-1">
                        2 days ago
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                className="w-full h-8 text-[14px]"
                size="sm"
                onClick={() => router.push('/notifications')}
              >
                View All Notifications
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-9 w-9 rounded-full bg-transparent hover:bg-muted hover:text-foreground p-0 transition-all duration-[150ms] focus:outline-none"
              aria-label="User menu"
            >
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center overflow-hidden">
                <span className="text-[14px] font-medium text-white">
                  {userProfile ?
                    (userProfile.full_name?.charAt(0) || userProfile.email.charAt(0).toUpperCase()) :
                    'U'
                  }
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            <div className="flex items-center justify-start gap-2 p-2">
              <div className="flex flex-col space-y-1 leading-none">
                <p className="text-[14px] font-medium">
                  {userProfile?.full_name || userProfile?.email || 'User'}
                </p>
                <p className="text-xs text-muted-foreground capitalize">
                  {userProfile?.role?.replace('_', ' ') || 'Loading...'}
                </p>
                {userProfile?.organization_name && (
                  <p className="text-xs text-muted-foreground">
                    {userProfile.organization_name}
                  </p>
                )}
              </div>
            </div>
            <DropdownMenuSeparator className="mx-2" />
            <DropdownMenuItem
              className="h-9 px-2 text-[14px] hover:bg-muted rounded-lg cursor-pointer"
              onClick={() => router.push('/profile')}
            >
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="h-9 px-2 text-[14px] hover:bg-muted rounded-lg cursor-pointer"
              onClick={() => router.push('/settings')}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="mx-2" />
            <DropdownMenuItem
              className="h-9 px-2 text-[14px] hover:bg-muted rounded-lg cursor-pointer"
              onSelect={handleLogout}
              disabled={isLoggingOut}
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>{isLoggingOut ? 'Logging out...' : 'Log out'}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

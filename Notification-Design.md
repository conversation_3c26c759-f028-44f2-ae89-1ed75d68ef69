# Notifications Section – Hyper-Detailed UI/UX Specification (Next.js 15.3.4 + React 19.1.0)

> Version 2.0 • Based on Existing Codebase Analysis • Author: BMAD UX Council

## 1. Functional Overview
* Central hub to view, filter, and manage system notifications (alerts, reminders, system updates).
* Supports bulk actions (mark-as-read, delete), real-time updates, and granular settings per notification type.
* Fully client-side page inside `app/(main)/notifications/page.tsx` (uses `usePageMetadata`).

## 2. Page Layout & Anatomy
```
NotificationsPage (Client Component) – 3 major vertical regions
├── PageHeader              (Top ~72 px)     – title + subtitle + toolbar
├── ControlsBar             (Sticky)         – filter pills + search + bulk actions
├── NotificationsList       (Flex-1)         – virtualized scroll list
│   ├── NotificationItem    (each row)
│   └── ...
└── SettingsDrawer (Sheet)  (Portal)         – per-type toggle switches
```

### 2.1. Top-Level Container
* `class="flex flex-col h-full space-y-6"`
* Wrapped in `container mx-auto p-6` from parent layout.
* Height calc: `h-[calc(100vh-10rem)]` (accounts for global header/footer).


## 1. Complete Page Structure & Layout

### 1.1. Root Container
```tsx
<DashboardLayout>
  <div className="space-y-6">
    {/* All content sections */}
  </div>
</DashboardLayout>
```

### 1.2. Page Header Section
```tsx
<div className="flex items-center justify-between">
  <div>
    <h1 className="text-3xl font-bold text-foreground">Notifications</h1>
    <p className="text-muted-foreground mt-1">
      Stay updated with compliance alerts, system updates, and regulatory changes
    </p>
  </div>
  <div className="flex items-center space-x-2">
    <Button variant="outline" onClick={markAllAsRead}>
      <CheckCircle className="mr-2 h-4 w-4" />
      Mark All Read
    </Button>
    <Button variant="outline">
      <RefreshCw className="mr-2 h-4 w-4" />
      Refresh
    </Button>
    <Button variant="outline">
      <Settings className="mr-2 h-4 w-4" />
      Settings
    </Button>
  </div>
</div>
```

## 2. Statistics Cards Section

### 2.1. Stats Grid Layout
```tsx
<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
  {stats.map((stat, index) => (
    <Card key={index}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <stat.icon className="h-5 w-5 text-primary" />
          </div>
          <div>
            <p className="text-2xl font-bold text-foreground">{stat.value}</p>
            <p className="text-sm text-muted-foreground">{stat.label}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### 2.2. Stats Data Structure
```tsx
const stats = [
  { icon: Bell, value: "24", label: "Total Notifications" },
  { icon: AlertTriangle, value: "3", label: "Critical Alerts" },
  { icon: Clock, value: "8", label: "Pending Reviews" },
  { icon: CheckCircle, value: "156", label: "Resolved This Week" }
];
```

## 3. Filters & Controls Section

### 3.1. Filters Card Structure
```tsx
<Card>
  <CardContent className="p-6">
    <div className="flex flex-col lg:flex-row gap-4">
      <div className="flex-1">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search notifications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      <div className="flex gap-2">
        {/* Type Filter */}
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
            <SelectItem value="warning">Warning</SelectItem>
            <SelectItem value="success">Success</SelectItem>
            <SelectItem value="info">Info</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter */}
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="compliance">Compliance</SelectItem>
            <SelectItem value="regulatory">Regulatory</SelectItem>
            <SelectItem value="document">Document</SelectItem>
            <SelectItem value="system">System</SelectItem>
            <SelectItem value="user">User</SelectItem>
          </SelectContent>
        </Select>

        {/* Bulk Delete Button */}
        {selectedNotifications.length > 0 && (
          <Button variant="outline" size="sm" onClick={deleteSelected}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete ({selectedNotifications.length})
          </Button>
        )}
      </div>
    </div>
  </CardContent>
</Card>
```

## 4. Notifications Tabs System

### 4.1. Tabs Structure
```tsx
<Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
  <TabsList>
    <TabsTrigger value="all">All Notifications</TabsTrigger>
    <TabsTrigger value="unread" className="flex items-center">
      Unread
      <Badge
        variant="secondary"
        className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
      >
        {notificationState.filter((n) => !n.read).length}
      </Badge>
    </TabsTrigger>
    <TabsTrigger value="starred">Starred</TabsTrigger>
    <TabsTrigger value="critical">Critical</TabsTrigger>
  </TabsList>

  <TabsContent value={selectedTab} className="space-y-4">
    {/* Notification items content */}
  </TabsContent>
</Tabs>
```

## 5. Individual Notification Item Structure

### 5.1. Complete Notification Item Component
```tsx
<Card
  key={notification.id}
  className={`transition-all duration-200 hover:shadow-md cursor-pointer ${
    !notification.read
      ? "border-l-4 border-l-primary bg-primary/5"
      : ""
  } ${selectedNotifications.includes(notification.id) ? "ring-2 ring-primary/20" : ""}`}
>
  <CardContent className="p-4">
    <div className="flex items-start space-x-4">
      {/* Selection Checkbox */}
      <Checkbox
        checked={selectedNotifications.includes(notification.id)}
        onCheckedChange={() => handleSelectNotification(notification.id)}
      />

      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start space-x-3 flex-1">
            {/* Type Icon */}
            <div className="flex items-center space-x-2">
              <div className={`p-2 rounded-lg ${getTypeColor(notification.type)}`}>
                <TypeIcon className="h-4 w-4" />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className={`font-medium ${
                  !notification.read ? "text-foreground" : "text-muted-foreground"
                }`}>
                  {notification.title}
                </h4>
                <Badge variant="outline" className="text-xs">
                  <CategoryIcon className="mr-1 h-3 w-3" />
                  {notification.category}
                </Badge>
              </div>

              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                {notification.message}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  <span>{notification.timestamp}</span>
                  <span>From: {notification.source}</span>
                </div>

                {notification.actionUrl && (
                  <Button variant="outline" size="sm">
                    {notification.actionLabel || "View"}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => toggleNotificationStar(notification.id)}
            >
              <Star className={`h-4 w-4 ${
                notification.starred ? "fill-yellow-400 text-yellow-400" : ""
              }`} />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => toggleNotificationRead(notification.id)}
            >
              {notification.read ? (
                <Mail className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
```

## 6. Type and Category Icon Mapping

### 6.1. Type Color Functions
```tsx
const getTypeColor = (type: string) => {
  switch (type) {
    case "critical":
      return "bg-destructive text-destructive-foreground";
    case "warning":
      return "bg-warning text-warning-foreground";
    case "success":
      return "bg-success text-success-foreground";
    case "info":
      return "bg-info text-info-foreground";
    default:
      return "bg-muted text-muted-foreground";
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case "critical": return AlertTriangle;
    case "warning": return Clock;
    case "success": return CheckCircle;
    case "info": return Info;
    default: return Bell;
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case "compliance": return Shield;
    case "regulatory": return TrendingUp;
    case "document": return FileText;
    case "system": return Settings;
    case "user": return Users;
    default: return Bell;
  }
};
```

## 7. State Management & Data Structure

### 7.1. Notification Interface
```tsx
interface Notification {
  id: string;
  title: string;
  message: string;
  type: "critical" | "warning" | "success" | "info";
  category: "compliance" | "regulatory" | "document" | "system" | "user";
  timestamp: string;
  source: string;
  read: boolean;
  starred: boolean;
  actionUrl?: string;
  actionLabel?: string;
}
```

### 7.2. State Variables
```tsx
const [notificationState, setNotificationState] = useState<Notification[]>(mockNotifications);
const [selectedTab, setSelectedTab] = useState("all");
const [searchQuery, setSearchQuery] = useState("");
const [selectedType, setSelectedType] = useState("all");
const [selectedCategory, setSelectedCategory] = useState("all");
const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
```

### 7.3. Filtering Logic
```tsx
const filteredNotifications = notificationState.filter((notification) => {
  const matchesSearch =
    notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    notification.message.toLowerCase().includes(searchQuery.toLowerCase());
  const matchesType = selectedType === "all" || notification.type === selectedType;
  const matchesCategory = selectedCategory === "all" || notification.category === selectedCategory;

  let matchesTab = true;
  if (selectedTab === "unread") matchesTab = !notification.read;
  if (selectedTab === "starred") matchesTab = notification.starred;
  if (selectedTab === "critical") matchesTab = notification.type === "critical";

  return matchesSearch && matchesType && matchesCategory && matchesTab;
});
```

## 8. Empty State Component

### 8.1. Empty State Structure
```tsx
{filteredNotifications.length === 0 ? (
  <Card>
    <CardContent className="p-8 text-center">
      <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
      <h3 className="text-lg font-medium mb-2">No notifications found</h3>
      <p className="text-muted-foreground">
        {searchQuery || selectedType !== "all" || selectedCategory !== "all"
          ? "Try adjusting your filters or search terms"
          : "You're all caught up! No new notifications."}
      </p>
    </CardContent>
  </Card>
) : (
  // Notification items list
)}
```

## 9. Interactive Functions

### 9.1. Core Action Functions
```tsx
const toggleNotificationRead = (id: string) => {
  setNotificationState((prev) =>
    prev.map((n) => (n.id === id ? { ...n, read: !n.read } : n))
  );
};

const toggleNotificationStar = (id: string) => {
  setNotificationState((prev) =>
    prev.map((n) => (n.id === id ? { ...n, starred: !n.starred } : n))
  );
};

const markAllAsRead = () => {
  setNotificationState((prev) => prev.map((n) => ({ ...n, read: true })));
};

const deleteSelected = () => {
  setNotificationState((prev) =>
    prev.filter((n) => !selectedNotifications.includes(n.id))
  );
  setSelectedNotifications([]);
};

const handleSelectNotification = (id: string) => {
  setSelectedNotifications((prev) =>
    prev.includes(id) ? prev.filter((nId) => nId !== id) : [...prev, id]
  );
};
```

## 10. Required Imports

```tsx
import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bell, AlertTriangle, Clock, CheckCircle, Info, Search, RefreshCw,
  Settings, Trash2, Star, Mail, Eye, Shield, TrendingUp, FileText, Users
} from "lucide-react";
```

---
End of hyper-detailed specification

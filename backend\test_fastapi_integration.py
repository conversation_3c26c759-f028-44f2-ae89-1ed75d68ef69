#!/usr/bin/env python3
"""
VCP_002 FastAPI Integration Test
Tests the FastAPI authentication integration.
"""

import sys
import os
from datetime import datetime, timezone
from uuid import UUID

# Add backend to path
sys.path.append('.')

def test_fastapi_auth_dependencies():
    """Test FastAPI authentication dependencies."""
    print("🧪 Testing FastAPI Authentication Dependencies...")

    try:
        from auth.dependencies import get_current_user, get_current_active_user, require_role, require_permissions, require_mfa
        from auth.models import AuthUser, PharmaceuticalRole
        from auth.rbac import PharmaceuticalPermissions

        # Create test user
        test_user = AuthUser(
            id=UUID('12345678-1234-5678-9012-123456789012'),
            email='<EMAIL>',
            email_confirmed_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.QUALITY_MANAGER,
            organization_id=UUID('12345678-1234-5678-9012-123456789013'),
            organization_name='Test Company',
            is_active=True,
            is_mfa_enabled=True,
            mfa_methods=[]
        )

        # Test dependency functions exist
        assert callable(get_current_user)
        assert callable(get_current_active_user)
        assert callable(require_role)
        assert callable(require_permissions)
        assert callable(require_mfa)

        # Test role requirement factory
        admin_checker = require_role(PharmaceuticalRole.SUPER_ADMIN)
        assert callable(admin_checker)

        # Test permission requirement factory
        permission_checker = require_permissions(PharmaceuticalPermissions.DOCUMENT_READ)
        assert callable(permission_checker)

        # Test MFA requirement factory
        mfa_checker = require_mfa("document_sign")
        assert callable(mfa_checker)

        print("✅ FastAPI authentication dependencies working correctly")
        return True

    except Exception as e:
        print(f"❌ FastAPI authentication dependencies failed: {e}")
        return False

def test_supabase_config():
    """Test Supabase configuration."""
    print("🧪 Testing Supabase Configuration...")

    try:
        from auth.dependencies import SupabaseConfig

        # Test configuration with defaults (for testing)
        config = SupabaseConfig()

        assert config.url is not None
        assert config.service_role_key is not None
        assert config.anon_key is not None
        assert config.jwt_secret is not None

        print("✅ Supabase configuration working correctly")
        print(f"   - URL: {config.url}")
        print(f"   - Has service role key: {bool(config.service_role_key)}")
        return True

    except Exception as e:
        print(f"❌ Supabase configuration failed: {e}")
        return False

def test_jwt_bearer():
    """Test JWT Bearer authentication class."""
    print("🧪 Testing JWT Bearer Authentication...")

    try:
        from auth.dependencies import SupabaseJWTBearer

        # Test JWT bearer can be instantiated
        jwt_bearer = SupabaseJWTBearer()

        assert jwt_bearer is not None
        assert hasattr(jwt_bearer, 'supabase')

        print("✅ JWT Bearer authentication working correctly")
        return True

    except Exception as e:
        print(f"❌ JWT Bearer authentication failed: {e}")
        return False

def test_pharmaceutical_compliance():
    """Test pharmaceutical compliance features."""
    print("🧪 Testing Pharmaceutical Compliance Features...")

    try:
        from auth.models import PharmaceuticalRole
        from auth.rbac import PharmaceuticalPermissions, get_role_permissions
        from auth.mfa import MFAManager

        # Test all pharmaceutical roles exist
        roles = [
            PharmaceuticalRole.SUPER_ADMIN,
            PharmaceuticalRole.ORG_ADMIN,
            PharmaceuticalRole.QUALITY_MANAGER,
            PharmaceuticalRole.REGULATORY_LEAD,
            PharmaceuticalRole.COMPLIANCE_OFFICER,
            PharmaceuticalRole.DOCUMENT_REVIEWER,
            PharmaceuticalRole.ANALYST,
            PharmaceuticalRole.AUDITOR,
            PharmaceuticalRole.VIEWER,
        ]

        assert len(roles) == 9

        # Test pharmaceutical permissions exist
        key_permissions = [
            PharmaceuticalPermissions.DOCUMENT_CREATE,
            PharmaceuticalPermissions.DOCUMENT_READ,
            PharmaceuticalPermissions.DOCUMENT_UPDATE,
            PharmaceuticalPermissions.DOCUMENT_DELETE,
            PharmaceuticalPermissions.QUALITY_CREATE_BATCH,
            PharmaceuticalPermissions.QUALITY_APPROVE_BATCH,
            PharmaceuticalPermissions.REGULATORY_SUBMIT_TO_AGENCY,
            PharmaceuticalPermissions.AUDIT_VIEW_LOGS,
            PharmaceuticalPermissions.USER_CREATE,
            PharmaceuticalPermissions.USER_DELETE,
        ]

        # Test role hierarchy
        super_admin_perms = get_role_permissions(PharmaceuticalRole.SUPER_ADMIN)
        viewer_perms = get_role_permissions(PharmaceuticalRole.VIEWER)

        assert len(super_admin_perms) > len(viewer_perms)

        # Test MFA requirements for compliance
        manager = MFAManager(None)

        # High-privilege roles should require MFA
        assert manager.is_mfa_required(type('User', (), {'role': PharmaceuticalRole.SUPER_ADMIN})())
        assert manager.is_mfa_required(type('User', (), {'role': PharmaceuticalRole.QUALITY_MANAGER})())

        # Low-privilege roles should not require MFA by default
        assert not manager.is_mfa_required(type('User', (), {'role': PharmaceuticalRole.VIEWER})())

        print("✅ Pharmaceutical compliance features working correctly")
        print(f"   - {len(roles)} pharmaceutical roles defined")
        print(f"   - {len(super_admin_perms)} permissions for super admin")
        print(f"   - {len(viewer_perms)} permissions for viewer")
        return True

    except Exception as e:
        print(f"❌ Pharmaceutical compliance features failed: {e}")
        return False

def test_latest_versions():
    """Test that we're using latest July 2025 package versions."""
    print("🧪 Testing Latest Package Versions...")

    try:
        import fastapi
        import pydantic
        import supabase

        # Check FastAPI version (should be 0.116.x for July 2025)
        fastapi_version = fastapi.__version__
        assert fastapi_version.startswith('0.116'), f"FastAPI version {fastapi_version} may not be latest July 2025"

        # Check Pydantic version (should be 2.x for July 2025)
        pydantic_version = pydantic.__version__
        assert pydantic_version.startswith('2.'), f"Pydantic version {pydantic_version} may not be latest July 2025"

        print("✅ Latest package versions confirmed")
        print(f"   - FastAPI: {fastapi_version}")
        print(f"   - Pydantic: {pydantic_version}")
        print(f"   - Supabase: Available")
        return True

    except Exception as e:
        print(f"❌ Package version check failed: {e}")
        return False

def main():
    """Run all FastAPI integration tests."""
    print("🚀 VCP_002 FastAPI Integration Test Suite")
    print("=" * 50)

    tests = [
        test_fastapi_auth_dependencies,
        test_supabase_config,
        test_jwt_bearer,
        test_pharmaceutical_compliance,
        test_latest_versions,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 50)
    print(f"📊 Integration Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All VCP_002 FastAPI integration working correctly!")
        print("✅ System ready for production deployment")
        return True
    else:
        print("❌ Some integration tests failed - review implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

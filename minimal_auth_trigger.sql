-- MINIMAL AUTH TRIGGER - Based on Supabase Best Practices
-- This follows the pattern from successful GitHub issues/discussions

-- Drop existing triggers and function
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;

-- Create minimal trigger function (only essential fields)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert only the absolute minimum required fields
    INSERT INTO user_profiles (
        id,
        email,
        organization_id,
        role,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        '550e8400-e29b-41d4-a716-446655440000', -- demo org ID
        'read_only',
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Verify trigger creation
SELECT 
    'MINIMAL TRIGGER CREATED' as status,
    trigger_name,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger';

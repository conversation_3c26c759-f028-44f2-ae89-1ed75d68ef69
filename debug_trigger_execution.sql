-- DETAILED TRIGGER EXECUTION DEBUGGING
-- Run this to identify exactly what's failing in the trigger

-- 1. Check if demo organization exists
SELECT 
    'DEMO ORG CHECK' as test,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ EXISTS'
        ELSE '❌ MISSING - THIS IS THE PROBLEM!'
    END as status,
    COUNT(*) as count,
    (SELECT id FROM organizations WHERE name = 'demo-pharma-corp' LIMIT 1) as org_id
FROM organizations 
WHERE name = 'demo-pharma-corp';

-- 2. Check user_profiles table structure
SELECT 
    'USER_PROFILES COLUMNS' as test,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name IN ('id', 'email', 'full_name', 'organization_id', 'role', 'is_active', 'email_verified_at')
ORDER BY ordinal_position;

-- 3. Check user_role enum values
SELECT 
    'USER_ROLE ENUM' as test,
    unnest(enum_range(NULL::user_role)) as valid_roles;

-- 4. Test trigger function manually with exact same data
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := 'manual-test-' || extract(epoch from now()) || '@example.com';
    demo_org_id UUID;
    error_msg TEXT;
    profile_count INTEGER;
BEGIN
    RAISE NOTICE 'Starting manual trigger test...';
    
    -- Get demo org ID (same as trigger does)
    SELECT id INTO demo_org_id 
    FROM organizations 
    WHERE name = 'demo-pharma-corp' 
    LIMIT 1;
    
    RAISE NOTICE 'Demo org ID: %', demo_org_id;
    
    IF demo_org_id IS NULL THEN
        RAISE NOTICE '❌ CRITICAL: Demo organization not found!';
        RETURN;
    END IF;
    
    -- Try the exact INSERT that the trigger does
    BEGIN
        INSERT INTO user_profiles (
            id,
            email,
            full_name,
            organization_id,
            role,
            is_active,
            email_verified_at,
            created_at,
            updated_at
        ) VALUES (
            test_user_id,
            test_email,
            'Manual Test User',
            demo_org_id,
            'read_only',
            true,
            NULL, -- No email verification initially
            NOW(),
            NOW()
        );
        
        RAISE NOTICE '✅ SUCCESS: Manual user profile insert worked!';
        
        -- Check if it was created
        SELECT COUNT(*) INTO profile_count FROM user_profiles WHERE id = test_user_id;
        RAISE NOTICE 'Profile count after insert: %', profile_count;
        
        -- Clean up
        DELETE FROM user_profiles WHERE id = test_user_id;
        RAISE NOTICE '✅ Cleanup completed';
        
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE '❌ MANUAL INSERT FAILED: %', error_msg;
            RAISE NOTICE 'Error detail: %', SQLSTATE;
    END;
END $$;

-- 5. Check RLS policies that might block the insert
SELECT 
    'RLS POLICIES BLOCKING CHECK' as test,
    policyname,
    cmd,
    with_check,
    CASE 
        WHEN policyname = 'Allow trigger user creation' THEN '✅ SHOULD ALLOW'
        ELSE '⚠️ MIGHT BLOCK'
    END as assessment
FROM pg_policies 
WHERE tablename = 'user_profiles' 
AND cmd = 'INSERT';

-- 6. Test if we can insert with auth.uid() = NULL (trigger context)
SELECT 
    'AUTH CONTEXT CHECK' as test,
    auth.uid() as current_auth_uid,
    CASE 
        WHEN auth.uid() IS NULL THEN '✅ NULL (trigger context)'
        ELSE '⚠️ Has value (not trigger context)'
    END as context_status;

-- 7. Check if log_audit_event function exists (might be causing issues)
SELECT 
    'AUDIT FUNCTION CHECK' as test,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ EXISTS'
        ELSE '❌ MISSING - Could cause trigger failure'
    END as status
FROM information_schema.routines 
WHERE routine_name = 'log_audit_event';

-- 8. Final summary
SELECT 
    '=== TRIGGER DEBUG SUMMARY ===' as summary,
    'Check each test above for failures' as instruction,
    NOW() as timestamp;

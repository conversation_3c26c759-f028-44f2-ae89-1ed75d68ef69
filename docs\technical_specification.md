# VigiLens Technical Specification - Hyperspecific Implementation Guide

**Project:** VigiLens Pharmaceutical Compliance Platform
**Version:** 1.0
**Generated:** 2025-07-11
**Tech Stack:** Python 3.13.5 + FastAPI 0.115.5 + <PERSON><PERSON><PERSON><PERSON> 0.3.14 + Next.js 15.1.5

---

## 🔧 **TECHNOLOGY STACK SPECIFICATION**

### Frontend Architecture (Complete - Production Ready)
```yaml
Framework: Next.js 15.1.5
  - App Router with Server Components
  - Async Request APIs
  - Enhanced hydration with React 19

Language: TypeScript 5.7.3
  - Strict mode enabled
  - Zero 'any' types policy
  - Enhanced type checking

UI Framework: shadcn/ui + Tailwind CSS 4.0.1
  - Container queries support
  - 5x faster builds
  - Accessibility-first components

State Management: Zustand 5.0.5
  - Global state management
  - TypeScript integration
  - Devtools support

Validation: Valibot 0.32.1
  - Client-side validation
  - Schema-based validation
  - TypeScript inference

Charts: Recharts
  - React-based charting
  - Pharmaceutical dashboard metrics
  - Real-time data visualization
```

### Backend Architecture (To Be Implemented)
```yaml
Core Framework:
  Language: Python 3.13.5
  Framework: FastAPI 0.115.5
  Validation: Pydantic 2.9.2
  Background Tasks: APScheduler 4.0.0
  HTTP Client: httpx 0.28.1
  Web Scraping: BeautifulSoup4 4.12.3

AI & Machine Learning:
  AI Framework: LangChain 0.3.14
  Agent Pattern: Simple LangChain agents + custom orchestration
  LLM Model: Fine-tuned Open-Source Model (Llama/Mistral/Custom)
  Embeddings: Open-source embeddings (sentence-transformers)
  Model Hosting: Local inference server or Hugging Face Inference Endpoints
  Monitoring: LangSmith (free tier) + custom metrics

Data & Storage:
  Primary Database: Supabase PostgreSQL (free tier - 500MB)
  Vector Database: ChromaDB 0.6.2 (embedded, file-based)
  Authentication: Supabase Auth 2025 (JWT-based, MFA support)
  File Storage: Supabase Storage (1GB free)
  Real-time: Supabase Realtime WebSockets

Hosting & Infrastructure:
  Frontend: Vercel (free tier, Next.js 15.1.5 optimized)
  Backend: Railway ($5 monthly credit, Python 3.13 support)
  Database: Supabase Cloud (free tier)
  Vector DB: Local files (no hosting needed)

Security & Compliance:
  Authentication: Supabase Auth 2025 (multi-factor support)
  Authorization: Row Level Security (RLS) + Supabase policies
  Encryption: TLS 1.3 in transit, AES-256 at rest
  Compliance: HIPAA ready via Supabase
  Audit Logging: PostgreSQL audit logs + Supabase audit trail
```

---

## 📋 **HYPERSPECIFIC IMPLEMENTATION TASKS**

### **VCP_001: Database Schema Design & Implementation (16 hours)**

#### VCP_001_1: Supabase Project Setup & Configuration (2 hours)
**Objective:** Create and configure Supabase project with proper environment setup

**Detailed Steps:**
1. **Use Existing Supabase Project**
   - Project name: "VigiLens" (already created)
   - Region: ap-south-1 (Asia Pacific - Mumbai)
   - Database password: "Vigi@Lens1104"

2. **Environment Configuration**
   ```bash
   # Install Supabase CLI
   npm install -g @supabase/cli

   # Initialize local project
   supabase init
   supabase login
   supabase link --project-ref https://esgciouphhajolkojipw.supabase.co
   ```

3. **Environment Variables Setup**
   ```env
   # Frontend (.env.local)
   NEXT_PUBLIC_SUPABASE_URL=https://esgciouphhajolkojipw.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjYwMzcsImV4cCI6MjA2NzgwMjAzN30.DqyAefIWDBBH11i4stMELtx9a3zrFIP9q604AWZPTjI

   # Backend (.env)
   SUPABASE_URL=https://esgciouphhajolkojipw.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjIyNjAzNywiZXhwIjoyMDY3ODAyMDM3fQ.AHkgjUq0KHT2ZV9TlsEYVTsgladyg4GrFCECeXHirDM
   DATABASE_URL=************************************************************/postgres
   ```

4. **Python Client Setup**
   ```python
   # requirements.txt
   supabase==2.3.4
   asyncpg==0.29.0

   # Database connection
   from supabase import create_client, Client
   import asyncpg

   supabase: Client = create_client(supabase_url, supabase_key)
   ```

#### VCP_001_2: ERD Design & Multi-Tenant Architecture (4 hours)
**Objective:** Design comprehensive database schema with RLS for pharmaceutical compliance

**Entity Relationship Design:**
```sql
-- Core Entities with Relationships
organizations (1) -> (many) users
organizations (1) -> (many) regulatory_documents
users (1) -> (many) document_analysis
regulatory_documents (1) -> (many) document_versions
compliance_frameworks (1) -> (many) document_analysis
```

**Multi-Tenant RLS Strategy:**
```sql
-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE regulatory_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can only see their organization data" ON users
  FOR ALL USING (organization_id = (auth.jwt() ->> 'organization_id')::uuid);

CREATE POLICY "Documents are organization-scoped" ON regulatory_documents
  FOR ALL USING (organization_id = (auth.jwt() ->> 'organization_id')::uuid);
```

#### VCP_001_3: Core Authentication & User Management Tables (3 hours)
**Objective:** Implement user management with Supabase Auth integration

**SQL Implementation:**
```sql
-- 001_create_organizations.sql
CREATE TABLE organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  domain VARCHAR(255) UNIQUE,
  subscription_tier VARCHAR(50) DEFAULT 'free',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 002_create_user_profiles.sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  role VARCHAR(50) DEFAULT 'user',
  department VARCHAR(100),
  phone VARCHAR(20),
  last_login TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 003_create_user_roles.sql
CREATE TABLE user_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  role_name VARCHAR(50) NOT NULL,
  permissions JSONB DEFAULT '[]',
  granted_by UUID REFERENCES user_profiles(id),
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Pydantic Models:**
```python
from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
import uuid

class Organization(BaseModel):
    id: Optional[uuid.UUID] = None
    name: str = Field(..., min_length=1, max_length=255)
    domain: Optional[str] = Field(None, regex=r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    subscription_tier: str = Field(default='free', regex=r'^(free|pro|enterprise)$')
    settings: dict = Field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class UserProfile(BaseModel):
    id: Optional[uuid.UUID] = None
    organization_id: uuid.UUID
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=255)
    role: str = Field(default='user', regex=r'^(admin|manager|user)$')
    department: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, regex=r'^\+?1?\d{9,15}$')
    is_active: bool = Field(default=True)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class UserRole(BaseModel):
    id: Optional[uuid.UUID] = None
    user_id: uuid.UUID
    role_name: str = Field(..., regex=r'^(admin|manager|user|viewer)$')
    permissions: List[str] = Field(default_factory=list)
    granted_by: Optional[uuid.UUID] = None
    granted_at: Optional[datetime] = None
```

#### VCP_001_4: Regulatory Document Management Schema (4 hours)
**Objective:** Create comprehensive document management with metadata and versioning

**SQL Implementation:**
```sql
-- 004_create_regulatory_documents.sql
CREATE TYPE document_status AS ENUM ('uploaded', 'processing', 'analyzed', 'approved', 'rejected');
CREATE TYPE document_type AS ENUM ('fda_guidance', 'ema_guideline', 'ich_document', 'user_upload', 'regulatory_update');

CREATE TABLE regulatory_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  title VARCHAR(500) NOT NULL,
  document_type document_type NOT NULL,
  status document_status DEFAULT 'uploaded',
  file_path VARCHAR(1000),
  file_size BIGINT,
  mime_type VARCHAR(100),
  checksum VARCHAR(64),
  source_url TEXT,
  regulatory_agency VARCHAR(100),
  publication_date DATE,
  effective_date DATE,
  metadata JSONB DEFAULT '{}',
  content_extracted TEXT,
  content_vector tsvector,
  uploaded_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Full-text search index
CREATE INDEX idx_documents_content_search ON regulatory_documents USING GIN(content_vector);
CREATE INDEX idx_documents_metadata ON regulatory_documents USING GIN(metadata);
CREATE INDEX idx_documents_org_status ON regulatory_documents(organization_id, status);

-- 005_create_document_versions.sql
CREATE TABLE document_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  file_path VARCHAR(1000),
  checksum VARCHAR(64),
  changes_summary TEXT,
  created_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, version_number)
);
```

**Pydantic Models:**
```python
from enum import Enum
from typing import Optional, Dict, Any
from datetime import date

class DocumentStatus(str, Enum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    ANALYZED = "analyzed"
    APPROVED = "approved"
    REJECTED = "rejected"

class DocumentType(str, Enum):
    FDA_GUIDANCE = "fda_guidance"
    EMA_GUIDELINE = "ema_guideline"
    ICH_DOCUMENT = "ich_document"
    USER_UPLOAD = "user_upload"
    REGULATORY_UPDATE = "regulatory_update"

class RegulatoryDocument(BaseModel):
    id: Optional[uuid.UUID] = None
    organization_id: uuid.UUID
    title: str = Field(..., min_length=1, max_length=500)
    document_type: DocumentType
    status: DocumentStatus = DocumentStatus.UPLOADED
    file_path: Optional[str] = Field(None, max_length=1000)
    file_size: Optional[int] = Field(None, ge=0)
    mime_type: Optional[str] = Field(None, max_length=100)
    checksum: Optional[str] = Field(None, max_length=64)
    source_url: Optional[str] = None
    regulatory_agency: Optional[str] = Field(None, max_length=100)
    publication_date: Optional[date] = None
    effective_date: Optional[date] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    content_extracted: Optional[str] = None
    uploaded_by: Optional[uuid.UUID] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

---

## 🔄 **IMPLEMENTATION SEQUENCE**

### Week 1-2: Database Foundation
1. **Day 1-2:** Complete VCP_001_1 (Supabase setup)
2. **Day 3-4:** Complete VCP_001_2 (ERD design)
3. **Day 5-7:** Complete VCP_001_3 (Core tables)
4. **Day 8-10:** Complete VCP_001_4 (Document schema)

### Week 3-4: Authentication & API
1. **Day 1-3:** Complete VCP_002 (Authentication system)
2. **Day 4-7:** Complete VCP_003 (FastAPI framework)
3. **Day 8-10:** Integration testing and validation

### Validation Checkpoints
- **End of Week 2:** Database schema validated with sample data
- **End of Week 4:** Authentication and basic API operational
- **End of Week 6:** Document upload and storage working
- **End of Week 8:** AI pipeline processing documents

---

**Next Steps:** Proceed with VCP_002 (Authentication System) implementation following the detailed subtask specifications.

/**
 * Supabase Edge Function: Calculate Compliance Score
 * 
 * Calculates pharmaceutical compliance scores for regulatory documents
 * against specific compliance frameworks (FDA cGMP, ICH Q7, ISO 13485, etc.)
 * 
 * Compatible with Deno 2.1 and Supabase Edge Runtime
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, no 'any' types
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// TypeScript interfaces for pharmaceutical compliance
interface ComplianceRequest {
  documentId: string
  frameworkId?: string
  organizationId: string
  userId: string
}

interface ComplianceFramework {
  id: string
  name: string
  version: string
  requirements: ComplianceRequirement[]
  weight_factors: Record<string, number>
}

interface ComplianceRequirement {
  id: string
  category: string
  description: string
  severity: 'critical' | 'major' | 'minor'
  weight: number
  validation_rules: string[]
}

interface ComplianceScore {
  overall_score: number
  category_scores: Record<string, number>
  compliance_status: 'compliant' | 'non_compliant' | 'partial'
  critical_violations: ComplianceViolation[]
  recommendations: string[]
  calculated_at: string
  framework_version: string
}

interface ComplianceViolation {
  requirement_id: string
  severity: string
  description: string
  suggested_action: string
}

interface DocumentMetadata {
  id: string
  title: string
  document_type: string
  content_hash: string
  metadata: Record<string, unknown>
  processing_status: string
}

// Pharmaceutical compliance scoring algorithms
class PharmaceuticalComplianceEngine {
  private frameworks: Map<string, ComplianceFramework> = new Map()

  constructor() {
    this.initializeFrameworks()
  }

  private initializeFrameworks(): void {
    // FDA cGMP Framework
    const fdaCgmpFramework: ComplianceFramework = {
      id: 'fda_cgmp_2025',
      name: 'FDA Current Good Manufacturing Practice',
      version: '2025.1',
      requirements: [
        {
          id: 'cgmp_001',
          category: 'documentation',
          description: 'Complete batch records with electronic signatures',
          severity: 'critical',
          weight: 0.25,
          validation_rules: ['electronic_signature_present', 'batch_record_complete']
        },
        {
          id: 'cgmp_002', 
          category: 'quality_control',
          description: 'Quality control testing documentation',
          severity: 'critical',
          weight: 0.20,
          validation_rules: ['qc_testing_documented', 'results_within_specifications']
        }
      ],
      weight_factors: {
        documentation: 0.4,
        quality_control: 0.3,
        manufacturing: 0.2,
        validation: 0.1
      }
    }

    this.frameworks.set('fda_cgmp_2025', fdaCgmpFramework)
  }

  async calculateScore(
    document: DocumentMetadata,
    frameworkId: string = 'fda_cgmp_2025'
  ): Promise<ComplianceScore> {
    const framework = this.frameworks.get(frameworkId)
    if (!framework) {
      throw new Error(`Framework ${frameworkId} not found`)
    }

    const violations: ComplianceViolation[] = []
    const categoryScores: Record<string, number> = {}

    // Analyze document against each requirement
    for (const requirement of framework.requirements) {
      const score = await this.evaluateRequirement(document, requirement)
      
      if (!categoryScores[requirement.category]) {
        categoryScores[requirement.category] = 0
      }
      categoryScores[requirement.category] += score * requirement.weight

      // Track violations for critical/major issues
      if (score < 0.8 && requirement.severity !== 'minor') {
        violations.push({
          requirement_id: requirement.id,
          severity: requirement.severity,
          description: requirement.description,
          suggested_action: this.generateRecommendation(requirement, score)
        })
      }
    }

    // Calculate weighted overall score
    let overallScore = 0
    for (const [category, score] of Object.entries(categoryScores)) {
      const weight = framework.weight_factors[category] || 0.1
      overallScore += score * weight
    }

    // Determine compliance status
    const complianceStatus = this.determineComplianceStatus(overallScore, violations)

    return {
      overall_score: Math.round(overallScore * 100) / 100,
      category_scores: categoryScores,
      compliance_status: complianceStatus,
      critical_violations: violations.filter(v => v.severity === 'critical'),
      recommendations: this.generateRecommendations(violations),
      calculated_at: new Date().toISOString(),
      framework_version: framework.version
    }
  }

  private async evaluateRequirement(
    document: DocumentMetadata,
    requirement: ComplianceRequirement
  ): Promise<number> {
    let score = 1.0

    // Evaluate each validation rule
    for (const rule of requirement.validation_rules) {
      const ruleScore = await this.evaluateValidationRule(document, rule)
      score = Math.min(score, ruleScore)
    }

    return score
  }

  private async evaluateValidationRule(
    document: DocumentMetadata,
    rule: string
  ): Promise<number> {
    // Pharmaceutical compliance validation logic
    switch (rule) {
      case 'electronic_signature_present':
        return document.metadata?.electronic_signatures ? 1.0 : 0.0
      
      case 'batch_record_complete':
        const requiredFields = ['batch_number', 'manufacturing_date', 'expiry_date']
        const presentFields = requiredFields.filter(field => 
          document.metadata?.[field] !== undefined
        )
        return presentFields.length / requiredFields.length

      case 'qc_testing_documented':
        return document.metadata?.qc_tests ? 1.0 : 0.5

      case 'results_within_specifications':
        const qcResults = document.metadata?.qc_results as Record<string, unknown>[] || []
        if (qcResults.length === 0) return 0.5
        
        const passingResults = qcResults.filter(result => 
          result.status === 'pass' || result.within_spec === true
        )
        return passingResults.length / qcResults.length

      default:
        console.warn(`Unknown validation rule: ${rule}`)
        return 0.5
    }
  }

  private determineComplianceStatus(
    score: number,
    violations: ComplianceViolation[]
  ): 'compliant' | 'non_compliant' | 'partial' {
    const criticalViolations = violations.filter(v => v.severity === 'critical')
    
    if (criticalViolations.length > 0) return 'non_compliant'
    if (score >= 0.9) return 'compliant'
    if (score >= 0.7) return 'partial'
    return 'non_compliant'
  }

  private generateRecommendation(requirement: ComplianceRequirement, score: number): string {
    if (score < 0.5) {
      return `Critical: Immediate action required for ${requirement.description}`
    } else if (score < 0.8) {
      return `Improvement needed: Address gaps in ${requirement.description}`
    }
    return `Minor adjustments recommended for ${requirement.description}`
  }

  private generateRecommendations(violations: ComplianceViolation[]): string[] {
    const recommendations: string[] = []
    
    const criticalCount = violations.filter(v => v.severity === 'critical').length
    const majorCount = violations.filter(v => v.severity === 'major').length

    if (criticalCount > 0) {
      recommendations.push(`Address ${criticalCount} critical compliance violations immediately`)
    }
    
    if (majorCount > 0) {
      recommendations.push(`Resolve ${majorCount} major compliance issues within 30 days`)
    }

    if (violations.length === 0) {
      recommendations.push('Document meets current compliance requirements')
    }

    return recommendations
  }
}

// Edge Function handler
serve(async (req: Request) => {
  // CORS headers for pharmaceutical compliance platform
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Validate request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse and validate request body
    const requestBody = await req.json() as ComplianceRequest
    
    if (!requestBody.documentId || !requestBody.organizationId || !requestBody.userId) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: documentId, organizationId, userId' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Fetch document with RLS security
    const { data: document, error: docError } = await supabase
      .from('regulatory_documents')
      .select('*')
      .eq('id', requestBody.documentId)
      .eq('organization_id', requestBody.organizationId)
      .single()

    if (docError || !document) {
      return new Response(
        JSON.stringify({ error: 'Document not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Calculate compliance score
    const complianceEngine = new PharmaceuticalComplianceEngine()
    const complianceScore = await complianceEngine.calculateScore(
      document as DocumentMetadata,
      requestBody.frameworkId
    )

    // Store compliance assessment in database
    const { error: insertError } = await supabase
      .from('document_compliance_assessments')
      .insert({
        document_id: requestBody.documentId,
        organization_id: requestBody.organizationId,
        framework_id: requestBody.frameworkId || 'fda_cgmp_2025',
        overall_score: complianceScore.overall_score,
        category_scores: complianceScore.category_scores,
        compliance_status: complianceScore.compliance_status,
        violations: complianceScore.critical_violations,
        recommendations: complianceScore.recommendations,
        assessed_by: requestBody.userId,
        assessed_at: complianceScore.calculated_at
      })

    if (insertError) {
      console.error('Failed to store compliance assessment:', insertError)
      // Continue execution - don't fail the request if storage fails
    }

    // Return compliance score
    return new Response(
      JSON.stringify({
        success: true,
        data: complianceScore,
        metadata: {
          document_id: requestBody.documentId,
          framework_id: requestBody.frameworkId || 'fda_cgmp_2025',
          processed_at: new Date().toISOString()
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Compliance scoring error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error during compliance calculation',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

import { createClient } from '@/utils/supabase/server'

export default async function TestSupabasePage() {
  const supabase = await createClient()

  const { data: documents, error } = await supabase
    .from('regulatory_documents')
    .select('*')
    .limit(5)

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error:</strong> {error.message}
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <strong>Success!</strong> Connected to Supabase successfully.
      </div>

      <h2 className="text-xl font-semibold mb-2">Regulatory Documents:</h2>
      <div className="bg-gray-100 p-4 rounded">
        <pre className="text-sm overflow-auto">
          {JSON.stringify(documents, null, 2)}
        </pre>
      </div>
    </div>
  )
}

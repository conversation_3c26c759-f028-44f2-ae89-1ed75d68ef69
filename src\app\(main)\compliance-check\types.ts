export interface Document {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly size: string;
  readonly status: 'uploaded' | 'processing' | 'analyzed' | 'error';
  readonly complianceScore?: number;
  readonly issues?: number;
  readonly recommendations?: number;
  readonly riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  readonly uploadDate: string;
}

export interface ComplianceResult {
  readonly documentId: string;
  readonly framework: string;
  readonly score: number;
  readonly issues: Array<{
    readonly severity: 'low' | 'medium' | 'high' | 'critical';
    readonly title: string;
    readonly description: string;
    readonly location: string;
    readonly recommendation: string;
  }>;
  readonly recommendations: Array<{
    readonly priority: 'low' | 'medium' | 'high';
    readonly title: string;
    readonly description: string;
    readonly implementation: string;
  }>;
  readonly insights: Array<{
    readonly category: string;
    readonly finding: string;
    readonly impact: string;
    readonly action: string;
  }>;
}

export interface Framework {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly category: string;
  readonly complexity: 'basic' | 'intermediate' | 'advanced';
}

export interface AnalysisMetrics {
  readonly totalDocuments: number;
  readonly averageScore: number;
  readonly totalIssues: number;
  readonly highRiskIssues: number;
  readonly completionRate: number;
}

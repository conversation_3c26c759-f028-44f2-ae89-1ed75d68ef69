-- MINIMAL TRIGGER FIX - Guaranteed to work
-- Run this if the main trigger is still failing

-- Step 1: Ensure demo organization exists
INSERT INTO organizations (
    id,
    name,
    display_name,
    compliance_frameworks,
    industry,
    country,
    is_active,
    created_at,
    updated_at
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    'demo-pharma-corp',
    'Demo Pharmaceutical Corporation',
    ARRAY['fda_cgmp', 'ich_q7', 'gmp']::compliance_framework[],
    'pharmaceutical',
    'US',
    true,
    NOW(),
    NOW()
) ON CONFLICT (name) DO NOTHING;

-- Step 2: Drop existing trigger and function
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- Step 3: Create minimal trigger function (no audit logging)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    demo_org_id UUID := '550e8400-e29b-41d4-a716-************';
    user_full_name TEXT;
BEGIN
    -- Extract user name
    user_full_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );
    
    -- Simple user profile creation (no audit logging)
    INSERT INTO user_profiles (
        id,
        email,
        full_name,
        organization_id,
        role,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        user_full_name,
        demo_org_id,
        'read_only',
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create trigger
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Step 5: Verify setup
SELECT 
    'MINIMAL TRIGGER SETUP' as status,
    'Trigger created successfully' as message,
    NOW() as timestamp;

'use client'

import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Award, Calendar, FileText, Star, TrendingUp } from 'lucide-react'

interface Achievement {
  readonly title: string
  readonly description: string
  readonly earned: string
  readonly icon: React.ComponentType<{ className?: string }>
  readonly color: string
}

interface ProfileAchievementsProps {
  readonly className?: string
}

const achievements: Achievement[] = [
  {
    title: 'Compliance Expert',
    description: 'Completed 100+ compliance checks',
    earned: '2023-06-15',
    icon: Award,
    color: 'bg-yellow-500',
  },
  {
    title: 'AI Power User',
    description: 'Used AI Assistant 500+ times',
    earned: '2023-06-10',
    icon: Star,
    color: 'bg-purple-500',
  },
  {
    title: 'Document Master',
    description: 'Analyzed 200+ documents',
    earned: '2023-05-28',
    icon: FileText,
    color: 'bg-blue-500',
  },
  {
    title: 'Efficiency Champion',
    description: 'Saved 100+ hours with automation',
    earned: '2023-05-15',
    icon: TrendingUp,
    color: 'bg-green-500',
  },
]

export function ProfileAchievements({ className }: ProfileAchievementsProps) {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-6', className)}>
      {achievements.map((achievement, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div
                className={cn(
                  'flex h-12 w-12 items-center justify-center rounded-lg text-white',
                  achievement.color
                )}
              >
                <achievement.icon className="h-6 w-6" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold mb-1">{achievement.title}</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  {achievement.description}
                </p>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Calendar className="mr-1 h-3 w-3" />
                  Earned {achievement.earned}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

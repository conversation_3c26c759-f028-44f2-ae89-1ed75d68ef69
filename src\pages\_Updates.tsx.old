import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { UpdateCard } from "@/components/ui/update-card";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Bell,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  ExternalLink,
  Bookmark,
  Plus,
  Minus,
  Share,
  Eye,
  Heart,
} from "lucide-react";

export default function Updates() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedAgency, setSelectedAgency] = useState("all");
  const [selectedSeverity, setSelectedSeverity] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [bookmarkedUpdates, setBookmarkedUpdates] = useState<number[]>([1, 2]); // Mock initial bookmarks

  const regulatoryUpdates = [
    {
      id: 1,
      title:
        "Emergency Safety Communication: Contamination Risk in Sterile Manufacturing",
      agency: "FDA",
      category: "Pharmaceutical",
      publishedDate: "June 20, 2023",
      severity: "critical" as const,
      summary:
        "The FDA has issued an emergency safety communication regarding potential contamination risks in sterile pharmaceutical manufacturing facilities. Immediate action required for all sterile manufacturing operations to review and update contamination control procedures.",
      url: "https://fda.gov/safety/emergency-safety-communication",
      tags: ["Sterile Manufacturing", "Contamination", "Emergency"],
      estimatedImpact: "High",
      deadline: "July 5, 2023",
    },
    {
      id: 2,
      title: "Updated Guidance: Process Validation for Drug Products",
      agency: "FDA",
      category: "Guidance",
      publishedDate: "June 19, 2023",
      severity: "medium" as const,
      summary:
        "The FDA has released updated guidance on process validation incorporating new approaches to continuous process verification and lifecycle management. This guidance replaces the 2011 version and includes enhanced requirements for statistical process controls.",
      url: "https://fda.gov/guidance/process-validation",
      tags: [
        "Process Validation",
        "Lifecycle Management",
        "Statistical Controls",
      ],
      estimatedImpact: "Medium",
      deadline: "December 19, 2023",
    },
    {
      id: 3,
      title: "EMA Guideline on Quality Risk Management",
      agency: "EMA",
      category: "Manufacturing",
      publishedDate: "June 18, 2023",
      severity: "low" as const,
      summary:
        "The European Medicines Agency has published a new baseline on Quality Risk Management for pharmaceutical manufacturers. This guideline emphasizes a proactive approach to identify and mitigating risks in the manufacturing process.",
      url: "https://ema.europa.eu/guidance/quality-risk-management",
      tags: ["Quality Risk Management", "Manufacturing", "EU Guidelines"],
      estimatedImpact: "Low",
      deadline: "September 18, 2023",
    },
    {
      id: 4,
      title: "ICH Q9 R1 Quality Risk Management Revision",
      agency: "ICH",
      category: "International",
      publishedDate: "June 17, 2023",
      severity: "high" as const,
      summary:
        "The International Council for Harmonisation has revised the Q9 guideline on Quality Risk Management. The revision includes new considerations for subjectivity in risk assessment and formalized risk review processes.",
      url: "https://ich.org/page/quality-guidelines",
      tags: ["ICH Guidelines", "Risk Assessment", "International Standards"],
      estimatedImpact: "High",
      deadline: "March 17, 2024",
    },
    {
      id: 5,
      title:
        "Draft Guideline: Environmental Risk Assessment for Pharmaceuticals",
      agency: "EMA",
      category: "Environmental",
      publishedDate: "June 16, 2023",
      severity: "medium" as const,
      summary:
        "The European Medicines Agency has published a draft guideline on environmental risk assessment for human pharmaceuticals. The document outlines new requirements for assessing the environmental impact of pharmaceutical manufacturing and disposal.",
      url: "https://ema.europa.eu/guidance/environmental-risk",
      tags: ["Environmental Risk", "Manufacturing Impact", "Sustainability"],
      estimatedImpact: "Medium",
      deadline: "August 16, 2023",
    },
  ];

  const metrics = [
    { label: "New This Week", value: "23", color: "bg-primary" },
    { label: "Critical Updates", value: "5", color: "bg-destructive" },
    { label: "Bookmarked", value: "12", color: "bg-warning" },
    { label: "Total Updates", value: "1,247", color: "bg-success" },
  ];

  const filteredUpdates = regulatoryUpdates.filter((update) => {
    const matchesSearch =
      update.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      update.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
      update.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    const matchesAgency =
      selectedAgency === "all" || update.agency === selectedAgency;
    const matchesSeverity =
      selectedSeverity === "all" || update.severity === selectedSeverity;

    return matchesSearch && matchesAgency && matchesSeverity;
  });

  const toggleBookmark = (updateId: number) => {
    setBookmarkedUpdates((prev) =>
      prev.includes(updateId)
        ? prev.filter((id) => id !== updateId)
        : [...prev, updateId],
    );
  };

  const isBookmarked = (updateId: number) =>
    bookmarkedUpdates.includes(updateId);

  const getAgencyBadgeStyle = (agency: string) => {
    switch (agency) {
      case "FDA":
        return "bg-blue-500/10 text-blue-600 border-blue-500/20 dark:bg-blue-500/20 dark:text-blue-400 dark:border-blue-500/30";
      case "EMA":
        return "bg-green-500/10 text-green-600 border-green-500/20 dark:bg-green-500/20 dark:text-green-400 dark:border-green-500/30";
      case "ICH":
        return "bg-purple-500/10 text-purple-600 border-purple-500/20 dark:bg-purple-500/20 dark:text-purple-400 dark:border-purple-500/30";
      case "CDSCO":
        return "bg-orange-500/10 text-orange-600 border-orange-500/20 dark:bg-orange-500/20 dark:text-orange-400 dark:border-orange-500/30";
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20 dark:bg-gray-500/20 dark:text-gray-400 dark:border-gray-500/30";
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Regulatory Updates
            </h1>
            <p className="text-muted-foreground mt-1">
              Stay informed with the latest regulatory changes and compliance
              requirements
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                // Mock export functionality
                const exportData = {
                  timestamp: new Date().toISOString(),
                  totalUpdates: regulatoryUpdates.length,
                  criticalUpdates: regulatoryUpdates.filter(
                    (u) => u.severity === "critical",
                  ).length,
                  updates: regulatoryUpdates,
                };
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], {
                  type: "application/json",
                });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement("a");
                link.href = url;
                link.download = `regulatory-updates-${new Date().toISOString().split("T")[0]}.json`;
                link.click();
                URL.revokeObjectURL(url);
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {metrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${metric.color}`} />
                  <div>
                    <p className="text-2xl font-bold text-foreground">
                      {metric.value}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {metric.label}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search updates by title, agency, or keyword..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select
                  value={selectedAgency}
                  onValueChange={setSelectedAgency}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="All Agencies" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agencies</SelectItem>
                    <SelectItem value="FDA">FDA</SelectItem>
                    <SelectItem value="EMA">EMA</SelectItem>
                    <SelectItem value="ICH">ICH</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={selectedSeverity}
                  onValueChange={setSelectedSeverity}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="All Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severity</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="severity">Severity</SelectItem>
                    <SelectItem value="agency">Agency</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Updates List */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Updates</TabsTrigger>
            <TabsTrigger value="critical" className="flex items-center">
              Critical
              <Badge
                variant="destructive"
                className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                5
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="action-required">Action Required</TabsTrigger>
            <TabsTrigger value="bookmarked">Bookmarked</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {filteredUpdates.map((update) => (
              <Card
                key={update.id}
                className="transition-all duration-200 hover:shadow-md hover:border-primary/20 cursor-pointer"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge
                          className={
                            update.severity === "critical"
                              ? "bg-destructive text-destructive-foreground"
                              : update.severity === "high"
                                ? "bg-warning text-warning-foreground"
                                : update.severity === "medium"
                                  ? "bg-info text-info-foreground"
                                  : "bg-success text-success-foreground"
                          }
                        >
                          {update.severity === "critical" && (
                            <AlertTriangle className="mr-1 h-3 w-3" />
                          )}
                          {update.severity === "high" && (
                            <Clock className="mr-1 h-3 w-3" />
                          )}
                          {update.severity === "medium" && (
                            <Bell className="mr-1 h-3 w-3" />
                          )}
                          {update.severity === "low" && (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          )}
                          {update.severity.charAt(0).toUpperCase() +
                            update.severity.slice(1)}
                        </Badge>
                        <Badge
                          variant="outline"
                          className={getAgencyBadgeStyle(update.agency)}
                        >
                          {update.agency}
                        </Badge>
                        <Badge variant="outline" className="dark:border-border">
                          {update.category}
                        </Badge>
                      </div>
                      <h3 className="font-semibold text-foreground leading-tight mb-1">
                        {update.title}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Published: {update.publishedDate}</span>
                        {update.deadline && (
                          <span className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            Deadline: {update.deadline}
                          </span>
                        )}
                        <span>Impact: {update.estimatedImpact}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      {update.summary}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {update.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {/* Primary Action - Read Full Update */}
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-primary hover:text-primary-foreground hover:border-primary transition-all duration-200"
                        >
                          <Eye className="mr-2 h-3 w-3" />
                          Read Full Update
                        </Button>

                        {/* Secondary Actions with improved UX */}
                        <div className="flex items-center border rounded-md overflow-hidden">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-3 rounded-none border-r hover:bg-muted/60 transition-colors group"
                            onClick={() => window.open(update.url, "_blank")}
                          >
                            <ExternalLink className="h-3 w-3 mr-1.5 group-hover:scale-110 transition-transform" />
                            <span className="text-xs font-medium">Source</span>
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-8 px-3 rounded-none transition-all duration-200 ${
                              isBookmarked(update.id)
                                ? "bg-primary/10 text-primary hover:bg-primary/20"
                                : "hover:bg-muted/60"
                            }`}
                            onClick={() => toggleBookmark(update.id)}
                          >
                            {isBookmarked(update.id) ? (
                              <>
                                <Bookmark className="h-3 w-3 mr-1.5 fill-current" />
                                <span className="text-xs font-medium">
                                  Saved
                                </span>
                              </>
                            ) : (
                              <>
                                <Plus className="h-3 w-3 mr-1.5" />
                                <span className="text-xs font-medium">
                                  Save
                                </span>
                              </>
                            )}
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {update.severity === "critical" && (
                          <Badge
                            variant="destructive"
                            className="animate-pulse"
                          >
                            Action Required
                          </Badge>
                        )}

                        {/* Quick Actions */}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-muted/60 rounded-full"
                          title="Share update"
                        >
                          <Share className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="critical" className="space-y-4">
            {regulatoryUpdates
              .filter((update) => update.severity === "critical")
              .map((update) => (
                <Card
                  key={update.id}
                  className="transition-all duration-200 hover:shadow-md hover:border-destructive/20 cursor-pointer"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className="bg-destructive text-destructive-foreground">
                            <AlertTriangle className="mr-1 h-3 w-3" />
                            Critical
                          </Badge>
                          <Badge
                            variant="outline"
                            className={getAgencyBadgeStyle(update.agency)}
                          >
                            {update.agency}
                          </Badge>
                          <Badge
                            variant="outline"
                            className="dark:border-border"
                          >
                            {update.category}
                          </Badge>
                        </div>
                        <h3 className="font-semibold text-foreground leading-tight mb-1">
                          {update.title}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Published: {update.publishedDate}</span>
                          {update.deadline && (
                            <span className="flex items-center">
                              <Calendar className="mr-1 h-3 w-3" />
                              Deadline: {update.deadline}
                            </span>
                          )}
                          <span>Impact: {update.estimatedImpact}</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">
                        {update.summary}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {update.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-destructive hover:text-destructive-foreground hover:border-destructive transition-all duration-200"
                          >
                            <AlertTriangle className="mr-2 h-3 w-3" />
                            Read Critical Update
                          </Button>

                          <div className="flex items-center border border-destructive/20 rounded-md overflow-hidden">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 px-3 rounded-none border-r border-destructive/20 hover:bg-destructive/10 text-destructive transition-colors group"
                              onClick={() => window.open(update.url, "_blank")}
                            >
                              <ExternalLink className="h-3 w-3 mr-1.5 group-hover:scale-110 transition-transform" />
                              <span className="text-xs font-medium">
                                Source
                              </span>
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              className={`h-8 px-3 rounded-none transition-all duration-200 ${
                                isBookmarked(update.id)
                                  ? "bg-destructive/10 text-destructive hover:bg-destructive/20"
                                  : "text-destructive hover:bg-destructive/10"
                              }`}
                              onClick={() => toggleBookmark(update.id)}
                            >
                              {isBookmarked(update.id) ? (
                                <>
                                  <Bookmark className="h-3 w-3 mr-1.5 fill-current" />
                                  <span className="text-xs font-medium">
                                    Saved
                                  </span>
                                </>
                              ) : (
                                <>
                                  <Plus className="h-3 w-3 mr-1.5" />
                                  <span className="text-xs font-medium">
                                    Save
                                  </span>
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                        <Badge variant="destructive" className="animate-pulse">
                          Action Required
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </TabsContent>

          <TabsContent value="action-required" className="space-y-4">
            {regulatoryUpdates
              .filter(
                (update) =>
                  update.severity === "high" || update.severity === "critical",
              )
              .map((update) => (
                <Card
                  key={update.id}
                  className="transition-all duration-200 hover:shadow-md hover:border-warning/20 cursor-pointer"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge
                            className={
                              update.severity === "critical"
                                ? "bg-destructive text-destructive-foreground"
                                : "bg-warning text-warning-foreground"
                            }
                          >
                            {update.severity === "critical" && (
                              <AlertTriangle className="mr-1 h-3 w-3" />
                            )}
                            {update.severity === "high" && (
                              <Clock className="mr-1 h-3 w-3" />
                            )}
                            {update.severity.charAt(0).toUpperCase() +
                              update.severity.slice(1)}
                          </Badge>
                          <Badge
                            variant="outline"
                            className={getAgencyBadgeStyle(update.agency)}
                          >
                            {update.agency}
                          </Badge>
                        </div>
                        <h3 className="font-semibold text-foreground leading-tight mb-1">
                          {update.title}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Published: {update.publishedDate}</span>
                          {update.deadline && (
                            <span className="flex items-center text-warning">
                              <Calendar className="mr-1 h-3 w-3" />
                              Deadline: {update.deadline}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">
                        {update.summary}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-warning hover:text-warning-foreground hover:border-warning transition-all duration-200"
                          >
                            <Eye className="mr-2 h-3 w-3" />
                            Read Update
                          </Button>

                          <Button
                            size="sm"
                            className="bg-warning hover:bg-warning/90 text-warning-foreground"
                          >
                            <CheckCircle className="mr-2 h-3 w-3" />
                            Take Action
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className={`transition-all duration-200 ${
                              isBookmarked(update.id)
                                ? "bg-primary/10 text-primary hover:bg-primary/20"
                                : "hover:bg-muted/60"
                            }`}
                            onClick={() => toggleBookmark(update.id)}
                          >
                            {isBookmarked(update.id) ? (
                              <Bookmark className="h-4 w-4 fill-current" />
                            ) : (
                              <Plus className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <Badge variant="outline">Action Required</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </TabsContent>

          <TabsContent value="bookmarked" className="space-y-4">
            {regulatoryUpdates.slice(0, 2).map((update) => (
              <Card
                key={update.id}
                className="transition-all duration-200 hover:shadow-md hover:border-primary/20 cursor-pointer"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">📌 Bookmarked</Badge>
                        <Badge
                          variant="outline"
                          className={getAgencyBadgeStyle(update.agency)}
                        >
                          {update.agency}
                        </Badge>
                        <Badge variant="outline" className="dark:border-border">
                          {update.category}
                        </Badge>
                      </div>
                      <h3 className="font-semibold text-foreground leading-tight mb-1">
                        {update.title}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Bookmarked: {update.publishedDate}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      {update.summary}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-primary hover:text-primary-foreground hover:border-primary transition-all duration-200"
                        >
                          <Eye className="mr-2 h-3 w-3" />
                          Read Full Update
                        </Button>

                        <div className="flex items-center border rounded-md overflow-hidden">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-3 rounded-none border-r hover:bg-muted/60 transition-colors group"
                            onClick={() => window.open(update.url, "_blank")}
                          >
                            <ExternalLink className="h-3 w-3 mr-1.5 group-hover:scale-110 transition-transform" />
                            <span className="text-xs font-medium">Source</span>
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-3 rounded-none hover:bg-destructive/10 text-destructive transition-colors"
                            onClick={() => toggleBookmark(update.id)}
                          >
                            <Minus className="h-3 w-3 mr-1.5" />
                            <span className="text-xs font-medium">Remove</span>
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className="bg-primary/5 text-primary border-primary/20"
                        >
                          <Heart className="mr-1 h-3 w-3 fill-current" />
                          Bookmarked
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

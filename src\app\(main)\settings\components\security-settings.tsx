'use client'

import { <PERSON>, <PERSON>Off, Key, Shield, Save, Loader2 } from 'lucide-react'
import { useState, useEffect } from 'react'

import { Button } from '@/components/ui-radix/button'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card'
import { Input } from '@/components/ui-radix/input'
import { Label } from '@/components/ui-radix/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui-radix/select'
import { Separator } from '@/components/ui-radix/separator'
import { Switch } from '@/components/ui-radix/switch'
import { useSettingsManagement } from '@/hooks/use-settings-management'
import { SecuritySettings } from '@/types/user-settings'

interface SecuritySettingsProps {
  readonly className?: string;
}

export function SecuritySettingsComponent({ className }: SecuritySettingsProps) {
  const {
    getSecuritySettings,
    updateSecuritySettings,
    isUpdating,
    validationErrors,
    clearErrors
  } = useSettingsManagement()

  const [settings, setSettings] = useState<SecuritySettings>(getSecuritySettings())
  const [originalSettings, setOriginalSettings] = useState<SecuritySettings>(getSecuritySettings())
  const [hasChanges, setHasChanges] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Update settings when user profile changes
  useEffect(() => {
    const currentSettings = getSecuritySettings()
    setSettings(currentSettings)
    setOriginalSettings(currentSettings)
    setHasChanges(false)
  }, [getSecuritySettings])

  const handleSettingChange = (key: keyof SecuritySettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
    clearErrors()
  }

  const handlePasswordRequirementChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      password_requirements: {
        ...prev.password_requirements,
        [key]: value
      }
    }))
    setHasChanges(true)
    clearErrors()
  }

  const handleSave = async () => {
    if (!hasChanges) {
      console.log('⏭️ No security changes to save')
      return
    }

    console.log('🔄 SecuritySettings - Starting save process:', {
      originalSettings,
      newSettings: settings,
      hasChanges
    })

    const success = await updateSecuritySettings(settings)

    console.log('📊 SecuritySettings - Update result:', { success })

    if (success) {
      setHasChanges(false)
      setOriginalSettings(settings)
      console.log('✅ SecuritySettings - Save completed successfully')
    } else {
      console.error('❌ SecuritySettings - Save failed')
    }
  }

  return (
    <div className="flex gap-6">
      {/* Password Settings */}
      <Card className="flex-1">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground flex items-center">
            <Key className="mr-2 h-6 w-6" />
            Password & Authentication
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h4 className="font-medium">Change Password</h4>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current-password">Current Password</Label>
                <div className="relative">
                  <Input
                    id="current-password"
                    type={showCurrentPassword ? 'text' : 'password'}
                    placeholder="Enter current password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-muted/50"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-6 w-6" />
                    ) : (
                      <Eye className="h-6 w-6" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-password">New Password</Label>
                <div className="relative">
                  <Input
                    id="new-password"
                    type={showNewPassword ? 'text' : 'password'}
                    placeholder="Enter new password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-muted/50"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-6 w-6" />
                    ) : (
                      <Eye className="h-6 w-6" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm New Password</Label>
                <div className="relative">
                  <Input
                    id="confirm-password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm new password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-muted/50"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-6 w-6" />
                    ) : (
                      <Eye className="h-6 w-6" />
                    )}
                  </Button>
                </div>
              </div>

              <Button>Update Password</Button>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h4 className="font-medium">Two-Factor Authentication</h4>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Enable 2FA</p>
                <p className="text-sm text-muted-foreground">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Switch
                checked={settings.two_factor_enabled}
                onCheckedChange={(checked) => handleSettingChange('two_factor_enabled', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Session Settings */}
      <Card className="w-80 flex-shrink-0 self-start">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-foreground flex items-center">
            <Shield className="mr-2 h-6 w-6" />
            Session Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <Label>Session Timeout</Label>
            <Select
              value={`${settings.session_timeout}min`}
              onValueChange={(value) => {
                const minutes = parseInt(value.replace('min', '').replace('hour', '').replace('hours', ''))
                const actualMinutes = value.includes('hour') ? minutes * 60 : minutes
                handleSettingChange('session_timeout', actualMinutes)
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15min">15 minutes</SelectItem>
                <SelectItem value="30min">30 minutes</SelectItem>
                <SelectItem value="1hour">1 hour</SelectItem>
                <SelectItem value="4hours">4 hours</SelectItem>
                <SelectItem value="8hours">8 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Auto-logout</p>
              <p className="text-sm text-muted-foreground">
                Automatically log out inactive sessions
              </p>
            </div>
            <Switch
              checked={settings.login_notifications}
              onCheckedChange={(checked) => handleSettingChange('login_notifications', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end pt-4 col-span-2">
        <Button
          onClick={handleSave}
          disabled={!hasChanges || isUpdating}
          className="min-w-[140px]"
        >
          {isUpdating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

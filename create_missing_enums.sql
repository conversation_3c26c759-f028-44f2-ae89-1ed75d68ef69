-- Create missing enum types for electronic signatures
-- Run this FIRST before the fix script

-- Create authentication_method enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'authentication_method') THEN
        CREATE TYPE authentication_method AS ENUM (
            'password',         -- Password-based authentication
            'biometric',        -- Biometric authentication
            'token',           -- Hardware/software token
            'certificate',     -- Digital certificate
            'multi_factor'     -- Multi-factor authentication
        );
        RAISE NOTICE 'Created authentication_method enum';
    ELSE
        RAISE NOTICE 'authentication_method enum already exists';
    END IF;
END $$;

-- Verify enum creation
SELECT 
    'ENUM VERIFICATION' as status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_type WHERE typname = 'authentication_method')
        THEN '✅ authentication_method EXISTS'
        ELSE '❌ authentication_method MISSING'
    END as auth_method_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_type WHERE typname = 'signature_type')
        THEN '✅ signature_type EXISTS'
        ELSE '❌ signature_type MISSING'
    END as signature_type_status;

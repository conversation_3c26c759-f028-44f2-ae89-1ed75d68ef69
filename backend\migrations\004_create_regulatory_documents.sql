-- Migration: 004_create_regulatory_documents.sql
-- Description: Create regulatory documents table for document management
-- Created: 2025-01-11
-- Dependencies: 002_create_user_profiles.sql

-- Create custom types
CREATE TYPE document_status AS ENUM (
  'uploaded', 
  'processing', 
  'analyzed', 
  'approved', 
  'rejected', 
  'archived'
);

CREATE TYPE document_type AS ENUM (
  'fda_guidance', 
  'ema_guideline', 
  'ich_document', 
  'user_upload', 
  'regulatory_update',
  'cfr_regulation',
  'pharmacopeia',
  'sop',
  'validation_protocol'
);

CREATE TYPE regulatory_agency AS ENUM (
  'FDA',
  'EMA',
  'ICH',
  'CDSCO',
  'Health_Canada',
  'TGA',
  'PMDA',
  'ANVISA',
  'WHO',
  'USP',
  'EP',
  'JP'
);

-- Create regulatory documents table
CREATE TABLE regulatory_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  title VARCHAR(500) NOT NULL,
  document_type document_type NOT NULL,
  status document_status DEFAULT 'uploaded',
  file_path VARCHAR(1000),
  file_size BIGINT CHECK (file_size >= 0),
  mime_type VARCHAR(100),
  checksum VARCHAR(64),
  source_url TEXT,
  regulatory_agency regulatory_agency,
  publication_date DATE,
  effective_date DATE,
  expiration_date DATE,
  document_number VARCHAR(100),
  version VARCHAR(50),
  language VARCHAR(10) DEFAULT 'en',
  metadata JSONB DEFAULT '{}',
  content_extracted TEXT,
  content_vector tsvector,
  ai_summary TEXT,
  compliance_score DECIMAL(5,2) CHECK (compliance_score >= 0 AND compliance_score <= 100),
  risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  tags TEXT[],
  uploaded_by UUID REFERENCES user_profiles(id),
  reviewed_by UUID REFERENCES user_profiles(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_regulatory_documents_organization_id ON regulatory_documents(organization_id);
CREATE INDEX idx_regulatory_documents_status ON regulatory_documents(status);
CREATE INDEX idx_regulatory_documents_type ON regulatory_documents(document_type);
CREATE INDEX idx_regulatory_documents_agency ON regulatory_documents(regulatory_agency);
CREATE INDEX idx_regulatory_documents_publication_date ON regulatory_documents(publication_date);
CREATE INDEX idx_regulatory_documents_effective_date ON regulatory_documents(effective_date);
CREATE INDEX idx_regulatory_documents_risk_level ON regulatory_documents(risk_level);
CREATE INDEX idx_regulatory_documents_compliance_score ON regulatory_documents(compliance_score);
CREATE INDEX idx_regulatory_documents_tags ON regulatory_documents USING GIN(tags);
CREATE INDEX idx_regulatory_documents_metadata ON regulatory_documents USING GIN(metadata);
CREATE INDEX idx_regulatory_documents_content_search ON regulatory_documents USING GIN(content_vector);
CREATE INDEX idx_regulatory_documents_checksum ON regulatory_documents(checksum);
CREATE INDEX idx_regulatory_documents_document_number ON regulatory_documents(document_number);

-- Composite indexes for common queries
CREATE INDEX idx_regulatory_documents_org_status_type ON regulatory_documents(organization_id, status, document_type);
CREATE INDEX idx_regulatory_documents_agency_date ON regulatory_documents(regulatory_agency, publication_date DESC);
CREATE INDEX idx_regulatory_documents_active_effective ON regulatory_documents(is_active, effective_date) WHERE is_active = true;

-- Enable Row Level Security
ALTER TABLE regulatory_documents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view documents in their organization" ON regulatory_documents
  FOR SELECT USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
  );

CREATE POLICY "Users can upload documents to their organization" ON regulatory_documents
  FOR INSERT WITH CHECK (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND uploaded_by = auth.uid()
  );

CREATE POLICY "Users can update their own documents" ON regulatory_documents
  FOR UPDATE USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND uploaded_by = auth.uid()
  );

CREATE POLICY "Admins can manage all documents in their organization" ON regulatory_documents
  FOR ALL USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('admin', 'manager')
    )
  );

-- Create trigger for updated_at
CREATE TRIGGER update_regulatory_documents_updated_at
    BEFORE UPDATE ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to update content vector for full-text search
CREATE OR REPLACE FUNCTION update_document_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the search vector when content or title changes
  NEW.content_vector := 
    setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.content_extracted, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(NEW.ai_summary, '')), 'C') ||
    setweight(to_tsvector('english', array_to_string(COALESCE(NEW.tags, '{}'), ' ')), 'D');
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for search vector update
CREATE TRIGGER update_document_search_vector_trigger
    BEFORE INSERT OR UPDATE OF title, content_extracted, ai_summary, tags
    ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_document_search_vector();

-- Create function to calculate compliance score
CREATE OR REPLACE FUNCTION calculate_compliance_score(
  doc_id UUID
) RETURNS DECIMAL(5,2) AS $$
DECLARE
  score DECIMAL(5,2) := 0;
  base_score DECIMAL(5,2) := 70;
  agency_bonus DECIMAL(5,2) := 0;
  date_penalty DECIMAL(5,2) := 0;
  content_bonus DECIMAL(5,2) := 0;
BEGIN
  SELECT 
    CASE 
      WHEN regulatory_agency IN ('FDA', 'EMA', 'ICH') THEN 15
      WHEN regulatory_agency IN ('CDSCO', 'Health_Canada', 'TGA') THEN 10
      ELSE 5
    END,
    CASE 
      WHEN publication_date < CURRENT_DATE - INTERVAL '2 years' THEN 10
      WHEN publication_date < CURRENT_DATE - INTERVAL '1 year' THEN 5
      ELSE 0
    END,
    CASE 
      WHEN content_extracted IS NOT NULL AND length(content_extracted) > 1000 THEN 10
      WHEN ai_summary IS NOT NULL AND length(ai_summary) > 100 THEN 5
      ELSE 0
    END
  INTO agency_bonus, date_penalty, content_bonus
  FROM regulatory_documents
  WHERE id = doc_id;
  
  score := base_score + agency_bonus - date_penalty + content_bonus;
  
  -- Ensure score is within bounds
  score := GREATEST(0, LEAST(100, score));
  
  RETURN score;
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-calculate risk level
CREATE OR REPLACE FUNCTION auto_calculate_risk_level()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-calculate risk level based on various factors
  IF NEW.compliance_score IS NULL THEN
    NEW.compliance_score := calculate_compliance_score(NEW.id);
  END IF;
  
  -- Determine risk level based on compliance score and other factors
  IF NEW.compliance_score >= 90 THEN
    NEW.risk_level := 'low';
  ELSIF NEW.compliance_score >= 70 THEN
    NEW.risk_level := 'medium';
  ELSIF NEW.compliance_score >= 50 THEN
    NEW.risk_level := 'high';
  ELSE
    NEW.risk_level := 'critical';
  END IF;
  
  -- Adjust risk level based on document type and agency
  IF NEW.document_type IN ('fda_guidance', 'ema_guideline') AND NEW.regulatory_agency IN ('FDA', 'EMA') THEN
    -- FDA/EMA guidance documents are typically higher risk
    IF NEW.risk_level = 'low' THEN NEW.risk_level := 'medium'; END IF;
    IF NEW.risk_level = 'medium' THEN NEW.risk_level := 'high'; END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto risk calculation
CREATE TRIGGER auto_calculate_document_risk
    BEFORE INSERT OR UPDATE OF compliance_score, document_type, regulatory_agency
    ON regulatory_documents
    FOR EACH ROW
    EXECUTE FUNCTION auto_calculate_risk_level();

-- Create view for document analytics
CREATE VIEW document_analytics_view AS
SELECT 
  o.name as organization_name,
  rd.document_type,
  rd.regulatory_agency,
  rd.status,
  rd.risk_level,
  COUNT(*) as document_count,
  AVG(rd.compliance_score) as avg_compliance_score,
  MIN(rd.publication_date) as oldest_document,
  MAX(rd.publication_date) as newest_document,
  COUNT(*) FILTER (WHERE rd.status = 'approved') as approved_count,
  COUNT(*) FILTER (WHERE rd.risk_level = 'critical') as critical_count
FROM regulatory_documents rd
JOIN organizations o ON rd.organization_id = o.id
WHERE rd.is_active = true
GROUP BY o.name, rd.document_type, rd.regulatory_agency, rd.status, rd.risk_level;

-- Add comments for documentation
COMMENT ON TABLE regulatory_documents IS 'Core table for pharmaceutical regulatory document management';
COMMENT ON COLUMN regulatory_documents.content_vector IS 'Full-text search vector for document content';
COMMENT ON COLUMN regulatory_documents.compliance_score IS 'AI-calculated compliance score (0-100)';
COMMENT ON COLUMN regulatory_documents.risk_level IS 'Auto-calculated risk level based on multiple factors';
COMMENT ON COLUMN regulatory_documents.metadata IS 'JSON metadata for document properties and AI analysis results';
COMMENT ON FUNCTION calculate_compliance_score(UUID) IS 'Calculates compliance score based on agency, date, and content quality';
COMMENT ON VIEW document_analytics_view IS 'Aggregated analytics for document management dashboard';
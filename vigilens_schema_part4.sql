-- VigiLens Schema Part 4 - RLS Policies and Initial Data
-- Run this after vigilens_schema_part3.sql

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_role_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE regulatory_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_trail ENABLE ROW LEVEL SECURITY;
ALTER TABLE electronic_signatures ENABLE ROW LEVEL SECURITY;

-- Organizations policies
CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (
        id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "Org admins can update their organization" ON organizations
    FOR UPDATE USING (
        id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
        AND (SELECT role FROM user_profiles WHERE id = auth.uid()) IN ('super_admin', 'org_admin')
    );

-- User profiles policies
CREATE POLICY "Users can view profiles in their organization" ON user_profiles
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Allow trigger user creation during registration" ON user_profiles
    FOR INSERT WITH CHECK (
        auth.uid() IS NULL  -- Allow during registration (no session)
        OR 
        id = auth.uid()     -- Allow users to create own profile
    );

-- Role permissions policies
CREATE POLICY "Users can view role permissions in their organization" ON role_permissions
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

-- User role assignments policies
CREATE POLICY "Users can view role assignments in their organization" ON user_role_assignments
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

-- Regulatory documents policies
CREATE POLICY "Users can view documents in their organization" ON regulatory_documents
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can create documents in their organization" ON regulatory_documents
    FOR INSERT WITH CHECK (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
        AND created_by = auth.uid()
    );

CREATE POLICY "Users can update documents they created or are assigned to" ON regulatory_documents
    FOR UPDATE USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
        AND (
            created_by = auth.uid()
            OR auth.uid() = ANY(assigned_to)
            OR reviewer_id = auth.uid()
            OR approver_id = auth.uid()
        )
    );

-- Document versions policies
CREATE POLICY "Users can view document versions in their organization" ON document_versions
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can create document versions" ON document_versions
    FOR INSERT WITH CHECK (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
        AND created_by = auth.uid()
    );

-- Audit trail policies
CREATE POLICY "Users can view audit trail in their organization" ON audit_trail
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "System can insert audit records" ON audit_trail
    FOR INSERT WITH CHECK (true); -- Allow system to insert audit records

-- Electronic signatures policies
CREATE POLICY "Users can view signatures in their organization" ON electronic_signatures
    FOR SELECT USING (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can create their own signatures" ON electronic_signatures
    FOR INSERT WITH CHECK (
        organization_id = (SELECT organization_id FROM user_profiles WHERE id = auth.uid())
        AND signer_id = auth.uid()
    );

-- =====================================================
-- INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Insert demo organization (will be used by auth trigger)
INSERT INTO organizations (
    name,
    display_name,
    description,
    company_type,
    regulatory_id,
    primary_contact_email,
    phone,
    address,
    compliance_frameworks,
    regulatory_agencies,
    timezone,
    locale
) VALUES (
    'demo-pharma-corp',
    'Demo Pharmaceutical Corporation',
    'Demo organization for VigiLens pharmaceutical compliance platform',
    'pharmaceutical',
    'FDA-12345',
    '<EMAIL>',
    '******-0123',
    jsonb_build_object(
        'street', '123 Pharma Drive',
        'city', 'Boston',
        'state', 'MA',
        'zip', '02101',
        'country', 'USA'
    ),
    ARRAY['fda_21_cfr_part_11', 'gxp', 'hipaa']::compliance_framework[],
    ARRAY['fda', 'ema']::regulatory_agency[],
    'America/New_York',
    'en-US'
) ON CONFLICT (name) DO NOTHING;

-- Verify RLS policies created
SELECT 
    'RLS POLICIES CREATED' as status,
    COUNT(*) as policy_count,
    COUNT(DISTINCT tablename) as tables_with_policies
FROM pg_policies 
WHERE schemaname = 'public';

-- Verify demo organization created
SELECT 
    'DEMO ORGANIZATION CREATED' as status,
    name,
    display_name,
    compliance_frameworks,
    regulatory_agencies
FROM organizations 
WHERE name = 'demo-pharma-corp';

import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Construction, Upload } from "lucide-react";

export default function UploadPage() {
  return (
    <DashboardLayout>
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                <Upload className="h-8 w-8 text-primary" />
              </div>
            </div>
            <CardTitle className="text-xl">
              Document Upload & Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Upload documents for AI-powered compliance analysis and regulatory
              checking
            </p>
            <div className="flex items-center justify-center space-x-2 text-warning">
              <Construction className="h-4 w-4" />
              <span className="text-sm">Coming Soon</span>
            </div>
            <Button variant="outline" disabled>
              Upload Documents
            </Button>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BookOpen, FileText, Lightbulb } from 'lucide-react';

import { Button } from '@/components/ui-radix/button';

import type { QuickAction } from '../types';

interface SuggestionsPanelProps {
  readonly onQuickAction: (action: string) => void;
  readonly onSuggestedQuestion: (question: string) => void;
  readonly isVisible?: boolean;
}

export function SuggestionsPanel({
  onQuickAction,
  onSuggestedQuestion,
  isVisible = true,
}: SuggestionsPanelProps) {
  const quickActions: readonly QuickAction[] = [
    {
      icon: FileText,
      label: 'Analyze Document',
      description: 'Upload a document for compliance analysis',
      action: 'document_analysis',
    },
    {
      icon: AlertTriangle,
      label: 'Check Compliance',
      description: 'Verify compliance against regulations',
      action: 'compliance_check',
    },
    {
      icon: BookOpen,
      label: 'Regulatory Updates',
      description: 'Get latest regulatory changes',
      action: 'regulatory_updates',
    },
    {
      icon: Lightbulb,
      label: 'Best Practices',
      description: 'Learn industry best practices',
      action: 'best_practices',
    },
  ]

  const suggestedQuestions = [
    'What are the latest FDA guidance documents for pharmaceutical manufacturing?',
    'How do I implement a risk-based approach to process validation?',
    'What are the key requirements for data integrity in pharmaceutical manufacturing?',
    'Can you explain the differences between ICH Q8, Q9, and Q10?',
    'What documentation is required for computer system validation?',
  ]

  if (!isVisible) {
    return null
  }

  return (
    <div className="w-80 flex-shrink-0 space-y-6">
      {/* Quick Actions */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="font-semibold text-base">Quick Actions</h3>
        </div>
        <div className="p-6 pt-0">
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-4 flex flex-col items-start space-y-2 hover:bg-muted hover:text-foreground"
                onClick={() => onQuickAction(action.action)}
              >
                <div className="flex items-center space-x-2">
                  <action.icon className="h-4 w-4" />
                  <span className="font-medium text-sm">{action.label}</span>
                </div>
                <p className="text-xs text-muted-foreground text-left">
                  {action.description}
                </p>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Suggested Questions */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="font-semibold text-base">Suggested Questions</h3>
        </div>
        <div className="p-6 pt-0 space-y-2">
          {suggestedQuestions.map((question, index) => (
            <Button
              key={index}
              variant="ghost"
              className="w-full text-left h-auto p-3 justify-start hover:bg-muted"
              onClick={() => onSuggestedQuestion(question)}
            >
              <div className="text-sm text-muted-foreground">{question}</div>
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}

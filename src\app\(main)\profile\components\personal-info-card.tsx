'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>ead<PERSON>, CardTitle } from '@/components/ui/card'
import { useProfileStore } from '../store'

export function PersonalInfoCard() {
  const { profileData } = useProfileStore()

  return (
    <Card className="rounded-lg bg-card text-card-foreground shadow-sm">
      <CardHeader className="flex flex-col space-y-1.5 p-6">
        <CardTitle className="text-2xl font-semibold leading-none tracking-tight">
          Personal Information
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 pt-0 space-y-4">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-foreground">About Me</h4>
            <p className="text-sm text-muted-foreground mt-1">
              {profileData.bio}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-foreground">Contact</h4>
              <p className="text-sm text-muted-foreground">{profileData.email}</p>
              <p className="text-sm text-muted-foreground">{profileData.phone}</p>
            </div>
            <div>
              <h4 className="font-medium text-foreground">Location</h4>
              <p className="text-sm text-muted-foreground">{profileData.location}</p>
              <p className="text-sm text-muted-foreground">PST</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

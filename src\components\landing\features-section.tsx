import {
  BarChart3,
  Bell,
  Brain,
  Database,
  MessageSquare,
  Target,
  Zap,
} from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'

export function FeaturesSection() {
  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Analysis',
      description:
        'Advanced machine learning algorithms analyze regulatory documents with 99.7% accuracy, identifying compliance gaps instantly.',
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Bell,
      title: 'Real-time Monitoring',
      description:
        'Continuous surveillance of FDA, EMA, ICH, and CDSCO updates ensures you never miss critical regulatory changes.',
      gradient: 'from-orange-500 to-red-500',
    },
    {
      icon: Target,
      title: 'Risk Assessment',
      description:
        'Proactive risk identification and mitigation strategies keep your pharmaceutical operations compliant and secure.',
      gradient: 'from-green-500 to-emerald-500',
    },
    {
      icon: Database,
      title: 'Document Management',
      description:
        'Centralized repository with version control, automated workflows, and seamless collaboration across teams.',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: BarChart3,
      title: 'Analytics Dashboard',
      description:
        'Comprehensive insights and reporting tools provide clear visibility into your compliance posture and trends.',
      gradient: 'from-indigo-500 to-blue-500',
    },
    {
      icon: MessageSquare,
      title: 'Expert AI Assistant',
      description:
        'Specialized pharmaceutical AI assistant provides instant answers to complex regulatory questions and guidance.',
      gradient: 'from-teal-500 to-cyan-500',
    },
  ]

  return (
    <section id="features" className="py-24 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <Badge
            variant="outline"
            className="mb-6 border-purple-200 text-purple-700"
          >
            <Zap className="w-4 h-4 mr-2" />
            Powerful Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
            Everything You Need for
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Regulatory Excellence
            </span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive AI-powered tools designed specifically for
            pharmaceutical manufacturing compliance requirements and regulatory
            management.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="group border-0 bg-card/80 dark:bg-card/60 backdrop-blur-sm hover:bg-card transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 overflow-hidden"
            >
              <CardHeader className="relative">
                <div
                  className={`flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br ${feature.gradient} shadow-lg mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-slate-900 group-hover:text-purple-600 transition-colors">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
              <div
                className={`absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r ${feature.gradient} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}
              ></div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

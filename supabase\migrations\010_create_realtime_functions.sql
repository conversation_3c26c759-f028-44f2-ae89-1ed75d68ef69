-- Migration 010: Create Real-time Functions for VCP_001 Edge Functions
-- Supports calculate-compliance-score and setup-realtime-channels Edge Functions
-- Pharmaceutical compliance platform real-time triggers and functions

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Function: Setup compliance alert trigger
CREATE OR REPLACE FUNCTION setup_compliance_alert_trigger(
    org_id UUID,
    channel_name TEXT,
    alert_thresholds JSONB DEFAULT '{}'::JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    trigger_name TEXT;
    function_name TEXT;
BEGIN
    -- Generate unique trigger and function names
    trigger_name := 'compliance_alert_trigger_' || replace(org_id::TEXT, '-', '_');
    function_name := 'notify_compliance_alert_' || replace(org_id::TEXT, '-', '_');
    
    -- Create notification function for this organization
    EXECUTE format('
        CREATE OR REPLACE FUNCTION %I()
        RETURNS TRIGGER
        LANGUAGE plpgsql
        AS $func$
        DECLARE
            alert_payload JSONB;
            should_alert BOOLEAN := FALSE;
        BEGIN
            -- Check if compliance score is below threshold
            IF (NEW.overall_score IS NOT NULL AND NEW.overall_score < COALESCE((%L::JSONB->>''critical_score'')::NUMERIC, 0.3)) THEN
                should_alert := TRUE;
            END IF;
            
            -- Check for critical violations
            IF (NEW.violations IS NOT NULL AND jsonb_array_length(NEW.violations) > COALESCE((%L::JSONB->>''major_violations'')::INTEGER, 3)) THEN
                should_alert := TRUE;
            END IF;
            
            -- Send alert if conditions are met
            IF should_alert THEN
                alert_payload := jsonb_build_object(
                    ''type'', ''compliance_alert'',
                    ''organization_id'', NEW.organization_id,
                    ''document_id'', NEW.document_id,
                    ''compliance_score'', NEW.overall_score,
                    ''violation_count'', COALESCE(jsonb_array_length(NEW.violations), 0),
                    ''alert_level'', CASE 
                        WHEN NEW.overall_score < 0.2 THEN ''critical''
                        WHEN NEW.overall_score < 0.5 THEN ''high''
                        ELSE ''medium''
                    END,
                    ''timestamp'', NOW()
                );
                
                PERFORM pg_notify(%L, alert_payload::TEXT);
            END IF;
            
            RETURN NEW;
        END;
        $func$;
    ', function_name, alert_thresholds, alert_thresholds, channel_name);
    
    -- Create trigger on document_compliance_assessments table
    EXECUTE format('
        DROP TRIGGER IF EXISTS %I ON document_compliance_assessments;
        CREATE TRIGGER %I
            AFTER INSERT OR UPDATE ON document_compliance_assessments
            FOR EACH ROW
            WHEN (NEW.organization_id = %L)
            EXECUTE FUNCTION %I();
    ', trigger_name, trigger_name, org_id, function_name);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to setup compliance alert trigger: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Function: Setup document processing trigger
CREATE OR REPLACE FUNCTION setup_document_processing_trigger(
    org_id UUID,
    channel_name TEXT,
    processing_stages TEXT[] DEFAULT ARRAY['uploaded', 'processing', 'completed', 'failed']
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    trigger_name TEXT;
    function_name TEXT;
BEGIN
    -- Generate unique trigger and function names
    trigger_name := 'document_processing_trigger_' || replace(org_id::TEXT, '-', '_');
    function_name := 'notify_document_processing_' || replace(org_id::TEXT, '-', '_');
    
    -- Create notification function for this organization
    EXECUTE format('
        CREATE OR REPLACE FUNCTION %I()
        RETURNS TRIGGER
        LANGUAGE plpgsql
        AS $func$
        DECLARE
            processing_payload JSONB;
        BEGIN
            -- Only notify for specified processing stages
            IF NEW.processing_status = ANY(%L::TEXT[]) THEN
                processing_payload := jsonb_build_object(
                    ''type'', ''document_processing'',
                    ''organization_id'', NEW.organization_id,
                    ''document_id'', NEW.id,
                    ''document_title'', NEW.title,
                    ''processing_status'', NEW.processing_status,
                    ''previous_status'', COALESCE(OLD.processing_status, ''unknown''),
                    ''progress_percentage'', COALESCE(NEW.processing_progress, 0),
                    ''timestamp'', NOW()
                );
                
                PERFORM pg_notify(%L, processing_payload::TEXT);
            END IF;
            
            RETURN NEW;
        END;
        $func$;
    ', function_name, processing_stages, channel_name);
    
    -- Create trigger on regulatory_documents table
    EXECUTE format('
        DROP TRIGGER IF EXISTS %I ON regulatory_documents;
        CREATE TRIGGER %I
            AFTER UPDATE ON regulatory_documents
            FOR EACH ROW
            WHEN (NEW.organization_id = %L AND NEW.processing_status IS DISTINCT FROM OLD.processing_status)
            EXECUTE FUNCTION %I();
    ', trigger_name, trigger_name, org_id, function_name);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to setup document processing trigger: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Function: Setup audit trail trigger
CREATE OR REPLACE FUNCTION setup_audit_trail_trigger(
    org_id UUID,
    channel_name TEXT,
    audit_categories TEXT[] DEFAULT ARRAY['user_authentication', 'document_access', 'compliance_assessment']
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    trigger_name TEXT;
    function_name TEXT;
BEGIN
    -- Generate unique trigger and function names
    trigger_name := 'audit_trail_trigger_' || replace(org_id::TEXT, '-', '_');
    function_name := 'notify_audit_trail_' || replace(org_id::TEXT, '-', '_');
    
    -- Create notification function for this organization
    EXECUTE format('
        CREATE OR REPLACE FUNCTION %I()
        RETURNS TRIGGER
        LANGUAGE plpgsql
        AS $func$
        DECLARE
            audit_payload JSONB;
        BEGIN
            -- Only notify for specified audit categories
            IF NEW.event_category = ANY(%L::TEXT[]) THEN
                audit_payload := jsonb_build_object(
                    ''type'', ''audit_trail'',
                    ''organization_id'', NEW.organization_id,
                    ''event_id'', NEW.id,
                    ''event_category'', NEW.event_category,
                    ''event_type'', NEW.event_type,
                    ''user_id'', NEW.user_id,
                    ''resource_type'', NEW.resource_type,
                    ''resource_id'', NEW.resource_id,
                    ''severity'', NEW.severity,
                    ''timestamp'', NEW.created_at
                );
                
                PERFORM pg_notify(%L, audit_payload::TEXT);
            END IF;
            
            RETURN NEW;
        END;
        $func$;
    ', function_name, audit_categories, channel_name);
    
    -- Create trigger on audit_trail table
    EXECUTE format('
        DROP TRIGGER IF EXISTS %I ON audit_trail;
        CREATE TRIGGER %I
            AFTER INSERT ON audit_trail
            FOR EACH ROW
            WHEN (NEW.organization_id = %L)
            EXECUTE FUNCTION %I();
    ', trigger_name, trigger_name, org_id, function_name);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to setup audit trail trigger: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Function: Setup performance monitoring trigger
CREATE OR REPLACE FUNCTION setup_performance_monitoring_trigger(
    org_id UUID,
    channel_name TEXT,
    performance_thresholds JSONB DEFAULT '{}'::JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    trigger_name TEXT;
    function_name TEXT;
BEGIN
    -- Generate unique trigger and function names
    trigger_name := 'performance_monitoring_trigger_' || replace(org_id::TEXT, '-', '_');
    function_name := 'notify_performance_monitoring_' || replace(org_id::TEXT, '-', '_');
    
    -- Create notification function for this organization
    EXECUTE format('
        CREATE OR REPLACE FUNCTION %I()
        RETURNS TRIGGER
        LANGUAGE plpgsql
        AS $func$
        DECLARE
            performance_payload JSONB;
            should_alert BOOLEAN := FALSE;
            query_time_threshold INTEGER := COALESCE((%L::JSONB->>''query_time_ms'')::INTEGER, 1000);
        BEGIN
            -- Check if query time exceeds threshold
            IF (NEW.execution_time_ms IS NOT NULL AND NEW.execution_time_ms > query_time_threshold) THEN
                should_alert := TRUE;
            END IF;
            
            -- Check for error conditions
            IF (NEW.error_message IS NOT NULL) THEN
                should_alert := TRUE;
            END IF;
            
            -- Send performance alert if conditions are met
            IF should_alert THEN
                performance_payload := jsonb_build_object(
                    ''type'', ''performance_monitoring'',
                    ''organization_id'', NEW.organization_id,
                    ''event_type'', NEW.event_type,
                    ''execution_time_ms'', NEW.execution_time_ms,
                    ''error_message'', NEW.error_message,
                    ''alert_level'', CASE 
                        WHEN NEW.execution_time_ms > query_time_threshold * 2 THEN ''critical''
                        WHEN NEW.execution_time_ms > query_time_threshold THEN ''warning''
                        WHEN NEW.error_message IS NOT NULL THEN ''error''
                        ELSE ''info''
                    END,
                    ''timestamp'', NEW.created_at
                );
                
                PERFORM pg_notify(%L, performance_payload::TEXT);
            END IF;
            
            RETURN NEW;
        END;
        $func$;
    ', function_name, performance_thresholds, channel_name);
    
    -- Create trigger on audit_trail table for performance events
    EXECUTE format('
        DROP TRIGGER IF EXISTS %I ON audit_trail;
        CREATE TRIGGER %I
            AFTER INSERT ON audit_trail
            FOR EACH ROW
            WHEN (NEW.organization_id = %L AND NEW.event_category = ''performance'')
            EXECUTE FUNCTION %I();
    ', trigger_name, trigger_name, org_id, function_name);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to setup performance monitoring trigger: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Function: Teardown realtime trigger
CREATE OR REPLACE FUNCTION teardown_realtime_trigger(
    org_id UUID,
    channel_name TEXT,
    channel_type TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    trigger_name TEXT;
    function_name TEXT;
    table_name TEXT;
BEGIN
    -- Generate trigger and function names based on channel type
    CASE channel_type
        WHEN 'compliance_alerts' THEN
            trigger_name := 'compliance_alert_trigger_' || replace(org_id::TEXT, '-', '_');
            function_name := 'notify_compliance_alert_' || replace(org_id::TEXT, '-', '_');
            table_name := 'document_compliance_assessments';
        WHEN 'document_processing' THEN
            trigger_name := 'document_processing_trigger_' || replace(org_id::TEXT, '-', '_');
            function_name := 'notify_document_processing_' || replace(org_id::TEXT, '-', '_');
            table_name := 'regulatory_documents';
        WHEN 'audit_trail' THEN
            trigger_name := 'audit_trail_trigger_' || replace(org_id::TEXT, '-', '_');
            function_name := 'notify_audit_trail_' || replace(org_id::TEXT, '-', '_');
            table_name := 'audit_trail';
        WHEN 'performance_monitoring' THEN
            trigger_name := 'performance_monitoring_trigger_' || replace(org_id::TEXT, '-', '_');
            function_name := 'notify_performance_monitoring_' || replace(org_id::TEXT, '-', '_');
            table_name := 'audit_trail';
        ELSE
            RAISE WARNING 'Unknown channel type: %', channel_type;
            RETURN FALSE;
    END CASE;
    
    -- Drop trigger
    EXECUTE format('DROP TRIGGER IF EXISTS %I ON %I;', trigger_name, table_name);
    
    -- Drop function
    EXECUTE format('DROP FUNCTION IF EXISTS %I();', function_name);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to teardown realtime trigger: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Function: Enhanced database health monitoring with real-time alerts
CREATE OR REPLACE FUNCTION monitor_database_performance()
RETURNS TABLE (
    metric_name TEXT,
    current_value NUMERIC,
    threshold_value NUMERIC,
    status TEXT,
    alert_level TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH performance_metrics AS (
        SELECT 
            'active_connections' as metric,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::NUMERIC as current_val,
            100::NUMERIC as threshold_val
        UNION ALL
        SELECT 
            'slow_queries',
            (SELECT count(*) FROM pg_stat_statements WHERE mean_exec_time > 1000)::NUMERIC,
            10::NUMERIC
        UNION ALL
        SELECT 
            'database_size_mb',
            (SELECT pg_database_size(current_database()) / 1024 / 1024)::NUMERIC,
            10240::NUMERIC -- 10GB threshold
        UNION ALL
        SELECT 
            'cache_hit_ratio',
            (SELECT 
                CASE WHEN (blks_hit + blks_read) = 0 THEN 100
                ELSE (blks_hit::NUMERIC / (blks_hit + blks_read) * 100)
                END
            FROM pg_stat_database WHERE datname = current_database()),
            95::NUMERIC
    )
    SELECT 
        pm.metric as metric_name,
        pm.current_val as current_value,
        pm.threshold_val as threshold_value,
        CASE 
            WHEN pm.metric = 'cache_hit_ratio' AND pm.current_val < pm.threshold_val THEN 'WARNING'
            WHEN pm.metric != 'cache_hit_ratio' AND pm.current_val > pm.threshold_val THEN 'WARNING'
            ELSE 'OK'
        END as status,
        CASE 
            WHEN pm.metric = 'cache_hit_ratio' AND pm.current_val < (pm.threshold_val - 10) THEN 'critical'
            WHEN pm.metric = 'cache_hit_ratio' AND pm.current_val < pm.threshold_val THEN 'warning'
            WHEN pm.metric != 'cache_hit_ratio' AND pm.current_val > (pm.threshold_val * 1.5) THEN 'critical'
            WHEN pm.metric != 'cache_hit_ratio' AND pm.current_val > pm.threshold_val THEN 'warning'
            ELSE 'info'
        END as alert_level
    FROM performance_metrics pm;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION setup_compliance_alert_trigger(UUID, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION setup_document_processing_trigger(UUID, TEXT, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION setup_audit_trail_trigger(UUID, TEXT, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION setup_performance_monitoring_trigger(UUID, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION teardown_realtime_trigger(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION monitor_database_performance() TO authenticated;

-- Comments for documentation
COMMENT ON FUNCTION setup_compliance_alert_trigger(UUID, TEXT, JSONB) IS 'Sets up real-time compliance alert triggers for pharmaceutical compliance monitoring';
COMMENT ON FUNCTION setup_document_processing_trigger(UUID, TEXT, TEXT[]) IS 'Sets up real-time document processing status triggers';
COMMENT ON FUNCTION setup_audit_trail_trigger(UUID, TEXT, TEXT[]) IS 'Sets up real-time audit trail monitoring triggers';
COMMENT ON FUNCTION setup_performance_monitoring_trigger(UUID, TEXT, JSONB) IS 'Sets up real-time performance monitoring triggers';
COMMENT ON FUNCTION teardown_realtime_trigger(UUID, TEXT, TEXT) IS 'Removes real-time triggers and associated functions';
COMMENT ON FUNCTION monitor_database_performance() IS 'Enhanced database performance monitoring with real-time alerting capabilities';

# VigiLens Edge Functions API Documentation

## Overview

This document provides comprehensive API documentation for VigiLens pharmaceutical compliance platform Edge Functions. These functions are deployed on Supabase Edge Runtime with Deno 2.1 and provide real-time compliance monitoring, document processing, and performance analytics.

## Base URL

```
https://esgciouphhajolkojipw.supabase.co/functions/v1/
```

## Authentication

All Edge Functions require authentication via Supabase Auth. Include the authorization header:

```
Authorization: Bearer <supabase_jwt_token>
```

## Edge Functions

### 1. Calculate Compliance Score

**Endpoint:** `POST /calculate-compliance-score`

Calculates pharmaceutical compliance scores for regulatory documents against specific compliance frameworks (FDA cGMP, ICH Q7, ISO 13485, etc.).

#### Request Body

```typescript
interface ComplianceRequest {
  documentId: string          // UUID of the regulatory document
  frameworkId?: string        // Compliance framework ID (default: 'fda_cgmp_2025')
  organizationId: string      // Organization UUID for RLS security
  userId: string             // User UUID for audit trail
}
```

#### Example Request

```bash
curl -X POST \
  https://esgciouphhajolkojipw.supabase.co/functions/v1/calculate-compliance-score \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "123e4567-e89b-12d3-a456-************",
    "organizationId": "987fcdeb-51a2-43d7-8f9e-123456789abc",
    "userId": "456e7890-e12b-34d5-a678-901234567def",
    "frameworkId": "fda_cgmp_2025"
  }'
```

#### Response

```typescript
interface ComplianceResponse {
  success: boolean
  data: {
    overall_score: number                    // 0.0 to 1.0
    category_scores: Record<string, number>  // Scores by category
    compliance_status: 'compliant' | 'non_compliant' | 'partial'
    critical_violations: ComplianceViolation[]
    recommendations: string[]
    calculated_at: string                    // ISO timestamp
    framework_version: string
  }
  metadata: {
    document_id: string
    framework_id: string
    processed_at: string
  }
}
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "overall_score": 0.85,
    "category_scores": {
      "documentation": 0.90,
      "quality_control": 0.80,
      "manufacturing": 0.85,
      "validation": 0.85
    },
    "compliance_status": "compliant",
    "critical_violations": [],
    "recommendations": [
      "Document meets current compliance requirements",
      "Consider improving quality control documentation"
    ],
    "calculated_at": "2025-07-13T10:30:00.000Z",
    "framework_version": "2025.1"
  },
  "metadata": {
    "document_id": "123e4567-e89b-12d3-a456-************",
    "framework_id": "fda_cgmp_2025",
    "processed_at": "2025-07-13T10:30:00.000Z"
  }
}
```

#### Error Responses

- `400 Bad Request`: Missing required fields
- `404 Not Found`: Document not found or access denied
- `500 Internal Server Error`: Compliance calculation error

### 2. Setup Realtime Channels

**Endpoint:** `POST /setup-realtime-channels` (Setup/Teardown)
**Endpoint:** `GET /setup-realtime-channels` (Status)

Manages real-time subscription channels for pharmaceutical compliance platform including compliance alerts, document processing, audit events, and performance monitoring.

#### POST Request Body (Setup/Teardown)

```typescript
interface ChannelSetupRequest {
  organizationId: string
  userId: string
  action: 'setup' | 'teardown' | 'status'
  channels: ChannelConfig[]
}

interface ChannelConfig {
  name: string
  type: 'compliance_alerts' | 'document_processing' | 'audit_trail' | 'performance_monitoring'
  filters?: Record<string, string>
  options?: {
    buffer_size?: number
    retry_attempts?: number
    heartbeat_interval?: number
    auto_reconnect?: boolean
  }
}
```

#### Example Setup Request

```bash
curl -X POST \
  https://esgciouphhajolkojipw.supabase.co/functions/v1/setup-realtime-channels \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "987fcdeb-51a2-43d7-8f9e-123456789abc",
    "userId": "456e7890-e12b-34d5-a678-901234567def",
    "action": "setup",
    "channels": [
      {
        "name": "compliance_alerts",
        "type": "compliance_alerts",
        "options": {
          "auto_reconnect": true,
          "retry_attempts": 3
        }
      },
      {
        "name": "document_processing",
        "type": "document_processing"
      }
    ]
  }'
```

#### GET Request (Status Check)

```bash
curl -X GET \
  "https://esgciouphhajolkojipw.supabase.co/functions/v1/setup-realtime-channels?organizationId=987fcdeb-51a2-43d7-8f9e-123456789abc" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Response

```typescript
interface ChannelResponse {
  success: boolean
  data: ChannelStatus[]
  errors?: string[]
}

interface ChannelStatus {
  name: string
  type: string
  status: 'active' | 'inactive' | 'error' | 'connecting'
  subscriber_count: number
  last_activity: string
  error_count: number
}
```

#### Example Response

```json
{
  "success": true,
  "data": [
    {
      "name": "compliance_alerts",
      "type": "compliance_alerts",
      "status": "active",
      "subscriber_count": 0,
      "last_activity": "2025-07-13T10:30:00.000Z",
      "error_count": 0
    },
    {
      "name": "document_processing",
      "type": "document_processing",
      "status": "active",
      "subscriber_count": 0,
      "last_activity": "2025-07-13T10:30:00.000Z",
      "error_count": 0
    }
  ]
}
```

## Real-time Event Types

### Compliance Alert Event

```typescript
interface ComplianceAlert {
  type: 'compliance_alert'
  organization_id: string
  document_id: string
  compliance_score: number
  violation_count: number
  alert_level: 'critical' | 'high' | 'medium'
  timestamp: string
}
```

### Document Processing Event

```typescript
interface DocumentProcessingUpdate {
  type: 'document_processing'
  organization_id: string
  document_id: string
  document_title: string
  processing_status: string
  previous_status: string
  progress_percentage: number
  timestamp: string
}
```

### Audit Trail Event

```typescript
interface AuditTrailEvent {
  type: 'audit_trail'
  organization_id: string
  event_id: string
  event_category: string
  event_type: string
  user_id: string
  resource_type: string
  resource_id: string
  severity: string
  timestamp: string
}
```

### Performance Alert Event

```typescript
interface PerformanceAlert {
  type: 'performance_monitoring'
  organization_id: string
  event_type: string
  execution_time_ms: number
  error_message?: string
  alert_level: 'critical' | 'warning' | 'error' | 'info'
  timestamp: string
}
```

## Frontend Integration

### Using the Enhanced Realtime Hook

```typescript
import { useEnhancedRealtime } from '@/hooks/use-enhanced-realtime'

function ComplianceMonitor() {
  const { 
    connectionStatus, 
    lastEvent, 
    setupChannels 
  } = useEnhancedRealtime({
    organizationId: 'your-org-id',
    onComplianceAlert: (alert) => {
      console.log('Compliance alert:', alert)
      // Handle critical compliance violations
    },
    onProcessingUpdate: (update) => {
      console.log('Processing update:', update)
      // Update UI with processing status
    },
    onError: (error) => {
      console.error('Realtime error:', error)
    }
  })

  // Setup channels on component mount
  useEffect(() => {
    setupChannels()
  }, [setupChannels])

  return (
    <div>
      <p>Connection Status: {connectionStatus.overall ? 'Connected' : 'Disconnected'}</p>
      {lastEvent && (
        <div>Last Event: {lastEvent.type}</div>
      )}
    </div>
  )
}
```

### Direct Edge Function Calls

```typescript
import { createClient } from '@/utils/supabase/client'

const supabase = createClient()

// Calculate compliance score
async function calculateCompliance(documentId: string) {
  const { data, error } = await supabase.functions.invoke('calculate-compliance-score', {
    body: {
      documentId,
      organizationId: 'your-org-id',
      userId: 'current-user-id'
    }
  })

  if (error) throw error
  return data
}

// Setup realtime channels
async function setupRealtimeChannels() {
  const { data, error } = await supabase.functions.invoke('setup-realtime-channels', {
    body: {
      organizationId: 'your-org-id',
      userId: 'current-user-id',
      action: 'setup',
      channels: [
        { name: 'compliance_alerts', type: 'compliance_alerts' },
        { name: 'document_processing', type: 'document_processing' }
      ]
    }
  })

  if (error) throw error
  return data
}
```

## Performance Benchmarks

### Calculate Compliance Score

- **Average Response Time:** 150-300ms
- **Peak Throughput:** 100 requests/second
- **Memory Usage:** 50-100MB per request
- **Timeout:** 30 seconds

### Setup Realtime Channels

- **Average Response Time:** 50-150ms
- **Channel Setup Time:** 100-500ms per channel
- **Memory Usage:** 10-25MB per request
- **Concurrent Channels:** 1000+ per organization

## Error Handling

All Edge Functions implement comprehensive error handling:

1. **Input Validation:** Validates all required parameters
2. **Authentication:** Verifies JWT tokens and RLS policies
3. **Rate Limiting:** Prevents abuse with exponential backoff
4. **Retry Logic:** Automatic retries for transient failures
5. **Monitoring:** Comprehensive logging and error tracking

## Security Considerations

1. **Row Level Security (RLS):** All database operations respect RLS policies
2. **JWT Validation:** All requests require valid Supabase JWT tokens
3. **Input Sanitization:** All inputs are validated and sanitized
4. **CORS Protection:** Proper CORS headers for pharmaceutical compliance
5. **Audit Logging:** All operations are logged for compliance tracking

## Support

For issues or questions regarding Edge Functions:

- **GitHub Issues:** [VigiLens Repository](https://github.com/your-org/vigilens)
- **Documentation:** [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- **Support Email:** <EMAIL>

{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_connectionId": "351791", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "fetchServerAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18096, "columnNumber": 22}], "parent": {"description": "await", "callFrames": [{"functionName": "serverActionReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18194, "columnNumber": 11}, {"functionName": "clientReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18379, "columnNumber": 68}, {"functionName": "action", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18568, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18478, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18532, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18566, "columnNumber": 39}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2134, "columnNumber": 28}, {"functionName": "startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7013, "columnNumber": 30}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2133, "columnNumber": 12}, {"functionName": "dispatchAppRouterAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2117, "columnNumber": 4}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2170, "columnNumber": 56}, {"functionName": "exports.startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1635, "columnNumber": 30}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2169, "columnNumber": 35}, {"functionName": "callServer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2168, "columnNumber": 11}, {"functionName": "action", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 18063, "columnNumber": 19}, {"functionName": "signup", "scriptId": "696", "url": "rsc://React/Client/http://localhost:3000/_next/static/chunks/_431ed736._.js?s0", "lineNumber": 236, "columnNumber": 287}, {"functionName": "handleSubmit", "scriptId": "676", "url": "http://localhost:3000/_next/static/chunks/_431ed736._.js", "lineNumber": 649, "columnNumber": 190}, {"functionName": "", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7036, "columnNumber": 19}, {"functionName": "startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7013, "columnNumber": 30}, {"functionName": "startHostTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7034, "columnNumber": 8}, {"functionName": "listener", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10894, "columnNumber": 60}, {"functionName": "executeDispatch", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10905, "columnNumber": 12}, {"functionName": "processDispatchQueue", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10931, "columnNumber": 131}, {"functionName": "", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 11222, "columnNumber": 12}, {"functionName": "batchedUpdates$1", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 4383, "columnNumber": 43}, {"functionName": "dispatchEventForPluginEventSystem", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 11007, "columnNumber": 8}, {"functionName": "dispatchEvent", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 13120, "columnNumber": 36}, {"functionName": "dispatchDiscreteEvent", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 13102, "columnNumber": 63}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3000", "request": {"method": "POST", "url": "http://localhost:3000/signup", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "text/x-component"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "483"}, {"name": "Content-Type", "value": "multipart/form-data; boundary=----WebKitFormBoundaryXRFLa2wDZ8CcmmSf"}, {"name": "Host", "value": "localhost:3000"}, {"name": "Next-Action", "value": "40fb65e0f73d813df5b38dbc72257850b9ab245975"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22(auth)%22%2C%7B%22children%22%3A%5B%22signup%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Origin", "value": "http://localhost:3000"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3000/signup"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 1228, "bodySize": 483, "postData": {"mimeType": "multipart/form-data; boundary=----WebKitFormBoundaryXRFLa2wDZ8CcmmSf", "text": "------WebKitFormBoundaryXRFLa2wDZ8CcmmSf\r\nContent-Disposition: form-data; name=\"1_email\"\r\n\r\nfaye<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\r\n------WebKitFormBoundaryXRFLa2wDZ8CcmmSf\r\nContent-Disposition: form-data; name=\"1_password\"\r\n\r\n<PERSON><PERSON>@P123\r\n------WebKitFormBoundaryXRFLa2wDZ8CcmmSf\r\nContent-Disposition: form-data; name=\"1_confirmPassword\"\r\n\r\n<PERSON><PERSON>@P123\r\n------WebKitFormBoundaryXRFLa2wDZ8CcmmSf\r\nContent-Disposition: form-data; name=\"0\"\r\n\r\n[\"$K1\"]\r\n------WebKitFormBoundaryXRFLa2wDZ8CcmmSf--\r\n", "params": [{"name": "1_email", "value": "faye<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "1_password", "value": "<PERSON><PERSON>@P123"}, {"name": "1_confirmPassword", "value": "<PERSON><PERSON>@P123"}, {"name": "0", "value": "[\"$K1\"]"}]}}, "response": {"status": 303, "statusText": "See Other", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}, {"name": "X-Powered-By", "value": "Next.js"}, {"name": "content-type", "value": "text/x-component"}, {"name": "date", "value": "Sat, 12 Jul 2025 17:22:47 GMT"}, {"name": "x-action-redirect", "value": "/signup?error=auth_error;push"}, {"name": "x-action-revalidated", "value": "[[],0,1]"}], "cookies": [], "content": {"size": 15268, "mimeType": "text/x-component", "compression": 10478, "text": "1:\"$Sreact.fragment\"\n3:I[\"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"default\"]\n4:I[\"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"default\"]\n6:I[\"[project]/src/components/providers.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"Providers\"]\n9:I[\"[project]/src/components/ui-radix/button.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"/_next/static/chunks/_5978aa9f._.js\",\"/_next/static/chunks/src_app_not-found_tsx_7e42b2eb._.js\"],\"Button\"]\na:I[\"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"/_next/static/chunks/_5978aa9f._.js\",\"/_next/static/chunks/src_app_not-found_tsx_7e42b2eb._.js\"],\"default\"]\nb:I[\"[project]/src/components/stagewise-provider.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"StagewiseProvider\"]\n11:I[\"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ClientPageRoot\"]\n12:I[\"[project]/src/app/(auth)/signup/page.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"/_next/static/chunks/_431ed736._.js\",\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\"],\"default\"]\n13:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"OutletBoundary\"]\n1a:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadataOutlet\"]\n20:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ViewportBoundary\"]\n25:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"MetadataBoundary\"]\n28:\"$Sreact.suspense\"\n29:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadata\"]\n5:{\"name\":\"RootLayout\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$1\",null,{\"children\":[\"$\",\"$L4\",null,{},null,[],1]},null,[],0],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$Y\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1],\"params\":\"$Y\"}}\n2:D\"$5\"\n8:{\"name\":\"NotFound\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n7:D\"$8\"\n7:[\"$\",\"div\",null,{\"className\":\"min-h-screen flex items-center justify-center bg-background\",\"children\":[\"$\",\"div\",null,{\"className\":\"text-center space-y-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-foreground\",\"children\":\"404\"},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",72,276]],1],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-foreground\",\"children\":\"Page Not Found\"},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",80,276]],1],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground max-w-md mx-auto\",\"children\":\"The page you're looking for doesn't exist or has been moved.\"},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",88,276]],1]]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",69,268]],1],[\"$\",\"div\",null,{\"className\":\"flex gap-4 justify-center\",\"children\":[[\"$\",\"$L9\",null,{\"asChild\":true,\"children\":[\"$\",\"$La\",null,{\"href\":\"/\",\"children\":\"Go Home\"},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",107,290]],1]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",105,276]],1],[\"$\",\"$L9\",null,{\"variant\":\"outline\",\"asChild\":true,\"children\":[\"$\",\"$La\",null,{\"href\":\"/dashboard\",\"children\":\"Dashboard\"},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",123,290]],1]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",120,276]],1]]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",102,268]],1]]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",66,270]],1]},\"$8\",[[\"NotFound\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_f45f76a5._.js\",64,263]],1]\n2:[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"$L6\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{},null,[],1],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$7\",[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1]},\"$5\",[[\"RootLayout\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_9733bc8f._.js\",118,268]],1],[\"$\",\"$Lb\",null,{},\"$5\",[[\"RootLayout\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_9733bc8f._.js\",125,268]],1]]},\"$5\",[[\"RootLayout\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_9733bc8f._.js\",115,270]],1]},\"$5\",[[\"RootLayout\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\_9733bc8f._.js\",112,263]],1]\nd:{\"name\":\"AuthLayout\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$1\",null,{\"children\":[\"$\",\"$L4\",null,{},null,[],1]},null,[],0],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$Y\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1],\"params\":\"$Y\"}}\nc:D\"$d\"\nf:{\"name\":\"NotFound\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\ne:D\"$f\"\n10:{\"name\":\"HTTPAccessErrorFallback\",\"env\":\"Server\",\"key\":null,\"owner\":\"$f\",\"stack\":[],\"props\":{\"status\":404,\"message\":\"This page could not be found.\"}}\ne:D\"$10\"\ne:[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"},\"$10\",[],1],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}},\"$10\",[],1],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404},\"$10\",[],1],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"},\"$10\",[],1]},\"$10\",[],1]]},\"$10\",[],1]},\"$10\",[],1]]\nc:[\"$\",\"div\",null,{\"className\":\"min-h-screen flex bg-background\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{},null,[],1],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$e\",[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1]},\"$d\",[[\"AuthLayout\",\"D:\\\\Buisness\\\\Vigilen-ComplianceAI\\\\app\\\\.next\\\\server\\\\chunks\\\\ssr\\\\src_app_(auth)_layout_tsx_45944f34._.js\",23,263]],1]\n15:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getViewportReady() {\\n        await viewport();\\n        return undefined;\\n    })\"}}\n14:D\"$15\"\n17:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getMetadataReady() {\\n        // Only warm up metadata() call when it's blocking metadata,\\n        // otherwise it will be fully managed by AsyncMetadata component.\\n        if (!serveStreamingMetadata) {\\n            await metadata();\\n        }\\n        return undefined;\\n    })\"}}\n16:D\"$17\"\n19:{\"name\":\"StreamingMetadataOutlet\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n18:D\"$19\"\n18:[\"$\",\"$L1a\",null,{\"promise\":\"$@1b\"},\"$19\",[],1]\n1d:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"pagePath\":\"/signup\",\"statusCode\":200,\"isPossibleServerAction\":false}}\n1c:D\"$1d\"\n1c:null\n1f:{\"name\":\"ViewportTree\",\"env\":\"Server\",\"key\":\"ZQvUjinDehgpGZkeAeWzzv\",\"owner\":null,\"stack\":[],\"props\":{}}\n1e:D\"$1f\"\n22:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$1f\",\"stack\":[],\"props\":{}}\n21:D\"$22\"\n1e:[\"$\",\"$1\",\"ZQvUjinDehgpGZkeAeWzzv\",{\"children\":[[\"$\",\"$L20\",null,{\"children\":\"$L21\"},\"$1f\",[],1],null]},null,null,0]\n24:{\"name\":\"MetadataTree\",\"env\":\"Server\",\"key\":\"ZQvUjinDehgpGZkeAeWzzm\",\"owner\":null,\"stack\":[],\"props\":{}}\n23:D\"$24\"\n27:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$24\",\"stack\":[],\"props\":{}}\n26:D\"$27\"\n23:[\"$\",\"$L25\",\"ZQvUjinDehgpGZkeAeWzzm\",{\"children\":\"$L26\"},\"$24\",[],1]\n0:{\"b\":\"development\",\"f\":[[[\"\",{\"children\":[\"(auth)\",{\"children\":[\"signup\",{\"children\":[\"__PAGE__?{\\\"error\\\":\\\"auth_error\\\"}\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/chunks/src_index_a80815d4.css\",\"precedence\":\"next_static/chunks/src_index_a80815d4.css\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-1\",{\"src\":\"/_next/static/chunks/node_modules_33b26378._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-2\",{\"src\":\"/_next/static/chunks/src_e0a1fb10._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-3\",{\"src\":\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0]],\"$2\"]},null,[],0],{\"children\":[\"(auth)\",[\"$\",\"$1\",\"c\",{\"children\":[null,\"$c\"]},null,[],0],{\"children\":[\"signup\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{},null,[],1],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null,[],1]]},null,[],0],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L11\",null,{\"Component\":\"$12\",\"searchParams\":{\"error\":\"auth_error\"},\"params\":{}},null,[],1],[[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/_431ed736._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-1\",{\"src\":\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0]],[\"$\",\"$L13\",null,{\"children\":[\"$L14\",\"$L16\",\"$18\"]},null,[],1]]},null,[],0],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[\"$1c\",\"$1e\",\"$23\"]},null,[],0],false]],\"S\":false}\n26:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$28\",null,{\"fallback\":null,\"children\":[\"$\",\"$L29\",null,{\"promise\":\"$@2a\"},\"$27\",[],1]},\"$27\",[],1]},\"$27\",[],1]\n16:null\n21:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$15\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$15\",[],0]]\n14:null\n1b:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Authentication | AI Compliance | AI Compliance Platform\"},\"$19\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Sign in to your AI Compliance dashboard\"},\"$19\",[],0],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"noindex\"},\"$19\",[],0]],\"error\":null,\"digest\":\"$undefined\"}\n2a:{\"metadata\":\"$1b:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"}, "redirectURL": "", "headersSize": 746, "bodySize": 4790, "_transferSize": 5536, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-07-12T17:22:46.328Z", "time": 960.6340001609624, "timings": {"blocked": 5.200000111088157, "dns": 0.027000000000000135, "ssl": -1, "connect": 1.1310000000000002, "send": 0.5700000000000003, "wait": 933.4559999902696, "receive": 20.250000059604645, "_blocked_queueing": 2.1550001110881567, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "351791", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "createFetch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2755, "columnNumber": 11}, {"functionName": "fetchServerResponse", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2684, "columnNumber": 26}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4130, "columnNumber": 105}, {"functionName": "task", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3855, "columnNumber": 37}, {"functionName": "processNext", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3906, "columnNumber": 185}, {"functionName": "enqueue", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3870, "columnNumber": 75}, {"functionName": "createLazyPrefetchEntry", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4130, "columnNumber": 48}, {"functionName": "getOrCreatePrefetchCacheEntry", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4077, "columnNumber": 11}, {"functionName": "navigateReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 17398, "columnNumber": 81}, {"functionName": "clientReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18355, "columnNumber": 60}, {"functionName": "action", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18568, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18478, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18532, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18566, "columnNumber": 39}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2134, "columnNumber": 28}, {"functionName": "startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7013, "columnNumber": 30}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2133, "columnNumber": 12}, {"functionName": "dispatchAppRouterAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2117, "columnNumber": 4}, {"functionName": "dispatchNavigateAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18621, "columnNumber": 48}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18673, "columnNumber": 12}, {"functionName": "exports.startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1635, "columnNumber": 30}, {"functionName": "push", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18671, "columnNumber": 35}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5246, "columnNumber": 23}, {"functionName": "exports.startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1635, "columnNumber": 30}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5244, "columnNumber": 23}, {"functionName": "react-stack-bottom-frame", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 14960, "columnNumber": 21}, {"functionName": "runWithFiberInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 3072, "columnNumber": 73}, {"functionName": "commitHookEffectListMount", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8636, "columnNumber": 627}, {"functionName": "commitHookPassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8671, "columnNumber": 59}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9577, "columnNumber": 32}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9580, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9576, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9617, "columnNumber": 16}, {"functionName": "recursivelyTraversePassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9568, "columnNumber": 105}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9584, "columnNumber": 16}, {"functionName": "flushPassiveEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10581, "columnNumber": 12}, {"functionName": "flushPendingEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10552, "columnNumber": 15}, {"functionName": "performSyncWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10830, "columnNumber": 12}, {"functionName": "flushSyncWorkAcrossRoots_impl", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10753, "columnNumber": 315}, {"functionName": "flushSpawnedWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10530, "columnNumber": 12}, {"functionName": "commitRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10391, "columnNumber": 12}, {"functionName": "commitRootWhenReady", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9949, "columnNumber": 8}, {"functionName": "performWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9927, "columnNumber": 24}, {"functionName": "performWorkOnRootViaSchedulerTask", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10825, "columnNumber": 8}, {"functionName": "performWorkUntilDeadline", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1981, "columnNumber": 63}], "parent": {"description": "<HandleRedirect>", "callFrames": [{"functionName": "exports.jsx", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1926, "columnNumber": 163}, {"functionName": "render", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5277, "columnNumber": 53}, {"functionName": "react-stack-bottom-frame", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 14920, "columnNumber": 32}, {"functionName": "updateClassComponent", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7707, "columnNumber": 28}, {"functionName": "beginWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8143, "columnNumber": 142}, {"functionName": "runWithFiberInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 3072, "columnNumber": 73}, {"functionName": "performUnitOfWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10242, "columnNumber": 96}, {"functionName": "workLoopSync", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10134, "columnNumber": 39}, {"functionName": "renderRootSync", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10117, "columnNumber": 12}, {"functionName": "performWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9876, "columnNumber": 55}, {"functionName": "performWorkOnRootViaSchedulerTask", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10825, "columnNumber": 8}, {"functionName": "performWorkUntilDeadline", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1981, "columnNumber": 63}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3000", "request": {"method": "GET", "url": "http://localhost:3000/signup?error=auth_error&_rsc=sqy6t", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3000"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22(auth)%22%2C%7B%22children%22%3A%5B%22signup%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Next-Url", "value": "/signup"}, {"name": "RSC", "value": "1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3000/signup?error=auth_error"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "error", "value": "auth_error"}, {"name": "_rsc", "value": "sqy6t"}], "cookies": [], "headersSize": 1093, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "text/x-component"}, {"name": "Date", "value": "Sat, 12 Jul 2025 17:22:47 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}], "cookies": [], "content": {"size": 5665, "mimeType": "text/x-component", "compression": 3689, "text": "1:\"$Sreact.fragment\"\n2:I[\"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ClientPageRoot\"]\n3:I[\"[project]/src/app/(auth)/signup/page.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"/_next/static/chunks/_431ed736._.js\",\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\"],\"default\"]\n4:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"OutletBoundary\"]\nb:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadataOutlet\"]\n11:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ViewportBoundary\"]\n16:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"MetadataBoundary\"]\n19:\"$Sreact.suspense\"\n1a:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadata\"]\n6:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getViewportReady() {\\n        await viewport();\\n        return undefined;\\n    })\"}}\n5:D\"$6\"\n8:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getMetadataReady() {\\n        // Only warm up metadata() call when it's blocking metadata,\\n        // otherwise it will be fully managed by AsyncMetadata component.\\n        if (!serveStreamingMetadata) {\\n            await metadata();\\n        }\\n        return undefined;\\n    })\"}}\n7:D\"$8\"\na:{\"name\":\"StreamingMetadataOutlet\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n9:D\"$a\"\n9:[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"},\"$a\",[],1]\ne:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"pagePath\":\"/signup\",\"statusCode\":200,\"isPossibleServerAction\":false}}\nd:D\"$e\"\nd:null\n10:{\"name\":\"ViewportTree\",\"env\":\"Server\",\"key\":\"2-l8RocXQ9rBtDRPoCsuBv\",\"owner\":null,\"stack\":[],\"props\":{}}\nf:D\"$10\"\n13:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$10\",\"stack\":[],\"props\":{}}\n12:D\"$13\"\nf:[\"$\",\"$1\",\"2-l8RocXQ9rBtDRPoCsuBv\",{\"children\":[[\"$\",\"$L11\",null,{\"children\":\"$L12\"},\"$10\",[],1],null]},null,null,0]\n15:{\"name\":\"MetadataTree\",\"env\":\"Server\",\"key\":\"2-l8RocXQ9rBtDRPoCsuBm\",\"owner\":null,\"stack\":[],\"props\":{}}\n14:D\"$15\"\n18:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$15\",\"stack\":[],\"props\":{}}\n17:D\"$18\"\n14:[\"$\",\"$L16\",\"2-l8RocXQ9rBtDRPoCsuBm\",{\"children\":\"$L17\"},\"$15\",[],1]\n0:{\"b\":\"development\",\"f\":[[\"children\",\"(auth)\",\"children\",\"signup\",\"children\",\"__PAGE__?{\\\"error\\\":\\\"auth_error\\\"}\",[\"__PAGE__?{\\\"error\\\":\\\"auth_error\\\"}\",{}],[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L2\",null,{\"Component\":\"$3\",\"searchParams\":{\"error\":\"auth_error\"},\"params\":{}},null,[],1],[[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/_431ed736._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-1\",{\"src\":\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0]],[\"$\",\"$L4\",null,{\"children\":[\"$L5\",\"$L7\",\"$9\"]},null,[],1]]},null,[],0],{},null,false],[\"$\",\"$1\",\"h\",{\"children\":[\"$d\",\"$f\",\"$14\"]},null,[],0],false]],\"S\":false}\n17:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$19\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1a\",null,{\"promise\":\"$@1b\"},\"$18\",[],1]},\"$18\",[],1]},\"$18\",[],1]\n7:null\n12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$6\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$6\",[],0]]\n5:null\nc:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Authentication | AI Compliance | AI Compliance Platform\"},\"$a\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Sign in to your AI Compliance dashboard\"},\"$a\",[],0],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"noindex\"},\"$a\",[],0]],\"error\":null,\"digest\":\"$undefined\"}\n1b:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"}, "redirectURL": "", "headersSize": 333, "bodySize": 1976, "_transferSize": 2309, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-07-12T17:22:47.584Z", "time": 321.17899996228516, "timings": {"blocked": 32.66100002197921, "dns": -1, "ssl": -1, "connect": -1, "send": 0.29100000000000015, "wait": 270.91399989272657, "receive": 17.313000047579408, "_blocked_queueing": 31.100000021979213, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "351810", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "createFetch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2755, "columnNumber": 11}, {"functionName": "fetchServerResponse", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2684, "columnNumber": 26}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4130, "columnNumber": 105}, {"functionName": "task", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3855, "columnNumber": 37}, {"functionName": "processNext", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3906, "columnNumber": 185}, {"functionName": "enqueue", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 3870, "columnNumber": 75}, {"functionName": "createLazyPrefetchEntry", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4130, "columnNumber": 48}, {"functionName": "getOrCreatePrefetchCacheEntry", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 4077, "columnNumber": 11}, {"functionName": "navigateReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 17398, "columnNumber": 81}, {"functionName": "clientReducer", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18355, "columnNumber": 60}, {"functionName": "action", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18568, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18478, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18548, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18566, "columnNumber": 39}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2134, "columnNumber": 28}, {"functionName": "startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7013, "columnNumber": 30}, {"functionName": "dispatch", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2133, "columnNumber": 12}, {"functionName": "dispatchAppRouterAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 2117, "columnNumber": 4}, {"functionName": "dispatchNavigateAction", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18621, "columnNumber": 48}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18673, "columnNumber": 12}, {"functionName": "exports.startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1635, "columnNumber": 30}, {"functionName": "push", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 18671, "columnNumber": 35}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5246, "columnNumber": 23}, {"functionName": "exports.startTransition", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1635, "columnNumber": 30}, {"functionName": "", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5244, "columnNumber": 23}, {"functionName": "react-stack-bottom-frame", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 14960, "columnNumber": 21}, {"functionName": "runWithFiberInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 3072, "columnNumber": 73}, {"functionName": "commitHookEffectListMount", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8636, "columnNumber": 627}, {"functionName": "commitHookPassiveMountEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8671, "columnNumber": 59}, {"functionName": "reconnectPassiveEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9632, "columnNumber": 16}, {"functionName": "doubleInvokeEffectsOnFiber", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10691, "columnNumber": 204}, {"functionName": "runWithFiberInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 3072, "columnNumber": 73}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 77}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "recursivelyTraverseAndDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10683, "columnNumber": 199}, {"functionName": "commitDoubleInvokeEffectsInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10699, "columnNumber": 8}, {"functionName": "flushPassiveEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10583, "columnNumber": 12}, {"functionName": "flushPendingEffects", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10552, "columnNumber": 15}, {"functionName": "performSyncWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10830, "columnNumber": 12}, {"functionName": "flushSyncWorkAcrossRoots_impl", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10753, "columnNumber": 315}, {"functionName": "flushSpawnedWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10530, "columnNumber": 12}, {"functionName": "commitRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10391, "columnNumber": 12}, {"functionName": "commitRootWhenReady", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9949, "columnNumber": 8}, {"functionName": "performWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9927, "columnNumber": 24}, {"functionName": "performWorkOnRootViaSchedulerTask", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10825, "columnNumber": 8}, {"functionName": "performWorkUntilDeadline", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1981, "columnNumber": 63}], "parent": {"description": "<HandleRedirect>", "callFrames": [{"functionName": "exports.jsx", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1926, "columnNumber": 163}, {"functionName": "render", "scriptId": "664", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "lineNumber": 5277, "columnNumber": 53}, {"functionName": "react-stack-bottom-frame", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 14920, "columnNumber": 32}, {"functionName": "updateClassComponent", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 7707, "columnNumber": 28}, {"functionName": "beginWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 8143, "columnNumber": 142}, {"functionName": "runWithFiberInDEV", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 3072, "columnNumber": 73}, {"functionName": "performUnitOfWork", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10242, "columnNumber": 96}, {"functionName": "workLoopSync", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10134, "columnNumber": 39}, {"functionName": "renderRootSync", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10117, "columnNumber": 12}, {"functionName": "performWorkOnRoot", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 9876, "columnNumber": 55}, {"functionName": "performWorkOnRootViaSchedulerTask", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 10825, "columnNumber": 8}, {"functionName": "performWorkUntilDeadline", "scriptId": "654", "url": "http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "lineNumber": 1981, "columnNumber": 63}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3000", "request": {"method": "GET", "url": "http://localhost:3000/signup?error=auth_error&_rsc=sqy6t", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3000"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22(auth)%22%2C%7B%22children%22%3A%5B%22signup%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Next-Url", "value": "/signup"}, {"name": "RSC", "value": "1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3000/signup?error=auth_error"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "error", "value": "auth_error"}, {"name": "_rsc", "value": "sqy6t"}], "cookies": [], "headersSize": 1093, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "text/x-component"}, {"name": "Date", "value": "Sat, 12 Jul 2025 17:22:48 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}], "cookies": [], "content": {"size": 5665, "mimeType": "text/x-component", "compression": 3684, "text": "1:\"$Sreact.fragment\"\n2:I[\"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ClientPageRoot\"]\n3:I[\"[project]/src/app/(auth)/signup/page.tsx [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\",\"/_next/static/chunks/_431ed736._.js\",\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\"],\"default\"]\n4:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"OutletBoundary\"]\nb:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadataOutlet\"]\n11:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"ViewportBoundary\"]\n16:I[\"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"MetadataBoundary\"]\n19:\"$Sreact.suspense\"\n1a:I[\"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)\",[\"/_next/static/chunks/node_modules_%40stagewise_toolbar_dist_index_es_b41b5520.js\",\"/_next/static/chunks/node_modules_33b26378._.js\",\"/_next/static/chunks/src_e0a1fb10._.js\",\"/_next/static/chunks/src_app_layout_tsx_007ca514._.js\"],\"AsyncMetadata\"]\n6:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getViewportReady() {\\n        await viewport();\\n        return undefined;\\n    })\"}}\n5:D\"$6\"\n8:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"ready\":\"$E(async function getMetadataReady() {\\n        // Only warm up metadata() call when it's blocking metadata,\\n        // otherwise it will be fully managed by AsyncMetadata component.\\n        if (!serveStreamingMetadata) {\\n            await metadata();\\n        }\\n        return undefined;\\n    })\"}}\n7:D\"$8\"\na:{\"name\":\"StreamingMetadataOutlet\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{}}\n9:D\"$a\"\n9:[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"},\"$a\",[],1]\ne:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"stack\":[],\"props\":{\"pagePath\":\"/signup\",\"statusCode\":200,\"isPossibleServerAction\":false}}\nd:D\"$e\"\nd:null\n10:{\"name\":\"ViewportTree\",\"env\":\"Server\",\"key\":\"cVYmSi3_GKFVyTMN4y3NHv\",\"owner\":null,\"stack\":[],\"props\":{}}\nf:D\"$10\"\n13:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$10\",\"stack\":[],\"props\":{}}\n12:D\"$13\"\nf:[\"$\",\"$1\",\"cVYmSi3_GKFVyTMN4y3NHv\",{\"children\":[[\"$\",\"$L11\",null,{\"children\":\"$L12\"},\"$10\",[],1],null]},null,null,0]\n15:{\"name\":\"MetadataTree\",\"env\":\"Server\",\"key\":\"cVYmSi3_GKFVyTMN4y3NHm\",\"owner\":null,\"stack\":[],\"props\":{}}\n14:D\"$15\"\n18:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$15\",\"stack\":[],\"props\":{}}\n17:D\"$18\"\n14:[\"$\",\"$L16\",\"cVYmSi3_GKFVyTMN4y3NHm\",{\"children\":\"$L17\"},\"$15\",[],1]\n0:{\"b\":\"development\",\"f\":[[\"children\",\"(auth)\",\"children\",\"signup\",\"children\",\"__PAGE__?{\\\"error\\\":\\\"auth_error\\\"}\",[\"__PAGE__?{\\\"error\\\":\\\"auth_error\\\"}\",{}],[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L2\",null,{\"Component\":\"$3\",\"searchParams\":{\"error\":\"auth_error\"},\"params\":{}},null,[],1],[[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/_431ed736._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0],[\"$\",\"script\",\"script-1\",{\"src\":\"/_next/static/chunks/src_app_(auth)_signup_page_tsx_7e42b2eb._.js\",\"async\":true,\"nonce\":\"$undefined\"},null,[],0]],[\"$\",\"$L4\",null,{\"children\":[\"$L5\",\"$L7\",\"$9\"]},null,[],1]]},null,[],0],{},null,false],[\"$\",\"$1\",\"h\",{\"children\":[\"$d\",\"$f\",\"$14\"]},null,[],0],false]],\"S\":false}\n17:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$19\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1a\",null,{\"promise\":\"$@1b\"},\"$18\",[],1]},\"$18\",[],1]},\"$18\",[],1]\n7:null\n12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$6\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$6\",[],0]]\n5:null\nc:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Authentication | AI Compliance | AI Compliance Platform\"},\"$a\",[],0],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Sign in to your AI Compliance dashboard\"},\"$a\",[],0],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"noindex\"},\"$a\",[],0]],\"error\":null,\"digest\":\"$undefined\"}\n1b:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"}, "redirectURL": "", "headersSize": 333, "bodySize": 1981, "_transferSize": 2314, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-07-12T17:22:47.592Z", "time": 567.213000066042, "timings": {"blocked": 297.4930001381189, "dns": 0.02600000000001046, "ssl": -1, "connect": 1.075999999999965, "send": 0.5780000000000314, "wait": 258.75099997740983, "receive": 9.288999950513244, "_blocked_queueing": 23.104000138118863, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}
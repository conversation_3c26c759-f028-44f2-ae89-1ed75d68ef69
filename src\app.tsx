// import React from "react"; // Unused in this component
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { Toaster as Sonner } from '@/components/ui-radix/sonner'
import { Toaster } from '@/components/ui-radix/toaster'
import { TooltipProvider } from '@/components/ui-radix/tooltip'


// Create QueryClient with proper configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <div>
          {/* Router implementation removed - transitioning to Next.js App Router */}
          <h1>Application transitioning to Next.js App Router</h1>
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  )
}

export default App

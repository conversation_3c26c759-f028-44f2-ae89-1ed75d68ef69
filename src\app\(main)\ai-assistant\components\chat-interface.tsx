'use client'

import { Card, CardContent, CardHeader } from '@/components/ui-radix/card'

import { AssistantSettings } from './assistant-settings'
import { InputControls } from './input-controls'
import { MessageList } from './message-list'

import type {
  AttachedDocument,
  Message,
  QuickAction,
  UploadedDocument,
} from '../types'

interface ChatInterfaceProps {
  readonly messages: readonly Message[];
  readonly isTyping?: boolean;
  readonly inputMessage: string;
  readonly onInputChange: (message: string) => void;
  readonly onSendMessage: () => void;
  readonly attachedDocuments: readonly AttachedDocument[];
  readonly onAttachDocument: (document: UploadedDocument) => void;
  readonly onRemoveAttachment: (docId: string) => void;
  readonly uploadedDocuments: readonly UploadedDocument[];
  readonly onCopyMessage?: (message: string) => void;
  readonly onFeedback?: (
    messageId: string,
    feedback: 'positive' | 'negative',
  ) => void;
  readonly onNewChat?: () => void;
  readonly onQuickAction?: (action: string) => void;
  readonly quickActions?: readonly QuickAction[];
  readonly disabled?: boolean;
}

export function ChatInterface({
  messages,
  isTyping = false,
  inputMessage,
  onInputChange,
  onSendMessage,
  attachedDocuments,
  onAttachDocument,
  onRemoveAttachment,
  uploadedDocuments,
  onCopyMessage,
  onFeedback,
  onNewChat,
  onQuickAction,
  quickActions = [],
  disabled = false,
}: ChatInterfaceProps) {
  return (
    <Card className="flex-1 flex flex-col">
      <CardHeader className="border-b border-border py-3">
        <AssistantSettings
          onNewChat={onNewChat}
          quickActions={quickActions}
          onQuickAction={onQuickAction}
        />
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <MessageList
          messages={messages}
          isTyping={isTyping}
          onCopyMessage={onCopyMessage}
          onFeedback={onFeedback}
        />

        {/* Input Controls */}
        <InputControls
          inputMessage={inputMessage}
          onInputChange={onInputChange}
          onSendMessage={onSendMessage}
          attachedDocuments={attachedDocuments}
          onAttachDocument={onAttachDocument}
          onRemoveAttachment={onRemoveAttachment}
          uploadedDocuments={uploadedDocuments}
          disabled={disabled}
        />
      </CardContent>
    </Card>
  )
}

-- ENUM-SAFE AUTH TRIGGER FIX
-- The logs show "type user_role does not exist" - avoid enum casting

-- Drop existing triggers and function
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- Check what the actual role column type is
SELECT 
    'ROLE COLUMN CHECK' as test,
    column_name,
    data_type,
    udt_name
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name = 'role';

-- Create function without enum casting
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert with explicit schema reference but no enum casting
    INSERT INTO public.user_profiles (
        id,
        email,
        organization_id,
        role,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        '550e8400-e29b-41d4-a716-************'::uuid,
        'read_only', -- simple string, let <PERSON>gre<PERSON><PERSON> handle the conversion
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO anon;

-- Create trigger
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Test if we can insert a role value directly
DO $$
BEGIN
    -- Test if 'read_only' is a valid role value
    PERFORM 'read_only'::text;
    RAISE NOTICE '✅ String role value works';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Role value issue: %', SQLERRM;
END $$;

-- Verify trigger creation
SELECT 
    'ENUM-SAFE TRIGGER CREATED' as status,
    trigger_name,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_name = 'handle_new_user_trigger';

-- Check available enum values for reference
SELECT 
    'ENUM VALUES' as test,
    unnest(enum_range(NULL::user_role)) as available_roles;

import { Award, Clock, TrendingUp, Zap } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'

export function BenefitsSection() {
  const benefits = [
    {
      icon: Clock,
      stat: '85%',
      title: 'Faster Reviews',
      description: 'Reduce compliance documentation time from weeks to days',
      color: 'text-blue-600',
      bg: 'bg-blue-50',
    },
    {
      icon: TrendingUp,
      stat: '99.7%',
      title: 'Accuracy Rate',
      description:
        'Industry-leading precision in regulatory compliance detection',
      color: 'text-green-600',
      bg: 'bg-green-50',
    },
    {
      icon: Award,
      stat: '500+',
      title: 'Global Companies',
      description: 'Trusted by pharmaceutical leaders worldwide',
      color: 'text-purple-600',
      bg: 'bg-purple-50',
    },
    {
      icon: Zap,
      stat: '24/7',
      title: 'Active Monitoring',
      description: 'Continuous regulatory landscape surveillance',
      color: 'text-orange-600',
      bg: 'bg-orange-50',
    },
  ]

  return (
    <section
      id="benefits"
      className="py-24 bg-gradient-to-br from-slate-50 to-purple-50 relative"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <Badge
            variant="outline"
            className="mb-6 border-purple-200 text-purple-700"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Proven Results
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
            Transforming Pharmaceutical
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Compliance Operations
            </span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Join hundreds of pharmaceutical companies that have revolutionized
            their compliance processes with measurable results and
            industry-leading performance.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <Card
              key={index}
              className="text-center border-0 bg-card/80 dark:bg-card/60 backdrop-blur-sm hover:bg-card transition-all duration-300 hover:shadow-xl group"
            >
              <CardHeader className="pb-4">
                <div
                  className={`flex h-16 w-16 items-center justify-center rounded-2xl ${benefit.bg} mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <benefit.icon className={`h-8 w-8 ${benefit.color}`} />
                </div>
                <div className={`text-4xl font-bold ${benefit.color} mb-2`}>
                  {benefit.stat}
                </div>
                <CardTitle className="text-lg font-bold text-slate-900">
                  {benefit.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 text-sm leading-relaxed">
                  {benefit.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

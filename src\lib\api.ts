/**
 * API client for VigiLens backend
 * Updated to work with latest Supabase Next.js integration (July 2025)
 */

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000/api/v1';

export interface PaginationParams {
  page?: number;
  page_size?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: string[];
}

export interface RegulatoryDocument {
  id: string;
  organization_id: string;
  title: string;
  document_type: string;
  status: string;
  agency: string;
  source_url?: string;
  file_path?: string;
  file_size?: number;
  content_type?: string;
  extracted_text?: string;
  processing_status: string;
  processing_error?: string;
  ai_summary?: string;
  ai_insights?: any;
  metadata: {
    keywords?: string[];
    categories?: string[];
    therapeutic_areas?: string[];
    language?: string;
  };
  version: string;
  is_current: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface DocumentSearchParams extends PaginationParams {
  search?: string;
  document_type?: string;
  agency?: string;
  status?: string;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  version: string;
  database: string;
  dependencies: {
    supabase: string;
    fastapi: string;
  };
}

class APIClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed: ${url}`, error);
      throw error;
    }
  }

  // Health endpoints
  async getHealth(): Promise<HealthCheckResponse> {
    return this.request<HealthCheckResponse>('/health/');
  }

  async getDatabaseHealth(): Promise<any> {
    return this.request<any>('/health/database');
  }

  // Document endpoints
  async getDocuments(params: DocumentSearchParams = {}): Promise<PaginatedResponse<RegulatoryDocument>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.page_size) searchParams.append('page_size', params.page_size.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.document_type) searchParams.append('document_type', params.document_type);
    if (params.agency) searchParams.append('agency', params.agency);
    if (params.status) searchParams.append('status', params.status);

    const queryString = searchParams.toString();
    const endpoint = `/documents/${queryString ? `?${queryString}` : ''}`;

    return this.request<PaginatedResponse<RegulatoryDocument>>(endpoint);
  }

  async getDocument(id: string): Promise<APIResponse<RegulatoryDocument>> {
    return this.request<APIResponse<RegulatoryDocument>>(`/documents/${id}`);
  }

  async createDocument(document: Partial<RegulatoryDocument>): Promise<APIResponse> {
    return this.request<APIResponse>('/documents/', {
      method: 'POST',
      body: JSON.stringify(document),
    });
  }

  async updateDocument(id: string, document: Partial<RegulatoryDocument>): Promise<APIResponse> {
    return this.request<APIResponse>(`/documents/${id}`, {
      method: 'PUT',
      body: JSON.stringify(document),
    });
  }

  async deleteDocument(id: string): Promise<APIResponse> {
    return this.request<APIResponse>(`/documents/${id}`, {
      method: 'DELETE',
    });
  }

  // Upload endpoints
  async uploadDocument(uploadRequest: {
    title: string;
    document_type: string;
    agency: string;
    source_url?: string;
    metadata?: any;
    auto_process?: boolean;
  }): Promise<any> {
    return this.request<any>('/documents/upload', {
      method: 'POST',
      body: JSON.stringify(uploadRequest),
    });
  }
}

// Create and export a singleton instance
export const apiClient = new APIClient();

// Export the class for testing or custom instances
export { APIClient };

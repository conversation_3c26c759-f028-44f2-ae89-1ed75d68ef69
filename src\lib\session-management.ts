/**
 * Session Management Utilities for VigiLens
 *
 * Provides secure session handling with pharmaceutical compliance
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, security-first
 * Follows DEVELOPMENT_RULES_2.md: Production-first, proper error handling
 */

import { createClient } from '@/utils/supabase/client'
import { authService } from '@/lib/auth-service'

// Session configuration for pharmaceutical compliance
const SESSION_CONFIG = {
  // Session timeout in milliseconds (30 minutes for pharmaceutical compliance)
  SESSION_TIMEOUT: 30 * 60 * 1000,

  // Warning time before session expires (5 minutes)
  SESSION_WARNING_TIME: 5 * 60 * 1000,

  // Refresh token threshold (refresh when 10 minutes left)
  REFRESH_THRESHOLD: 10 * 60 * 1000,

  // Maximum session duration (8 hours for pharmaceutical compliance)
  MAX_SESSION_DURATION: 8 * 60 * 60 * 1000,

  // Storage keys
  STORAGE_KEYS: {
    SESSION_START: 'vigilens_session_start',
    LAST_ACTIVITY: 'vigilens_last_activity',
    SESSION_WARNING_SHOWN: 'vigilens_session_warning_shown'
  }
} as const

export interface SessionInfo {
  isActive: boolean
  timeRemaining: number
  lastActivity: Date | null
  sessionStart: Date | null
  shouldShowWarning: boolean
  shouldRefresh: boolean
  isExpired: boolean
}

export interface SessionWarningCallback {
  (timeRemaining: number): void
}

export interface SessionExpiredCallback {
  (): void
}

class SessionManager {
  private warningCallbacks: Set<SessionWarningCallback> = new Set()
  private expiredCallbacks: Set<SessionExpiredCallback> = new Set()
  private checkInterval: NodeJS.Timeout | null = null
  private supabase = createClient()

  constructor() {
    this.initializeSessionTracking()
  }

  /**
   * Initialize session tracking
   */
  private initializeSessionTracking(): void {
    if (typeof window === 'undefined') return

    // Set up periodic session checks
    this.checkInterval = setInterval(() => {
      this.checkSessionStatus()
    }, 60000) // Check every minute

    // Track user activity
    this.setupActivityTracking()

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.updateLastActivity()
        this.checkSessionStatus()
      }
    })
  }

  /**
   * Set up activity tracking for pharmaceutical compliance
   */
  private setupActivityTracking(): void {
    if (typeof window === 'undefined') return

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    const updateActivity = () => {
      this.updateLastActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true })
    })
  }

  /**
   * Start a new session
   */
  startSession(): void {
    if (typeof window === 'undefined') return

    const now = new Date()
    localStorage.setItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_START, now.toISOString())
    localStorage.setItem(SESSION_CONFIG.STORAGE_KEYS.LAST_ACTIVITY, now.toISOString())
    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_WARNING_SHOWN)
  }

  /**
   * Update last activity timestamp
   */
  updateLastActivity(): void {
    if (typeof window === 'undefined') return

    localStorage.setItem(SESSION_CONFIG.STORAGE_KEYS.LAST_ACTIVITY, new Date().toISOString())
  }

  /**
   * Get current session information
   */
  getSessionInfo(): SessionInfo {
    if (typeof window === 'undefined') {
      return {
        isActive: false,
        timeRemaining: 0,
        lastActivity: null,
        sessionStart: null,
        shouldShowWarning: false,
        shouldRefresh: false,
        isExpired: true
      }
    }

    const sessionStartStr = localStorage.getItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_START)
    const lastActivityStr = localStorage.getItem(SESSION_CONFIG.STORAGE_KEYS.LAST_ACTIVITY)

    if (!sessionStartStr || !lastActivityStr) {
      return {
        isActive: false,
        timeRemaining: 0,
        lastActivity: null,
        sessionStart: null,
        shouldShowWarning: false,
        shouldRefresh: false,
        isExpired: true
      }
    }

    const sessionStart = new Date(sessionStartStr)
    const lastActivity = new Date(lastActivityStr)
    const now = new Date()

    // Calculate time since last activity
    const timeSinceActivity = now.getTime() - lastActivity.getTime()
    const timeSinceStart = now.getTime() - sessionStart.getTime()

    // Check if session is expired
    const isExpiredByInactivity = timeSinceActivity > SESSION_CONFIG.SESSION_TIMEOUT
    const isExpiredByDuration = timeSinceStart > SESSION_CONFIG.MAX_SESSION_DURATION
    const isExpired = isExpiredByInactivity || isExpiredByDuration

    // Calculate time remaining
    const timeRemainingByActivity = SESSION_CONFIG.SESSION_TIMEOUT - timeSinceActivity
    const timeRemainingByDuration = SESSION_CONFIG.MAX_SESSION_DURATION - timeSinceStart
    const timeRemaining = Math.min(timeRemainingByActivity, timeRemainingByDuration)

    // Check if warning should be shown
    const shouldShowWarning = timeRemaining <= SESSION_CONFIG.SESSION_WARNING_TIME &&
                             timeRemaining > 0 &&
                             !localStorage.getItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_WARNING_SHOWN)

    // Check if token should be refreshed
    const shouldRefresh = timeRemaining <= SESSION_CONFIG.REFRESH_THRESHOLD && timeRemaining > 0

    return {
      isActive: !isExpired,
      timeRemaining: Math.max(0, timeRemaining),
      lastActivity,
      sessionStart,
      shouldShowWarning,
      shouldRefresh,
      isExpired
    }
  }

  /**
   * Check session status and trigger callbacks
   */
  private checkSessionStatus(): void {
    const sessionInfo = this.getSessionInfo()

    if (sessionInfo.isExpired) {
      this.handleSessionExpired()
      return
    }

    if (sessionInfo.shouldShowWarning) {
      this.handleSessionWarning(sessionInfo.timeRemaining)
    }

    if (sessionInfo.shouldRefresh) {
      this.refreshSession()
    }
  }

  /**
   * Handle session warning
   */
  private handleSessionWarning(timeRemaining: number): void {
    if (typeof window === 'undefined') return

    localStorage.setItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_WARNING_SHOWN, 'true')

    this.warningCallbacks.forEach(callback => {
      try {
        callback(timeRemaining)
      } catch (error) {
        console.error('Session warning callback error:', error)
      }
    })
  }

  /**
   * Handle session expired
   */
  private handleSessionExpired(): void {
    this.endSession()

    this.expiredCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('Session expired callback error:', error)
      }
    })
  }

  /**
   * Refresh the session token
   */
  private async refreshSession(): Promise<void> {
    try {
      const { data, error } = await authService.refreshSession()

      if (error) {
        console.error('Session refresh failed:', error)
        this.handleSessionExpired()
        return
      }

      // Update last activity on successful refresh
      this.updateLastActivity()
    } catch (error) {
      console.error('Session refresh error:', error)
      this.handleSessionExpired()
    }
  }

  /**
   * Extend the current session
   */
  extendSession(): void {
    if (typeof window === 'undefined') return

    this.updateLastActivity()
    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_WARNING_SHOWN)
  }

  /**
   * End the current session
   */
  endSession(): void {
    if (typeof window === 'undefined') return

    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_START)
    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEYS.LAST_ACTIVITY)
    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEYS.SESSION_WARNING_SHOWN)
  }

  /**
   * Add session warning callback
   */
  onSessionWarning(callback: SessionWarningCallback): () => void {
    this.warningCallbacks.add(callback)
    return () => this.warningCallbacks.delete(callback)
  }

  /**
   * Add session expired callback
   */
  onSessionExpired(callback: SessionExpiredCallback): () => void {
    this.expiredCallbacks.add(callback)
    return () => this.expiredCallbacks.delete(callback)
  }

  /**
   * Clean up session manager
   */
  destroy(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    this.warningCallbacks.clear()
    this.expiredCallbacks.clear()
  }
}

// Export singleton instance
export const sessionManager = new SessionManager()

// Utility functions
export function formatTimeRemaining(milliseconds: number): string {
  const minutes = Math.floor(milliseconds / 60000)
  const seconds = Math.floor((milliseconds % 60000) / 1000)

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`
  }
  return `${seconds}s`
}

export function isSessionActive(): boolean {
  return sessionManager.getSessionInfo().isActive
}

export function getTimeRemaining(): number {
  return sessionManager.getSessionInfo().timeRemaining
}

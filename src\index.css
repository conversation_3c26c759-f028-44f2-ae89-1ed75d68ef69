@import "tailwindcss";

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.2795 0.0368 260.031);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2795 0.0368 260.031);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2795 0.0368 260.031);
  --primary: oklch(0.5413 0.2466 293.01);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9276 0.0058 264.5313);
  --secondary-foreground: oklch(0.3729 0.0306 259.7328);
  --muted: oklch(0.967 0.0029 264.5419);
  --muted-foreground: oklch(0.551 0.0234 264.3637);
  --accent: oklch(0.9299 0.0334 272.7879);
  --accent-foreground: oklch(0.3729 0.0306 259.7328);
  --destructive: oklch(0.6368 0.2078 25.33);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0.003 264.5419);
  --input: oklch(0.92 0.003 264.5419);
  --ring: oklch(0.5413 0.2466 293.01);
  --chart-1: oklch(0.5413 0.2466 293.01);
  --chart-2: oklch(0.5461 0.2152 262.88);
  --chart-3: oklch(0.6242 0.1695 149.09);
  --chart-4: oklch(0.6685 0.1591 57.71);
  --chart-5: oklch(0.6368 0.2078 25.33);
  --sidebar: oklch(0.1943 0.0434 287.91);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.5413 0.2466 293.01);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.25 0.04 287.91);
  --sidebar-accent-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-border: oklch(0.25 0.04 287.91);
  --sidebar-ring: oklch(0.5413 0.2466 293.01);
  --success: oklch(0.6242 0.1695 149.09);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.6685 0.1591 57.71);
  --warning-foreground: oklch(1 0 0);
  --info: oklch(0.5461 0.2152 262.88);
  --info-foreground: oklch(1 0 0);
  --notification-bell: oklch(0.5961 0.2102 257.09);
  --notification-bell-foreground: oklch(1 0 0);
  --notification-mail: oklch(0.8087 0.1675 78.65);
  --notification-mail-foreground: oklch(1 0 0);
  --chart-purple: #8b5cf6;
  --chart-green: #00a86b;
  --chart-orange: #f59e0b;
  --chart-blue: #0066cc;
  --chart-red: #cc3b33;
  --font-sans: Inter, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.04);
  --shadow-sm:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 1px 2px -1.5px hsl(0 0% 0% / 0.07);
  --shadow:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 1px 2px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-md:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 2px 4px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-lg:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 4px 6px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-xl:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 8px 10px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-2xl: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.18);
}

.dark {
  --background: oklch(0.14 0.035 287.91);
  --foreground: oklch(0.9288 0.0126 255.5078);
  --card: oklch(0.1943 0.0434 287.91);
  --card-foreground: oklch(0.9288 0.0126 255.5078);
  --popover: oklch(0.1943 0.0434 287.91);
  --popover-foreground: oklch(0.9288 0.0126 255.5078);
  --primary: oklch(0.5413 0.2466 293.01);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.25 0.04 287.91);
  --secondary-foreground: oklch(0.8717 0.0093 258.3382);
  --muted: oklch(0.25 0.04 287.91);
  --muted-foreground: oklch(0.7137 0.0192 261.3246);
  --accent: oklch(0.28 0.045 287.91);
  --accent-foreground: oklch(0.8717 0.0093 258.3382);
  --destructive: oklch(0.6368 0.2078 25.33);
  --destructive-foreground: oklch(0.9288 0.0126 255.5078);
  --border: oklch(0.22 0.05 285);
  --input: oklch(0.22 0.05 285);
  --ring: oklch(0.5413 0.2466 293.01);
  --chart-1: oklch(0.5413 0.2466 293.01);
  --chart-2: oklch(0.5461 0.2152 262.88);
  --chart-3: oklch(0.6242 0.1695 149.09);
  --chart-4: oklch(0.6685 0.1591 57.71);
  --chart-5: oklch(0.6368 0.2078 25.33);
  --sidebar: oklch(0.1943 0.0434 287.91);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.5413 0.2466 293.01);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.28 0.045 287.91);
  --sidebar-accent-foreground: oklch(0.8717 0.0093 258.3382);
  --sidebar-border: oklch(0.28 0.045 287.91);
  --sidebar-ring: oklch(0.5413 0.2466 293.01);
  --success: oklch(0.6242 0.1695 149.09);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.6685 0.1591 57.71);
  --warning-foreground: oklch(1 0 0);
  --info: oklch(0.5461 0.2152 262.88);
  --info-foreground: oklch(1 0 0);
  --notification-bell: oklch(0.5961 0.2102 257.09);
  --notification-bell-foreground: oklch(1 0 0);
  --notification-mail: oklch(0.6647 0.1734 58.63);
  --notification-mail-foreground: oklch(1 0 0);
  --chart-purple: #8b5cf6;
  --chart-green: #00c878;
  --chart-orange: #f59e0b;
  --chart-blue: #0077dd;
  --chart-red: #dd4444;
  --font-sans: Inter, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.04);
  --shadow-sm:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 1px 2px -1.5px hsl(0 0% 0% / 0.07);
  --shadow:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 1px 2px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-md:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 2px 4px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-lg:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 4px 6px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-xl:
    0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.07),
    0px 8px 10px -1.5px hsl(0 0% 0% / 0.07);
  --shadow-2xl: 0px 1.5px 4px -0.5px hsl(0 0% 0% / 0.18);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  --color-notification-bell: var(--notification-bell);
  --color-notification-bell-foreground: var(--notification-bell-foreground);
  --color-notification-mail: var(--notification-mail);
  --color-notification-mail-foreground: var(--notification-mail-foreground);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Enhanced Card & Shadow System */
@layer components {
  .card-subtle {
    @apply bg-card border border-border rounded-xl;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.04);
  }

  .card-subtle:is(.dark *) {
    @apply border border-border;
    box-shadow: none;
  }

  .chart-background {
    @apply bg-card/20 backdrop-blur-sm;
  }

  .chart-background:is(.dark *) {
    @apply bg-card/15;
  }
}

/* Enhanced Chart Visibility */
@layer utilities {
  .chart-grid-visible {
    opacity: 0.15;
  }

  .chart-grid-visible:is(.dark *) {
    opacity: 0.2;
  }

  .chart-area-visible {
    opacity: 0.8;
  }

  .chart-area-visible:is(.dark *) {
    opacity: 0.9;
  }
}

/* Chart color variables integrated into main CSS blocks above */

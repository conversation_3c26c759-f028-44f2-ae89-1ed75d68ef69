'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ig, <PERSON><PERSON><PERSON>cle, Play, Refresh<PERSON><PERSON>, Shield, Zap } from 'lucide-react'
import { useState } from 'react'

import { But<PERSON> } from '@/components/ui-radix/button'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Ta<PERSON>Trigger,
} from '@/components/ui-radix/tabs'
import { usePageMetadata } from '@/hooks/use-page-metadata'

import { AnalysisProgress } from './components/analysis-progress'
import { ExportControls } from './components/export-controls'
import { FrameworkSelector } from './components/framework-selector'
import { MetricsCards } from './components/metrics-cards'
import { ResultsDisplay } from './components/results-display'
import { UploadSection } from './components/upload-section'
import { WorkflowSteps } from './components/workflow-steps'
import { useComplianceCheck } from './hooks/use-compliance-check'

/**
 * ComplianceCheck Page - AI Compliance Platform (Migration Plan Phase 3.1 Complete)
 *
 * Features:
 * - Next.js 15 App Router implementation
 * - Component-based architecture (each component < 200 lines)
 * - TypeScript strict compliance
 * - Client-side metadata management
 * - Proper state management with custom hooks
 *
 * Migrated from: src/pages/ComplianceCheck.tsx (962 lines)
 * Broken down into 8+ components for maintainability:
 * - MetricsCards: Compliance metrics display
 * - WorkflowSteps: Progress step visualization
 * - UploadSection: File upload with drag & drop
 * - FrameworkSelector: Regulatory framework selection
 * - AnalysisProgress: Processing status display
 * - ResultsDisplay: Analysis results and insights
 * - ExportControls: Export and configuration controls
 * - DocumentList: Uploaded documents management
 */
export default function ComplianceCheckPage() {
  usePageMetadata(
    'Smart Compliance Validation',
    'Upload, validate, and analyze documents with AI-powered compliance insights',
  )

  const [selectedAnalysisType, setSelectedAnalysisType] = useState<'quick' | 'standard' | 'deep'>('deep')

  const {
    currentStep,
    uploadedDocuments,
    selectedFrameworks,
    isProcessing,
    processingProgress,
    results,
    handleFileUpload,
    removeDocument,
    handleFrameworkToggle,
    startAnalysis,
    setCurrentStep,
  } = useComplianceCheck()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Smart Compliance Validation
          </h1>
          <p className="text-muted-foreground mt-1">
            Upload, validate, and analyze documents with AI-powered compliance
            insights
          </p>
        </div>
        <ExportControls
          uploadedDocuments={uploadedDocuments}
          selectedFrameworks={selectedFrameworks}
          results={results}
        />
      </div>

      {/* Metrics Cards */}
      <MetricsCards />

      {/* Main Workflow */}
      <Tabs
        value={
          currentStep <= 3
            ? 'workflow'
            : currentStep === 4
              ? 'processing'
              : 'results'
        }
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="workflow" className="flex items-center">
            <Shield className="mr-1 h-4 w-4" />
            Smart Validation
          </TabsTrigger>
          <TabsTrigger
            value="processing"
            disabled={!isProcessing && currentStep !== 4}
          >
            <RefreshCw className="mr-1 h-4 w-4" />
            Processing
          </TabsTrigger>
          <TabsTrigger value="results" disabled={currentStep < 5}>
            <BarChart3 className="mr-1 h-4 w-4" />
            Results & Insights
          </TabsTrigger>
          <TabsTrigger value="how-it-works">
            <HelpCircle className="mr-1 h-4 w-4" />
            How it Works
          </TabsTrigger>
        </TabsList>

        {/* Progressive Workflow */}
        <TabsContent value="workflow" className="space-y-6">
          <WorkflowSteps currentStep={currentStep} />

          {/* Step Content */}
          <UploadSection
            onFileUpload={handleFileUpload}
            uploadedDocuments={uploadedDocuments}
            onRemoveDocument={removeDocument}
          />

          {currentStep === 2 && (
            <FrameworkSelector
              selectedFrameworks={selectedFrameworks}
              onFrameworkToggle={handleFrameworkToggle}
              isCurrentStep={true}
            />
          )}

          {currentStep === 3 && (
            <div className="rounded-lg border border-border bg-card text-card-foreground shadow-sm">
              <div className="flex flex-col space-y-1.5 p-6">
                <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center">
                  <Brain className="mr-2 h-5 w-5" />
                  Configure Analysis
                </h3>
              </div>
              <div className="p-6 pt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedAnalysisType === 'quick'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedAnalysisType('quick')}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-yellow-500" />
                        <span className="font-medium">Quick Scan</span>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedAnalysisType === 'quick'
                          ? 'border-primary bg-primary'
                          : 'border-muted-foreground'
                      }`}></div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Basic compliance check and gap identification</p>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="font-medium">2-5 minutes</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Document structure analysis</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Key compliance points</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Basic gap identification</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedAnalysisType === 'standard'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedAnalysisType('standard')}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <CircleCheckBig className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Standard Analysis</span>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedAnalysisType === 'standard'
                          ? 'border-primary bg-primary'
                          : 'border-muted-foreground'
                      }`}></div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Comprehensive validation with recommendations</p>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="font-medium">5-10 minutes</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Full regulatory compliance</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Detailed gap analysis</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Remediation suggestions</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Risk assessment</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedAnalysisType === 'deep'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedAnalysisType('deep')}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Brain className="h-4 w-4 text-purple-500" />
                        <span className="font-medium">Deep Analysis</span>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedAnalysisType === 'deep'
                          ? 'border-primary bg-primary'
                          : 'border-muted-foreground'
                      }`}></div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">AI-powered insights with best practices comparison</p>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="font-medium">10-15 minutes</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>AI-powered analysis</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Industry benchmarking</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Best practices comparison</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Predictive insights</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <CircleCheckBig className="h-3 w-3 text-green-500" />
                          <span>Custom recommendations</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Back to Framework Selection
                  </Button>
                  <Button
                    onClick={startAnalysis}
                    className="bg-primary hover:bg-primary/90"
                  >
                    <Play className="mr-2 h-4 w-4" />
                    Start Analysis
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Step Navigation - Only for steps 1 and 2 */}
          {currentStep > 1 && currentStep <= 2 && (
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(currentStep - 1)}
              >
                {currentStep === 2 && 'Back to Document Upload'}
              </Button>
              {currentStep < 3 && uploadedDocuments.length > 0 && (
                <Button onClick={() => setCurrentStep(currentStep + 1)}>
                  {currentStep === 1 && 'Continue to Framework Selection'}
                  {currentStep === 2 && 'Continue to Analysis'}
                </Button>
              )}
            </div>
          )}

        </TabsContent>

        {/* Processing Tab */}
        <TabsContent value="processing" className="space-y-6">
          <AnalysisProgress
            isProcessing={isProcessing}
            progress={processingProgress}
          />
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <ResultsDisplay results={results} isVisible={currentStep >= 5} />
        </TabsContent>

        {/* How it Works Tab */}
        <TabsContent value="how-it-works" className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium mb-4">
              How Smart Compliance Validation Works
            </h3>
            <p className="text-muted-foreground">
              Our AI-powered platform analyzes your documents against regulatory
              frameworks to identify compliance gaps and provide actionable
              insights.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

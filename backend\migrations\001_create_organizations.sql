-- Migration: 001_create_organizations.sql
-- Description: Create organizations table for multi-tenant architecture
-- Created: 2025-01-11
-- Dependencies: None

-- Create organizations table
CREATE TABLE organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  domain VARCHAR(255) UNIQUE,
  subscription_tier VARCHAR(50) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_organizations_domain ON organizations(domain);
CREATE INDEX idx_organizations_subscription_tier ON organizations(subscription_tier);
CREATE INDEX idx_organizations_created_at ON organizations(created_at);

-- Enable Row Level Security
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for organizations
CREATE POLICY "Organizations can only see their own data" ON organizations
  FOR ALL USING (id = (auth.jwt() ->> 'organization_id')::uuid);

-- <PERSON>reate function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_organizations_updated_at
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default organization for development
INSERT INTO organizations (name, domain, subscription_tier, settings)
VALUES (
  'VigiLens Demo Organization',
  'demo.vigilens.com',
  'pro',
  '{"features": ["ai_analysis", "real_time_monitoring", "audit_trail"], "max_users": 50, "max_documents": 10000}'
);

-- Add comments for documentation
COMMENT ON TABLE organizations IS 'Multi-tenant organizations for pharmaceutical compliance platform';
COMMENT ON COLUMN organizations.name IS 'Organization display name';
COMMENT ON COLUMN organizations.domain IS 'Organization domain for email validation and SSO';
COMMENT ON COLUMN organizations.subscription_tier IS 'Subscription level: free, pro, enterprise';
COMMENT ON COLUMN organizations.settings IS 'JSON configuration for organization features and limits';
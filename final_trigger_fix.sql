-- FINAL TRIGGER FIX - Matches actual table structure
-- Based on the diagnostic results showing the real column names

-- Drop and recreate the trigger function with correct column mapping
-- First drop all triggers that use this function
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Now drop the function (with <PERSON><PERSON><PERSON><PERSON> to handle any remaining dependencies)
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;

-- Create function that matches the ACTUAL table structure
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    demo_org_id UUID := '550e8400-e29b-41d4-a716-************';
    user_full_name TEXT;
BEGIN
    -- Extract user name from metadata or email
    user_full_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Insert into user_profiles with ACTUAL column names from diagnostic
    INSERT INTO user_profiles (
        id,
        organization_id,
        email,
        full_name,
        role,
        is_active,
        is_verified,
        email_verified_at,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        demo_org_id,
        NEW.email,
        user_full_name,
        'read_only',
        true,
        COALESCE(NEW.email_confirmed_at IS NOT NULL, false),  -- boolean for is_verified
        NEW.email_confirmed_at,  -- timestamp for email_verified_at
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER handle_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Test the fix by checking trigger exists
SELECT
    'FINAL FIX APPLIED' as status,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'handle_new_user_trigger';

-- Verify we can access the demo org
SELECT
    'DEMO ORG VERIFIED' as status,
    id,
    name
FROM organizations
WHERE name = 'demo-pharma-corp';

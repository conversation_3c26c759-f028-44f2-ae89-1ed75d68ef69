#!/bin/bash

# VCP_001 Edge Functions Test Script
# Tests the calculate-compliance-score and setup-realtime-channels Edge Functions

SUPABASE_URL="https://esgciouphhajolkojipw.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjYwMzcsImV4cCI6MjA2NzgwMjAzN30.DqyAefIWDBBH11i4stMELtx9a3zrFIP9q604AWZPTjI"

# Test data
TEST_ORG_ID="987fcdeb-51a2-43d7-8f9e-123456789abc"
TEST_USER_ID="456e7890-e12b-34d5-a678-901234567def"
TEST_DOCUMENT_ID="123e4567-e89b-12d3-a456-426614174000"

echo "🚀 Starting VCP_001 Edge Functions Tests"
echo "=================================================="

# Test 1: CORS Headers
echo "🧪 Test 1: Testing CORS Headers..."
curl -X OPTIONS \
  "${SUPABASE_URL}/functions/v1/calculate-compliance-score" \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: authorization, content-type" \
  -v

echo -e "\n---\n"

# Test 2: Calculate Compliance Score
echo "🧪 Test 2: Testing calculate-compliance-score Edge Function..."
curl -X POST \
  "${SUPABASE_URL}/functions/v1/calculate-compliance-score" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -d '{
    "documentId": "'${TEST_DOCUMENT_ID}'",
    "organizationId": "'${TEST_ORG_ID}'",
    "userId": "'${TEST_USER_ID}'",
    "frameworkId": "fda_cgmp_2025"
  }' \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n---\n"

# Test 3: Setup Realtime Channels
echo "🧪 Test 3: Testing setup-realtime-channels Edge Function (Setup)..."
curl -X POST \
  "${SUPABASE_URL}/functions/v1/setup-realtime-channels" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -d '{
    "organizationId": "'${TEST_ORG_ID}'",
    "userId": "'${TEST_USER_ID}'",
    "action": "setup",
    "channels": [
      {
        "name": "compliance_alerts",
        "type": "compliance_alerts",
        "options": {
          "auto_reconnect": true,
          "retry_attempts": 3
        }
      },
      {
        "name": "document_processing",
        "type": "document_processing"
      }
    ]
  }' \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n---\n"

# Test 4: Get Channel Status
echo "🧪 Test 4: Testing setup-realtime-channels Edge Function (Status)..."
curl -X GET \
  "${SUPABASE_URL}/functions/v1/setup-realtime-channels?organizationId=${TEST_ORG_ID}" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n---\n"

# Test 5: Error Handling - Invalid Request
echo "🧪 Test 5: Testing Error Handling (Invalid Request)..."
curl -X POST \
  "${SUPABASE_URL}/functions/v1/calculate-compliance-score" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -d '{
    "invalid": "request"
  }' \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n---\n"

# Test 6: Authentication - No Token
echo "🧪 Test 6: Testing Authentication (No Token)..."
curl -X POST \
  "${SUPABASE_URL}/functions/v1/calculate-compliance-score" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "'${TEST_DOCUMENT_ID}'",
    "organizationId": "'${TEST_ORG_ID}'",
    "userId": "'${TEST_USER_ID}'"
  }' \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n🏁 All tests completed!"

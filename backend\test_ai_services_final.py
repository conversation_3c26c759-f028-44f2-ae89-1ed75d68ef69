#!/usr/bin/env python3
"""
Final Comprehensive AI Services Test Suite

Validates all AI service implementations after applying the 6-Expert Protocol fixes:
- All dependencies installed and working
- Import structure fixed (no circular imports)
- Pydantic v2 compatibility
- Type safety and validation
- Basic functionality tests
- Architecture compliance
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

class FinalAIServicesTestSuite:
    """Final comprehensive test suite for AI services."""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    async def run_all_tests(self):
        """Run all tests and report final results."""
        print("🧪 FINAL AI SERVICES VALIDATION SUITE")
        print("=" * 60)
        print("Following 6-Expert Protocol Implementation")
        print("Stack: BGE-M3 + Qdrant + OpenRouter + CrewAI + LangGraph")
        print("=" * 60)
        
        # Test 1: Import Tests
        await self.test_imports()
        
        # Test 2: Instantiation Tests
        await self.test_instantiation()
        
        # Test 3: Pydantic Validation Tests
        await self.test_pydantic_validation()
        
        # Test 4: Architecture Compliance Tests
        await self.test_architecture_compliance()
        
        # Test 5: Dependency Resolution Tests
        await self.test_dependency_resolution()
        
        # Print final summary
        self.print_final_summary()
        
    async def test_imports(self):
        """Test all service imports."""
        print("\n📦 TESTING IMPORTS")
        print("-" * 30)
        
        services = [
            ("BGE-M3 Embeddings", "services.ai.bge_m3_embeddings", "BGEM3EmbeddingsService"),
            ("Qdrant Vector Store", "services.ai.qdrant_store", "QdrantVectorStore"),
            ("OpenRouter AI Client", "services.ai.client", "OpenRouterClient"),
            ("RAG Pipeline", "services.ai.rag_pipeline", "PharmaceuticalRAGPipeline"),
            ("CrewAI Agents", "services.ai.crewai_agents", "PharmaceuticalCrew"),
            ("LangGraph Workflow", "services.ai.langgraph_workflow", "PharmaceuticalWorkflow")
        ]
        
        for name, module_name, class_name in services:
            self.total_tests += 1
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"✅ {name}: Import successful")
                self.passed_tests += 1
            except Exception as e:
                print(f"❌ {name}: Import failed - {e}")
    
    async def test_instantiation(self):
        """Test basic service instantiation."""
        print("\n🏗️ TESTING INSTANTIATION")
        print("-" * 30)
        
        # Test BGE-M3 Embeddings
        self.total_tests += 1
        try:
            from services.ai.bge_m3_embeddings import BGEM3EmbeddingsService, EmbeddingRequest
            service = BGEM3EmbeddingsService()
            request = EmbeddingRequest(texts=["test document"])
            print("✅ BGE-M3 Embeddings: Instantiation successful")
            self.passed_tests += 1
        except Exception as e:
            print(f"❌ BGE-M3 Embeddings: Instantiation failed - {e}")
        
        # Test Qdrant Store
        self.total_tests += 1
        try:
            from services.ai.qdrant_store import QdrantVectorStore, VectorSearchRequest, DocumentMetadata
            store = QdrantVectorStore()
            request = VectorSearchRequest(query_vector=[0.1] * 1024)
            metadata = DocumentMetadata(source="test", title="test doc", created_at="2025-07-21")
            print("✅ Qdrant Store: Instantiation successful")
            self.passed_tests += 1
        except Exception as e:
            print(f"❌ Qdrant Store: Instantiation failed - {e}")
        
        # Test AI Client
        self.total_tests += 1
        try:
            from services.ai.client import OpenRouterClient, ChatRequest, ChatMessage
            client = OpenRouterClient(api_key="test_key")
            message = ChatMessage(role="user", content="test message")
            request = ChatRequest(messages=[message])
            print("✅ AI Client: Instantiation successful")
            self.passed_tests += 1
        except Exception as e:
            print(f"❌ AI Client: Instantiation failed - {e}")
    
    async def test_pydantic_validation(self):
        """Test Pydantic v2 validation."""
        print("\n🔍 TESTING PYDANTIC V2 VALIDATION")
        print("-" * 40)
        
        # Test validation catches errors
        validation_tests = [
            ("Empty texts validation", lambda: self._test_empty_texts()),
            ("Wrong vector dimensions", lambda: self._test_wrong_dimensions()),
            ("Invalid message role", lambda: self._test_invalid_role()),
            ("String field validation", lambda: self._test_string_validation())
        ]
        
        for test_name, test_func in validation_tests:
            self.total_tests += 1
            try:
                test_func()
                print(f"❌ {test_name}: Validation should have failed")
            except (ValueError, Exception):
                print(f"✅ {test_name}: Validation working correctly")
                self.passed_tests += 1
    
    def _test_empty_texts(self):
        from services.ai.bge_m3_embeddings import EmbeddingRequest
        EmbeddingRequest(texts=[])  # Should fail
    
    def _test_wrong_dimensions(self):
        from services.ai.qdrant_store import VectorSearchRequest
        VectorSearchRequest(query_vector=[0.1] * 512)  # Should fail - wrong dimension
    
    def _test_invalid_role(self):
        from services.ai.client import ChatMessage
        ChatMessage(role="invalid", content="test")  # Should fail
    
    def _test_string_validation(self):
        from services.ai.qdrant_store import DocumentMetadata
        DocumentMetadata(source="", title="test", created_at="2025-07-21")  # Should fail - empty source
    
    async def test_architecture_compliance(self):
        """Test architecture compliance."""
        print("\n🏛️ TESTING ARCHITECTURE COMPLIANCE")
        print("-" * 40)
        
        # Test no circular imports
        self.total_tests += 1
        try:
            # Import all services simultaneously
            from services.ai import bge_m3_embeddings, qdrant_store, client, rag_pipeline, crewai_agents, langgraph_workflow
            print("✅ No circular imports detected")
            self.passed_tests += 1
        except Exception as e:
            print(f"❌ Circular import detected: {e}")
        
        # Test singleton pattern availability
        self.total_tests += 1
        try:
            from services.ai.bge_m3_embeddings import get_embeddings_service
            from services.ai.qdrant_store import get_vector_store
            from services.ai.client import get_ai_client
            print("✅ Singleton patterns implemented correctly")
            self.passed_tests += 1
        except Exception as e:
            print(f"❌ Singleton pattern issue: {e}")
    
    async def test_dependency_resolution(self):
        """Test dependency resolution."""
        print("\n🔗 TESTING DEPENDENCY RESOLUTION")
        print("-" * 40)
        
        dependencies = [
            ("sentence-transformers", "sentence_transformers"),
            ("qdrant-client", "qdrant_client"),
            ("httpx", "httpx"),
            ("pydantic", "pydantic"),
            ("numpy", "numpy"),
            ("torch", "torch")
        ]
        
        for dep_name, import_name in dependencies:
            self.total_tests += 1
            try:
                __import__(import_name)
                print(f"✅ {dep_name}: Available")
                self.passed_tests += 1
            except ImportError:
                print(f"❌ {dep_name}: Missing")
    
    def print_final_summary(self):
        """Print final test summary."""
        print("\n" + "=" * 60)
        print("🎯 FINAL VALIDATION SUMMARY")
        print("=" * 60)
        
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print(f"📊 Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.total_tests - self.passed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 95:
            print("\n🎉 EXCELLENT! AI Services stack is ready for production!")
            print("✨ All 6-Expert Protocol requirements satisfied:")
            print("   • Dependencies installed and compatible")
            print("   • No circular imports")
            print("   • Pydantic v2 validation working")
            print("   • Type safety implemented")
            print("   • Architecture patterns correct")
            print("   • Error handling comprehensive")
        elif success_rate >= 80:
            print("\n⚠️ GOOD! Minor issues detected, but stack is functional.")
        else:
            print("\n🚨 ISSUES DETECTED! Please review failed tests.")
        
        print(f"\n🔧 Stack Configuration:")
        print(f"   • Embeddings: BGE-M3 via sentence-transformers")
        print(f"   • Vector DB: Qdrant Local (embedded)")
        print(f"   • AI Client: OpenRouter + MoonshotAI Kimi K2")
        print(f"   • Framework: LangGraph + CrewAI Hybrid")
        print(f"   • Backend: FastAPI + Supabase")
        print(f"   • Validation: Pydantic v2 with field_validator")

async def main():
    """Run the final test suite."""
    test_suite = FinalAIServicesTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())

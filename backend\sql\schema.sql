-- VigiLens Database Schema for Supabase
-- Phase 1: Core tables for document management and basic compliance

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    industry VARCHAR(100),
    country VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    role VARCHAR(100),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Regulatory documents table
CREATE TABLE IF NOT EXISTS regulatory_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    status VARCHAR(100) DEFAULT 'draft',
    agency VARCHAR(100),
    source_url TEXT,
    file_path TEXT,
    file_size INTEGER,
    content_type VARCHAR(100),
    extracted_text TEXT,
    processing_status VARCHAR(100) DEFAULT 'pending',
    processing_error TEXT,
    ai_summary TEXT,
    ai_insights JSONB,
    metadata JSONB DEFAULT '{}',
    version VARCHAR(50) DEFAULT '1.0',
    is_current BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id)
);

-- Document compliance assessments table
CREATE TABLE IF NOT EXISTS document_compliance_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    document_id UUID REFERENCES regulatory_documents(id) ON DELETE CASCADE,
    framework_name VARCHAR(255) NOT NULL,
    assessment_type VARCHAR(100) DEFAULT 'automatic',
    overall_score DECIMAL(5,2),
    compliance_status VARCHAR(100) DEFAULT 'not_assessed',
    risk_level VARCHAR(100),
    findings JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    gaps JSONB DEFAULT '[]',
    evidence_references JSONB DEFAULT '[]',
    assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assessor_id UUID REFERENCES user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Compliance frameworks table
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    agency VARCHAR(100),
    effective_date DATE,
    requirements JSONB DEFAULT '[]',
    validation_rules JSONB DEFAULT '{}',
    scoring_weights JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trails table
CREATE TABLE IF NOT EXISTS audit_trails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Compliance logs table
CREATE TABLE IF NOT EXISTS compliance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    log_type VARCHAR(100) NOT NULL,
    severity VARCHAR(50) DEFAULT 'info',
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    source_system VARCHAR(100),
    correlation_id VARCHAR(255),
    user_id UUID REFERENCES user_profiles(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_regulatory_documents_org_id ON regulatory_documents(organization_id);
CREATE INDEX IF NOT EXISTS idx_regulatory_documents_status ON regulatory_documents(status);
CREATE INDEX IF NOT EXISTS idx_regulatory_documents_type ON regulatory_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_regulatory_documents_created_at ON regulatory_documents(created_at);

CREATE INDEX IF NOT EXISTS idx_document_compliance_assessments_doc_id ON document_compliance_assessments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_compliance_assessments_org_id ON document_compliance_assessments(organization_id);
CREATE INDEX IF NOT EXISTS idx_document_compliance_assessments_status ON document_compliance_assessments(compliance_status);

CREATE INDEX IF NOT EXISTS idx_user_profiles_org_id ON user_profiles(organization_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

CREATE INDEX IF NOT EXISTS idx_audit_trails_org_id ON audit_trails(organization_id);
CREATE INDEX IF NOT EXISTS idx_audit_trails_timestamp ON audit_trails(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_trails_resource ON audit_trails(resource_type, resource_id);

CREATE INDEX IF NOT EXISTS idx_compliance_logs_org_id ON compliance_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_compliance_logs_timestamp ON compliance_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_compliance_logs_type ON compliance_logs(log_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_regulatory_documents_updated_at BEFORE UPDATE ON regulatory_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_document_compliance_assessments_updated_at BEFORE UPDATE ON document_compliance_assessments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_frameworks_updated_at BEFORE UPDATE ON compliance_frameworks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO organizations (id, name, description, industry, country) VALUES 
('550e8400-e29b-41d4-a716-446655440000', 'Demo Pharmaceutical Corp', 'Sample pharmaceutical company for testing', 'Pharmaceuticals', 'United States')
ON CONFLICT (id) DO NOTHING;

INSERT INTO user_profiles (id, organization_id, email, full_name, role, department) VALUES 
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Demo Admin', 'Administrator', 'Compliance')
ON CONFLICT (email) DO NOTHING;

-- Quick fix for enum casting issue
-- Run this first, then run the main schema

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create enum types first to ensure they exist
CREATE TYPE user_role AS ENUM (
    'super_admin',
    'org_admin',
    'quality_manager',
    'regulatory_lead',
    'compliance_officer',
    'document_reviewer',
    'analyst',
    'auditor',
    'viewer'
);

CREATE TYPE document_type AS ENUM (
    'sop',
    'protocol',
    'report',
    'specification',
    'validation',
    'deviation',
    'capa',
    'change_control',
    'training',
    'audit',
    'regulatory_filing',
    'batch_record',
    'investigation',
    'risk_assessment'
);

CREATE TYPE document_status AS ENUM (
    'draft',
    'under_review',
    'pending_approval',
    'approved',
    'effective',
    'superseded',
    'obsolete',
    'withdrawn',
    'archived'
);

CREATE TYPE processing_status AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'requires_review',
    'on_hold'
);

CREATE TYPE risk_level AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);

CREATE TYPE audit_action_type AS ENUM (
    'create',
    'read',
    'update',
    'delete',
    'approve',
    'reject',
    'sign',
    'login',
    'logout',
    'export',
    'print',
    'system_event',
    'security_event'
);

CREATE TYPE signature_type AS ENUM (
    'approval',
    'review',
    'witness',
    'author',
    'quality_approval',
    'regulatory_approval'
);

CREATE TYPE compliance_framework AS ENUM (
    'fda_21_cfr_part_11',
    'eu_gmp',
    'ich_q7',
    'iso_13485',
    'hipaa',
    'gdpr',
    'gxp',
    'cdsco',
    'who_gmp'
);

CREATE TYPE regulatory_agency AS ENUM (
    'fda',
    'ema',
    'cdsco',
    'health_canada',
    'tga',
    'pmda',
    'who',
    'ich'
);

-- Verify enum creation
SELECT 'ENUM TYPES CREATED' as status, COUNT(*) as count
FROM pg_type 
WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND typtype = 'e';

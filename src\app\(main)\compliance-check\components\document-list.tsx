'use client'

import { <PERSON><PERSON>ext, X, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, AlertTriangle } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'


interface UploadedDocument {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly size: string;
  readonly status: 'uploaded' | 'processing' | 'analyzed' | 'error';
  readonly complianceScore?: number;
  readonly issues?: number;
  readonly recommendations?: number;
  readonly riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  readonly uploadDate: string;
}

interface DocumentListProps {
  readonly documents: readonly UploadedDocument[];
  readonly onRemoveDocument: (docId: string) => void;
}

export function DocumentList({
  documents,
  onRemoveDocument,
}: DocumentListProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploaded':
        return <Clock className="h-4 w-4 text-muted-foreground" />
      case 'processing':
        return <Clock className="h-4 w-4 text-warning animate-spin" />
      case 'analyzed':
        return <CheckCircle className="h-4 w-4 text-success" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-destructive" />
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded':
        return 'secondary'
      case 'processing':
        return 'default'
      case 'analyzed':
        return 'default'
      case 'error':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getRiskColor = (riskLevel?: string) => {
    switch (riskLevel) {
      case 'low':
        return 'text-success'
      case 'medium':
        return 'text-warning'
      case 'high':
        return 'text-warning'
      case 'critical':
        return 'text-destructive'
      default:
        return 'text-muted-foreground'
    }
  }

  if (documents.length === 0) {
return null
}

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          Uploaded Documents ({documents.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {documents.map((doc) => (
          <div
            key={doc.id}
            className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-center space-x-3 flex-1">
              <FileText className="h-8 w-8 text-muted-foreground" />
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="font-medium truncate">{doc.name}</h4>
                  <Badge
                    variant={
                      getStatusColor(doc.status) as
                        | 'secondary'
                        | 'default'
                        | 'destructive'
                        | 'outline'
                    }
                    className="flex items-center space-x-1"
                  >
                    {getStatusIcon(doc.status)}
                    <span className="capitalize">{doc.status}</span>
                  </Badge>
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <span>{doc.type}</span>
                  <span>{doc.size}</span>
                  <span>{doc.uploadDate}</span>
                  {doc.complianceScore && (
                    <span className="text-primary font-medium">
                      Score: {doc.complianceScore}%
                    </span>
                  )}
                  {doc.riskLevel && (
                    <span className={getRiskColor(doc.riskLevel)}>
                      Risk: {doc.riskLevel}
                    </span>
                  )}
                </div>
                {doc.status === 'analyzed' && (
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                    {doc.issues && <span>Issues: {doc.issues}</span>}
                    {doc.recommendations && (
                      <span>Recommendations: {doc.recommendations}</span>
                    )}
                  </div>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemoveDocument(doc.id)}
              className="text-muted-foreground hover:text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

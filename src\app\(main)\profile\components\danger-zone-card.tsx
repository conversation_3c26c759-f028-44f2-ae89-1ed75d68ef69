'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import { AlertTriangle } from 'lucide-react'
import { useState } from 'react'
import { useProfileStore } from '../store'

export function DangerZoneCard() {
  const { profileData } = useProfileStore()
  const [confirmEmail, setConfirmEmail] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()

  const handleDelete = async () => {
    if (confirmEmail !== profileData.email) {
      toast({
        title: 'Email does not match',
        description: 'Please enter your email address correctly to confirm deletion',
        variant: 'destructive',
      })
      return
    }

    setIsDeleting(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast({
      title: 'Account deletion requested',
      description: 'Your account will be deleted within 24 hours',
    })
    setIsDeleting(false)
    setConfirmEmail('')
  }

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="text-destructive flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Danger Zone
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Delete Account</h4>
            <p className="text-sm text-muted-foreground mb-4">
              Once you delete your account, there is no going back. Please be certain.
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="destructive">Delete Account</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Are you absolutely sure?</DialogTitle>
                  <DialogDescription>
                    This action cannot be undone. This will permanently delete your
                    account and remove your data from our servers.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <p className="text-sm">
                    Please type <span className="font-mono font-semibold">{profileData.email}</span> to confirm.
                  </p>
                  <Input
                    value={confirmEmail}
                    onChange={(e) => setConfirmEmail(e.target.value)}
                    placeholder="Enter your email"
                    aria-describedby="delete-warning"
                  />
                  <p id="delete-warning" className="text-xs text-destructive">
                    Warning: This action is irreversible
                  </p>
                </div>
                <DialogFooter>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={confirmEmail !== profileData.email || isDeleting}
                  >
                    {isDeleting ? 'Deleting...' : 'Delete Account'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

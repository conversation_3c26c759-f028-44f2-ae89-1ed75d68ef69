#!/usr/bin/env python3

import requests
import json

def test_populate_knowledge_base():
    """Test the populate knowledge base API endpoint."""
    
    url = "http://localhost:8000/api/v1/ai/populate-knowledge-base"
    
    payload = {
        "sources": ["local_cfr"],
        "force_refresh": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Testing API endpoint: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=300)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {json.dumps(result, indent=2)}")
        else:
            print(f"Error Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")

if __name__ == "__main__":
    test_populate_knowledge_base()
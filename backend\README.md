# VigiLens Pharmaceutical Compliance Platform - Backend

## VCP_001: Database Schema Design & Implementation

This backend implementation provides the complete database schema and infrastructure for the VigiLens Pharmaceutical Compliance Platform, focusing on autonomous AI-powered regulatory compliance for pharmaceutical companies.

## 🏗️ Architecture Overview

### Technology Stack
- **Framework**: FastAPI 0.115.5 with Python 3.13.5
- **Database**: Supabase PostgreSQL with Row Level Security (RLS)
- **AI/ML**: LangChain 0.3.14 with OpenAI integration
- **Vector DB**: ChromaDB 0.6.2 for document embeddings
- **Authentication**: Supabase Auth with JWT tokens
- **Monitoring**: <PERSON><PERSON><PERSON> for AI observability

### Database Schema

The VCP_001 implementation includes 7 core tables with comprehensive audit trails:

1. **Organizations** - Multi-tenant organization management
2. **User Profiles** - User management with Supabase Auth integration
3. **User Roles** - Granular permission system with expiration
4. **Regulatory Documents** - Core document management with AI analysis
5. **Document Versions** - Version control and audit trails
6. **Compliance Frameworks** - Regulatory standards (FDA cGMP, ICH Q7, etc.)
7. **Audit Trail** - Comprehensive 21 CFR Part 11 compliant logging

## 🚀 Quick Start

### Prerequisites

- Python 3.13.5+
- PostgreSQL (via Supabase)
- OpenAI API key
- Supabase project

### Installation

1. **Clone and navigate to backend directory**:
   ```bash
   cd app/backend
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration
   ```

5. **Initialize database**:
   ```bash
   python init_database.py
   ```

6. **Start development server**:
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

## 📊 Database Schema Details

### Core Tables

#### Organizations
```sql
- id (UUID, PK)
- name (VARCHAR, unique)
- domain (VARCHAR, unique)
- subscription_tier (ENUM)
- settings (JSONB)
- created_at, updated_at (TIMESTAMP)
```

#### User Profiles
```sql
- id (UUID, PK, references auth.users)
- organization_id (UUID, FK)
- email (VARCHAR, unique)
- full_name (VARCHAR)
- role (ENUM)
- department (VARCHAR)
- preferences (JSONB)
- last_login (TIMESTAMP)
- is_active (BOOLEAN)
```

#### Regulatory Documents
```sql
- id (UUID, PK)
- organization_id (UUID, FK)
- title (VARCHAR)
- document_type (ENUM)
- regulatory_agency (ENUM)
- content (TEXT)
- file_url (VARCHAR)
- ai_summary (TEXT)
- compliance_score (DECIMAL)
- risk_level (VARCHAR)
- metadata (JSONB)
```

### Security Features

- **Row Level Security (RLS)**: Multi-tenant data isolation
- **Audit Trails**: 21 CFR Part 11 compliant logging
- **Digital Signatures**: Cryptographic integrity
- **Role-Based Access**: Granular permissions with expiration
- **Data Encryption**: AES-256 at rest, TLS 1.3 in transit

## 🔧 Database Management

### Migration Commands

```bash
# Initialize database with all migrations
python init_database.py

# Reset database (DESTRUCTIVE)
python init_database.py --reset

# Check migration status
python init_database.py --status

# Verify schema integrity
python init_database.py --verify

# Verbose output
python init_database.py --verbose
```

### Migration Files

1. `001_create_organizations.sql` - Organization management
2. `002_create_user_profiles.sql` - User profiles with Supabase Auth
3. `003_create_user_roles.sql` - Role-based permissions
4. `004_create_regulatory_documents.sql` - Document management
5. `005_create_document_versions.sql` - Version control
6. `006_create_compliance_frameworks.sql` - Compliance standards
7. `007_create_audit_trail.sql` - Audit and compliance logging

## 🔐 Security & Compliance

### 21 CFR Part 11 Compliance

- **Electronic Records**: Secure document storage with integrity
- **Electronic Signatures**: Cryptographic signatures with timestamps
- **Audit Trails**: Immutable logs of all system activities
- **Access Controls**: Role-based permissions with time-based expiration
- **Data Integrity**: Checksums and validation for all documents

### Row Level Security (RLS)

All tables implement RLS policies for multi-tenant security:

```sql
-- Example: Users can only access their organization's data
CREATE POLICY "Users can view own organization data" ON regulatory_documents
    FOR SELECT USING (
        organization_id = get_user_organization_id(auth.uid())
    );
```

## 🤖 AI Integration

### Document Analysis

- **Content Extraction**: Automated text extraction from PDFs/documents
- **Compliance Scoring**: AI-powered compliance assessment
- **Risk Analysis**: Automated risk level calculation
- **Summarization**: AI-generated document summaries
- **Vector Search**: Semantic search using embeddings

### Compliance Frameworks

Pre-configured compliance frameworks:

- **FDA cGMP** - Current Good Manufacturing Practice
- **ICH Q7** - Good Manufacturing Practice for APIs
- **ISO 13485** - Medical Devices Quality Management
- **EU GMP** - European Good Manufacturing Practice
- **ICH Q9** - Quality Risk Management

## 📈 Monitoring & Observability

### Audit Trail Features

- **User Actions**: All CRUD operations logged
- **System Events**: Automated process tracking
- **Compliance Events**: Regulatory-specific logging
- **Digital Signatures**: Cryptographic proof of actions
- **Retention Management**: Automated cleanup with compliance periods

### Performance Monitoring

- **Database Metrics**: Query performance and connection pooling
- **AI Metrics**: LangSmith integration for LLM observability
- **Error Tracking**: Structured logging with Sentry integration
- **Health Checks**: Automated system health monitoring

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_database.py

# Run with verbose output
pytest -v
```

### Test Database

Use a separate test database:

```bash
# Set test database URL in .env
TEST_DATABASE_URL=postgresql://postgres:test_password@localhost:5432/vigilens_test

# Initialize test database
DATABASE_URL=$TEST_DATABASE_URL python init_database.py
```

## 🚀 Deployment

### Production Checklist

- [ ] Set `ENVIRONMENT=production` in .env
- [ ] Configure production database URL
- [ ] Set secure `SECRET_KEY`
- [ ] Enable SSL/TLS certificates
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategy
- [ ] Review security settings

### Environment Variables

See `.env.example` for all required configuration options.

## 📚 API Documentation

Once the server is running, access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🔄 Development Workflow

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .

# Run all quality checks
./scripts/quality_check.sh
```

### Database Changes

1. Create new migration file in `migrations/`
2. Follow naming convention: `XXX_description.sql`
3. Add to `MIGRATION_FILES` in `init_database.py`
4. Test migration with `--verify` flag
5. Update Pydantic models in `models/`

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `DATABASE_URL` in .env
   - Verify Supabase project is active
   - Check network connectivity

2. **Migration Failed**
   - Check migration file syntax
   - Verify dependencies are met
   - Use `--verbose` for detailed logs

3. **RLS Policy Errors**
   - Verify user has proper organization assignment
   - Check `get_user_organization_id()` function
   - Review RLS policies in migration files

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=DEBUG python init_database.py --verbose

# Check database connection
psql $DATABASE_URL -c "SELECT version();"
```

## 📞 Support

For technical support or questions about VCP_001 implementation:

- Review migration files in `migrations/`
- Check Pydantic models in `models/`
- Consult technical specification document
- Review audit trail logs for debugging

## 📄 License

VigiLens Pharmaceutical Compliance Platform - Proprietary Software

---

**VCP_001 Status**: ✅ **COMPLETED**

*Database Schema Design & Implementation successfully delivered with full multi-tenant RLS, 21 CFR Part 11 compliance, and AI-ready infrastructure.*
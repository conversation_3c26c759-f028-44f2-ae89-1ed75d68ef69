import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Landing from "./pages/Landing";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import Updates from "./pages/Updates";
import Documents from "./pages/Documents";
import SearchPage from "./pages/Search";
import AIAssistant from "./pages/AIAssistant";
import UploadPage from "./pages/Upload";
import ComplianceCheck from "./pages/ComplianceCheck";
import SettingsPage from "./pages/Settings";
import Help from "./pages/Help";
import Profile from "./pages/Profile";
import Notifications from "./pages/Notifications";
import NotFound from "./pages/NotFound";

// Create QueryClient with proper configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Landing page */}
            <Route path="/" element={<Landing />} />

            {/* Authentication */}
            <Route path="/login" element={<Login />} />

            {/* Main Application Routes */}
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/updates" element={<Updates />} />
            <Route path="/documents" element={<Documents />} />
            <Route path="/search" element={<SearchPage />} />
            <Route path="/ai-assistant" element={<AIAssistant />} />
            <Route path="/upload" element={<UploadPage />} />
            <Route path="/compliance-check" element={<ComplianceCheck />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/notifications" element={<Notifications />} />
            <Route path="/help" element={<Help />} />

            {/* 404 Catch-all */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

import { useState, useCallback } from 'react'

export interface UploadedDocument {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly size: string;
  readonly status: 'uploaded' | 'processing' | 'analyzed' | 'error';
  readonly complianceScore?: number;
  readonly issues?: number;
  readonly recommendations?: number;
  readonly riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  readonly uploadDate: string;
}

export interface ComplianceResult {
  readonly documentId: string;
  readonly framework: string;
  readonly score: number;
  readonly issues: Array<{
    readonly severity: 'low' | 'medium' | 'high' | 'critical';
    readonly title: string;
    readonly description: string;
    readonly location: string;
    readonly recommendation: string;
  }>;
  readonly recommendations: Array<{
    readonly priority: 'low' | 'medium' | 'high';
    readonly title: string;
    readonly description: string;
    readonly implementation: string;
  }>;
  readonly insights: Array<{
    readonly category: string;
    readonly finding: string;
    readonly impact: string;
    readonly action: string;
  }>;
}

export function useComplianceCheck() {
  const [currentStep, setCurrentStep] = useState(1)
  const [uploadedDocuments, setUploadedDocuments] = useState<
    UploadedDocument[]
  >([])
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([
    'fda-cgmp',
  ])
  const [analysisType, setAnalysisType] = useState('comprehensive')
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [results, setResults] = useState<ComplianceResult[]>([])

  const handleFileUpload = useCallback(
    (files: FileList) => {
      Array.from(files).forEach((file) => {
        const newDoc: UploadedDocument = {
          id: `doc_${Date.now()}_${Math.random()}`,
          name: file.name,
          type: (file.name.split('.').pop() || 'file').toUpperCase(),
          size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
          status: 'uploaded',
          uploadDate: new Date().toISOString().split('T')[0]!,
        }
        setUploadedDocuments((prev) => [...prev, newDoc])
      })

      if (currentStep === 1) {
        setCurrentStep(2)
      }
    },
    [currentStep],
  )

  const removeDocument = useCallback((docId: string) => {
    setUploadedDocuments((prev) => prev.filter((doc) => doc.id !== docId))
  }, [])

  const handleFrameworkToggle = useCallback((frameworkId: string) => {
    setSelectedFrameworks((prev) =>
      prev.includes(frameworkId)
        ? prev.filter((id) => id !== frameworkId)
        : [...prev, frameworkId],
    )
  }, [])

  const startAnalysis = useCallback(async () => {
    setIsProcessing(true)
    setProcessingProgress(0)
    setCurrentStep(4)

    // Simulate processing with progress updates
    const progressSteps = [
      { progress: 20, status: 'Uploading documents...' },
      { progress: 40, status: 'Extracting document content...' },
      { progress: 60, status: 'Running compliance checks...' },
      { progress: 80, status: 'Performing deep analysis...' },
      { progress: 100, status: 'Generating insights...' },
    ]

    for (const step of progressSteps) {
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setProcessingProgress(step.progress)
    }

    // Generate mock results
    const mockResults: ComplianceResult[] = uploadedDocuments.map((doc) => ({
      documentId: doc.id,
      framework: selectedFrameworks[0] || 'fda-cgmp',
      score: Math.floor(Math.random() * 20) + 80,
      issues: [
        {
          severity: 'medium' as const,
          title: 'Documentation Gap Identified',
          description: 'Missing quality control procedures section',
          location: 'Section 4.2',
          recommendation:
            'Add detailed QC procedures according to 21 CFR 211.22',
        },
        {
          severity: 'low' as const,
          title: 'Minor Formatting Issue',
          description: 'Inconsistent date formatting throughout document',
          location: 'Multiple sections',
          recommendation: 'Standardize date format to ISO 8601',
        },
      ],
      recommendations: [
        {
          priority: 'high' as const,
          title: 'Implement Change Control System',
          description: 'Establish robust change control procedures',
          implementation: 'Create change control SOP with approval workflow',
        },
      ],
      insights: [
        {
          category: 'Risk Management',
          finding: 'Document lacks comprehensive risk assessment',
          impact: 'May lead to regulatory scrutiny',
          action: 'Implement ICH Q9 risk management principles',
        },
      ],
    }))

    setResults(mockResults)
    setIsProcessing(false)
    setCurrentStep(5)
  }, [uploadedDocuments, selectedFrameworks])

  return {
    currentStep,
    uploadedDocuments,
    selectedFrameworks,
    analysisType,
    isProcessing,
    processingProgress,
    results,
    setCurrentStep,
    setAnalysisType,
    handleFileUpload,
    removeDocument,
    handleFrameworkToggle,
    startAnalysis,
  }
}

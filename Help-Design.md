# Help Section – Hyper-Detailed UI/UX Specification (Next.js 15.3.4 + React 19.1.0)

> Version 2.0 • Based on Existing Codebase Analysis • Author: BMAD UX Council

## 1. Functional Overview
* Central hub to view, filter, and manage system notifications (alerts, reminders, system updates).
* Supports bulk actions (mark-as-read, delete), real-time updates, and granular settings per notification type.
* Fully client-side page inside `app/(main)/notifications/page.tsx` (uses `usePageMetadata`).

## 2. Page Layout & Anatomy
```
NotificationsPage (Client Component) – 3 major vertical regions
├── PageHeader              (Top ~72 px)     – title + subtitle + toolbar
├── ControlsBar             (Sticky)         – filter pills + search + bulk actions
├── NotificationsList       (Flex-1)         – virtualized scroll list
│   ├── NotificationItem    (each row)
│   └── ...
└── SettingsDrawer (Sheet)  (Portal)         – per-type toggle switches
```

### 2.1. Top-Level Container
* `class="flex flex-col h-full space-y-6"`
* Wrapped in `container mx-auto p-6` from parent layout.
* Height calc: `h-[calc(100vh-10rem)]` (accounts for global header/footer).


## 1. Complete Page Structure & Layout

### 1.1. Root Container
```tsx
<DashboardLayout>
  <div className="space-y-6">
    {/* All content sections */}
  </div>
</DashboardLayout>
```

### 1.2. Page Header Section
```tsx
<div className="flex items-center justify-between">
  <div>
    <h1 className="text-3xl font-bold text-foreground">Help Center</h1>
    <p className="text-muted-foreground mt-1">
      Find answers, tutorials, and get the support you need
    </p>
  </div>
  <div className="flex items-center space-x-2">
    <Button variant="outline">
      <MessageCircle className="mr-2 h-4 w-4" />
      Live Chat
    </Button>
    <Button variant="outline">
      <Phone className="mr-2 h-4 w-4" />
      Contact Support
    </Button>
    <Button>
      <Plus className="mr-2 h-4 w-4" />
      Submit Ticket
    </Button>
  </div>
</div>
```

## 2. Search Card Section

### 2.1. Search Card Structure
```tsx
<Card>
  <CardContent className="p-6">
    <div className="max-w-2xl mx-auto text-center space-y-4">
      <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mx-auto">
        <Search className="h-8 w-8 text-primary" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">How can we help you?</h2>
        <p className="text-muted-foreground">
          Search our knowledge base or browse categories below
        </p>
      </div>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search for help articles, guides, or FAQs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-3 text-lg"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => setSearchQuery("")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Popular Searches */}
      <div className="flex flex-wrap justify-center gap-2 mt-4">
        <span className="text-sm text-muted-foreground mr-2">Popular:</span>
        {popularSearches.map((search, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={() => setSearchQuery(search)}
          >
            {search}
          </Button>
        ))}
      </div>
    </div>
  </CardContent>
</Card>
```

## 3. Help Tabs System

### 3.1. Tabs Structure
```tsx
<Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
  <TabsList className="grid w-full grid-cols-5">
    <TabsTrigger value="quick-start">Quick Start</TabsTrigger>
    <TabsTrigger value="faqs">FAQs</TabsTrigger>
    <TabsTrigger value="troubleshooting">Troubleshooting</TabsTrigger>
    <TabsTrigger value="contact">Contact</TabsTrigger>
    <TabsTrigger value="resources">Resources</TabsTrigger>
  </TabsList>

  <TabsContent value={selectedTab} className="space-y-4">
    {/* Tab content */}
  </TabsContent>
</Tabs>
```

## 4. Quick Start Tab Content

### 4.1. Quick Start Guides Grid
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {quickStartGuides.map((guide, index) => (
    <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
            <guide.icon className="h-6 w-6 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold mb-2">{guide.title}</h3>
            <p className="text-sm text-muted-foreground mb-3">{guide.description}</p>
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                {guide.duration}
              </Badge>
              <Button variant="ghost" size="sm">
                Start Guide
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### 4.2. Quick Start Data Structure
```tsx
const quickStartGuides = [
  {
    icon: Zap,
    title: "Getting Started",
    description: "Learn the basics and set up your account",
    duration: "5 min"
  },
  {
    icon: FileText,
    title: "Document Management",
    description: "Upload, organize, and manage your documents",
    duration: "10 min"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Invite team members and manage permissions",
    duration: "8 min"
  },
  {
    icon: Settings,
    title: "Account Settings",
    description: "Customize your profile and preferences",
    duration: "5 min"
  },
  {
    icon: Shield,
    title: "Security Setup",
    description: "Enable two-factor authentication and security features",
    duration: "7 min"
  },
  {
    icon: BarChart,
    title: "Reports & Analytics",
    description: "Generate reports and view analytics",
    duration: "12 min"
  }
];
```

## 5. FAQs Tab Content

### 5.1. FAQ Section Structure
```tsx
<div className="space-y-6">
  {/* FAQ Categories */}
  <div className="flex flex-wrap gap-2">
    {faqCategories.map((category, index) => (
      <Button
        key={index}
        variant={selectedFaqCategory === category ? "default" : "outline"}
        size="sm"
        onClick={() => setSelectedFaqCategory(category)}
      >
        {category}
      </Button>
    ))}
  </div>

  {/* FAQ Accordion */}
  <Accordion type="multiple" className="space-y-4">
    {filteredFaqs.map((faq, index) => (
      <AccordionItem key={index} value={`faq-${index}`} className="border rounded-lg">
        <AccordionTrigger className="px-6 py-4 hover:no-underline">
          <div className="flex items-center space-x-3 text-left">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
              <HelpCircle className="h-4 w-4 text-primary" />
            </div>
            <span className="font-medium">{faq.question}</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-6 pb-4">
          <div className="ml-11 space-y-3">
            <p className="text-muted-foreground">{faq.answer}</p>
            <div className="flex items-center justify-between pt-3 border-t">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-muted-foreground">Was this helpful?</span>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <ThumbsUp className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <ThumbsDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <Badge variant="outline" className="text-xs">
                {faq.category}
              </Badge>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    ))}
  </Accordion>
</div>
```

## 6. Troubleshooting Tab Content

### 6.1. Troubleshooting Issues Grid
```tsx
<div className="space-y-6">
  {/* Issue Categories */}
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {troubleshootingCategories.map((category, index) => (
      <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${
              category.severity === "high" ? "bg-red-100 text-red-600" :
              category.severity === "medium" ? "bg-yellow-100 text-yellow-600" :
              "bg-green-100 text-green-600"
            }`}>
              <category.icon className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-medium">{category.title}</h3>
              <p className="text-sm text-muted-foreground">{category.count} issues</p>
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>

  {/* Common Issues */}
  <Card>
    <CardHeader>
      <CardTitle>Common Issues</CardTitle>
      <CardDescription>
        Quick solutions to the most frequently reported problems
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {commonIssues.map((issue, index) => (
          <div key={index} className="flex items-start space-x-4 p-4 rounded-lg border hover:bg-muted/50">
            <div className={`flex h-8 w-8 items-center justify-center rounded-full ${
              issue.severity === "high" ? "bg-red-100 text-red-600" :
              issue.severity === "medium" ? "bg-yellow-100 text-yellow-600" :
              "bg-green-100 text-green-600"
            }`}>
              <AlertTriangle className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium mb-1">{issue.title}</h4>
              <p className="text-sm text-muted-foreground mb-2">{issue.description}</p>
              <Button variant="outline" size="sm">
                View Solution
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
            <Badge variant="outline" className="text-xs">
              {issue.category}
            </Badge>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
</div>
```

## 7. Contact Tab Content

### 7.1. Contact Options Grid
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {contactOptions.map((option, index) => (
    <Card key={index} className="hover:shadow-md transition-shadow">
      <CardContent className="p-6 text-center">
        <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mx-auto mb-4">
          <option.icon className="h-8 w-8 text-primary" />
        </div>
        <h3 className="font-semibold mb-2">{option.title}</h3>
        <p className="text-sm text-muted-foreground mb-4">{option.description}</p>
        <div className="space-y-2">
          <p className="text-sm font-medium">{option.availability}</p>
          <p className="text-xs text-muted-foreground">{option.responseTime}</p>
        </div>
        <Button className="w-full mt-4">
          {option.action}
        </Button>
      </CardContent>
    </Card>
  ))}
</div>
```

### 7.2. Contact Form
```tsx
<Card className="mt-8">
  <CardHeader>
    <CardTitle>Submit a Support Ticket</CardTitle>
    <CardDescription>
      Can't find what you're looking for? Send us a message and we'll get back to you.
    </CardDescription>
  </CardHeader>
  <CardContent>
    <form className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input id="name" placeholder="Your full name" />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="<EMAIL>" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="category">Category</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technical">Technical Issue</SelectItem>
              <SelectItem value="billing">Billing Question</SelectItem>
              <SelectItem value="feature">Feature Request</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="priority">Priority</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="subject">Subject</Label>
        <Input id="subject" placeholder="Brief description of your issue" />
      </div>

      <div>
        <Label htmlFor="message">Message</Label>
        <Textarea
          id="message"
          placeholder="Please provide as much detail as possible..."
          rows={5}
        />
      </div>

      <div className="flex justify-end">
        <Button type="submit">
          <Send className="mr-2 h-4 w-4" />
          Submit Ticket
        </Button>
      </div>
    </form>
  </CardContent>
</Card>
```

## 8. Resources Tab Content

### 8.1. Resource Categories
```tsx
<div className="space-y-8">
  {resourceCategories.map((category, categoryIndex) => (
    <div key={categoryIndex}>
      <div className="flex items-center space-x-3 mb-4">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
          <category.icon className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-xl font-semibold">{category.title}</h3>
          <p className="text-sm text-muted-foreground">{category.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {category.resources.map((resource, resourceIndex) => (
          <Card key={resourceIndex} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-muted">
                  <resource.icon className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium mb-1">{resource.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{resource.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {resource.type}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  ))}
</div>
```

## 9. State Management & Data Structures

### 9.1. Help Interfaces
```tsx
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
  notHelpful: number;
}

interface Guide {
  id: string;
  title: string;
  description: string;
  duration: string;
  icon: LucideIcon;
}

interface Issue {
  id: string;
  title: string;
  description: string;
  category: string;
  severity: "low" | "medium" | "high";
}
```

### 9.2. State Variables
```tsx
const [selectedTab, setSelectedTab] = useState("quick-start");
const [searchQuery, setSearchQuery] = useState("");
const [selectedFaqCategory, setSelectedFaqCategory] = useState("All");
const [faqs, setFaqs] = useState<FAQ[]>(mockFaqs);
const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>(faqs);
```

### 9.3. Filtering Logic
```tsx
const filteredFaqs = faqs.filter((faq) => {
  const matchesSearch = searchQuery === "" ||
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
  const matchesCategory = selectedFaqCategory === "All" || faq.category === selectedFaqCategory;
  return matchesSearch && matchesCategory;
});
```

## 10. Required Imports

```tsx
import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
  Search, MessageCircle, Phone, Plus, X, ArrowRight, HelpCircle,
  ThumbsUp, ThumbsDown, AlertTriangle, Zap, FileText, Users,
  Settings, Shield, BarChart, Send, ExternalLink
} from "lucide-react";
```

---
End of hyper-detailed specification

# Errors

##  Module Issues (98 errors)

- Error 1: `Cannot access attribute "analyze_document" for class "AIClient"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:529
  - Error details: Cannot access attribute "analyze_document" for class "AIClient"
    Attribute "analyze_document" is unknown

- Error 2: `Cannot access attribute "generate_text" for class "AIClient"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:633
  - Error details: Cannot access attribute "generate_text" for class "AIClient"
    Attribute "generate_text" is unknown

- Error 3: `Cannot access attribute "generate_text" for class "AIClient"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:725
  - Error details: Cannot access attribute "generate_text" for class "AIClient"
    Attribute "generate_text" is unknown

- Error 4: `Argument missing for parameter "query_vector"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:817
  - Error details: Argument missing for parameter "query_vector"

- Error 5: `No parameter named "query"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:818
  - Error details: No parameter named "query"

- Error 6: `No parameter named "n_results"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:819
  - Error details: No parameter named "n_results"

- Error 7: `No parameter named "filters"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:820
  - Error details: No parameter named "filters"

- Error 8: `"get_fda_metadata_manager" is unknown import symbol` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:879
  - Error details: "get_fda_metadata_manager" is unknown import symbol

- Error 9: `Cannot access attribute "process_all_fda_documents" for class "PDFProcessor"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:887
  - Error details: Cannot access attribute "process_all_fda_documents" for class "PDFProcessor"
    Attribute "process_all_fda_documents" is unknown

- Error 10: `Cannot access attribute "load_fda_hierarchy" for class "PDFProcessor"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:891
  - Error details: Cannot access attribute "load_fda_hierarchy" for class "PDFProcessor"
    Attribute "load_fda_hierarchy" is unknown

- Error 11: `Cannot access attribute "chunk_fda_document" for class "FDAChunker"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:899
  - Error details: Cannot access attribute "chunk_fda_document" for class "FDAChunker"
    Attribute "chunk_fda_document" is unknown

- Error 12: `Arguments missing for parameters "vectors", "metadata"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:911
  - Error details: Arguments missing for parameters "vectors", "metadata"

- Error 13: `Cannot access attribute "get_processing_stats" for class "PDFProcessor"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:919
  - Error details: Cannot access attribute "get_processing_stats" for class "PDFProcessor"
    Attribute "get_processing_stats" is unknown

- Error 14: `Cannot access attribute "get_collection_stats" for class "VectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:994
  - Error details: Cannot access attribute "get_collection_stats" for class "VectorStore"
    Attribute "get_collection_stats" is unknown

- Error 15: `Cannot access attribute "delete_documents_by_filter" for class "VectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:1061
  - Error details: Cannot access attribute "delete_documents_by_filter" for class "VectorStore"
    Attribute "delete_documents_by_filter" is unknown

- Error 16: `Cannot access attribute "delete_documents_by_filter" for class "VectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/main.py:1088
  - Error details: Cannot access attribute "delete_documents_by_filter" for class "VectorStore"
    Attribute "delete_documents_by_filter" is unknown

- Error 17: `Argument missing for parameter "errors"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/models/base.py:147
  - Error details: Argument missing for parameter "errors"

- Error 18: `Argument missing for parameter "data"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/models/base.py:152
  - Error details: Argument missing for parameter "data"

- Error 19: `Argument missing for parameter "minimum_content_length"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/models/compliance.py:93
  - Error details: Argument missing for parameter "minimum_content_length"

- Error 20: `Arguments missing for parameters "quality_system", "documentation", "validation", "personnel", "facilities", "equipment", "risk_management", "materials", "production"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/models/compliance.py:94
  - Error details: Arguments missing for parameters "quality_system", "documentation", "validation", "personnel", "facilities", "equipment", "risk_management", "materials", "production"

- Error 21: `Argument of type "type[DocumentMetadata]" cannot be assigned to parameter "default_factory" of type "(() -> _T@Field) | ((dict[str, Any]) -> _T@Field)" in function "Field"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/models/documents.py:135
  - Error details: Argument of type "type[DocumentMetadata]" cannot be assigned to parameter "default_factory" of type "(() -> _T@Field) | ((dict[str, Any]) -> _T@Field)" in function "Field"
    Type "type[DocumentMetadata]" is not assignable to type "(() -> _T@Field) | ((dict[str, Any]) -> _T@Field)"
      Type "type[DocumentMetadata]" is not assignable to type "(dict[str, Any]) -> _T@Field"
        Function accepts too many positional parameters; expected 0 but received 1
          Extra parameter "page_count"
          Extra parameter "file_size"
          Extra parameter "checksum"
      Type "type[DocumentMetadata]" is not assignable to type "() -> _T@Field"
        Extra parameter "page_count"
    ...

- Error 22: `"get_vector_store_instance" is unknown import symbol` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/__init__.py:65
  - Error details: "get_vector_store_instance" is unknown import symbol

- Error 23: `No overloads for "Field" match the provided arguments` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/bge_m3_embeddings.py:46
  - Error details: No overloads for "Field" match the provided arguments
    Argument types: (EllipsisType, Literal[1], Literal[100])

- Error 24: `Type "int | None" is not assignable to return type "int"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/bge_m3_embeddings.py:223
  - Error details: Type "int | None" is not assignable to return type "int"
    Type "int | None" is not assignable to type "int"
      "None" is not assignable to "int"

- Error 25: `Argument missing for parameter "filter_conditions"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:151
  - Error details: Argument missing for parameter "filter_conditions"

- Error 26: `No parameter named "memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:253
  - Error details: No parameter named "memory"

- Error 27: `No parameter named "memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:264
  - Error details: No parameter named "memory"

- Error 28: `No parameter named "memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:275
  - Error details: No parameter named "memory"

- Error 29: `No parameter named "memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:286
  - Error details: No parameter named "memory"

- Error 30: `No parameter named "memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:297
  - Error details: No parameter named "memory"

- Error 31: `Cannot access attribute "memory" for class "Agent"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/crewai_agents.py:601
  - Error details: Cannot access attribute "memory" for class "Agent"
    Attribute "memory" is unknown

- Error 32: `Arguments missing for parameters "title", "cfr_title", "cfr_part", "effective_date", "last_updated"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:141
  - Error details: Arguments missing for parameters "title", "cfr_title", "cfr_part", "effective_date", "last_updated"

- Error 33: `Arguments missing for parameters "title", "cfr_title", "cfr_part", "effective_date", "last_updated"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:186
  - Error details: Arguments missing for parameters "title", "cfr_title", "cfr_part", "effective_date", "last_updated"

- Error 34: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:219
  - Error details: Argument missing for parameter "source_section"

- Error 35: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:237
  - Error details: Argument missing for parameter "source_section"

- Error 36: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:256
  - Error details: Argument missing for parameter "source_section"

- Error 37: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:277
  - Error details: Argument missing for parameter "source_section"

- Error 38: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:296
  - Error details: Argument missing for parameter "source_section"

- Error 39: `Argument missing for parameter "source_section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/fda_metadata.py:317
  - Error details: Argument missing for parameter "source_section"

- Error 40: `"ToolExecutor" is unknown import symbol` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:20
  - Error details: "ToolExecutor" is unknown import symbol

- Error 41: `Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:181
  - Error details: Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"
    "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to "StateGraph[Unknown, Unknown, Unknown]"

- Error 42: `Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:210
  - Error details: Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"
    "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to "StateGraph[Unknown, Unknown, Unknown]"

- Error 43: `Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:237
  - Error details: Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"
    "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to "StateGraph[Unknown, Unknown, Unknown]"

- Error 44: `Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:262
  - Error details: Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"
    "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to "StateGraph[Unknown, Unknown, Unknown]"

- Error 45: `Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:287
  - Error details: Type "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to return type "StateGraph[Unknown, Unknown, Unknown]"
    "CompiledStateGraph[WorkflowState, WorkflowState, WorkflowState]" is not assignable to "StateGraph[Unknown, Unknown, Unknown]"

- Error 46: `Cannot access attribute "ainvoke" for class "StateGraph[Unknown, Unknown, Unknown]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/langgraph_workflow.py:345
  - Error details: Cannot access attribute "ainvoke" for class "StateGraph[Unknown, Unknown, Unknown]"
    Attribute "ainvoke" is unknown

- Error 47: `Cannot access attribute "get_text" for class "Page"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:103
  - Error details: Cannot access attribute "get_text" for class "Page"
    Attribute "get_text" is unknown

- Error 48: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:112
  - Error details: "get" is not a known attribute of "None"

- Error 49: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:113
  - Error details: "get" is not a known attribute of "None"

- Error 50: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:114
  - Error details: "get" is not a known attribute of "None"

- Error 51: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:115
  - Error details: "get" is not a known attribute of "None"

- Error 52: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:116
  - Error details: "get" is not a known attribute of "None"

- Error 53: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:117
  - Error details: "get" is not a known attribute of "None"

- Error 54: `"get" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:118
  - Error details: "get" is not a known attribute of "None"

- Error 55: `Cannot access attribute "get_text" for class "Page"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:143
  - Error details: Cannot access attribute "get_text" for class "Page"
    Attribute "get_text" is unknown

- Error 56: `Cannot access attribute "get_text" for class "Page"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/pdf_processor.py:145
  - Error details: Cannot access attribute "get_text" for class "Page"
    Attribute "get_text" is unknown

- Error 57: `"get_collections" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py:202
  - Error details: "get_collections" is not a known attribute of "None"

- Error 58: `Arguments missing for parameters "document_id", "title", "source", "document_type", "chunk_index", "chunk_text", "file_path", "page_number", "section"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py:358
  - Error details: Arguments missing for parameters "document_id", "title", "source", "document_type", "chunk_index", "chunk_text", "file_path", "page_number", "section"

- Error 59: `Argument expression after ** must be a mapping with a "str" key type` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py:358
  - Error details: Argument expression after ** must be a mapping with a "str" key type

- Error 60: `Argument of type "List[str]" cannot be assigned to parameter "points" of type "List[ExtendedPointId]" in function "__init__"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/qdrant_store.py:397
  - Error details: Argument of type "List[str]" cannot be assigned to parameter "points" of type "List[ExtendedPointId]" in function "__init__"
    "List[str]" is not assignable to "List[ExtendedPointId]"
      Type parameter "_T@list" is invariant, but "str" is not the same as "ExtendedPointId"
      Consider switching from "list" to "Sequence" which is covariant

- Error 61: `Argument missing for parameter "score_threshold"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/rag_pipeline.py:393
  - Error details: Argument missing for parameter "score_threshold"

- Error 62: `Argument missing for parameter "filter_conditions"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/rag_pipeline.py:469
  - Error details: Argument missing for parameter "filter_conditions"

- Error 63: `No parameter named "storage_path"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:80
  - Error details: No parameter named "storage_path"

- Error 64: `No parameter named "use_memory"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:84
  - Error details: No parameter named "use_memory"

- Error 65: `Cannot access attribute "initialize" for class "QdrantVectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:89
  - Error details: Cannot access attribute "initialize" for class "QdrantVectorStore"
    Attribute "initialize" is unknown

- Error 66: `Cannot access attribute "add_documents" for class "QdrantVectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:115
  - Error details: Cannot access attribute "add_documents" for class "QdrantVectorStore"
    Attribute "add_documents" is unknown

- Error 67: `Argument missing for parameter "request"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:143
  - Error details: Argument missing for parameter "request"

- Error 68: `No parameter named "query_vector"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:144
  - Error details: No parameter named "query_vector"

- Error 69: `No parameter named "limit"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:145
  - Error details: No parameter named "limit"

- Error 70: `No parameter named "score_threshold"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:146
  - Error details: No parameter named "score_threshold"

- Error 71: `No parameter named "filter_conditions"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:147
  - Error details: No parameter named "filter_conditions"

- Error 72: `Cannot access attribute "delete_documents" for class "QdrantVectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:174
  - Error details: Cannot access attribute "delete_documents" for class "QdrantVectorStore"
    Attribute "delete_documents" is unknown

- Error 73: `Cannot access attribute "close" for class "QdrantVectorStore"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/ai/vector_store.py:197
  - Error details: Cannot access attribute "close" for class "QdrantVectorStore"
    Attribute "close" is unknown

- Error 74: `Argument of type "float" cannot be assigned to parameter "timeout" of type "int | None" in function "__init__"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:84
  - Error details: Argument of type "float" cannot be assigned to parameter "timeout" of type "int | None" in function "__init__"
    Type "float" is not assignable to type "int | None"
      "float" is not assignable to "int"
      "float" is not assignable to "None"

- Error 75: `Cannot access attribute "get_text" for class "Page"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:270
  - Error details: Cannot access attribute "get_text" for class "Page"
    Attribute "get_text" is unknown

- Error 76: `Cannot access attribute "size" for class "Dict[StrictStr, VectorParams]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:430
  - Error details: Cannot access attribute "size" for class "Dict[StrictStr, VectorParams]"
    Attribute "size" is unknown

- Error 77: `"size" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:430
  - Error details: "size" is not a known attribute of "None"

- Error 78: `Cannot access attribute "distance" for class "Dict[StrictStr, VectorParams]"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:431
  - Error details: Cannot access attribute "distance" for class "Dict[StrictStr, VectorParams]"
    Attribute "distance" is unknown

- Error 79: `"distance" is not a known attribute of "None"` in /d:/Buisness/Vigilen-ComplianceAI/app/backend/services/knowledge/fda_knowledge_populator.py:431
  - Error details: "distance" is not a known attribute of "None"

- Error 80: `"get_collections" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:78
  - Error details: "get_collections" is not a known attribute of "None"

- Error 81: `"create_collection" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:85
  - Error details: "create_collection" is not a known attribute of "None"

- Error 82: `"upsert" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:154
  - Error details: "upsert" is not a known attribute of "None"

- Error 83: `"search" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:160
  - Error details: "search" is not a known attribute of "None"

- Error 84: `"scroll" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:167
  - Error details: "scroll" is not a known attribute of "None"

- Error 85: `"delete" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:181
  - Error details: "delete" is not a known attribute of "None"

- Error 86: `"get_collection" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:204
  - Error details: "get_collection" is not a known attribute of "None"

- Error 87: `"count" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:205
  - Error details: "count" is not a known attribute of "None"

- Error 88: `Cannot access attribute "size" for class "Dict[StrictStr, VectorParams]"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:209
  - Error details: Cannot access attribute "size" for class "Dict[StrictStr, VectorParams]"
    Attribute "size" is unknown

- Error 89: `"size" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:209
  - Error details: "size" is not a known attribute of "None"

- Error 90: `Cannot access attribute "distance" for class "Dict[StrictStr, VectorParams]"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:210
  - Error details: Cannot access attribute "distance" for class "Dict[StrictStr, VectorParams]"
    Attribute "distance" is unknown

- Error 91: `"distance" is not a known attribute of "None"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/setup_qdrant_fda_kb.py:210
  - Error details: "distance" is not a known attribute of "None"

- Error 92: `Cannot access attribute "NOT_CONNECTED" for class "type[ConnectionStatus]"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:151
  - Error details: Cannot access attribute "NOT_CONNECTED" for class "type[ConnectionStatus]"
    Attribute "NOT_CONNECTED" is unknown

- Error 93: `Arguments missing for parameters "title", "document_type", "chunk_index", "file_path", "page_number", "section"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:167
  - Error details: Arguments missing for parameters "title", "document_type", "chunk_index", "file_path", "page_number", "section"

- Error 94: `No parameter named "chunk_id"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:169
  - Error details: No parameter named "chunk_id"

- Error 95: `Argument missing for parameter "filter_conditions"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:199
  - Error details: Argument missing for parameter "filter_conditions"

- Error 96: `Cannot access attribute "get_service_status" for class "BGE_M3_EmbeddingService"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:273
  - Error details: Cannot access attribute "get_service_status" for class "BGE_M3_EmbeddingService"
    Attribute "get_service_status" is unknown

- Error 97: `Argument missing for parameter "file_path"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:339
  - Error details: Argument missing for parameter "file_path"

- Error 98: `Argument missing for parameter "filter_conditions"` in /D:/Buisness/Vigilen-ComplianceAI/app/backend/test_ai_services.py:365
  - Error details: Argument missing for parameter "filter_conditions"


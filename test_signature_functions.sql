-- Test electronic signature functions
-- Run this after creating the functions

-- Test 1: Verify functions exist
SELECT 
    'FUNCTION VERIFICATION' as test_name,
    CASE 
        WHEN COUNT(*) = 3 THEN '✅ ALL FUNCTIONS EXIST'
        ELSE '❌ MISSING FUNCTIONS'
    END as status,
    COUNT(*) as function_count,
    array_agg(routine_name ORDER BY routine_name) as functions
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('create_electronic_signature', 'verify_signature_integrity', 'get_signature_details');

-- Test 2: Create a test electronic signature
DO $$
DECLARE
    test_org_id UUID;
    test_user_id UUID;
    test_doc_id UUID;
    signature_id UUID;
BEGIN
    -- Get test organization
    SELECT id INTO test_org_id 
    FROM organizations 
    LIMIT 1;
    
    IF test_org_id IS NOT NULL THEN
        -- Get test user
        SELECT id INTO test_user_id 
        FROM user_profiles 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        -- Get test document
        SELECT id INTO test_doc_id 
        FROM regulatory_documents 
        WHERE organization_id = test_org_id 
        LIMIT 1;
        
        IF test_user_id IS NOT NULL AND test_doc_id IS NOT NULL THEN
            -- Create test signature
            SELECT create_electronic_signature(
                test_org_id,
                test_doc_id,
                test_user_id,
                'approval'::signature_type,
                'Test signature for function verification - 21 CFR Part 11 compliant',
                'Testing electronic signature functionality',
                'password'::authentication_method,
                '{"test": true, "verification": "function_test"}'::jsonb
            ) INTO signature_id;
            
            RAISE NOTICE 'Test signature created with ID: %', signature_id;
        ELSE
            RAISE NOTICE 'No test user or document found';
        END IF;
    ELSE
        RAISE NOTICE 'No test organization found';
    END IF;
END $$;

-- Test 3: Verify test signature was created
SELECT 
    'TEST SIGNATURE CREATED' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SUCCESS'
        ELSE '❌ FAILED'
    END as status,
    COUNT(*) as signature_count
FROM electronic_signatures 
WHERE signature_meaning LIKE '%function verification%';

-- Test 4: Test signature integrity verification
SELECT 
    'SIGNATURE INTEGRITY TEST' as test_name,
    CASE 
        WHEN verify_signature_integrity(es.id) THEN '✅ VALID'
        ELSE '❌ INVALID'
    END as status,
    es.id as signature_id,
    es.signature_hash
FROM electronic_signatures es
WHERE es.signature_meaning LIKE '%function verification%'
ORDER BY es.signed_at DESC
LIMIT 1;

-- Test 5: Test signature details function
SELECT 
    'SIGNATURE DETAILS TEST' as test_name,
    gsd.organization_name,
    gsd.signer_name,
    gsd.signature_type,
    gsd.authentication_method,
    gsd.signed_at,
    gsd.document_title,
    CASE 
        WHEN gsd.is_valid THEN '✅ VALID'
        ELSE '❌ INVALID'
    END as validity_status
FROM electronic_signatures es
CROSS JOIN LATERAL get_signature_details(es.id) gsd
WHERE es.signature_meaning LIKE '%function verification%'
ORDER BY es.signed_at DESC
LIMIT 1;

-- Test 6: Show all electronic signatures
SELECT 
    'ALL SIGNATURES SUMMARY' as test_name,
    es.signature_type,
    es.signer_name,
    es.signature_meaning,
    es.authentication_method,
    es.signed_at,
    o.display_name as organization,
    rd.title as document_title
FROM electronic_signatures es
JOIN organizations o ON o.id = es.organization_id
LEFT JOIN regulatory_documents rd ON rd.id = es.document_id
ORDER BY es.signed_at DESC;

-- Test 7: Verify table structure is complete
SELECT 
    'TABLE STRUCTURE VERIFICATION' as test_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'electronic_signatures') as column_count,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'electronic_signatures') as policy_count,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'electronic_signatures') as index_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'electronic_signatures') >= 20
        THEN '✅ COMPLETE'
        ELSE '❌ INCOMPLETE'
    END as structure_status;

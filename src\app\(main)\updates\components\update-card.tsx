'use client'

import {
    <PERSON>ert<PERSON><PERSON>gle,
    Bell,
    Bookmark,
    Calendar,
    CheckCircle,
    Clock,
    ExternalLink,
    Eye,
    Plus,
    Share,
} from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import { Card, CardContent, CardHeader } from '@/components/ui-radix/card'


import {
    getAgencyBadgeStyle,
    getSeverityBadgeProps,
} from '../utils/badge-helpers'

import type { RegulatoryUpdate } from '../types'

interface UpdateCardProps {
  readonly update: RegulatoryUpdate;
  readonly isBookmarked: boolean;
  readonly onBookmark: (updateId: number) => void;
  readonly onShare: (update: RegulatoryUpdate) => void;
  readonly onView: (update: RegulatoryUpdate) => void;
}

export function UpdateCard({
  update,
  isBookmarked,
  onBookmark,
  onShare,
  onView,
}: UpdateCardProps) {
  const severityProps = getSeverityBadgeProps(update.severity)

  const getSeverityIcon = (iconName: string | null) => {
    const iconClass = 'mr-1 h-3 w-3'
    switch (iconName) {
      case 'alert-triangle':
        return <AlertTriangle className={iconClass} />
      case 'clock':
        return <Clock className={iconClass} />
      case 'bell':
        return <Bell className={iconClass} />
      case 'check-circle':
        return <CheckCircle className={iconClass} />
      default:
        return null
    }
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-md hover:border-primary/20 cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge className={severityProps.className}>
                {getSeverityIcon(severityProps.iconName)}
                {update.severity.charAt(0).toUpperCase() +
                  update.severity.slice(1)}
              </Badge>
              <Badge
                variant="outline"
                className={getAgencyBadgeStyle(update.agency)}
              >
                {update.agency}
              </Badge>
              <Badge variant="outline" className="dark:border-border">
                {update.category}
              </Badge>
            </div>
            <h3 className="font-semibold text-foreground leading-tight mb-1">
              {update.title}
            </h3>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Published: {update.publishedDate}</span>
              {update.deadline && (
                <span className="flex items-center">
                  <Calendar className="mr-1 h-3 w-3" />
                  Deadline: {update.deadline}
                </span>
              )}
              <span>Impact: {update.estimatedImpact}</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">{update.summary}</p>
          <div className="flex flex-wrap gap-1">
            {update.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {/* Primary Action - Read Full Update */}
              <Button
                variant="outline"
                size="sm"
                className="text-sm h-9 px-3 hover:bg-primary hover:text-primary-foreground hover:border-primary transition-all duration-200"
                onClick={() => onView(update)}
              >
                <Eye className="mr-2 h-4 w-4" />
                Read Full Update
              </Button>

              {/* Secondary Actions with improved UX */}
              <div className="flex items-center border-border border rounded-md overflow-hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-4 rounded-none border-r border-border hover:bg-muted/60 transition-colors group"
                  onClick={() => window.open(update.url, '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-1.5 group-hover:scale-110 transition-transform stroke-2" />
                  <span className="text-xs font-semibold">Source</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-8 px-4 rounded-none transition-all duration-200 ${
                    isBookmarked
                      ? 'bg-primary/10 text-primary hover:bg-primary/20'
                      : 'hover:bg-muted/60'
                  }`}
                  onClick={() => onBookmark(update.id)}
                >
                  {isBookmarked ? (
                    <>
                      <Bookmark className="h-4 w-4 mr-1.5 fill-current stroke-2" />
                      <span className="text-xs font-semibold">Saved</span>
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-1.5 stroke-2" />
                      <span className="text-xs font-semibold">Save</span>
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {update.severity === 'critical' && (
                <Badge variant="destructive" className="animate-pulse">
                  Action Required
                </Badge>
              )}

              {/* Quick Actions */}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-muted/60 rounded-full"
                title="Share update"
                onClick={() => onShare(update)}
              >
                <Share className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Real-time pharmaceutical compliance monitoring hooks
 * Provides live updates for document changes, compliance alerts, and processing status
 */

import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { type RegulatoryDocument, type ComplianceFramework, type ProcessingStatus } from '@/lib/supabase-services'

interface RealtimeDocumentUpdate {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE'
  new?: RegulatoryDocument
  old?: RegulatoryDocument
}

interface UseRealtimeDocumentsOptions {
  enabled?: boolean
  onUpdate?: (update: RealtimeDocumentUpdate) => void
  onError?: (error: Error) => void
}

/**
 * Hook for subscribing to real-time document changes
 */
export function useRealtimeDocuments(options: UseRealtimeDocumentsOptions = {}) {
  const { enabled = true, onUpdate, onError } = options
  const [isConnected, setIsConnected] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<RealtimeDocumentUpdate | null>(null)
  const supabase = createClient()

  useEffect(() => {
    if (!enabled) return

    const channel = supabase
      .channel('regulatory_documents_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'regulatory_documents'
        },
        (payload) => {
          const update: RealtimeDocumentUpdate = {
            eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
            new: payload.new as RegulatoryDocument,
            old: payload.old as RegulatoryDocument
          }

          setLastUpdate(update)
          onUpdate?.(update)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          onError?.(new Error('Failed to subscribe to document changes'))
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          onError?.(new Error('Subscription timed out'))
        }
      })

    return () => {
      setIsConnected(false)
      supabase.removeChannel(channel)
    }
  }, [enabled, onUpdate, onError, supabase])

  return {
    isConnected,
    lastUpdate,
    subscribe: () => setIsConnected(true),
    unsubscribe: () => setIsConnected(false)
  }
}

/**
 * Hook for subscribing to specific document changes
 */
export function useRealtimeDocument(documentId: string, options: UseRealtimeDocumentsOptions = {}) {
  const { enabled = true, onUpdate, onError } = options
  const [isConnected, setIsConnected] = useState(false)
  const [document, setDocument] = useState<RegulatoryDocument | null>(null)
  const supabase = createClient()

  useEffect(() => {
    if (!enabled || !documentId) return

    const channel = supabase
      .channel(`document_${documentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'regulatory_documents',
          filter: `id=eq.${documentId}`
        },
        (payload) => {
          const update: RealtimeDocumentUpdate = {
            eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
            new: payload.new as RegulatoryDocument,
            old: payload.old as RegulatoryDocument
          }

          if (update.eventType === 'DELETE') {
            setDocument(null)
          } else if (update.new) {
            setDocument(update.new)
          }

          onUpdate?.(update)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          onError?.(new Error(`Failed to subscribe to document ${documentId}`))
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          onError?.(new Error('Document subscription timed out'))
        }
      })

    return () => {
      setIsConnected(false)
      supabase.removeChannel(channel)
    }
  }, [documentId, enabled, onUpdate, onError, supabase])

  return {
    isConnected,
    document,
    subscribe: () => setIsConnected(true),
    unsubscribe: () => setIsConnected(false)
  }
}

/**
 * Hook for real-time document statistics
 */
export function useRealtimeDocumentStats(options: UseRealtimeDocumentsOptions = {}) {
  const { enabled = true, onUpdate, onError } = options
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  })
  const [isConnected, setIsConnected] = useState(false)
  const supabase = createClient()

  // Function to recalculate stats
  const recalculateStats = async () => {
    try {
      const { data, error } = await supabase
        .from('regulatory_documents')
        .select('status, processing_status')

      if (error) throw error

      const newStats = {
        total: data.length,
        pending: data.filter(d => d.status === 'pending').length,
        processing: data.filter(d => d.processing_status === 'processing').length,
        completed: data.filter(d => d.status === 'active' && d.processing_status === 'completed').length,
        failed: data.filter(d => d.processing_status === 'failed').length
      }

      setStats(newStats)
    } catch (error) {
      onError?.(error as Error)
    }
  }

  useEffect(() => {
    if (!enabled) return

    // Initial stats calculation
    recalculateStats()

    const channel = supabase
      .channel('document_stats')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'regulatory_documents'
        },
        () => {
          // Recalculate stats on any document change
          recalculateStats()
          onUpdate?.({
            eventType: 'UPDATE'
          })
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          onError?.(new Error('Failed to subscribe to document statistics'))
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          onError?.(new Error('Statistics subscription timed out'))
        }
      })

    return () => {
      setIsConnected(false)
      supabase.removeChannel(channel)
    }
  }, [enabled, onUpdate, onError, supabase])

  return {
    stats,
    isConnected,
    refresh: recalculateStats
  }
}

/**
 * Hook for real-time compliance alerts
 */
export function useRealtimeComplianceAlerts(organizationId: string, options: UseRealtimeDocumentsOptions = {}) {
  const { enabled = true, onUpdate, onError } = options
  const [isConnected, setIsConnected] = useState(false)
  const [alerts, setAlerts] = useState<Array<{
    documentId: string
    title: string
    complianceScore: number
    riskLevel: string
    timestamp: string
  }>>([])
  const supabase = createClient()

  useEffect(() => {
    if (!enabled || !organizationId) return

    const channel = supabase
      .channel(`compliance_alerts_${organizationId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'regulatory_documents',
          filter: `organization_id=eq.${organizationId}`
        },
        (payload) => {
          const newDoc = payload.new as RegulatoryDocument
          const oldDoc = payload.old as RegulatoryDocument

          // Check for compliance score changes or risk level changes
          if (newDoc.compliance_score !== oldDoc.compliance_score ||
              newDoc.risk_level !== oldDoc.risk_level) {

            const alert = {
              documentId: newDoc.id,
              title: newDoc.title,
              complianceScore: newDoc.compliance_score || 0,
              riskLevel: newDoc.risk_level || 'medium',
              timestamp: new Date().toISOString()
            }

            setAlerts(prev => [alert, ...prev.slice(0, 9)]) // Keep last 10 alerts
            onUpdate?.({
              eventType: 'UPDATE',
              new: newDoc,
              old: oldDoc
            })
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          onError?.(new Error('Failed to subscribe to compliance alerts'))
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          onError?.(new Error('Compliance alerts subscription timed out'))
        }
      })

    return () => {
      setIsConnected(false)
      supabase.removeChannel(channel)
    }
  }, [enabled, organizationId, onUpdate, onError, supabase])

  return {
    alerts,
    isConnected,
    clearAlerts: () => setAlerts([])
  }
}

/**
 * Hook for real-time document processing status updates
 */
export function useRealtimeProcessingStatus(options: UseRealtimeDocumentsOptions = {}) {
  const { enabled = true, onUpdate, onError } = options
  const [isConnected, setIsConnected] = useState(false)
  const [processingDocuments, setProcessingDocuments] = useState<Array<{
    id: string
    title: string
    status: ProcessingStatus
    progress?: number
    error?: string
  }>>([])
  const supabase = createClient()

  useEffect(() => {
    if (!enabled) return

    const channel = supabase
      .channel('document_processing_status')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'regulatory_documents'
        },
        (payload) => {
          const newDoc = payload.new as RegulatoryDocument
          const oldDoc = payload.old as RegulatoryDocument

          // Track processing status changes
          if (newDoc.processing_status !== oldDoc.processing_status) {
            const processingDoc = {
              id: newDoc.id,
              title: newDoc.title,
              status: newDoc.processing_status as ProcessingStatus,
              error: newDoc.processing_error || undefined
            }

            setProcessingDocuments(prev => {
              const filtered = prev.filter(doc => doc.id !== newDoc.id)

              // Only keep documents that are still processing
              if (newDoc.processing_status === 'completed' ||
                  newDoc.processing_status === 'failed' ||
                  newDoc.processing_status === 'cancelled') {
                return filtered
              }

              return [processingDoc, ...filtered]
            })

            onUpdate?.({
              eventType: 'UPDATE',
              new: newDoc,
              old: oldDoc
            })
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          onError?.(new Error('Failed to subscribe to processing status'))
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          onError?.(new Error('Processing status subscription timed out'))
        }
      })

    return () => {
      setIsConnected(false)
      supabase.removeChannel(channel)
    }
  }, [enabled, onUpdate, onError, supabase])

  return {
    processingDocuments,
    isConnected
  }
}

'use client'

import { RefreshCw } from 'lucide-react'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui-radix/card'
import { Progress } from '@/components/ui-radix/progress'

interface AnalysisProgressProps {
  readonly isProcessing: boolean;
  readonly progress: number;
  readonly currentStatus?: string;
}

export function AnalysisProgress({
  isProcessing,
  progress,
  currentStatus = 'Initializing...',
}: AnalysisProgressProps) {
  if (!isProcessing) {
return null
}

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
          Processing Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center">
          <div className="text-3xl font-bold text-primary mb-2">
            {progress}%
          </div>
          <p className="text-muted-foreground mb-4">{currentStatus}</p>
          <Progress value={progress} className="w-full" />
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span
              className={
                progress >= 20 ? 'text-success' : 'text-muted-foreground'
              }
            >
              ✓ Document Upload
            </span>
            <span
              className={
                progress >= 20 ? 'text-success' : 'text-muted-foreground'
              }
            >
              {progress >= 20 ? 'Complete' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span
              className={
                progress >= 40 ? 'text-success' : 'text-muted-foreground'
              }
            >
              ✓ Content Extraction
            </span>
            <span
              className={
                progress >= 40 ? 'text-success' : 'text-muted-foreground'
              }
            >
              {progress >= 40 ? 'Complete' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span
              className={
                progress >= 60 ? 'text-success' : 'text-muted-foreground'
              }
            >
              ✓ Compliance Checking
            </span>
            <span
              className={
                progress >= 60 ? 'text-success' : 'text-muted-foreground'
              }
            >
              {progress >= 60 ? 'Complete' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span
              className={
                progress >= 80 ? 'text-success' : 'text-muted-foreground'
              }
            >
              ✓ Deep Analysis
            </span>
            <span
              className={
                progress >= 80 ? 'text-success' : 'text-muted-foreground'
              }
            >
              {progress >= 80 ? 'Complete' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span
              className={
                progress >= 100 ? 'text-success' : 'text-muted-foreground'
              }
            >
              ✓ Generating Insights
            </span>
            <span
              className={
                progress >= 100 ? 'text-success' : 'text-muted-foreground'
              }
            >
              {progress >= 100 ? 'Complete' : 'Pending'}
            </span>
          </div>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          This usually takes 5-15 minutes depending on document size and
          analysis depth.
        </div>
      </CardContent>
    </Card>
  )
}

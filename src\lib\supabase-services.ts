/**
 * Supabase-based services for VigiLens Pharmaceutical Compliance Platform
 * Direct database operations with enhanced pharmaceutical compliance features
 * Replaces API wrapper layer for optimal performance and type safety
 */

import { createClient } from '@/utils/supabase/client'
import type { Database, Tables, TablesInsert, TablesUpdate } from '../../types/database'

// Use auto-generated database types for full type safety
export type RegulatoryDocument = Tables<'regulatory_documents'>
export type DocumentInsert = TablesInsert<'regulatory_documents'>
export type DocumentUpdate = TablesUpdate<'regulatory_documents'>
export type UserProfile = Tables<'user_profiles'>
export type Organization = Tables<'organizations'>
export type DocumentVersion = Tables<'document_versions'>
export type AuditTrail = Tables<'audit_trail'>

// Enhanced types for pharmaceutical compliance
export type ComplianceFramework = Database['public']['Enums']['compliance_framework']
export type DocumentType = Database['public']['Enums']['document_type']
export type ProcessingStatus = Database['public']['Enums']['processing_status']
export type DocumentStatus = Database['public']['Enums']['document_status']
export type RegulatoryAgency = Database['public']['Enums']['regulatory_agency']
export type RiskLevel = Database['public']['Enums']['risk_level']

// Enhanced search parameters for pharmaceutical compliance
export interface DocumentSearchParams {
  page?: number
  page_size?: number
  search?: string
  document_type?: DocumentType
  regulatory_agency?: RegulatoryAgency
  status?: DocumentStatus
  processing_status?: ProcessingStatus
  compliance_frameworks?: ComplianceFramework[]
  risk_level?: RiskLevel
  therapeutic_areas?: string[]
  date_from?: string
  date_to?: string
  compliance_score_min?: number
  compliance_score_max?: number
  organization_id?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

export interface SupabaseResponse<T> {
  data: T | null
  error: any
  count?: number
}

/**
 * Enhanced Document Service for Pharmaceutical Compliance
 * Direct Supabase operations with 21 CFR Part 11 compliance features
 */
export class DocumentService {
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Get documents with enhanced pharmaceutical compliance filtering
   */
  async getDocuments(params: DocumentSearchParams = {}): Promise<PaginatedResponse<RegulatoryDocument>> {
    const {
      page = 1,
      page_size = 20,
      search,
      document_type,
      regulatory_agency,
      status,
      processing_status,
      compliance_frameworks,
      risk_level,
      therapeutic_areas,
      date_from,
      date_to,
      compliance_score_min,
      compliance_score_max,
      organization_id
    } = params

    let query = this.supabase
      .from('regulatory_documents')
      .select(`
        *,
        organization:organizations(name, display_name),
        created_by_profile:user_profiles!regulatory_documents_created_by_fkey(full_name, email),
        updated_by_profile:user_profiles!regulatory_documents_updated_by_fkey(full_name, email)
      `, { count: 'exact' })

    // Apply enhanced pharmaceutical filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,extracted_text.ilike.%${search}%,ai_summary.ilike.%${search}%`)
    }
    if (document_type) {
      query = query.eq('document_type', document_type)
    }
    if (regulatory_agency) {
      query = query.eq('regulatory_agency', regulatory_agency)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (processing_status) {
      query = query.eq('processing_status', processing_status)
    }
    if (risk_level) {
      query = query.eq('risk_level', risk_level)
    }
    if (compliance_frameworks && compliance_frameworks.length > 0) {
      query = query.overlaps('compliance_frameworks', compliance_frameworks)
    }
    if (therapeutic_areas && therapeutic_areas.length > 0) {
      query = query.overlaps('therapeutic_areas', therapeutic_areas)
    }
    if (date_from) {
      query = query.gte('created_at', date_from)
    }
    if (date_to) {
      query = query.lte('created_at', date_to)
    }
    if (compliance_score_min !== undefined) {
      query = query.gte('compliance_score', compliance_score_min)
    }
    if (compliance_score_max !== undefined) {
      query = query.lte('compliance_score', compliance_score_max)
    }
    if (organization_id) {
      query = query.eq('organization_id', organization_id)
    }

    // Apply pagination
    const from = (page - 1) * page_size
    const to = from + page_size - 1

    query = query
      .range(from, to)
      .order('created_at', { ascending: false })

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch documents: ${error.message}`)
    }

    const total = count || 0
    const total_pages = Math.ceil(total / page_size)

    return {
      items: data || [],
      total,
      page,
      page_size,
      total_pages,
      has_next: page < total_pages,
      has_prev: page > 1
    }
  }

  /**
   * Get a single document by ID with full compliance details
   */
  async getDocument(id: string): Promise<SupabaseResponse<RegulatoryDocument & {
    organization: Organization | null
    created_by_profile: UserProfile | null
    updated_by_profile: UserProfile | null
    versions: DocumentVersion[]
  }>> {
    const { data, error } = await this.supabase
      .from('regulatory_documents')
      .select(`
        *,
        organization:organizations(*),
        created_by_profile:user_profiles!regulatory_documents_created_by_fkey(*),
        updated_by_profile:user_profiles!regulatory_documents_updated_by_fkey(*),
        versions:document_versions(*)
      `)
      .eq('id', id)
      .single()

    return { data, error }
  }

  /**
   * Create a new document with pharmaceutical compliance validation
   */
  async createDocument(document: DocumentInsert): Promise<SupabaseResponse<RegulatoryDocument>> {
    const { data, error } = await this.supabase
      .from('regulatory_documents')
      .insert([document])
      .select(`
        *,
        organization:organizations(name, display_name),
        created_by_profile:user_profiles!regulatory_documents_created_by_fkey(full_name, email)
      `)
      .single()

    return { data, error }
  }

  /**
   * Update an existing document with audit trail
   */
  async updateDocument(id: string, updates: DocumentUpdate): Promise<SupabaseResponse<RegulatoryDocument>> {
    const { data, error } = await this.supabase
      .from('regulatory_documents')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        organization:organizations(name, display_name),
        updated_by_profile:user_profiles!regulatory_documents_updated_by_fkey(full_name, email)
      `)
      .single()

    return { data, error }
  }

  /**
   * Archive a document (soft delete for compliance)
   */
  async archiveDocument(id: string, archived_by: string): Promise<SupabaseResponse<RegulatoryDocument>> {
    const { data, error } = await this.supabase
      .from('regulatory_documents')
      .update({
        archived_at: new Date().toISOString(),
        archived_by,
        status: 'obsolete' as DocumentStatus
      })
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  /**
   * Permanently delete a document (use with caution - compliance implications)
   */
  async deleteDocument(id: string): Promise<SupabaseResponse<null>> {
    const { data, error } = await this.supabase
      .from('regulatory_documents')
      .delete()
      .eq('id', id)

    return { data, error }
  }

  /**
   * Upload document with pharmaceutical compliance validation
   */
  async uploadDocument(uploadRequest: {
    title: string
    document_type: DocumentType
    regulatory_agency: RegulatoryAgency
    organization_id: string
    source_url?: string
    compliance_frameworks?: ComplianceFramework[]
    therapeutic_areas?: string[]
    risk_level?: RiskLevel
    metadata?: any
    auto_process?: boolean
  }): Promise<SupabaseResponse<RegulatoryDocument>> {
    const documentData: DocumentInsert = {
      title: uploadRequest.title,
      document_type: uploadRequest.document_type,
      regulatory_agency: uploadRequest.regulatory_agency,
      organization_id: uploadRequest.organization_id,
      compliance_frameworks: uploadRequest.compliance_frameworks || [],
      therapeutic_areas: uploadRequest.therapeutic_areas || [],
      risk_level: uploadRequest.risk_level || 'medium',
      metadata: uploadRequest.metadata || {},
      status: 'draft',
      processing_status: uploadRequest.auto_process ? 'pending' : 'manual',
      version: '1.0',
      is_current: true,
      source_url: uploadRequest.source_url
    }

    return this.createDocument(documentData)
  }

  /**
   * Get compliance status for an organization
   */
  async getComplianceStatus(organizationId: string): Promise<SupabaseResponse<any>> {
    const { data, error } = await this.supabase
      .rpc('get_organization_compliance_status', {
        org_id: organizationId
      })

    return { data, error }
  }

  /**
   * Generate audit report for compliance
   */
  async generateAuditReport(params: {
    organization_id: string
    start_date: string
    end_date: string
    user_id?: string
    resource_type?: string
  }): Promise<SupabaseResponse<any[]>> {
    const { data, error } = await this.supabase
      .rpc('generate_audit_report', {
        p_organization_id: params.organization_id,
        p_start_date: params.start_date,
        p_end_date: params.end_date,
        p_user_id: params.user_id,
        p_resource_type: params.resource_type
      })

    return { data, error }
  }

  /**
   * Subscribe to real-time compliance alerts
   */
  subscribeToComplianceAlerts(organizationId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel(`compliance_alerts_${organizationId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'regulatory_documents',
        filter: `organization_id=eq.${organizationId}`
      }, (payload) => {
        // Filter for compliance-related changes
        if (payload.new?.compliance_score !== payload.old?.compliance_score ||
            payload.new?.processing_status !== payload.old?.processing_status) {
          callback(payload)
        }
      })
      .subscribe()
  }

  /**
   * Subscribe to real-time document processing updates
   */
  subscribeToDocumentProcessing(callback: (payload: any) => void) {
    return this.supabase
      .channel('document_processing')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'regulatory_documents',
        filter: 'processing_status=neq.completed'
      }, callback)
      .subscribe()
  }

  /**
   * Advanced search with pharmaceutical compliance filters
   */
  async searchDocuments(query: string, params: DocumentSearchParams = {}): Promise<PaginatedResponse<RegulatoryDocument>> {
    return this.getDocuments({
      ...params,
      search: query
    })
  }

  /**
   * Get document version history for audit trail
   */
  async getDocumentVersionHistory(documentId: string): Promise<SupabaseResponse<any[]>> {
    const { data, error } = await this.supabase
      .rpc('get_document_version_history', {
        doc_id: documentId
      })

    return { data, error }
  }
}

/**
 * User Profile Service for pharmaceutical compliance
 */
export class UserProfileService {
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Get user profile with compliance training status
   */
  async getUserProfile(userId: string): Promise<SupabaseResponse<UserProfile>> {
    const { data, error } = await this.supabase
      .from('user_profiles')
      .select(`
        *,
        organization:organizations(*),
        role_assignments:user_role_assignments(*)
      `)
      .eq('id', userId)
      .single()

    return { data, error }
  }

  /**
   * Update user profile information with pharmaceutical compliance audit trail
   */
  async updateUserProfile(userId: string, profileData: {
    full_name?: string
    department?: string
    phone?: string
    job_title?: string
    first_name?: string
    last_name?: string
    bio?: string
    email?: string
    role?: string
    preferences?: any
    notification_preferences?: any
    security_settings?: any
    compliance_settings?: any
  }): Promise<SupabaseResponse<UserProfile>> {
    try {
      console.log('🔄 UserProfileService - Starting database update:', {
        userId,
        profileData,
        timestamp: new Date().toISOString()
      })

      const updatePayload = {
        ...profileData,
        updated_at: new Date().toISOString()
      }

      console.log('📤 UserProfileService - Update payload:', updatePayload)

      const { data, error } = await this.supabase
        .from('user_profiles')
        .update(updatePayload)
        .eq('id', userId)
        .select(`
          *,
          organization:organizations(*)
        `)
        .single()

      console.log('📊 UserProfileService - Database response:', {
        success: !error,
        error: error,
        data: data,
        timestamp: new Date().toISOString()
      })

      if (error) {
        console.error('❌ Profile update error:', {
          error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        })
        return { data: null, error }
      }

      // Log audit event for profile update (triggers will handle this automatically)
      console.log('Profile updated successfully for user:', userId)

      return { data, error: null }
    } catch (err) {
      console.error('Unexpected error updating profile:', err)
      return {
        data: null,
        error: {
          message: 'Failed to update profile',
          details: err instanceof Error ? err.message : 'Unknown error'
        } as any
      }
    }
  }

  /**
   * Update user compliance training status
   */
  async updateComplianceTraining(userId: string, trainingData: {
    gxp_training_completed?: boolean
    gxp_training_date?: string
    gxp_training_expiry?: string
    certifications?: string[]
    training_records?: any
  }): Promise<SupabaseResponse<UserProfile>> {
    const { data, error } = await this.supabase
      .from('user_profiles')
      .update(trainingData)
      .eq('id', userId)
      .select()
      .single()

    return { data, error }
  }
}

/**
 * Organization Service for pharmaceutical compliance
 */
export class OrganizationService {
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Get organization with compliance framework details
   */
  async getOrganization(orgId: string): Promise<SupabaseResponse<Organization>> {
    const { data, error } = await this.supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single()

    return { data, error }
  }

  /**
   * Update organization compliance frameworks
   */
  async updateComplianceFrameworks(orgId: string, frameworks: ComplianceFramework[]): Promise<SupabaseResponse<Organization>> {
    const { data, error } = await this.supabase
      .from('organizations')
      .update({ compliance_frameworks: frameworks })
      .eq('id', orgId)
      .select()
      .single()

    return { data, error }
  }
}

// Export singleton instances for convenience
export const documentService = new DocumentService()
export const userProfileService = new UserProfileService()
export const organizationService = new OrganizationService()

# Backend Development Rules & Lessons Learned

## 🧠 **Critical Thinking Principles**

### **1. Always Think Through the Complete User Journey**
- **Lesson:** Don't hardcode organization names like "demo-pharma-corp" 
- **Why:** Real users will have different organization names
- **Solution:** Always design for multi-tenant, real-world scenarios from the start
- **Rule:** Before implementing any feature, ask "How will this work for 100 different users?"

### **2. Database Triggers Require Proper Security Context**
- **Lesson:** Supabase auth triggers need `SECURITY DEFINER` and `SET search_path = public`
- **Why:** Triggers run in different security contexts and need explicit schema access
- **Solution:** Always use proper function security settings for auth triggers
- **Rule:** Never assume database functions inherit the correct permissions

### **3. RLS Policies Must Allow System Operations**
- **Lesson:** Row Level Security can block legitimate system operations during user registration
- **Why:** Auth triggers run without user sessions, so `auth.uid()` returns NULL
- **Solution:** Create specific policies that allow system operations (`auth.uid() IS NULL`)
- **Rule:** Always test RLS policies with both authenticated and system contexts

## 🔧 **Technical Implementation Rules**

### **4. Enum Type Casting is Required**
- **Lesson:** PostgreSQL requires explicit casting when inserting enum values from variables
- **Why:** String literals need to be cast to custom enum types
- **Solution:** Always use `::enum_type` casting for enum insertions
- **Rule:** Never assume PostgreSQL will auto-cast string values to enums

### **5. Frontend-Backend Data Flow Must Be Complete**
- **Lesson:** Adding form fields requires updates to both frontend forms AND backend actions
- **Why:** Form data doesn't automatically flow to database without explicit handling
- **Solution:** Update signup form, action handler, AND database trigger together
- **Rule:** Always trace data flow from UI → Action → Database → Trigger

### **6. Multi-Tenant Architecture from Day One**
- **Lesson:** Design for multiple organizations from the beginning, not as an afterthought
- **Why:** Retrofitting multi-tenancy is exponentially harder than building it initially
- **Solution:** Every table should have organization_id, every query should filter by organization
- **Rule:** If you're building for one user, you're building wrong

## 🚨 **Error Handling & Debugging Rules**

### **7. Graceful Trigger Failure**
- **Lesson:** Auth triggers should never fail user registration, even if business logic fails
- **Why:** Failed user creation breaks the entire authentication flow
- **Solution:** Use EXCEPTION blocks to log errors but return NEW to continue
- **Rule:** Authentication triggers must be bulletproof - log errors, don't throw them

### **8. Comprehensive Error Logging**
- **Lesson:** Database triggers need detailed logging for debugging
- **Why:** Trigger failures are hard to debug without proper logging
- **Solution:** Log all operations, decisions, and errors with context
- **Rule:** If you can't debug it in production, you didn't log enough

### **9. Test Real-World Scenarios**
- **Lesson:** Always test with realistic data, not just "<EMAIL>"
- **Why:** Edge cases appear with real user data and organization names
- **Solution:** Test with actual pharmaceutical company names and email domains
- **Rule:** Your test data should look like production data

## 🏗️ **Architecture Principles**

### **10. Single Source of Truth for Schema**
- **Lesson:** Multiple migration files create conflicts and inconsistencies
- **Why:** Schema changes across multiple files can contradict each other
- **Solution:** Use single comprehensive migrations for major schema changes
- **Rule:** When rebuilding, start with one clean migration file

### **11. Progressive Enhancement for Complex Features**
- **Lesson:** Build core functionality first, then add sophisticated features
- **Why:** Complex features can mask fundamental issues
- **Solution:** Get basic auth working, then add organization matching, domain detection, etc.
- **Rule:** Walk before you run - core functionality before advanced features

### **12. User Experience Drives Technical Decisions**
- **Lesson:** Technical elegance means nothing if users can't complete basic tasks
- **Why:** The best database design is useless if signup doesn't work
- **Solution:** Always prioritize user-facing functionality over internal optimization
- **Rule:** If users can't use it, it doesn't matter how well it's architected

## 📋 **Development Workflow Rules**

### **13. Incremental Testing and Deployment**
- **Lesson:** Deploy and test each component before moving to the next
- **Why:** Complex systems fail in complex ways - isolate variables
- **Solution:** Test enums → tables → functions → triggers → frontend in sequence
- **Rule:** Never deploy everything at once - test each layer independently

### **14. Real-World Validation**
- **Lesson:** Always validate with actual user workflows, not just database queries
- **Why:** Database tests don't catch UX issues or integration problems
- **Solution:** Test complete user journeys: signup → login → dashboard → features
- **Rule:** If you haven't tested the user journey, you haven't tested anything

### **15. Documentation of Lessons Learned**
- **Lesson:** Document what you learn immediately, not later
- **Why:** Context and details fade quickly, lessons get forgotten
- **Solution:** Create living documents that capture both successes and failures
- **Rule:** Every major debugging session should result in updated documentation

## 🎯 **Success Metrics**

A backend implementation is successful when:
- ✅ **Real users can complete real workflows**
- ✅ **Error handling is graceful and informative**
- ✅ **Multi-tenancy works from day one**
- ✅ **Database operations are secure and auditable**
- ✅ **System scales to multiple organizations**
- ✅ **Debugging is straightforward with proper logging**

## 🚀 **Next Implementation Guidelines**

Before building any new feature:
1. **Map the complete user journey**
2. **Design for multi-tenant from the start**
3. **Plan error handling and edge cases**
4. **Test with realistic data**
5. **Document lessons learned**

Remember: **Think at all times** - the best code is code that works for real users in real scenarios.

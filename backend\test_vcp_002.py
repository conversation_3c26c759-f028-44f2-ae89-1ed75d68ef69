#!/usr/bin/env python3
"""
VCP_002 Implementation Test Suite
Tests the authentication system components individually.
"""

import sys
import os
from datetime import datetime, timezone
from uuid import UUID

# Add backend to path
sys.path.append('.')

def test_models():
    """Test authentication models."""
    print("🧪 Testing Authentication Models...")
    
    try:
        from auth.models import AuthUser, PharmaceuticalRole, MFAMethod
        
        # Create test user
        user = AuthUser(
            id=UUID('12345678-1234-5678-9012-123456789012'),
            email='<EMAIL>',
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.QUALITY_MANAGER,
            organization_id=UUID('12345678-1234-5678-9012-123456789013'),
            organization_name='Test Pharmaceutical Company',
            is_active=True,
            is_mfa_enabled=True,
            mfa_methods=[MFAMethod.TOTP]
        )
        
        assert user.email == '<EMAIL>'
        assert user.role == PharmaceuticalRole.QUALITY_MANAGER
        assert user.is_mfa_enabled is True
        
        print("✅ Authentication models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Authentication models failed: {e}")
        return False

def test_rbac():
    """Test Role-Based Access Control."""
    print("🧪 Testing RBAC System...")
    
    try:
        from auth.rbac import get_role_permissions, has_permission, PharmaceuticalPermissions
        from auth.models import PharmaceuticalRole
        
        # Test super admin permissions
        super_admin_perms = get_role_permissions(PharmaceuticalRole.SUPER_ADMIN)
        assert len(super_admin_perms) > 0
        
        # Test specific permission
        can_delete = has_permission(PharmaceuticalRole.SUPER_ADMIN, PharmaceuticalPermissions.DOCUMENT_DELETE)
        assert can_delete is True
        
        # Test viewer permissions (should be limited)
        viewer_perms = get_role_permissions(PharmaceuticalRole.VIEWER)
        can_viewer_delete = has_permission(PharmaceuticalRole.VIEWER, PharmaceuticalPermissions.DOCUMENT_DELETE)
        assert can_viewer_delete is False
        
        print(f"✅ RBAC system working correctly")
        print(f"   - Super admin has {len(super_admin_perms)} permissions")
        print(f"   - Viewer has {len(viewer_perms)} permissions")
        return True
        
    except Exception as e:
        print(f"❌ RBAC system failed: {e}")
        return False

def test_totp():
    """Test TOTP functionality."""
    print("🧪 Testing TOTP System...")
    
    try:
        from auth.mfa import TOTPGenerator
        
        # Test TOTP generation
        secret = "JBSWY3DPEHPK3PXP"
        totp = TOTPGenerator(secret)
        
        # Generate code
        code = totp.generate_totp()
        assert len(code) == 6
        assert code.isdigit()
        
        # Test verification
        verified = totp.verify_totp(code)
        assert verified is True
        
        # Test invalid code
        invalid_verified = totp.verify_totp("000000")
        assert invalid_verified is False
        
        print(f"✅ TOTP system working correctly")
        print(f"   - Generated code: {code}")
        print(f"   - Verification: {verified}")
        return True
        
    except Exception as e:
        print(f"❌ TOTP system failed: {e}")
        return False

def test_mfa_requirements():
    """Test MFA requirements system."""
    print("🧪 Testing MFA Requirements...")
    
    try:
        from auth.mfa import MFAManager
        from auth.models import AuthUser, PharmaceuticalRole
        
        # Create test users
        super_admin = AuthUser(
            id=UUID('12345678-1234-5678-9012-123456789012'),
            email='<EMAIL>',
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.SUPER_ADMIN,
            organization_id=UUID('12345678-1234-5678-9012-123456789013'),
            organization_name='Test Company',
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        
        viewer = AuthUser(
            id=UUID('12345678-1234-5678-9012-123456789014'),
            email='<EMAIL>',
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            role=PharmaceuticalRole.VIEWER,
            organization_id=UUID('12345678-1234-5678-9012-123456789013'),
            organization_name='Test Company',
            is_active=True,
            is_mfa_enabled=False,
            mfa_methods=[]
        )
        
        # Test MFA requirements
        manager = MFAManager(None)  # Mock client for testing
        
        # Super admin should require MFA
        admin_mfa_required = manager.is_mfa_required(super_admin)
        assert admin_mfa_required is True
        
        # Viewer should not require MFA by default
        viewer_mfa_required = manager.is_mfa_required(viewer)
        assert viewer_mfa_required is False
        
        # But viewer should require MFA for sensitive operations
        viewer_mfa_sensitive = manager.is_mfa_required(viewer, "document_sign")
        assert viewer_mfa_sensitive is True
        
        # Test supported methods
        admin_methods = manager.get_supported_mfa_methods(super_admin)
        viewer_methods = manager.get_supported_mfa_methods(viewer)
        
        assert "totp" in admin_methods
        assert "sms" in admin_methods
        assert len(viewer_methods) == 0
        
        print(f"✅ MFA requirements working correctly")
        print(f"   - Super admin MFA required: {admin_mfa_required}")
        print(f"   - Viewer MFA required: {viewer_mfa_required}")
        print(f"   - Viewer MFA for sensitive ops: {viewer_mfa_sensitive}")
        print(f"   - Admin methods: {admin_methods}")
        return True
        
    except Exception as e:
        print(f"❌ MFA requirements failed: {e}")
        return False

def test_backup_codes():
    """Test backup code generation."""
    print("🧪 Testing Backup Codes...")
    
    try:
        from auth.mfa import MFAManager
        
        manager = MFAManager(None)
        backup_codes = manager._generate_backup_codes()
        
        assert len(backup_codes) == 10
        assert all(len(code) == 9 for code in backup_codes)  # Format: XXXX-XXXX
        assert all('-' in code for code in backup_codes)
        
        # Test hashing
        hashed = manager._hash_backup_codes(backup_codes)
        assert isinstance(hashed, str)
        assert len(hashed) > 0
        
        print(f"✅ Backup codes working correctly")
        print(f"   - Generated {len(backup_codes)} codes")
        print(f"   - Example code: {backup_codes[0]}")
        return True
        
    except Exception as e:
        print(f"❌ Backup codes failed: {e}")
        return False

def main():
    """Run all VCP_002 tests."""
    print("🚀 VCP_002 Authentication System Test Suite")
    print("=" * 50)
    
    tests = [
        test_models,
        test_rbac,
        test_totp,
        test_mfa_requirements,
        test_backup_codes,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All VCP_002 components working correctly!")
        print("✅ Authentication system is ready for production")
        return True
    else:
        print("❌ Some tests failed - review implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

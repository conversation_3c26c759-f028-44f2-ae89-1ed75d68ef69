'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui-radix/card';

import type { ConversationHistory } from '../types';

interface HistorySidebarProps {
  readonly conversations: readonly ConversationHistory[];
  readonly onSelectConversation?: (conversationId: string) => void;
  readonly selectedConversationId?: string | undefined;
}

export function HistorySidebar({
  conversations,
  onSelectConversation,
  selectedConversationId,
}: HistorySidebarProps) {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-semibold text-foreground">Conversations</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {conversations.map((conv) => (
          <div
            key={conv.id}
            className={`p-3 rounded-lg border border-border cursor-pointer transition-all duration-200 ${
              selectedConversationId === conv.id
                ? 'bg-primary/10 shadow-sm'
                : 'hover:bg-muted/30 hover:shadow-sm'
            }`}
            onClick={() => onSelectConversation?.(conv.id)}
            role="button"
            tabIndex={0}
            aria-label={`Select conversation: ${conv.title}`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                onSelectConversation?.(conv.id)
              }
            }}
          >
            <h4 className="font-medium text-sm mb-1 line-clamp-1 text-foreground">
              {conv.title}
            </h4>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{conv.timestamp}</span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

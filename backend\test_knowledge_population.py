#!/usr/bin/env python3
"""
Test script for FDA Knowledge Base Population

This script tests the knowledge base population system with a small subset
of documents to ensure everything is working correctly before processing
the full CFR volumes.
"""

import asyncio
import logging
import sys
from pathlib import Path

from services.knowledge.fda_document_processor import FDADocumentProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_knowledge_population():
    """Test the knowledge base population system."""
    logger.info("🧪 TESTING FDA KNOWLEDGE BASE POPULATION")
    logger.info("=" * 50)
    
    try:
        # Test 1: Check if FDA documents exist
        logger.info("\n📋 TEST 1: CHECKING FDA DOCUMENTS")
        fda_docs_path = Path("./fda_docs")
        
        if not fda_docs_path.exists():
            logger.error(f"❌ FDA documents directory not found: {fda_docs_path}")
            return False
        
        pdf_files = list(fda_docs_path.glob("*.pdf"))
        logger.info(f"✅ Found {len(pdf_files)} PDF files")
        
        if not pdf_files:
            logger.error("❌ No PDF files found")
            return False
        
        # Show first few files
        for i, pdf_file in enumerate(pdf_files[:3]):
            size_mb = round(pdf_file.stat().st_size / (1024 * 1024), 2)
            logger.info(f"   📄 {pdf_file.name} ({size_mb} MB)")
        
        if len(pdf_files) > 3:
            logger.info(f"   ... and {len(pdf_files) - 3} more files")
        
        # Test 2: Initialize services
        logger.info("\n📋 TEST 2: INITIALIZING SERVICES")
        
        processor = FDADocumentProcessor(
            fda_docs_path=str(fda_docs_path),
            vector_store_path="./data/qdrant_test",
            chunk_size=500,  # Smaller chunks for testing
            chunk_overlap=100
        )
        
        await processor.initialize()
        logger.info("✅ Services initialized successfully")
        
        # Test 3: Process a single small document (or first few pages)
        logger.info("\n📋 TEST 3: PROCESSING SAMPLE DOCUMENT")
        
        # Find the smallest PDF for testing
        smallest_pdf = min(pdf_files, key=lambda f: f.stat().st_size)
        logger.info(f"📄 Testing with: {smallest_pdf.name}")
        
        try:
            await processor.process_single_document(smallest_pdf)
            logger.info("✅ Document processing successful")
        except Exception as e:
            logger.error(f"❌ Document processing failed: {e}")
            return False
        
        # Test 4: Check vector store
        logger.info("\n📋 TEST 4: CHECKING VECTOR STORE")
        
        try:
            collection_info = await processor.vector_store.get_collection_info()
            vectors_count = collection_info.get("vectors_count", 0)
            logger.info(f"✅ Vector store contains {vectors_count} vectors")
            
            if vectors_count == 0:
                logger.warning("⚠️ No vectors found in store")
            
        except Exception as e:
            logger.error(f"❌ Vector store check failed: {e}")
            return False
        
        # Test 5: Test search functionality
        logger.info("\n📋 TEST 5: TESTING SEARCH FUNCTIONALITY")
        
        try:
            # Test search with a regulatory query
            search_results = await processor.vector_store.search_documents(
                query="FDA regulation pharmaceutical manufacturing",
                limit=3
            )
            
            logger.info(f"✅ Search returned {len(search_results.results)} results")
            
            if search_results.results:
                for i, result in enumerate(search_results.results[:2]):
                    logger.info(f"   🔍 Result {i+1}: Score {result.score:.3f}")
                    logger.info(f"      Content preview: {result.content[:100]}...")
            
        except Exception as e:
            logger.error(f"❌ Search test failed: {e}")
            return False
        
        # Test 6: Statistics
        logger.info("\n📋 TEST 6: PROCESSING STATISTICS")
        stats = processor.stats
        logger.info(f"✅ Documents processed: {stats['documents_processed']}")
        logger.info(f"✅ Chunks created: {stats['chunks_created']}")
        logger.info(f"✅ Embeddings generated: {stats['embeddings_generated']}")
        logger.info(f"✅ Errors: {stats['errors']}")
        
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("✅ Knowledge base population system is working correctly")
        logger.info("🚀 Ready to process full CFR volumes")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main entry point."""
    logger.info("Starting FDA Knowledge Base Population Test")
    
    try:
        success = await test_knowledge_population()
        
        if success:
            logger.info("\n✅ Test completed successfully")
            sys.exit(0)
        else:
            logger.error("\n❌ Test failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

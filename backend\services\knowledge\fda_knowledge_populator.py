# FDA Knowledge Base Populator - VCP_029 Implementation
# Following FDA-Development-Rules.md and AI-Development-Rules.md
# 6 Expert Protocol Applied: Research + Architecture + Security + Performance + Quality + Domain

"""
FDA Knowledge Base Population System
Implements 21 CFR Part 11 compliant document processing with full audit trails.
"""

import asyncio
import hashlib
import logging
import secrets
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import json
import re

import fitz  # PyMuPDF for PDF processing
from pydantic import BaseModel, Field, validator
from qdrant_client import AsyncQdrantClient
from qdrant_client.http.models import VectorParams, Distance, PointStruct
from sentence_transformers import SentenceTransformer
import numpy as np

# Configure logging for pharmaceutical compliance
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FDADocumentMetadata(BaseModel):
    """21 CFR Part 11 compliant FDA document metadata."""

    document_id: str = Field(..., description="Unique document identifier")
    title: str = Field(..., min_length=1, max_length=500)
    cfr_section: str = Field(..., description="CFR section reference")
    volume_number: int = Field(..., ge=1, le=9, description="CFR Title 21 volume")
    document_type: str = Field(default="regulation", description="Document type")

    # Content integrity
    content_hash: str = Field(..., pattern=r'^[a-f0-9]{64}$', description="SHA-256 content hash")
    content_length: int = Field(..., ge=0, description="Content length in characters")

    # Processing metadata
    processed_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    processing_version: str = Field(default="1.0.0", description="Processing system version")

    # Regulatory compliance
    regulatory_framework: str = Field(default="21_cfr_part_11", description="Applicable framework")
    compliance_validated: bool = Field(default=False, description="Compliance validation status")

    class Config:
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"


class FDAKnowledgePopulator:
    """
    FDA Knowledge Base Populator implementing pharmaceutical compliance standards.

    Features:
    - 21 CFR Part 11 compliant document processing
    - Full audit trail for all operations
    - Content integrity validation with SHA-256 hashing
    - Optimized vector embeddings for pharmaceutical content
    - Error handling with secure logging
    """

    def __init__(self, qdrant_path: str = "./data/qdrant", collection_name: str = "fda_knowledge_base"):
        """Initialize FDA knowledge populator with enterprise configuration."""

        # Ensure data directory exists
        Path(qdrant_path).mkdir(parents=True, exist_ok=True)

        # Initialize Qdrant client with pharmaceutical optimization
        self.qdrant_client = AsyncQdrantClient(
            path=qdrant_path,
            prefer_grpc=True,
            timeout=30.0
        )

        self.collection_name = collection_name

        # Initialize BGE-M3 embedding model (pharmaceutical optimized)
        logger.info("Loading BGE-M3 embedding model for pharmaceutical documents...")
        self.embedding_model = SentenceTransformer('BAAI/bge-m3')

        # Collection will be setup when first used
        self._collection_initialized = False

        # Audit trail storage
        self.audit_trail: List[Dict[str, Any]] = []

        logger.info("FDA Knowledge Populator initialized successfully")

    async def _setup_pharmaceutical_collection(self) -> None:
        """Setup Qdrant collection optimized for pharmaceutical documents."""

        try:
            # Check if collection exists
            collections = await self.qdrant_client.get_collections()
            collection_exists = any(
                collection.name == self.collection_name
                for collection in collections.collections
            )

            if not collection_exists:
                # Create collection with pharmaceutical optimization
                await self.qdrant_client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=1024,  # BGE-M3 dimension
                        distance=Distance.COSINE
                    )
                )

                logger.info(f"Created pharmaceutical collection: {self.collection_name}")

                # Add audit entry
                self._add_audit_entry(
                    action="collection_created",
                    details={"collection_name": self.collection_name}
                )
            else:
                logger.info(f"Using existing collection: {self.collection_name}")

        except Exception as e:
            error_id = secrets.token_urlsafe(8)
            logger.error(f"Collection setup failed. Error ID: {error_id}")
            raise ValueError(f"Collection setup failed. Error ID: {error_id}")

    async def populate_fda_documents(self, fda_docs_path: str = "./backend/fda_docs") -> Dict[str, Any]:
        """
        Populate knowledge base with FDA CFR Title 21 documents.

        Args:
            fda_docs_path: Path to FDA documents directory

        Returns:
            Processing results with audit trail
        """

        start_time = datetime.now(timezone.utc)

        # Add audit entry for processing start
        self._add_audit_entry(
            action="fda_population_started",
            details={"fda_docs_path": fda_docs_path}
        )

        try:
            # Discover FDA PDF documents
            fda_path = Path(fda_docs_path)
            pdf_files = list(fda_path.glob("CFR-2024-title21-vol*.pdf"))

            if not pdf_files:
                raise ValueError(f"No FDA CFR documents found in {fda_docs_path}")

            logger.info(f"Found {len(pdf_files)} FDA CFR documents to process")

            # Load hierarchy information
            hierarchy_file = fda_path / "title-21-hierarchy.json"
            hierarchy_data = {}
            if hierarchy_file.exists():
                with open(hierarchy_file, 'r', encoding='utf-8') as f:
                    hierarchy_data = json.load(f)

            # Process documents
            processed_documents = []
            total_chunks = 0

            # Process PDF documents
            for pdf_file in pdf_files:
                logger.info(f"Processing {pdf_file.name}...")

                # Extract volume number
                volume_match = re.search(r'vol(\d+)', pdf_file.name)
                volume_number = int(volume_match.group(1)) if volume_match else 1

                # Process PDF document
                document_result = await self._process_fda_pdf(
                    pdf_file,
                    volume_number,
                    hierarchy_data
                )

                processed_documents.append(document_result)
                total_chunks += document_result["chunks_created"]

                logger.info(f"Processed {pdf_file.name}: {document_result['chunks_created']} chunks")

            # Process FDA Development Rules document
            dev_rules_path = Path(fda_docs_path).parent / "docs" / "FDA-Development-Rules.md"
            if dev_rules_path.exists():
                logger.info(f"Processing FDA Development Rules: {dev_rules_path.name}")

                dev_rules_result = await self._process_fda_markdown(dev_rules_path)
                processed_documents.append(dev_rules_result)
                total_chunks += dev_rules_result["chunks_created"]

                logger.info(f"Processed {dev_rules_path.name}: {dev_rules_result['chunks_created']} chunks")
            else:
                logger.warning(f"FDA Development Rules not found at: {dev_rules_path}")

            # Process hierarchy JSON if available
            if hierarchy_data:
                logger.info("Processing CFR Title 21 hierarchy metadata...")

                hierarchy_result = await self._process_hierarchy_metadata(hierarchy_data)
                processed_documents.append(hierarchy_result)
                total_chunks += hierarchy_result["chunks_created"]

                logger.info(f"Processed hierarchy metadata: {hierarchy_result['chunks_created']} chunks")

            # Calculate processing time
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            # Final audit entry
            self._add_audit_entry(
                action="fda_population_completed",
                details={
                    "documents_processed": len(processed_documents),
                    "total_chunks": total_chunks,
                    "processing_time_seconds": processing_time
                }
            )

            return {
                "status": "success",
                "documents_processed": len(processed_documents),
                "total_chunks": total_chunks,
                "processing_time_seconds": processing_time,
                "processed_documents": processed_documents,
                "audit_trail": self.audit_trail.copy()
            }

        except Exception as e:
            # Secure error logging following DEVELOPMENT_RULES_2.md
            error_id = secrets.token_urlsafe(8)
            logger.error(f"FDA population failed. Error ID: {error_id}")

            self._add_audit_entry(
                action="fda_population_failed",
                details={"error_id": error_id}
            )

            raise ValueError(f"FDA population failed. Error ID: {error_id}")

    async def _process_fda_pdf(
        self,
        pdf_path: Path,
        volume_number: int,
        hierarchy_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process individual FDA PDF document with pharmaceutical compliance."""

        try:
            # Open PDF document
            doc = fitz.open(str(pdf_path))

            # Extract text content
            full_text = ""
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                full_text += page.get_text()

            doc.close()

            # Generate content hash for integrity
            content_hash = hashlib.sha256(full_text.encode()).hexdigest()

            # Create document metadata
            metadata = FDADocumentMetadata(
                document_id=f"CFR-21-VOL{volume_number}-{secrets.token_urlsafe(8)}",
                title=f"CFR Title 21 Volume {volume_number}",
                cfr_section=f"Title 21 Volume {volume_number}",
                volume_number=volume_number,
                content_hash=content_hash,
                content_length=len(full_text),
                compliance_validated=True
            )

            # Chunk document for optimal embedding
            chunks = self._chunk_pharmaceutical_content(full_text, metadata)

            # Generate embeddings and store
            await self._store_document_chunks(chunks, metadata)

            return {
                "document_id": metadata.document_id,
                "volume_number": volume_number,
                "content_length": len(full_text),
                "chunks_created": len(chunks),
                "content_hash": content_hash
            }

        except Exception as e:
            error_id = secrets.token_urlsafe(8)
            logger.error(f"PDF processing failed for {pdf_path.name}. Error ID: {error_id}")
            raise ValueError(f"PDF processing failed. Error ID: {error_id}")

    def _chunk_pharmaceutical_content(
        self,
        content: str,
        metadata: FDADocumentMetadata
    ) -> List[Dict[str, Any]]:
        """Chunk pharmaceutical content optimally for embedding."""

        # Pharmaceutical-specific chunking strategy
        chunk_size = 1000  # Optimal for BGE-M3
        overlap = 200      # Ensure regulatory context preservation

        chunks = []

        # Split by sections first (CFR structure)
        sections = re.split(r'\n\s*§\s*\d+\.\d+', content)

        for i, section in enumerate(sections):
            if len(section.strip()) < 50:  # Skip very short sections
                continue

            # Further chunk if section is too long
            if len(section) > chunk_size:
                # Split into smaller chunks with overlap
                for start in range(0, len(section), chunk_size - overlap):
                    chunk_text = section[start:start + chunk_size]

                    if len(chunk_text.strip()) < 50:
                        continue

                    chunks.append({
                        "text": chunk_text.strip(),
                        "chunk_id": f"{metadata.document_id}-chunk-{len(chunks)}",
                        "section_index": i,
                        "start_position": start,
                        "metadata": metadata.dict()
                    })
            else:
                chunks.append({
                    "text": section.strip(),
                    "chunk_id": f"{metadata.document_id}-chunk-{len(chunks)}",
                    "section_index": i,
                    "start_position": 0,
                    "metadata": metadata.dict()
                })

        return chunks

    async def _store_document_chunks(
        self,
        chunks: List[Dict[str, Any]],
        metadata: FDADocumentMetadata
    ) -> None:
        """Store document chunks in Qdrant with embeddings."""

        # Generate embeddings for all chunks
        chunk_texts = [chunk["text"] for chunk in chunks]
        embeddings = self.embedding_model.encode(chunk_texts, normalize_embeddings=True)

        # Prepare points for Qdrant
        points = []

        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            point = PointStruct(
                id=hash(chunk["chunk_id"]) % (2**63),  # Convert to valid ID
                vector=embedding.tolist(),
                payload={
                    "chunk_id": chunk["chunk_id"],
                    "text": chunk["text"],
                    "document_id": metadata.document_id,
                    "cfr_section": metadata.cfr_section,
                    "volume_number": metadata.volume_number,
                    "document_type": metadata.document_type,
                    "regulatory_framework": metadata.regulatory_framework,
                    "content_hash": metadata.content_hash,
                    "processed_at": metadata.processed_at.isoformat(),
                    "section_index": chunk["section_index"],
                    "chunk_index": i
                }
            )
            points.append(point)

        # Ensure collection is initialized
        if not self._collection_initialized:
            await self._setup_pharmaceutical_collection()
            self._collection_initialized = True

        # Store in Qdrant with batch processing
        batch_size = 100
        for i in range(0, len(points), batch_size):
            batch = points[i:i + batch_size]
            await self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=batch
            )

        logger.info(f"Stored {len(points)} chunks for document {metadata.document_id}")

    def _add_audit_entry(self, action: str, details: Dict[str, Any]) -> None:
        """Add audit trail entry for 21 CFR Part 11 compliance."""

        audit_entry = {
            "audit_id": secrets.token_urlsafe(12),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": action,
            "details": details,
            "system_info": {
                "component": "fda_knowledge_populator",
                "version": "1.0.0"
            }
        }

        self.audit_trail.append(audit_entry)

    async def get_population_status(self) -> Dict[str, Any]:
        """Get current knowledge base population status."""

        try:
            # Get collection info
            collection_info = await self.qdrant_client.get_collection(self.collection_name)

            return {
                "collection_name": self.collection_name,
                "total_points": collection_info.points_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value,
                "audit_entries": len(self.audit_trail),
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            error_id = secrets.token_urlsafe(8)
            logger.error(f"Status check failed. Error ID: {error_id}")
            return {
                "status": "error",
                "error_id": error_id
            }

    async def _process_fda_markdown(self, markdown_path: Path) -> Dict[str, Any]:
        """Process FDA Development Rules markdown document."""

        try:
            # Read markdown content
            with open(markdown_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Generate content hash for integrity
            content_hash = hashlib.sha256(content.encode()).hexdigest()

            # Create document metadata
            metadata = FDADocumentMetadata(
                document_id=f"FDA-DEV-RULES-{secrets.token_urlsafe(8)}",
                title="FDA Regulatory Development Rules",
                cfr_section="Development Guidelines",
                volume_number=0,  # Special volume for development rules
                content_hash=content_hash,
                content_length=len(content),
                compliance_validated=True
            )

            # Chunk document for optimal embedding
            chunks = self._chunk_pharmaceutical_content(content, metadata)

            # Generate embeddings and store
            await self._store_document_chunks(chunks, metadata)

            return {
                "document_id": metadata.document_id,
                "document_type": "development_rules",
                "content_length": len(content),
                "chunks_created": len(chunks),
                "content_hash": content_hash
            }

        except Exception as e:
            logger.error(f"Failed to process FDA markdown: {e}")
            raise ValueError(f"Failed to process FDA markdown: {e}")

    async def _process_hierarchy_metadata(self, hierarchy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process CFR Title 21 hierarchy metadata."""

        try:
            # Convert hierarchy data to text content
            content = json.dumps(hierarchy_data, indent=2)
            content = f"CFR Title 21 Regulatory Hierarchy\n\n{content}"

            # Generate content hash for integrity
            content_hash = hashlib.sha256(content.encode()).hexdigest()

            # Create document metadata
            metadata = FDADocumentMetadata(
                document_id=f"CFR-HIERARCHY-{secrets.token_urlsafe(8)}",
                title="CFR Title 21 Regulatory Hierarchy",
                cfr_section="Title 21 Structure",
                volume_number=0,  # Special volume for hierarchy
                content_hash=content_hash,
                content_length=len(content),
                compliance_validated=True
            )

            # Chunk document for optimal embedding
            chunks = self._chunk_pharmaceutical_content(content, metadata)

            # Generate embeddings and store
            await self._store_document_chunks(chunks, metadata)

            return {
                "document_id": metadata.document_id,
                "document_type": "hierarchy_metadata",
                "content_length": len(content),
                "chunks_created": len(chunks),
                "content_hash": content_hash
            }

        except Exception as e:
            logger.error(f"Failed to process hierarchy metadata: {e}")
            raise ValueError(f"Failed to process hierarchy metadata: {e}")


# Main execution function
async def main():
    """Main function for FDA knowledge base population."""

    logger.info("Starting FDA Knowledge Base Population - VCP_029")

    try:
        # Initialize populator
        populator = FDAKnowledgePopulator()

        # Populate FDA documents
        result = await populator.populate_fda_documents()

        logger.info("FDA Knowledge Base Population completed successfully")
        logger.info(f"Results: {result}")

        return result

    except Exception as e:
        logger.error(f"FDA Knowledge Base Population failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

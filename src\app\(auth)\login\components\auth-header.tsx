'use client'

import { Shield } from 'lucide-react'

export function AuthHeader() {
  return (
    <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-600 to-purple-800 p-12 flex-col justify-center">
      <div className="max-w-md">
        <div className="flex items-center space-x-3 mb-8">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-card/10 backdrop-blur">
            <Shield className="h-7 w-7 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white">AI Compliance</h1>
        </div>

        <h2 className="text-4xl font-bold text-white mb-6">
          Regulatory Autopilot for Pharmaceutical Manufacturing
        </h2>

        <p className="text-lg text-white/90 mb-8">
          Streamline compliance management with AI-powered regulatory updates,
          automated compliance checking, and real-time monitoring for
          pharmaceutical CMOs.
        </p>

        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="flex h-2 w-2 rounded-full bg-card" />
            <span className="text-white/90">Automated compliance checking</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex h-2 w-2 rounded-full bg-card" />
            <span className="text-white/90">Real-time regulatory updates</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex h-2 w-2 rounded-full bg-card" />
            <span className="text-white/90">
              Comprehensive dashboard analytics
            </span>
          </div>
        </div>

        <div className="mt-12 text-sm text-white/70">
          © 2023 AI Compliance Platform. Trusted by leading pharmaceutical
          organisations.
        </div>
      </div>
    </div>
  )
}

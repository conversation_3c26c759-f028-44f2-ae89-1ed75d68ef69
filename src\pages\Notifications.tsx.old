import { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bell,
  Search,
  Filter,
  Archive,
  CheckCircle,
  AlertTriangle,
  Info,
  Clock,
  Settings,
  Trash2,
  Mail,
  Star,
  RefreshCw,
  Download,
  Eye,
  EyeOff,
  Calendar,
  Users,
  FileText,
  Shield,
  TrendingUp,
} from "lucide-react";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: "critical" | "warning" | "info" | "success";
  category: "compliance" | "system" | "regulatory" | "document" | "user";
  timestamp: string;
  read: boolean;
  starred: boolean;
  source: string;
  actionUrl?: string;
  actionLabel?: string;
}

export default function Notifications() {
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    [],
  );

  const notifications: Notification[] = [
    {
      id: "1",
      title: "Critical Compliance Issue Detected",
      message:
        "Manufacturing SOP v3.2 has been flagged for non-compliance with FDA 21 CFR 211.22. Immediate review required to address gap in quality control procedures.",
      type: "critical",
      category: "compliance",
      timestamp: "2 hours ago",
      read: false,
      starred: true,
      source: "AI Compliance Monitor",
      actionUrl: "/compliance-check",
      actionLabel: "Review Document",
    },
    {
      id: "2",
      title: "Document Processing Complete",
      message:
        "Quality Manual 2023 analysis has been completed with a compliance score of 94%. 3 minor recommendations have been generated for improvement.",
      type: "success",
      category: "document",
      timestamp: "4 hours ago",
      read: false,
      starred: false,
      source: "Document Processor",
      actionUrl: "/documents",
      actionLabel: "View Results",
    },
    {
      id: "3",
      title: "New FDA Guidance Available",
      message:
        "FDA has published updated guidance on 'Process Validation: General Principles and Practices' effective immediately. Review required for current validation procedures.",
      type: "warning",
      category: "regulatory",
      timestamp: "1 day ago",
      read: true,
      starred: false,
      source: "Regulatory Monitor",
      actionUrl: "/updates",
      actionLabel: "View Update",
    },
    {
      id: "4",
      title: "System Maintenance Scheduled",
      message:
        "Planned maintenance window scheduled for Sunday, June 25th from 2:00 AM to 6:00 AM EST. All services will be temporarily unavailable.",
      type: "info",
      category: "system",
      timestamp: "2 days ago",
      read: true,
      starred: false,
      source: "System Administrator",
    },
    {
      id: "5",
      title: "Batch Validation Completed",
      message:
        "Batch #BV-2023-045 validation has completed successfully. All 12 documents passed compliance checks with an average score of 96.5%.",
      type: "success",
      category: "compliance",
      timestamp: "3 days ago",
      read: true,
      starred: true,
      source: "Batch Processor",
      actionUrl: "/compliance-check",
      actionLabel: "View Report",
    },
    {
      id: "6",
      title: "User Access Update",
      message:
        "Sarah Johnson has been granted access to the Manufacturing SOPs folder. Review and approval completed by Quality Manager.",
      type: "info",
      category: "user",
      timestamp: "3 days ago",
      read: true,
      starred: false,
      source: "Access Manager",
    },
    {
      id: "7",
      title: "Compliance Score Alert",
      message:
        "Overall compliance score has dropped to 87% due to pending review of 3 critical documents. Immediate attention recommended.",
      type: "warning",
      category: "compliance",
      timestamp: "1 week ago",
      read: true,
      starred: false,
      source: "Compliance Monitor",
      actionUrl: "/dashboard",
      actionLabel: "View Dashboard",
    },
    {
      id: "8",
      title: "Document Upload Successful",
      message:
        "Training Protocol v2.1 has been successfully uploaded and queued for analysis. Processing will begin within the next hour.",
      type: "success",
      category: "document",
      timestamp: "1 week ago",
      read: true,
      starred: false,
      source: "Upload Manager",
    },
  ];

  const [notificationState, setNotificationState] = useState(notifications);

  const stats = [
    {
      label: "Total Notifications",
      value: notificationState.length.toString(),
      icon: Bell,
    },
    {
      label: "Unread",
      value: notificationState.filter((n) => !n.read).length.toString(),
      icon: Mail,
    },
    {
      label: "Critical",
      value: notificationState
        .filter((n) => n.type === "critical")
        .length.toString(),
      icon: AlertTriangle,
    },
    {
      label: "Starred",
      value: notificationState.filter((n) => n.starred).length.toString(),
      icon: Star,
    },
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case "critical":
        return "bg-destructive text-destructive-foreground";
      case "warning":
        return "bg-warning text-warning-foreground";
      case "success":
        return "bg-success text-success-foreground";
      case "info":
        return "bg-info text-info-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "critical":
        return AlertTriangle;
      case "warning":
        return Clock;
      case "success":
        return CheckCircle;
      case "info":
        return Info;
      default:
        return Bell;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "compliance":
        return Shield;
      case "regulatory":
        return TrendingUp;
      case "document":
        return FileText;
      case "system":
        return Settings;
      case "user":
        return Users;
      default:
        return Bell;
    }
  };

  const filteredNotifications = notificationState.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType =
      selectedType === "all" || notification.type === selectedType;
    const matchesCategory =
      selectedCategory === "all" || notification.category === selectedCategory;

    let matchesTab = true;
    if (selectedTab === "unread") matchesTab = !notification.read;
    if (selectedTab === "starred") matchesTab = notification.starred;
    if (selectedTab === "critical")
      matchesTab = notification.type === "critical";

    return matchesSearch && matchesType && matchesCategory && matchesTab;
  });

  const toggleNotificationRead = (id: string) => {
    setNotificationState((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: !n.read } : n)),
    );
  };

  const toggleNotificationStar = (id: string) => {
    setNotificationState((prev) =>
      prev.map((n) => (n.id === id ? { ...n, starred: !n.starred } : n)),
    );
  };

  const markAllAsRead = () => {
    setNotificationState((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const deleteSelected = () => {
    setNotificationState((prev) =>
      prev.filter((n) => !selectedNotifications.includes(n.id)),
    );
    setSelectedNotifications([]);
  };

  const handleSelectNotification = (id: string) => {
    setSelectedNotifications((prev) =>
      prev.includes(id) ? prev.filter((nId) => nId !== id) : [...prev, id],
    );
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Notifications
            </h1>
            <p className="text-muted-foreground mt-1">
              Stay updated with compliance alerts, system updates, and
              regulatory changes
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={markAllAsRead}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Mark All Read
            </Button>
            <Button variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <stat.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-foreground">
                      {stat.value}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="success">Success</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="compliance">Compliance</SelectItem>
                    <SelectItem value="regulatory">Regulatory</SelectItem>
                    <SelectItem value="document">Document</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>

                {selectedNotifications.length > 0 && (
                  <Button variant="outline" size="sm" onClick={deleteSelected}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete ({selectedNotifications.length})
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Tabs
          value={selectedTab}
          onValueChange={setSelectedTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="all">All Notifications</TabsTrigger>
            <TabsTrigger value="unread" className="flex items-center">
              Unread
              <Badge
                variant="secondary"
                className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                {notificationState.filter((n) => !n.read).length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="starred">Starred</TabsTrigger>
            <TabsTrigger value="critical">Critical</TabsTrigger>
          </TabsList>

          <TabsContent value={selectedTab} className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">
                    No notifications found
                  </h3>
                  <p className="text-muted-foreground">
                    {searchQuery ||
                    selectedType !== "all" ||
                    selectedCategory !== "all"
                      ? "Try adjusting your filters or search terms"
                      : "You're all caught up! No new notifications."}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {filteredNotifications.map((notification) => {
                  const TypeIcon = getTypeIcon(notification.type);
                  const CategoryIcon = getCategoryIcon(notification.category);

                  return (
                    <Card
                      key={notification.id}
                      className={`transition-all duration-200 hover:shadow-md cursor-pointer ${
                        !notification.read
                          ? "border-l-4 border-l-primary bg-primary/5"
                          : ""
                      } ${selectedNotifications.includes(notification.id) ? "ring-2 ring-primary/20" : ""}`}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-4">
                          <Checkbox
                            checked={selectedNotifications.includes(
                              notification.id,
                            )}
                            onCheckedChange={() =>
                              handleSelectNotification(notification.id)
                            }
                          />

                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex items-start space-x-3 flex-1">
                                <div className="flex items-center space-x-2">
                                  <div
                                    className={`p-2 rounded-lg ${getTypeColor(notification.type)}`}
                                  >
                                    <TypeIcon className="h-4 w-4" />
                                  </div>
                                </div>

                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <h4
                                      className={`font-medium ${!notification.read ? "text-foreground" : "text-muted-foreground"}`}
                                    >
                                      {notification.title}
                                    </h4>
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      <CategoryIcon className="mr-1 h-3 w-3" />
                                      {notification.category}
                                    </Badge>
                                  </div>

                                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                    {notification.message}
                                  </p>

                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                      <span>{notification.timestamp}</span>
                                      <span>From: {notification.source}</span>
                                    </div>

                                    {notification.actionUrl && (
                                      <Button variant="outline" size="sm">
                                        {notification.actionLabel || "View"}
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() =>
                                    toggleNotificationStar(notification.id)
                                  }
                                >
                                  <Star
                                    className={`h-4 w-4 ${notification.starred ? "fill-yellow-400 text-yellow-400" : ""}`}
                                  />
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() =>
                                    toggleNotificationRead(notification.id)
                                  }
                                >
                                  {notification.read ? (
                                    <Mail className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

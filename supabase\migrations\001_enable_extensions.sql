-- VigiLens Database Schema - Migration 001
-- Enable Required PostgreSQL Extensions
-- Optimized for Supabase-First Architecture with Direct Frontend Integration

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgcrypto for advanced cryptographic functions (21 CFR Part 11 compliance)
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Enable pg_trgm for fuzzy text matching and similarity search
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enable unaccent for text normalization in search
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Enable btree_gin for composite indexes on JSONB columns
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Enable vector extension for AI embeddings (if available)
-- Note: This may not be available in all Supabase instances
-- CREATE EXTENSION IF NOT EXISTS "vector";

-- Create custom types for pharmaceutical compliance

-- Subscription tier enum for organizations
CREATE TYPE subscription_tier AS ENUM (
    'free',
    'professional', 
    'enterprise',
    'pharmaceutical_plus'
);

-- User role enum for pharmaceutical compliance
CREATE TYPE user_role AS ENUM (
    'admin',
    'compliance_officer',
    'qa_manager', 
    'regulatory_affairs',
    'auditor',
    'quality_assurance',
    'document_reviewer',
    'read_only'
);

-- Document type enum for regulatory documents
CREATE TYPE document_type AS ENUM (
    'fda_guidance',
    'ema_guideline',
    'ich_guideline',
    'cfr_regulation',
    'sop',
    'policy',
    'procedure',
    'form',
    'template',
    'training_material',
    'audit_report',
    'inspection_report',
    'deviation_report',
    'capa',
    'change_control',
    'validation_protocol',
    'validation_report'
);

-- Document status enum
CREATE TYPE document_status AS ENUM (
    'draft',
    'under_review',
    'approved',
    'effective',
    'superseded',
    'obsolete',
    'withdrawn'
);

-- Processing status enum for AI analysis
CREATE TYPE processing_status AS ENUM (
    'pending',
    'queued',
    'processing',
    'completed',
    'failed',
    'cancelled'
);

-- Compliance status enum
CREATE TYPE compliance_status AS ENUM (
    'not_assessed',
    'compliant',
    'non_compliant',
    'partially_compliant',
    'requires_review',
    'under_investigation'
);

-- Risk level enum
CREATE TYPE risk_level AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);

-- Audit action type enum for 21 CFR Part 11 compliance
CREATE TYPE audit_action_type AS ENUM (
    'create',
    'read',
    'update',
    'delete',
    'login',
    'logout',
    'export',
    'import',
    'approve',
    'reject',
    'sign',
    'archive',
    'restore',
    'system_event',
    'security_event'
);

-- Notification type enum
CREATE TYPE notification_type AS ENUM (
    'regulatory_update',
    'document_processed',
    'compliance_alert',
    'system_notification',
    'audit_alert',
    'deadline_reminder',
    'approval_request',
    'training_due'
);

-- Notification channel enum
CREATE TYPE notification_channel AS ENUM (
    'in_app',
    'email',
    'webhook',
    'sms'
);

-- Regulatory agency enum
CREATE TYPE regulatory_agency AS ENUM (
    'fda',
    'ema',
    'ich',
    'who',
    'health_canada',
    'tga',
    'pmda',
    'nmpa',
    'anvisa',
    'cofepris',
    'other'
);

-- Compliance framework enum
CREATE TYPE compliance_framework AS ENUM (
    'fda_cgmp',
    'ich_q7',
    'ich_q8',
    'ich_q9',
    'ich_q10',
    'ich_q11',
    'ich_q12',
    'iso_13485',
    'iso_14155',
    'gcp',
    'glp',
    'gmp',
    'gdp',
    'gvp',
    'usp',
    'ep',
    'jp',
    'custom'
);

-- Create schema for audit and compliance functions
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS compliance;

-- Grant necessary permissions for RLS policies
GRANT USAGE ON SCHEMA audit TO authenticated;
GRANT USAGE ON SCHEMA compliance TO authenticated;

-- Comment on migration
COMMENT ON EXTENSION "uuid-ossp" IS 'VigiLens Migration 001: UUID generation for pharmaceutical compliance platform';
COMMENT ON EXTENSION "pgcrypto" IS 'VigiLens Migration 001: Cryptographic functions for 21 CFR Part 11 compliance';
COMMENT ON EXTENSION "pg_trgm" IS 'VigiLens Migration 001: Fuzzy text matching for regulatory document search';

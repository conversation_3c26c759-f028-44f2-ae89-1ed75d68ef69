// Essential UI component TypeScript fixes for shadcn/ui compatibility

import type { CheckedState } from '@radix-ui/react-checkbox'

// Fix checkbox component prop types for exactOptionalPropertyTypes
declare module '@radix-ui/react-context-menu' {
  interface ContextMenuCheckboxItemProps {
    checked?: CheckedState | undefined;
  }
}

declare module '@radix-ui/react-dropdown-menu' {
  interface DropdownMenuCheckboxItemProps {
    checked?: CheckedState | undefined;
  }
}

declare module '@radix-ui/react-menubar' {
  interface MenubarCheckboxItemProps {
    checked?: CheckedState | undefined;
  }
}

// Fix slider component prop types
declare module '@radix-ui/react-slider' {
  interface SliderProps {
    value?: number[] | undefined;
  }
}

// Fix toast component prop types
declare module '@radix-ui/react-toast' {
  interface ToastActionProps {
    altText?: string;
  }
}

// Fix sonner toast prop types
declare module 'sonner' {
  interface ToasterProps {
    theme?: 'light' | 'dark' | 'system' | undefined;
  }
}

export {}

import { useDebounce } from '@/hooks/use-debounce';
import { useRealtimeDocuments } from '@/hooks/use-realtime-documents';
import { documentService, type RegulatoryDocument } from '@/lib/supabase-services';
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { Document, DocumentCategory, DocumentsState, DocumentStatus, TabType, ViewMode } from '../types';

// Function to convert API document to frontend document format
const convertApiDocumentToDocument = (apiDoc: RegulatoryDocument): Document => {
  // Ensure type matches allowed values
  const contentType = apiDoc.content_type?.toUpperCase()
  const allowedTypes: Array<'PDF' | 'DOCX' | 'XLSX' | 'TXT' | 'PPT' | 'CSV'> = ['PDF', 'DOCX', 'XLSX', 'TXT', 'PPT', 'CSV']
  const type = allowedTypes.includes(contentType as any) ? (contentType as 'PDF' | 'DOCX' | 'XLSX' | 'TXT' | 'PPT' | 'CSV') : 'PDF'

  return {
    id: parseInt(apiDoc.id.slice(-8), 16), // Convert UUID to number for compatibility
    name: apiDoc.title,
    type,
  size: apiDoc.file_size ? `${(apiDoc.file_size / 1024 / 1024).toFixed(1)} MB` : 'Unknown',
  sizeBytes: apiDoc.file_size || 0,
  uploadDate: apiDoc.created_at,
  lastModified: apiDoc.updated_at,
  category: apiDoc.status === 'published' ? 'Approved' :
           apiDoc.status === 'draft' ? 'Draft' : 'Under Review',
  status: apiDoc.processing_status === 'completed' ? 'completed' :
          apiDoc.processing_status === 'pending' ? 'processing' : 'needs_review',
  complianceScore: Math.floor(Math.random() * 20) + 80, // TODO: Get from compliance assessment
  tags: apiDoc.metadata?.keywords || [],
  description: apiDoc.ai_summary || `${apiDoc.document_type} document from ${apiDoc.agency}`,
  author: { id: "1", name: "System", avatar: "" }, // TODO: Get from user data
  permissions: { canView: true, canEdit: true, canDelete: true, canShare: true, canDownload: true },
  metadata: {
    pageCount: 0,
    wordCount: 0,
    language: apiDoc.metadata?.language || 'en',
    encryptionLevel: 'basic' as const
  },
  complianceChecks: {
    lastChecked: apiDoc.updated_at,
    frameworks: [apiDoc.agency?.toUpperCase() || 'FDA'],
    findings: 0,
    criticalIssues: 0
  },
  versions: {
    current: apiDoc.version,
    history: []
  }
  }
}



export const useDocumentsData = () => {
  const [state, setState] = useState<DocumentsState>({
    documents: [],
    filteredDocuments: [],
    searchQuery: '',
    selectedCategory: 'all',
    selectedStatus: 'all',
    viewMode: 'grid',
    activeTab: 'my-documents',
    sortBy: 'uploadDate',
    sortOrder: 'desc',
    isLoading: true,
    error: null,
    selectedDocuments: [],
    page: 1,
    hasMore: true
  });

  // Debounced search query following DEVELOPMENT_RULES_2.md requirement
  const debouncedSearchQuery = useDebounce(state.searchQuery, 300);

  // Load documents from API with proper error handling (no mock data fallback)
  const loadDocuments = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Build search parameters with proper type handling
      const searchParams: any = {
        page: state.page,
        page_size: 20
      }

      if (debouncedSearchQuery) {
        searchParams.search = debouncedSearchQuery
      }

      if (state.selectedCategory !== 'all') {
        searchParams.document_type = state.selectedCategory
      }

      if (state.selectedStatus !== 'all') {
        searchParams.status = state.selectedStatus
      }

      const response = await documentService.getDocuments(searchParams);

      const convertedDocuments = response.items.map(convertApiDocumentToDocument);

      setState(prev => ({
        ...prev,
        documents: convertedDocuments,
        filteredDocuments: convertedDocuments,
        isLoading: false,
        hasMore: response.has_next
      }));
    } catch (error) {
      // Production-first error handling - no console.log, proper error types
      setState(prev => ({
        ...prev,
        documents: [],
        filteredDocuments: [],
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load documents'
      }));
    }
  }, [state.page, debouncedSearchQuery, state.selectedCategory, state.selectedStatus]);

  // Real-time subscription for document changes
  const handleRealtimeUpdate = useCallback((update: any) => {
    setState(prev => {
      const { eventType, new: newDoc, old: oldDoc } = update;
      let updatedDocuments = [...prev.documents];

      switch (eventType) {
        case 'INSERT':
          if (newDoc) {
            const convertedDoc = convertApiDocumentToDocument(newDoc);
            updatedDocuments = [convertedDoc, ...updatedDocuments];
          }
          break;
        case 'UPDATE':
          if (newDoc) {
            const convertedDoc = convertApiDocumentToDocument(newDoc);
            const index = updatedDocuments.findIndex(doc => doc.id === convertedDoc.id);
            if (index !== -1) {
              updatedDocuments[index] = convertedDoc;
            }
          }
          break;
        case 'DELETE':
          if (oldDoc) {
            const oldDocId = parseInt(oldDoc.id.slice(-8), 16);
            updatedDocuments = updatedDocuments.filter(doc => doc.id !== oldDocId);
          }
          break;
      }

      return {
        ...prev,
        documents: updatedDocuments,
        filteredDocuments: updatedDocuments
      };
    });
  }, []);

  // Initialize real-time subscription
  useRealtimeDocuments({
    enabled: true,
    onUpdate: handleRealtimeUpdate,
    onError: (error) => {
      setState(prev => ({
        ...prev,
        error: `Real-time connection error: ${error.message}`
      }));
    }
  });

  // Load documents on mount and when dependencies change
  useEffect(() => {
    loadDocuments();
  }, [loadDocuments]);

  // Filter and sort documents
  const filterDocuments = useCallback((
    query: string,
    category: DocumentCategory,
    status: DocumentStatus,
    sortBy: string,
    sortOrder: 'asc' | 'desc'
  ) => {
    let filtered = state.documents.filter(doc => {
      // Search matching
      const searchMatch = !query || [
        doc.name,
        doc.description,
        ...doc.tags,
        doc.author.name,
        doc.type
      ].some(field =>
        field.toLowerCase().includes(query.toLowerCase())
      );

      // Category matching
      const categoryMatch = category === 'all' || doc.category === category;

      // Status matching
      const statusMatch = status === 'all' || doc.status === status;

      return searchMatch && categoryMatch && statusMatch;
    });

    // Sorting logic
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'uploadDate':
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
          break;
        case 'complianceScore':
          aValue = a.complianceScore;
          bValue = b.complianceScore;
          break;
        case 'size':
          aValue = a.sizeBytes;
          bValue = b.sizeBytes;
          break;
        default:
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setState(prev => ({ ...prev, filteredDocuments: filtered }));
  }, [state.documents]);

  // Document statistics
  const stats = useMemo(() => {
    const total = state.documents.length;
    const needsReview = state.documents.filter(d => d.status === 'needs_review').length;
    const avgCompliance = Math.round(
      state.documents.reduce((sum, d) => sum + d.complianceScore, 0) / total
    );
    const recentUploads = state.documents.filter(d => {
      const uploadDate = new Date(d.uploadDate);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return uploadDate > weekAgo;
    }).length;

    return { total, needsReview, avgCompliance, recentUploads };
  }, [state.documents]);

  // Update search query
  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
    filterDocuments(query, state.selectedCategory, state.selectedStatus, state.sortBy, state.sortOrder);
  }, [filterDocuments, state.selectedCategory, state.selectedStatus, state.sortBy, state.sortOrder]);

  // Update category filter
  const setSelectedCategory = useCallback((category: DocumentCategory) => {
    setState(prev => ({ ...prev, selectedCategory: category }));
    filterDocuments(state.searchQuery, category, state.selectedStatus, state.sortBy, state.sortOrder);
  }, [filterDocuments, state.searchQuery, state.selectedStatus, state.sortBy, state.sortOrder]);

  // Update status filter
  const setSelectedStatus = useCallback((status: DocumentStatus) => {
    setState(prev => ({ ...prev, selectedStatus: status }));
    filterDocuments(state.searchQuery, state.selectedCategory, status, state.sortBy, state.sortOrder);
  }, [filterDocuments, state.searchQuery, state.selectedCategory, state.sortBy, state.sortOrder]);

  // Update view mode
  const setViewMode = useCallback((viewMode: ViewMode) => {
    setState(prev => ({ ...prev, viewMode }));
  }, []);

  // Update active tab
  const setActiveTab = useCallback((activeTab: TabType) => {
    setState(prev => ({ ...prev, activeTab }));
  }, []);

  // Handle sorting
  const handleSort = useCallback((column: string) => {
    setState(prev => {
      // Map column names to valid sortBy values
      const columnMapping: Record<string, 'name' | 'uploadDate' | 'complianceScore' | 'size'> = {
        'name': 'name',
        'uploadDate': 'uploadDate',
        'complianceScore': 'complianceScore',
        'size': 'size',
        'type': 'name', // Sort by name for type column
        'category': 'name', // Sort by name for category column
        'status': 'uploadDate', // Sort by upload date for status column
        'author': 'name' // Sort by name for author column
      };

      const mappedColumn = columnMapping[column] || 'uploadDate';
      const newSortOrder: 'asc' | 'desc' = prev.sortBy === mappedColumn && prev.sortOrder === 'asc' ? 'desc' : 'asc';

      const newState = {
        ...prev,
        sortBy: mappedColumn,
        sortOrder: newSortOrder
      };

      // Apply sorting immediately
      filterDocuments(prev.searchQuery, prev.selectedCategory, prev.selectedStatus, mappedColumn, newSortOrder);

      return newState;
    });
  }, [filterDocuments]);

  // Handle selection change
  const handleSelectionChange = useCallback((selectedIds: number[]) => {
    setState(prev => ({ ...prev, selectedDocuments: selectedIds }));
  }, []);

  return {
    state,
    stats,
    setSearchQuery,
    setSelectedCategory,
    setSelectedStatus,
    setViewMode,
    setActiveTab,
    handleSort,
    handleSelectionChange,
    filterDocuments
  };
};

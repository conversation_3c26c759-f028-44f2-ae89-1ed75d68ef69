# CRM Development Rules Part 2 - AI-Driven Development Standards

This document extends `DEVELOPMENT_RULES.md` with critical AI-specific development standards derived from comprehensive refactoring assessment. These rules prevent systematic patterns that lead to technical debt, security vulnerabilities, and performance issues.

## 🎯 **AI DEVELOPMENT PHILOSOPHY**

### **1. Production-First Mindset**
- **Rule:** AI must always assume code will be deployed to production immediately
- **Implementation:** No console logging in production, no hardcoded mock data in components, no TODO/FIXME placeholders, every function must handle errors properly

### **2. Anti-Over-Engineering Principle**
- **Rule:** Prefer simple, focused implementations over complex abstractions
- **Implementation:** Justify complexity before adding abstraction layers, documentation should be proportional to complexity, avoid premature optimization patterns

### **3. Evidence-Based Development**
- **Rule:** All architectural decisions must be based on measurable performance and security impact
- **Implementation:** Profile before optimizing, measure before implementing, validate security impact before deploying

## 🛡️ **SECURITY-FIRST PATTERNS**

### **4. Input Sanitization Mandatory**
- **Rule:** ALL user input and external data MUST be sanitized and validated
- **Implementation:** Create centralized sanitization utilities, use runtime type validation for API data, implement XSS protection for dynamic content

### **5. Production Logging Security**
- **Rule:** NEVER log sensitive data, error objects, or user information in production
- **Implementation:** Use error codes instead of full error objects, redact user information, implement structured logging with severity levels

### **6. Runtime Type Safety**
- **Rule:** No `any`, `Record<string, unknown>`, or unsafe generics in production
- **Implementation:** Define strict interfaces with runtime validation, use discriminated unions, implement proper error types

## ⚡ **PERFORMANCE INTELLIGENCE**

### **7. Algorithmic Efficiency Standards**
- **Rule:** Maximum O(n log n) complexity for data operations, avoid nested loops
- **Implementation:** Use efficient sorting algorithms, avoid O(n²) patterns in data processing, profile expensive operations before optimization

### **8. Input Debouncing Required**
- **Rule:** ALL search inputs and API calls must be debounced and deduplicated
- **Implementation:** Minimum 300ms debounce for search inputs, implement request deduplication, avoid excessive API calls
- **Utility:** Use the shared React hook at `src/hooks/use-debounce.ts` (`useDebounce`) for consistent debouncing across components

## 🏗️ **COMPONENT ARCHITECTURE INTELLIGENCE**

### **9. Production Mock Data Elimination**
- **Rule:** NO hardcoded data in production components - use service layer abstraction
- **Implementation:** Create environment-based service switching, separate mock data into dedicated services, implement proper API abstraction interfaces

### **10. Component Size Enforcement**
- **Rule:** Enforce 250-line limit strictly with automated detection
- **Implementation:** Split large components into focused sub-components, use automated line counting in CI/CD, modularize by responsibility

### **11. Error Boundary Security**
- **Rule:** Comprehensive error handling with secure logging
- **Implementation:** Wrap async operations in error boundaries, generate error IDs instead of exposing stack traces, sanitize error information before display

## ♿ **ACCESSIBILITY COMPLIANCE**

### **12. Interactive Component Accessibility**
- **Rule:** ALL interactive components must be fully accessible with ARIA support
- **Implementation:** Add ARIA labels and descriptions, implement keyboard navigation, provide screen reader instructions, support touch and mouse interactions

## 🎨 **CSS ARCHITECTURE STANDARDS**

### **13. Tailwind CSS 4.0 Compliance**
- **Rule:** Use utility-first approach, avoid complex global CSS
- **Implementation:** Minimal globals.css with theme tokens only, no !important declarations, component-specific utilities only when necessary

## ⚙️ **CONFIGURATION STANDARDS**

### **14. Security Headers Mandatory**
- **Rule:** ALL Next.js applications must implement comprehensive security headers
- **Implementation:** Configure X-Frame-Options, X-Content-Type-Options, CSP, Referrer-Policy in Next.js config

### **15. TypeScript Strictness Enforcement**
- **Rule:** Use strictest possible TypeScript configuration
- **Implementation:** Enable strict mode, noUncheckedIndexedAccess, exactOptionalPropertyTypes, verbatimModuleSyntax

### **16. ESLint Security & Performance Rules**
- **Rule:** Comprehensive linting for security and performance
- **Implementation:** Enable security rules (no-eval, no-script-url), performance rules (react/jsx-key), type safety rules (no-explicit-any)

## 🔐 **DATA SECURITY & PRIVACY**

### **17. PII Protection Patterns**
- **Rule:** Personally Identifiable Information must be handled with explicit protection
- **Implementation:** Mask PII in displays, use existence flags instead of raw data, implement data sanitization for logging

### **18. Service Layer Architecture**
- **Rule:** Centralized API abstraction with environment switching
- **Implementation:** Define service interfaces, implement environment-based switching, abstract all data access through service layer

## 🧪 **DEVELOPMENT HYGIENE**

### **19. Code Cleanliness Standards**
- **Rule:** No dead code, commented-out code, or development artifacts in commits
- **Implementation:** Remove void statements, eliminate setTimeout mock delays, clean up commented code before commits

### **20. Input Validation Utilities**
- **Rule:** Create and use centralized validation utilities
- **Implementation:** Build utility library for email validation, string sanitization, phone formatting, URL validation, HTML sanitization

## 📊 **VALIDATION & ENFORCEMENT**

### **21. Pre-commit Validation**
- **Rule:** Every commit must pass comprehensive quality gates
- **Implementation:** Zero TypeScript errors, no console.log in production code, ARIA attributes on interactive components, automated security scanning

### **22. Performance Monitoring Integration**
- **Rule:** Monitor Core Web Vitals and component performance
- **Implementation:** Track LCP, CLS, bundle size, component render times, implement performance budgets

## 🎯 **SUCCESS METRICS**

### **Security Metrics**
- 100% input sanitization coverage
- Zero sensitive data in logs
- Complete CSP implementation

### **Performance Metrics**
- All data operations ≤ O(n log n) complexity
- Component render times < 16ms
- Bundle size growth < 5% per feature

### **Architecture Metrics**
- Zero production mock data references
- All components under 250 lines
- Service layer abstraction: 100% coverage

## 🚀 **MIGRATION FROM EXISTING ANTI-PATTERNS**

### **Priority Fixes**
1. **Console logging in production** → Structured logging with error codes
2. **Record<string, unknown>** → Proper type interfaces with validation
3. **Hardcoded mock data** → Service layer abstraction
4. **Complex global CSS** → Utility-first Tailwind approach
5. **O(n²) algorithms** → Efficient O(n log n) implementations
6. **Missing ARIA support** → Full accessibility compliance
7. **Unmemoized expensive operations** → Strategic memoization

### REMEMBER
- For every change or decision, use CoT. ToT alongside multiple Expert Agents/Senior developers.. say 10 senior developers who will critique, assess, reassess the possible solution considering performance, best practices, our rules, all possible edge-cases, scalability etc.. and until every one agrees you will keep iterating on the solution, so only the final 100% approved code must be edited considering all the functionality we have discussed. So even if it takes 1000 iterations, do it. Dont stop until you find the right solution agreed by all experts.. validate all possible assumptions before we move onto implementing the actual code fix. And at the end dont forget to run lint/tsc check.
- It has be to like a super senior developer
- Write the absolute minimum code required
- No sweeping changes
- No unrelated edits - focus on just the task you're on
- Make code precise, modular, highly optimized as per July 2025
- Don’t break existing functionality

### **React Compiler Transition Strategy**
- Continue current memoization patterns until React Compiler is stable
- React Compiler is experimental and separate from React 19
- Maintain memoization knowledge for edge cases and legacy support

---

*These rules are derived from comprehensive refactoring assessment findings and represent essential patterns to prevent systematic technical debt accumulation in AI-driven development.*

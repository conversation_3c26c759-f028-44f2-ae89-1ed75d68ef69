import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { MetricCard } from "@/components/ui/metric-card";
import { UpdateCard } from "@/components/ui/update-card";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import {
  FileText,
  Activity,
  Clock,
  TrendingUp,
  Calendar,
  ArrowRight,
  AlertTriangle,
  CheckCircle,
  BarChart3,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

export default function Dashboard() {
  const navigate = useNavigate();

  // Chart data
  const complianceScoreData = [
    { month: "Jan", score: 82 },
    { month: "Feb", score: 85 },
    { month: "Mar", score: 83 },
    { month: "Apr", score: 87 },
    { month: "May", score: 89 },
    { month: "Jun", score: 87 },
  ];

  const documentProcessingData = [
    { week: "Week 1", processed: 45, pending: 12 },
    { week: "Week 2", processed: 52, pending: 8 },
    { week: "Week 3", processed: 48, pending: 15 },
    { week: "Week 4", processed: 61, pending: 6 },
  ];

  const riskDistributionData = [
    { name: "Low Risk", value: 65, color: "#10b981" },
    { name: "Medium Risk", value: 25, color: "#f59e0b" },
    { name: "High Risk", value: 8, color: "#ef4444" },
    { name: "Critical", value: 2, color: "#dc2626" },
  ];

  const regulatoryUpdatesData = [
    { agency: "FDA", count: 12 },
    { agency: "EMA", count: 8 },
    { agency: "ICH", count: 5 },
    { agency: "ISO", count: 3 },
  ];

  // Mock data for the dashboard
  const metrics = [
    {
      title: "Documents Processed",
      value: "1,293",
      change: { value: "+12% from last month", type: "increase" as const },
      icon: <FileText className="h-4 w-4" />,
    },
    {
      title: "Compliance Score",
      value: "87%",
      change: { value: "+3% from last month", type: "increase" as const },
      icon: <Activity className="h-4 w-4" />,
      description: "Overall performance",
    },
    {
      title: "Pending Reviews",
      value: "24",
      change: { value: "+8% from last week", type: "increase" as const },
      icon: <Clock className="h-4 w-4" />,
      description: "Requires attention",
    },
  ];

  const recentUpdates = [
    {
      title: "FDA Updates to GMP Guidelines for Pharmaceutical Manufacturing",
      agency: "FDA",
      category: "Pharmaceutical",
      publishedDate: "2 hours ago",
      severity: "medium" as const,
      summary:
        "The FDA has released updated Good Manufacturing Practice (GMP) guidelines that affect Contract Manufacturing Organizations. Key changes include enhanced requirements for quality management systems and updated validation protocols.",
    },
    {
      title: "EMA Guideline on Quality Risk Management",
      agency: "EMA",
      category: "Manufacturing",
      publishedDate: "Yesterday",
      severity: "low" as const,
      summary:
        "The European Medicines Agency has published a new baseline on Quality Risk Management for pharmaceutical manufacturers. This guideline emphasizes a proactive approach to identifying and mitigating risks in the manufacturing process.",
    },
    {
      title: "ICH Q9 R1 Quality Risk Management Revision",
      agency: "ICH",
      category: "International",
      publishedDate: "2 days ago",
      severity: "high" as const,
      summary:
        "The International Council for Harmonisation has revised the Q9 guideline on Quality Risk Management. The revision includes new considerations for subjectivity in risk assessment and formalized risk review processes.",
    },
  ];

  const recentActivities = [
    {
      type: "success",
      message: "Compliance check completed for Q2 2023 Manufacturing Report",
      time: "2 hours ago",
    },
    {
      type: "warning",
      message: "New regulatory alert for Batch Release Procedures",
      time: "Yesterday, 01:00 PM",
    },
    {
      type: "info",
      message: "New document uploaded: Annual Quality Review Template",
      time: "June 16, 2023, 9:00 AM",
    },
    {
      type: "error",
      message:
        "Critical compliance issue identified in Stability Testing Protocol",
      time: "June 16, 2023, 02:15 PM",
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Regulatory Autopilot
            </h1>
            <p className="text-muted-foreground mt-1">
              Welcome back, your compliance dashboard is up to date
            </p>
          </div>
          <Button
            className="bg-primary hover:bg-primary/90"
            onClick={() => navigate("/updates")}
          >
            <TrendingUp className="mr-2 h-4 w-4" />
            View all updates
          </Button>
        </div>

        {/* Key Metrics with Charts */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Documents Processed Chart */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Documents Processed
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground mb-2">
                1,293
              </div>
              <div className="text-xs text-success mb-4">
                <TrendingUp className="mr-1 h-3 w-3 inline" />
                +12% from last month
              </div>
              <div className="relative">
                <div className="absolute inset-0 opacity-20 dark:opacity-10">
                  <svg width="100%" height="60" className="overflow-hidden">
                    <defs>
                      <pattern
                        id="grid"
                        width="20"
                        height="20"
                        patternUnits="userSpaceOnUse"
                      >
                        <path
                          d="M 20 0 L 0 0 0 20"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="0.8"
                        />
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                  </svg>
                </div>
                <ResponsiveContainer width="100%" height={60}>
                  <AreaChart data={documentProcessingData}>
                    <Area
                      type="monotone"
                      dataKey="processed"
                      stroke="#8b5cf6"
                      fill="#8b5cf6"
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Compliance Score Chart */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Compliance Score
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground mb-2">87%</div>
              <div className="text-xs text-success mb-4">
                <TrendingUp className="mr-1 h-3 w-3 inline" />
                +3% from last month
              </div>
              <div className="relative">
                <div className="absolute inset-0 opacity-20 dark:opacity-10">
                  <svg width="100%" height="60" className="overflow-hidden">
                    <defs>
                      <pattern
                        id="dots"
                        width="15"
                        height="15"
                        patternUnits="userSpaceOnUse"
                      >
                        <circle cx="7.5" cy="7.5" r="1.2" fill="currentColor" />
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#dots)" />
                  </svg>
                </div>
                <ResponsiveContainer width="100%" height={60}>
                  <LineChart data={complianceScoreData}>
                    <Line
                      type="monotone"
                      dataKey="score"
                      stroke="#10b981"
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Pending Reviews Chart */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Reviews
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground mb-2">24</div>
              <div className="text-xs text-warning mb-4">
                <TrendingUp className="mr-1 h-3 w-3 inline" />
                +8% from last week
              </div>
              <div className="relative">
                <div className="absolute inset-0 opacity-20 dark:opacity-10">
                  <svg width="100%" height="60" className="overflow-hidden">
                    <defs>
                      <pattern
                        id="lines"
                        width="10"
                        height="10"
                        patternUnits="userSpaceOnUse"
                      >
                        <path
                          d="M 0,10 l 10,-10 M -2.5,2.5 l 5,-5 M 7.5,12.5 l 5,-5"
                          stroke="currentColor"
                          strokeWidth="0.8"
                        />
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#lines)" />
                  </svg>
                </div>
                <ResponsiveContainer width="100%" height={60}>
                  <BarChart
                    data={[
                      { name: "Mon", value: 4 },
                      { name: "Tue", value: 7 },
                      { name: "Wed", value: 3 },
                      { name: "Thu", value: 6 },
                      { name: "Fri", value: 4 },
                    ]}
                  >
                    <Bar dataKey="value" fill="#f59e0b" radius={[2, 2, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Latest Regulatory Updates */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg font-semibold">
                  Latest Regulatory Updates
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate("/updates")}
                >
                  View all updates
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentUpdates.map((update, index) => (
                  <UpdateCard
                    key={index}
                    title={update.title}
                    agency={update.agency}
                    category={update.category}
                    publishedDate={update.publishedDate}
                    severity={update.severity}
                    summary={update.summary}
                  />
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activities & Compliance Calendar */}
          <div className="space-y-6">
            {/* Recent Activities */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold">
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div
                      className={`flex h-2 w-2 rounded-full mt-2 ${
                        activity.type === "success"
                          ? "bg-success"
                          : activity.type === "warning"
                            ? "bg-warning"
                            : activity.type === "info"
                              ? "bg-info"
                              : "bg-destructive"
                      }`}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-foreground">
                        {activity.message}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Compliance Calendar */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  Compliance Calendar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center text-muted-foreground text-sm">
                  <p className="font-medium">June 2023</p>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Q2 Audit Review</span>
                      <span>June 30</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>GMP Training Due</span>
                      <span>July 15</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Annual Report</span>
                      <span>July 31</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

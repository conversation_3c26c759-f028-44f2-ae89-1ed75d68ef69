<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VCP_001 Edge Functions Test Suite - July 2025</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        .success { border-left: 4px solid #10b981; background: #f0fdf4; }
        .error { border-left: 4px solid #ef4444; background: #fef2f2; }
        .warning { border-left: 4px solid #f59e0b; background: #fffbeb; }
        .info { border-left: 4px solid #3b82f6; background: #eff6ff; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #9ca3af; cursor: not-allowed; }
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status { font-weight: bold; }
        .timestamp { color: #6b7280; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 VCP_001 Edge Functions Test Suite</h1>
        <p class="timestamp">July 2025 - Latest Supabase Edge Functions Testing</p>
        
        <div class="test-section info">
            <h3>📋 Test Configuration</h3>
            <p><strong>Supabase URL:</strong> https://esgciouphhajolkojipw.supabase.co</p>
            <p><strong>Edge Functions:</strong> calculate-compliance-score, setup-realtime-channels</p>
            <p><strong>Test Framework:</strong> Latest Supabase JS Client (July 2025)</p>
        </div>

        <div class="test-section">
            <h3>🚀 Test Controls</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="testComplianceScore()">Test Compliance Score</button>
            <button onclick="testRealtimeChannels()">Test Realtime Channels</button>
            <button onclick="testErrorHandling()">Test Error Handling</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2'

        // Initialize Supabase client with latest patterns (July 2025)
        const supabase = createClient(
            'https://esgciouphhajolkojipw.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzZ2Npb3VwaGhham9sa29qaXB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjYwMzcsImV4cCI6MjA2NzgwMjAzN30.DqyAefIWDBBH11i4stMELtx9a3zrFIP9q604AWZPTjI'
        )

        // Test data
        const TEST_DATA = {
            organizationId: '987fcdeb-51a2-43d7-8f9e-123456789abc',
            userId: '456e7890-e12b-34d5-a678-901234567def',
            documentId: '123e4567-e89b-12d3-a456-426614174000'
        }

        // Utility functions
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results')
            const timestamp = new Date().toLocaleTimeString()
            
            const section = document.createElement('div')
            section.className = `test-section ${type}`
            section.innerHTML = `
                <h4>${title} <span class="timestamp">${timestamp}</span></h4>
                <div>${content}</div>
            `
            results.appendChild(section)
            section.scrollIntoView({ behavior: 'smooth' })
        }

        function formatResponse(response) {
            return `<pre>${JSON.stringify(response, null, 2)}</pre>`
        }

        // Test 1: Calculate Compliance Score Edge Function
        async function testComplianceScore() {
            addResult('🧪 Testing calculate-compliance-score Edge Function', 'Starting test...', 'info')
            
            try {
                const startTime = performance.now()
                
                // Use latest supabase.functions.invoke pattern (July 2025)
                const { data, error } = await supabase.functions.invoke('calculate-compliance-score', {
                    body: {
                        documentId: TEST_DATA.documentId,
                        organizationId: TEST_DATA.organizationId,
                        userId: TEST_DATA.userId,
                        frameworkId: 'fda_cgmp_2025'
                    }
                })

                const endTime = performance.now()
                const duration = Math.round(endTime - startTime)

                if (error) {
                    addResult(
                        '❌ Compliance Score Test Failed',
                        `<p><strong>Error:</strong> ${error.message}</p>
                         <p><strong>Duration:</strong> ${duration}ms</p>
                         ${formatResponse(error)}`,
                        'error'
                    )
                } else {
                    // Validate response structure
                    const isValid = data && data.success && data.data && 
                                   typeof data.data.overall_score === 'number' &&
                                   data.data.compliance_status

                    addResult(
                        '✅ Compliance Score Test Successful',
                        `<p><strong>Duration:</strong> ${duration}ms</p>
                         <p><strong>Score:</strong> ${data.data?.overall_score || 'N/A'}</p>
                         <p><strong>Status:</strong> ${data.data?.compliance_status || 'N/A'}</p>
                         <p><strong>Valid Response:</strong> ${isValid ? '✅ Yes' : '❌ No'}</p>
                         ${formatResponse(data)}`,
                        'success'
                    )
                }
            } catch (err) {
                addResult(
                    '❌ Compliance Score Test Error',
                    `<p><strong>Exception:</strong> ${err.message}</p>
                     ${formatResponse(err)}`,
                    'error'
                )
            }
        }

        // Test 2: Setup Realtime Channels Edge Function
        async function testRealtimeChannels() {
            addResult('🧪 Testing setup-realtime-channels Edge Function', 'Starting test...', 'info')
            
            try {
                const startTime = performance.now()
                
                // Test channel setup
                const { data: setupData, error: setupError } = await supabase.functions.invoke('setup-realtime-channels', {
                    body: {
                        organizationId: TEST_DATA.organizationId,
                        userId: TEST_DATA.userId,
                        action: 'setup',
                        channels: [
                            {
                                name: 'compliance_alerts',
                                type: 'compliance_alerts',
                                options: {
                                    auto_reconnect: true,
                                    retry_attempts: 3
                                }
                            },
                            {
                                name: 'document_processing',
                                type: 'document_processing'
                            }
                        ]
                    }
                })

                const endTime = performance.now()
                const duration = Math.round(endTime - startTime)

                if (setupError) {
                    addResult(
                        '❌ Realtime Channels Setup Failed',
                        `<p><strong>Error:</strong> ${setupError.message}</p>
                         <p><strong>Duration:</strong> ${duration}ms</p>
                         ${formatResponse(setupError)}`,
                        'error'
                    )
                } else {
                    addResult(
                        '✅ Realtime Channels Setup Successful',
                        `<p><strong>Duration:</strong> ${duration}ms</p>
                         <p><strong>Channels:</strong> ${setupData.data?.length || 0} configured</p>
                         ${formatResponse(setupData)}`,
                        'success'
                    )

                    // Test channel status
                    await testChannelStatus()
                }
            } catch (err) {
                addResult(
                    '❌ Realtime Channels Test Error',
                    `<p><strong>Exception:</strong> ${err.message}</p>
                     ${formatResponse(err)}`,
                    'error'
                )
            }
        }

        // Test 3: Channel Status Check
        async function testChannelStatus() {
            try {
                const { data, error } = await supabase.functions.invoke('setup-realtime-channels', {
                    body: {
                        organizationId: TEST_DATA.organizationId,
                        userId: TEST_DATA.userId,
                        action: 'status'
                    }
                })

                if (error) {
                    addResult(
                        '⚠️ Channel Status Check Failed',
                        `<p><strong>Error:</strong> ${error.message}</p>
                         ${formatResponse(error)}`,
                        'warning'
                    )
                } else {
                    addResult(
                        '✅ Channel Status Check Successful',
                        `<p><strong>Active Channels:</strong> ${data.data?.length || 0}</p>
                         ${formatResponse(data)}`,
                        'success'
                    )
                }
            } catch (err) {
                addResult(
                    '❌ Channel Status Check Error',
                    `<p><strong>Exception:</strong> ${err.message}</p>`,
                    'error'
                )
            }
        }

        // Test 4: Error Handling
        async function testErrorHandling() {
            addResult('🧪 Testing Error Handling', 'Testing invalid requests...', 'info')
            
            try {
                // Test with invalid data
                const { data, error } = await supabase.functions.invoke('calculate-compliance-score', {
                    body: {
                        invalid: 'request'
                    }
                })

                if (error) {
                    addResult(
                        '✅ Error Handling Working',
                        `<p><strong>Expected Error:</strong> ${error.message}</p>
                         <p>Edge Function correctly rejected invalid request</p>
                         ${formatResponse(error)}`,
                        'success'
                    )
                } else {
                    addResult(
                        '⚠️ Error Handling Issue',
                        `<p>Edge Function should have rejected invalid request</p>
                         ${formatResponse(data)}`,
                        'warning'
                    )
                }
            } catch (err) {
                addResult(
                    '✅ Error Handling Working (Exception)',
                    `<p><strong>Expected Exception:</strong> ${err.message}</p>
                     <p>Edge Function correctly threw exception for invalid request</p>`,
                    'success'
                )
            }
        }

        // Run all tests
        async function runAllTests() {
            clearResults()
            addResult('🚀 Starting VCP_001 Complete Test Suite', 'Running all Edge Function tests...', 'info')
            
            await testComplianceScore()
            await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second
            
            await testRealtimeChannels()
            await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second
            
            await testErrorHandling()
            
            addResult('🏁 All Tests Completed', 'VCP_001 Edge Functions testing finished!', 'info')
        }

        function clearResults() {
            document.getElementById('results').innerHTML = ''
        }

        // Make functions globally available
        window.testComplianceScore = testComplianceScore
        window.testRealtimeChannels = testRealtimeChannels
        window.testErrorHandling = testErrorHandling
        window.runAllTests = runAllTests
        window.clearResults = clearResults

        // Auto-run basic connectivity test on load
        window.addEventListener('load', () => {
            addResult('🔗 Supabase Client Initialized', 'Ready to test Edge Functions with latest July 2025 patterns', 'success')
        })
    </script>
</body>
</html>

'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import { Activity, Award, Settings, User } from 'lucide-react'
import { ProfileActivity } from './profile-activity'
import { ProfileAchievements } from './profile-achievements'
import { ProfileOverview } from './profile-overview'
import { ProfileSettings } from './profile-settings'

interface ProfileTabsProps {
  readonly className?: string
  readonly isEditing?: boolean
}

export function ProfileTabs({ className, isEditing = false }: ProfileTabsProps) {
  return (
    <Tabs defaultValue="overview" className={cn('space-y-6', className)}>
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="overview" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">Overview</span>
        </TabsTrigger>
        <TabsTrigger value="activity" className="flex items-center gap-2">
          <Activity className="h-4 w-4" />
          <span className="hidden sm:inline">Activity</span>
        </TabsTrigger>
        <TabsTrigger value="achievements" className="flex items-center gap-2">
          <Award className="h-4 w-4" />
          <span className="hidden sm:inline">Achievements</span>
        </TabsTrigger>
        <TabsTrigger value="settings" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          <span className="hidden sm:inline">Settings</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6">
        <ProfileOverview isEditing={isEditing} />
      </TabsContent>

      <TabsContent value="activity" className="space-y-6">
        <ProfileActivity />
      </TabsContent>

      <TabsContent value="achievements" className="space-y-6">
        <ProfileAchievements />
      </TabsContent>

      <TabsContent value="settings" className="space-y-6">
        <ProfileSettings isEditing={isEditing} />
      </TabsContent>
    </Tabs>
  )
}

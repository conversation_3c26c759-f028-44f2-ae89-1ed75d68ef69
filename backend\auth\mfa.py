"""
Multi-Factor Authentication (MFA) Implementation for VigiLens

VCP_002_4: Multi-Factor Authentication Implementation
Implements TOTP and SMS-based MFA using Supabase Auth 2025 features.

Following pharmaceutical compliance standards for secure authentication.
"""

import base64
import hashlib
import hmac
import logging
import secrets
import time
from datetime import datetime, timezone, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from uuid import UUID

import qrcode
from io import BytesIO
from supabase import Client

from .models import MFAMethod, AuthUser, PharmaceuticalRole


logger = logging.getLogger(__name__)


class TOTPGenerator:
    """
    Time-based One-Time Password (TOTP) generator following RFC 6238.

    Implements pharmaceutical-grade TOTP for compliance with security standards.
    """

    def __init__(self, secret: str, digits: int = 6, period: int = 30):
        """
        Initialize TOTP generator.

        Args:
            secret: Base32-encoded secret key
            digits: Number of digits in TOTP (default: 6)
            period: Time period in seconds (default: 30)
        """
        self.secret = secret
        self.digits = digits
        self.period = period

    def generate_secret(self) -> str:
        """
        Generate a new TOTP secret key.

        Returns:
            Base32-encoded secret key
        """
        # Generate 160-bit (20-byte) secret for pharmaceutical security
        secret_bytes = secrets.token_bytes(20)
        return base64.b32encode(secret_bytes).decode('utf-8')

    def generate_totp(self, timestamp: Optional[int] = None) -> str:
        """
        Generate TOTP code for given timestamp.

        Args:
            timestamp: Unix timestamp (default: current time)

        Returns:
            TOTP code as string
        """
        if timestamp is None:
            timestamp = int(time.time())

        # Calculate time counter
        counter = timestamp // self.period

        # Convert counter to bytes
        counter_bytes = counter.to_bytes(8, byteorder='big')

        # Decode secret from base32
        secret_bytes = base64.b32decode(self.secret)

        # Generate HMAC-SHA1
        hmac_hash = hmac.new(secret_bytes, counter_bytes, hashlib.sha1).digest()

        # Dynamic truncation
        offset = hmac_hash[-1] & 0x0f
        truncated = hmac_hash[offset:offset + 4]

        # Convert to integer and apply modulo
        code = int.from_bytes(truncated, byteorder='big') & 0x7fffffff
        totp = code % (10 ** self.digits)

        return str(totp).zfill(self.digits)

    def verify_totp(self, code: str, timestamp: Optional[int] = None, window: int = 1) -> bool:
        """
        Verify TOTP code with time window tolerance.

        Args:
            code: TOTP code to verify
            timestamp: Unix timestamp (default: current time)
            window: Time window tolerance (default: 1 period)

        Returns:
            True if code is valid, False otherwise
        """
        if timestamp is None:
            timestamp = int(time.time())

        # Check current time and adjacent windows
        for i in range(-window, window + 1):
            test_time = timestamp + (i * self.period)
            if self.generate_totp(test_time) == code:
                return True

        return False

    def generate_qr_code(self, user_email: str, issuer: str = "VigiLens") -> str:
        """
        Generate QR code for TOTP setup.

        Args:
            user_email: User's email address
            issuer: Service name (default: VigiLens)

        Returns:
            Base64-encoded QR code image
        """
        # Create TOTP URI
        uri = f"otpauth://totp/{issuer}:{user_email}?secret={self.secret}&issuer={issuer}&digits={self.digits}&period={self.period}"

        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(uri)
        qr.make(fit=True)

        # Create image
        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"


class SMSProvider:
    """
    SMS provider interface for MFA verification.

    In production, integrate with providers like Twilio, AWS SNS, or Azure Communication Services.
    """

    def __init__(self, provider_config: Dict[str, str]):
        """
        Initialize SMS provider.

        Args:
            provider_config: Provider-specific configuration
        """
        self.config = provider_config
        self.provider = provider_config.get("provider", "mock")

    async def send_verification_code(self, phone: str, code: str) -> bool:
        """
        Send verification code via SMS.

        Args:
            phone: Phone number in E.164 format
            code: Verification code to send

        Returns:
            True if SMS sent successfully, False otherwise
        """
        try:
            if self.provider == "mock":
                # Mock implementation for development
                logger.info(f"Mock SMS: Sending code {code} to {phone}")
                return True

            elif self.provider == "twilio":
                # Twilio implementation
                return await self._send_twilio_sms(phone, code)

            elif self.provider == "aws_sns":
                # AWS SNS implementation
                return await self._send_aws_sns(phone, code)

            else:
                logger.error(f"Unsupported SMS provider: {self.provider}")
                return False

        except Exception as e:
            logger.error(f"SMS sending error: {str(e)}")
            return False

    async def _send_twilio_sms(self, phone: str, code: str) -> bool:
        """Send SMS via Twilio (placeholder for production implementation)."""
        # In production, implement Twilio client
        # from twilio.rest import Client
        # client = Client(account_sid, auth_token)
        # message = client.messages.create(
        #     body=f"Your VigiLens verification code is: {code}",
        #     from_=self.config["from_number"],
        #     to=phone
        # )
        return True

    async def _send_aws_sns(self, phone: str, code: str) -> bool:
        """Send SMS via AWS SNS (placeholder for production implementation)."""
        # In production, implement AWS SNS client
        # import boto3
        # sns = boto3.client('sns')
        # response = sns.publish(
        #     PhoneNumber=phone,
        #     Message=f"Your VigiLens verification code is: {code}"
        # )
        return True


class MFAManager:
    """
    Multi-Factor Authentication manager for VigiLens pharmaceutical compliance.

    Coordinates TOTP and SMS MFA methods with Supabase Auth 2025 integration.
    Following 6-expert synthesis for optimal security, compliance, and performance.
    """

    def __init__(self, supabase_client: Client, sms_config: Optional[Dict[str, str]] = None):
        """
        Initialize MFA manager with Supabase Auth 2025 integration.

        Args:
            supabase_client: Supabase client instance
            sms_config: SMS provider configuration
        """
        self.supabase = supabase_client
        self.sms_provider = SMSProvider(sms_config or {"provider": "mock"})

        # MFA requirement matrix for pharmaceutical compliance
        self.mfa_requirements = {
            PharmaceuticalRole.SUPER_ADMIN: {"required": True, "methods": ["totp", "sms"]},
            PharmaceuticalRole.ORG_ADMIN: {"required": True, "methods": ["totp", "sms"]},
            PharmaceuticalRole.QUALITY_MANAGER: {"required": True, "methods": ["totp"]},
            PharmaceuticalRole.REGULATORY_LEAD: {"required": True, "methods": ["totp"]},
            PharmaceuticalRole.COMPLIANCE_OFFICER: {"required": True, "methods": ["totp"]},
            PharmaceuticalRole.DOCUMENT_REVIEWER: {"required": False, "methods": ["totp"]},
            PharmaceuticalRole.ANALYST: {"required": False, "methods": ["totp"]},
            PharmaceuticalRole.AUDITOR: {"required": False, "methods": ["totp"]},
            PharmaceuticalRole.VIEWER: {"required": False, "methods": []},
        }

    async def setup_totp(self, user: AuthUser) -> Dict[str, str]:
        """
        Setup TOTP for user using Supabase Auth 2025 MFA enrollment.

        Args:
            user: Authenticated user

        Returns:
            Dict with TOTP setup data (secret, QR code, backup codes)
        """
        try:
            # Use Supabase Auth 2025 MFA enrollment
            mfa_response = self.supabase.auth.mfa.enroll(
                type="totp",
                friendly_name=f"VigiLens TOTP - {user.email}"
            )

            if mfa_response.error:
                logger.error(f"Supabase MFA enrollment failed: {mfa_response.error.message}")
                raise Exception(f"MFA enrollment failed: {mfa_response.error.message}")

            # Extract MFA data from Supabase response
            mfa_data = mfa_response.data
            factor_id = mfa_data.id
            secret = mfa_data.secret
            qr_code = mfa_data.qr_code

            # Generate backup codes for recovery
            backup_codes = self._generate_backup_codes()

            # Update user profile with MFA information
            self.supabase.table("user_profiles").update({
                "is_mfa_enabled": True,
                "mfa_methods": ["totp"],
                "mfa_factor_id": factor_id,  # Store Supabase factor ID
                "backup_codes_hash": self._hash_backup_codes(backup_codes),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }).eq("id", str(user.id)).execute()

            # Log MFA enrollment for pharmaceutical compliance audit
            await self._log_mfa_event(
                user, "mfa_enrollment", "success",
                {"method": "totp", "factor_id": factor_id}
            )

            return {
                "factor_id": factor_id,
                "secret": secret,
                "qr_code": qr_code,
                "backup_codes": backup_codes,
                "message": "TOTP setup completed successfully"
            }

        except Exception as e:
            logger.error(f"TOTP setup error for user {user.id}: {str(e)}")
            await self._log_mfa_event(
                user, "mfa_enrollment", "failure",
                {"method": "totp", "error": str(e)}
            )
            raise

    async def setup_sms(self, user: AuthUser, phone: str) -> Dict[str, str]:
        """
        Setup SMS MFA for user.

        Args:
            user: Authenticated user
            phone: Phone number in E.164 format

        Returns:
            Dict with SMS setup confirmation
        """
        try:
            # Update user profile with SMS MFA
            self.supabase.table("user_profiles").update({
                "phone": phone,
                "is_mfa_enabled": True,
                "mfa_methods": ["sms"],
                "updated_at": datetime.now(timezone.utc).isoformat()
            }).eq("id", str(user.id)).execute()

            return {
                "phone": phone,
                "message": "SMS MFA setup completed"
            }

        except Exception as e:
            logger.error(f"SMS MFA setup error for user {user.id}: {str(e)}")
            raise

    async def verify_totp(self, user: AuthUser, code: str) -> bool:
        """
        Verify TOTP code for user.

        Args:
            user: Authenticated user
            code: TOTP code to verify

        Returns:
            True if code is valid, False otherwise
        """
        try:
            # Get user's TOTP secret
            profile_response = self.supabase.table("user_profiles").select(
                "mfa_secret"
            ).eq("id", str(user.id)).single().execute()

            if not profile_response.data or not profile_response.data.get("mfa_secret"):
                return False

            secret = profile_response.data["mfa_secret"]
            totp = TOTPGenerator(secret)

            return totp.verify_totp(code)

        except Exception as e:
            logger.error(f"TOTP verification error for user {user.id}: {str(e)}")
            return False

    async def send_sms_code(self, user: AuthUser) -> Tuple[bool, str]:
        """
        Send SMS verification code to user.

        Args:
            user: Authenticated user

        Returns:
            Tuple of (success, code) - code is for testing only
        """
        try:
            if not user.phone:
                return False, ""

            # Generate 6-digit verification code
            code = str(secrets.randbelow(1000000)).zfill(6)

            # Send SMS
            success = await self.sms_provider.send_verification_code(user.phone, code)

            if success:
                # Store code hash for verification (expires in 5 minutes)
                code_hash = hashlib.sha256(code.encode()).hexdigest()
                expires_at = datetime.now(timezone.utc) + timedelta(minutes=5)

                self.supabase.table("sms_verification_codes").insert({
                    "user_id": str(user.id),
                    "code_hash": code_hash,
                    "expires_at": expires_at.isoformat(),
                    "created_at": datetime.now(timezone.utc).isoformat()
                }).execute()

            return success, code if self.sms_provider.provider == "mock" else ""

        except Exception as e:
            logger.error(f"SMS code sending error for user {user.id}: {str(e)}")
            return False, ""

    async def verify_sms_code(self, user: AuthUser, code: str) -> bool:
        """
        Verify SMS code for user.

        Args:
            user: Authenticated user
            code: SMS verification code

        Returns:
            True if code is valid, False otherwise
        """
        try:
            code_hash = hashlib.sha256(code.encode()).hexdigest()

            # Find valid code
            response = self.supabase.table("sms_verification_codes").select("*").eq(
                "user_id", str(user.id)
            ).eq("code_hash", code_hash).gte(
                "expires_at", datetime.now(timezone.utc).isoformat()
            ).order("created_at", desc=True).limit(1).execute()

            if response.data:
                # Mark code as used
                self.supabase.table("sms_verification_codes").delete().eq(
                    "id", response.data[0]["id"]
                ).execute()
                return True

            return False

        except Exception as e:
            logger.error(f"SMS verification error for user {user.id}: {str(e)}")
            return False

    def _generate_backup_codes(self) -> list:
        """
        Generate backup codes for TOTP recovery.

        Returns:
            List of backup codes
        """
        codes = []
        for _ in range(10):
            code = secrets.token_hex(4).upper()
            codes.append(f"{code[:4]}-{code[4:]}")
        return codes

    def _hash_backup_codes(self, codes: list) -> str:
        """
        Hash backup codes for secure storage.

        Args:
            codes: List of backup codes

        Returns:
            JSON string of hashed codes
        """
        import json
        hashed_codes = []
        for code in codes:
            code_hash = hashlib.sha256(code.encode()).hexdigest()
            hashed_codes.append(code_hash)
        return json.dumps(hashed_codes)

    async def _log_mfa_event(
        self,
        user: AuthUser,
        event_type: str,
        status: str,
        metadata: Dict[str, str]
    ):
        """
        Log MFA events for pharmaceutical compliance audit trail.

        Args:
            user: User performing MFA action
            event_type: Type of MFA event
            status: Success or failure status
            metadata: Additional event metadata
        """
        try:
            audit_data = {
                "event_type": event_type,
                "status": status,
                "user_id": str(user.id),
                "organization_id": str(user.organization_id),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metadata": metadata,
                "ip_address": None,  # Will be added by middleware
                "user_agent": None,  # Will be added by middleware
                "compliance_framework": "21_CFR_Part_11"
            }

            # Insert audit log (non-blocking for performance)
            self.supabase.table("audit_logs").insert(audit_data).execute()

        except Exception as e:
            logger.error(f"Failed to log MFA event: {str(e)}")
            # Don't raise - audit logging failure shouldn't break MFA operations

    def is_mfa_required(self, user: AuthUser, operation: Optional[str] = None) -> bool:
        """
        Check if MFA is required for user based on role and operation.

        Args:
            user: User to check MFA requirements for
            operation: Specific operation being performed

        Returns:
            True if MFA is required, False otherwise
        """
        role_requirements = self.mfa_requirements.get(user.role, {"required": False})

        # Check base role requirement
        if role_requirements["required"]:
            return True

        # Check operation-specific requirements
        sensitive_operations = [
            "document_sign", "batch_approve", "regulatory_submit",
            "user_create", "user_delete", "org_settings"
        ]

        if operation in sensitive_operations:
            # Require MFA for sensitive operations regardless of role
            return True

        return False

    def get_supported_mfa_methods(self, user: AuthUser) -> List[str]:
        """
        Get supported MFA methods for user based on role.

        Args:
            user: User to get MFA methods for

        Returns:
            List of supported MFA methods
        """
        role_requirements = self.mfa_requirements.get(user.role, {"methods": []})
        return role_requirements["methods"]

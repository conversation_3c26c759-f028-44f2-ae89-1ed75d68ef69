{"total_duration": 14.594959735870361, "total_tests": 17, "passed_tests": 14, "failed_tests": 3, "success_rate": 82.35294117647058, "suite_results": {"Qdrant Store": {"success": true, "duration": 0.041959285736083984}, "BGE-M3 Embeddings": {"success": true, "duration": 14.471919059753418}, "RAG Pipeline": {"success": true, "duration": 0.023861169815063477}, "CrewAI Agents": {"success": true, "duration": 0.02672433853149414}, "Service Integration": {"success": true, "duration": 0.011551618576049805}}, "detailed_results": [{"test_name": "Qdrant: Initialize", "success": true, "message": "Vector store initialized successfully", "duration": 6.67572021484375e-06, "details": {}}, {"test_name": "Qdrant: Connection Setup", "success": false, "message": "Connection setup failed: type object 'ConnectionStatus' has no attribute 'NOT_CONNECTED'", "duration": 5.626678466796875e-05, "details": {}}, {"test_name": "Qdrant: Vector Point Creation", "success": false, "message": "Vector point creation failed: 3 validation errors for DocumentMetadata\ntitle\n  Field required [type=missing, input_value={'document_id': 'test_doc...'source': 'test_source'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\ndocument_type\n  Field required [type=missing, input_value={'document_id': 'test_doc...'source': 'test_source'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nchunk_index\n  Field required [type=missing, input_value={'document_id': 'test_doc...'source': 'test_source'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", "duration": 0.0003266334533691406, "details": {}}, {"test_name": "Qdrant: Search Request", "success": true, "message": "Search request created successfully", "duration": 0.00011801719665527344, "details": {}}, {"test_name": "BGE-M3: Initialize", "success": true, "message": "Embedding service initialized", "duration": 0.002008676528930664, "details": {}}, {"test_name": "BGE-M3: Request Creation", "success": true, "message": "Embedding request created successfully", "duration": 0.00026297569274902344, "details": {"text_count": 1}}, {"test_name": "BGE-M3: Status Check", "success": false, "message": "Status check failed: 'BGE_M3_EmbeddingService' object has no attribute 'get_service_status'", "duration": 2.1457672119140625e-05, "details": {}}, {"test_name": "BGE-M3: Health Check", "success": true, "message": "Health check result: {'status': 'healthy', 'model_loaded': True, 'embedding_dimension': 1024, 'test_embedding_time_ms': 2087.629556655884}", "duration": 14.455962657928467, "details": {}}, {"test_name": "RAG: Initialize", "success": true, "message": "RAG pipeline initialized", "duration": 0.0016453266143798828, "details": {}}, {"test_name": "RAG: Document Input", "success": true, "message": "Document input created successfully", "duration": 6.67572021484375e-05, "details": {"content_length": 68}}, {"test_name": "RAG: Query Request", "success": true, "message": "Query request created successfully", "duration": 5.793571472167969e-05, "details": {"query_length": 55}}, {"test_name": "RAG: Status Check", "success": true, "message": "Pipeline status: PipelineStatus.NOT_INITIALIZED", "duration": 2.1457672119140625e-05, "details": {}}, {"test_name": "CrewAI: Initialize", "success": true, "message": "Agent system initialized", "duration": 0.005353689193725586, "details": {}}, {"test_name": "CrewAI: Analysis Request", "success": true, "message": "Analysis request created successfully", "duration": 6.103515625e-05, "details": {"content_length": 131}}, {"test_name": "CrewAI: Status Check", "success": true, "message": "Agent system status: AgentStatus.NOT_INITIALIZED", "duration": 2.1219253540039062e-05, "details": {}}, {"test_name": "Integration: RAG + Embeddings", "success": true, "message": "RAG pipeline embedding service integration verified", "duration": 8.58306884765625e-06, "details": {}}, {"test_name": "Integration: RAG + Vector Store", "success": true, "message": "RAG pipeline vector store integration verified", "duration": 1.0967254638671875e-05, "details": {}}]}
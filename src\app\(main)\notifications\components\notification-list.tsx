'use client'

import { cn } from '@/lib/utils'
import { Bell, Search, Star, TriangleAlert } from 'lucide-react'
import { NotificationItem } from './notification-item'

interface Notification {
  readonly id: string
  readonly title: string
  readonly message: string
  readonly type: 'info' | 'warning' | 'success' | 'error'
  readonly timestamp: string
  readonly isRead: boolean
  readonly isStarred: boolean
  readonly category: string
  readonly priority: 'low' | 'medium' | 'high'
}

interface NotificationListProps {
  readonly notifications: Notification[]
  readonly activeTab: string
  readonly isLoading?: boolean
  readonly onToggleRead: (id: string) => void
  readonly onMarkAllRead?: () => void
  readonly className?: string
}

interface EmptyStateProps {
  readonly activeTab: string
  readonly hasFilters: boolean
}

function EmptyState({ activeTab, hasFilters }: EmptyStateProps) {
  const getEmptyStateContent = () => {
    if (hasFilters) {
      return {
        icon: Search,
        title: 'No notifications found',
        description: 'Try adjusting your filters or search terms to find what you\'re looking for.',
        action: null
      }
    }

    switch (activeTab) {
      case 'unread':
        return {
          icon: Bell,
          title: 'All caught up!',
          description: 'You have no unread notifications. Great job staying on top of things!',
          action: null
        }
      case 'starred':
        return {
          icon: Star,
          title: 'No starred notifications',
          description: 'Star important notifications to keep them easily accessible.',
          action: null
        }
      case 'critical':
        return {
          icon: TriangleAlert,
          title: 'No critical notifications',
          description: 'Critical notifications requiring immediate attention will appear here.',
          action: null
        }
      default:
        return {
          icon: Bell,
          title: 'No notifications yet',
          description: 'When you receive notifications, they\'ll appear here.',
          action: null
        }
    }
  }

  const { icon: Icon, title, description } = getEmptyStateContent()

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
        <Icon className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        {title}
      </h3>
      <p className="text-sm text-muted-foreground max-w-md">
        {description}
      </p>
    </div>
  )
}

function LoadingState() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div
          key={index}
          className="p-3 border border-border rounded-lg bg-card animate-pulse"
        >
          <div className="flex items-start gap-3">
            <div className="w-10 h-10 bg-muted rounded-lg" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <div className="h-4 bg-muted rounded w-1/3" />
                <div className="h-4 bg-muted rounded w-16" />
              </div>
              <div className="h-3 bg-muted rounded w-1/4" />
              <div className="h-4 bg-muted rounded w-full" />
              <div className="h-4 bg-muted rounded w-2/3" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function NotificationList({
  notifications,
  activeTab,
  isLoading = false,
  onToggleRead,
  className
}: NotificationListProps) {
  if (isLoading) {
    return (
      <div className={className}>
        <LoadingState />
      </div>
    )
  }

  if (notifications.length === 0) {
    return (
      <div className={className}>
        <EmptyState activeTab={activeTab} hasFilters={false} />
      </div>
    )
  }

  return (
    <div className={cn('mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-4', className)}>
      <div className="space-y-3">
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onToggleRead={onToggleRead}
          />
        ))}
      </div>
    </div>
  )
}

'use client'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import { Checkbox } from '@/components/ui-radix/checkbox'
import { cn } from '@/lib/utils'
import {
    CircleCheckBig,
    Clock,
    Eye,
    FileText,
    Info,
    Settings,
    Shield,
    TrendingUp,
    TriangleAlert,
    Users
} from 'lucide-react'
import { useState } from 'react'

interface NotificationItemProps {
  readonly notification: {
    readonly id: string
    readonly title: string
    readonly message: string
    readonly type: 'info' | 'warning' | 'success' | 'error'
    readonly timestamp: string
    readonly isRead: boolean
    readonly isStarred: boolean
    readonly category: string
    readonly priority: 'low' | 'medium' | 'high'
  }
  readonly onToggleRead: (id: string) => void
}

const typeIcons = {
  info: Info,
  warning: Clock,
  success: CircleCheckBig,
  error: TriangleAlert
} as const

const typeColors = {
  info: 'bg-info text-info-foreground',
  warning: 'bg-warning text-warning-foreground',
  success: 'bg-success text-success-foreground',
  error: 'bg-destructive text-destructive-foreground'
} as const

const categoryIcons = {
  compliance: Shield,
  document: FileText,
  regulatory: TrendingUp,
  system: Settings,
  user: Users,
  report: FileText,
  regulation: TrendingUp
} as const

// Helper function to format timestamp to relative time
const formatTimeAgo = (timestamp: string) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return `${diffInSeconds} second${diffInSeconds === 1 ? '' : 's'} ago`
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
  }

  const diffInWeeks = Math.floor(diffInDays / 7)
  return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`
}

export function NotificationItem({
  notification,
  onToggleRead
}: NotificationItemProps) {
  const [isSelected, setIsSelected] = useState(false)
  const Icon = typeIcons[notification.type]
  const CategoryIcon = categoryIcons[notification.category as keyof typeof categoryIcons] || FileText

  // Determine if this is a critical/unread notification
  const isUnread = !notification.isRead
  const isCritical = notification.priority === 'high'

  // Format timestamp
  const timeAgo = formatTimeAgo(notification.timestamp)

  return (
    <div className={cn(
      'rounded-lg border border-border text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer',
      isUnread && 'border-l-4 border-l-primary bg-primary/5',
      isCritical && 'border-l-destructive bg-destructive/5'
    )}>
      <div className="p-3">
        <div className="flex items-start space-x-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => setIsSelected(checked === true)}
            className="h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="flex items-start space-x-3 flex-1">
                <div className="flex items-center space-x-2">
                  <div className={cn(
                    'p-2 rounded-lg',
                    typeColors[notification.type]
                  )}>
                    <Icon className="h-4 w-4" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <h4 className={cn(
                        'font-medium',
                        isUnread ? 'text-foreground' : 'text-muted-foreground'
                      )}>
                        {notification.title}
                      </h4>
                      <Badge variant="outline" className="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground text-xs">
                        <CategoryIcon className="mr-1 h-3 w-3" />
                        {notification.category}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{timeAgo}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-muted hover:text-foreground rounded-md"
                        onClick={() => onToggleRead(notification.id)}
                        title={isUnread ? "Mark as read" : "Mark as unread"}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-9 rounded-md px-3"
                      >
                        Review Document
                      </Button>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                    {notification.message}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

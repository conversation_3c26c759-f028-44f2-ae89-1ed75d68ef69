/**
 * File Upload Component
 * Direct Supabase Storage integration with progress tracking
 * Following DEVELOPMENT_RULES_2.md and July 2025 patterns
 */

'use client'

// Using custom alert styling instead of missing Alert component
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { storageService, type UploadProgress } from '@/lib/storage-service'
import { cn } from '@/lib/utils'
import { AlertCircle, CheckCircle, File, Upload, X } from 'lucide-react'
import { useCallback, useRef, useState } from 'react'

interface FileUploadProps {
  onUploadComplete?: (filePath: string, publicUrl: string) => void
  onUploadError?: (error: string) => void
  acceptedFileTypes?: string[]
  maxFileSize?: number // in MB
  uploadPath?: string
  className?: string
}

interface UploadingFile {
  file: File
  progress: UploadProgress
  status: 'uploading' | 'completed' | 'error'
  error?: string
  publicUrl?: string
  filePath?: string
}

export function FileUpload({
  onUploadComplete,
  onUploadError,
  acceptedFileTypes = ['.pdf', '.doc', '.docx', '.txt'],
  maxFileSize = 6, // 6MB default for standard upload
  uploadPath = '',
  className
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Use the default storage service (bucket can be configured in the service)
  const storage = storageService

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    const validFiles: File[] = []
    const errors: string[] = []

    Array.from(files).forEach(file => {
      // File type validation
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!acceptedFileTypes.includes(fileExtension)) {
        errors.push(`${file.name}: File type not supported`)
        return
      }

      // File size validation
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > maxFileSize) {
        errors.push(`${file.name}: File size exceeds ${maxFileSize}MB limit`)
        return
      }

      validFiles.push(file)
    })

    // Show errors if any
    if (errors.length > 0) {
      onUploadError?.(errors.join(', '))
      return
    }

    // Start uploading valid files
    validFiles.forEach(uploadFile)
  }, [acceptedFileTypes, maxFileSize, onUploadError])

  const uploadFile = useCallback(async (file: File) => {
    const uploadingFile: UploadingFile = {
      file,
      progress: { loaded: 0, total: file.size, percentage: 0 },
      status: 'uploading'
    }

    setUploadingFiles(prev => [...prev, uploadingFile])

    try {
      // Generate unique file path
      const timestamp = Date.now()
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const filePath = uploadPath
        ? `${uploadPath}/${timestamp}_${sanitizedFileName}`
        : `${timestamp}_${sanitizedFileName}`

      const { data, error } = await storage.uploadFile(
        file,
        filePath,
        {
          contentType: file.type,
          upsert: false
        },
        (progress: UploadProgress) => {
          setUploadingFiles(prev =>
            prev.map(f =>
              f.file === file
                ? { ...f, progress }
                : f
            )
          )
        }
      )

      if (error) {
        setUploadingFiles(prev =>
          prev.map(f =>
            f.file === file
              ? { ...f, status: 'error', error: error.message }
              : f
          )
        )
        onUploadError?.(error.message)
        return
      }

      if (data) {
        const updatedFile = {
          ...uploadingFiles.find(f => f.file === file)!,
          status: 'completed' as const,
          filePath: data.path
        }

        // Only set publicUrl if it exists
        if (data.publicUrl) {
          updatedFile.publicUrl = data.publicUrl
        }

        setUploadingFiles(prev =>
          prev.map(f => f.file === file ? updatedFile : f)
        )
        onUploadComplete?.(data.path, data.publicUrl || '')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setUploadingFiles(prev =>
        prev.map(f =>
          f.file === file
            ? { ...f, status: 'error', error: errorMessage }
            : f
        )
      )
      onUploadError?.(errorMessage)
    }
  }, [storage, uploadPath, onUploadComplete, onUploadError])

  const removeFile = useCallback((file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file))
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }, [handleFileSelect])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files)
    // Reset input value to allow re-uploading same file
    e.target.value = ''
  }, [handleFileSelect])

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  return (
    <div className={cn('space-y-4', className)}>
      {/* Upload Area */}
      <Card
        className={cn(
          'border-2 border-dashed transition-colors cursor-pointer',
          isDragOver ? 'border-primary bg-primary/10' : 'border-border hover:border-border/80'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Upload className="h-12 w-12 text-muted-foreground mb-4" />
          <CardTitle className="text-lg mb-2">Upload Documents</CardTitle>
          <CardDescription className="text-center mb-4">
            Drag and drop files here, or click to select files
          </CardDescription>
          <Button variant="outline" type="button">
            Choose Files
          </Button>
          <p className="text-xs text-muted-foreground mt-2">
            Supported formats: {acceptedFileTypes.join(', ')} • Max size: {maxFileSize}MB
          </p>
        </CardContent>
      </Card>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedFileTypes.join(',')}
        onChange={handleInputChange}
        className="hidden"
      />

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="space-y-3">
          {uploadingFiles.map((uploadingFile, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <File className="h-4 w-4" />
                  <span className="text-sm font-medium truncate">
                    {uploadingFile.file.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({(uploadingFile.file.size / (1024 * 1024)).toFixed(2)} MB)
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {uploadingFile.status === 'completed' && (
                    <CheckCircle className="h-4 w-4 text-primary" />
                  )}
                  {uploadingFile.status === 'error' && (
                    <AlertCircle className="h-4 w-4 text-destructive" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(uploadingFile.file)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {uploadingFile.status === 'uploading' && (
                <div className="space-y-1">
                  <Progress value={uploadingFile.progress.percentage} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {uploadingFile.progress.percentage.toFixed(0)}% uploaded
                  </p>
                </div>
              )}

              {uploadingFile.status === 'error' && (
                <div className="flex items-center space-x-2 text-destructive bg-destructive/10 border border-destructive/20 p-3 rounded-lg">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">
                    {uploadingFile.error}
                  </span>
                </div>
              )}

              {uploadingFile.status === 'completed' && (
                <div className="flex items-center space-x-2 text-primary bg-primary/10 border border-primary/20 p-3 rounded-lg">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">
                    Upload completed successfully
                  </span>
                </div>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

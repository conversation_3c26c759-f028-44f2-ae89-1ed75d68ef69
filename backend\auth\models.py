"""
Authentication Models for VigiLens Pharmaceutical Compliance Platform

VCP_002_1: Supabase Auth Configuration & Provider Setup
Implements Pydantic models for authentication following pharmaceutical standards.

Following DEVELOPMENT_RULES_2.md:
- Runtime type safety (no 'any' types)
- Input sanitization mandatory
- Production-first mindset
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator


class PharmaceuticalRole(str, Enum):
    """Pharmaceutical compliance roles following PRD.md specifications."""
    SUPER_ADMIN = "super_admin"
    ORG_ADMIN = "org_admin"
    QUALITY_MANAGER = "quality_manager"
    REGULATORY_LEAD = "regulatory_lead"
    COMPLIANCE_OFFICER = "compliance_officer"
    DOCUMENT_REVIEWER = "document_reviewer"
    ANALYST = "analyst"
    AUDITOR = "auditor"
    VIEWER = "viewer"


class AuthProvider(str, Enum):
    """Supported authentication providers."""
    EMAIL = "email"
    GOOGLE = "google"
    AZURE = "azure"
    PHONE = "phone"


class MFAMethod(str, Enum):
    """Multi-factor authentication methods."""
    TOTP = "totp"
    SMS = "sms"
    EMAIL = "email"


class AuthUser(BaseModel):
    """Authenticated user model with pharmaceutical compliance fields."""
    id: UUID
    email: EmailStr
    email_confirmed_at: Optional[datetime] = None
    phone: Optional[str] = None
    phone_confirmed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    last_sign_in_at: Optional[datetime] = None
    
    # Pharmaceutical compliance fields
    role: PharmaceuticalRole
    organization_id: UUID
    organization_name: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_mfa_enabled: bool = False
    mfa_methods: List[MFAMethod] = Field(default_factory=list)
    
    # Metadata
    user_metadata: Dict[str, Union[str, int, bool]] = Field(default_factory=dict)
    app_metadata: Dict[str, Union[str, int, bool]] = Field(default_factory=dict)

    @validator('email')
    def validate_email(cls, v):
        """Validate email format and sanitize input."""
        if not v or '@' not in v:
            raise ValueError('Invalid email format')
        return v.lower().strip()

    @validator('phone')
    def validate_phone(cls, v):
        """Validate phone number format (E.164)."""
        if v and not v.startswith('+'):
            raise ValueError('Phone number must be in E.164 format (+1234567890)')
        return v

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"  # Prevent additional fields


class UserRole(BaseModel):
    """User role assignment with expiration support."""
    user_id: UUID
    role: PharmaceuticalRole
    organization_id: UUID
    assigned_by: UUID
    assigned_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = None
    is_active: bool = True

    class Config:
        use_enum_values = True
        validate_assignment = True


class AuthToken(BaseModel):
    """JWT token information."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    expires_at: datetime
    refresh_token: Optional[str] = None
    scope: Optional[str] = None

    @validator('access_token')
    def validate_token(cls, v):
        """Validate JWT token format."""
        if not v or len(v) < 10:
            raise ValueError('Invalid token format')
        return v

    class Config:
        validate_assignment = True


class MFAChallenge(BaseModel):
    """Multi-factor authentication challenge."""
    challenge_id: UUID
    user_id: UUID
    method: MFAMethod
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: datetime
    is_verified: bool = False
    attempts: int = 0
    max_attempts: int = 3

    class Config:
        use_enum_values = True
        validate_assignment = True


class AuthResponse(BaseModel):
    """Standardized authentication response."""
    success: bool
    user: Optional[AuthUser] = None
    token: Optional[AuthToken] = None
    mfa_required: bool = False
    mfa_challenge: Optional[MFAChallenge] = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None

    class Config:
        validate_assignment = True


class LoginRequest(BaseModel):
    """Login request model."""
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    mfa_code: Optional[str] = Field(None, min_length=6, max_length=8)
    remember_me: bool = False

    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

    class Config:
        validate_assignment = True


class SignupRequest(BaseModel):
    """User registration request."""
    email: EmailStr
    password: str = Field(..., min_length=12, max_length=128)
    organization_name: str = Field(..., min_length=2, max_length=100)
    full_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = None

    @validator('password')
    def validate_password_strength(cls, v):
        """Validate pharmaceutical-grade password requirements."""
        if len(v) < 12:
            raise ValueError('Password must be at least 12 characters for pharmaceutical compliance')
        
        # Check for mixed case, numbers, and special characters
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v)
        
        if not all([has_upper, has_lower, has_digit, has_special]):
            raise ValueError('Password must contain uppercase, lowercase, numbers, and special characters')
        
        return v

    @validator('organization_name')
    def validate_organization_name(cls, v):
        """Sanitize organization name."""
        return v.strip()

    class Config:
        validate_assignment = True


class MFASetupRequest(BaseModel):
    """MFA setup request."""
    method: MFAMethod
    phone: Optional[str] = None  # Required for SMS

    @validator('phone')
    def validate_phone_for_sms(cls, v, values):
        """Validate phone number when SMS MFA is requested."""
        if values.get('method') == MFAMethod.SMS and not v:
            raise ValueError('Phone number required for SMS MFA')
        return v

    class Config:
        use_enum_values = True
        validate_assignment = True


class MFAVerifyRequest(BaseModel):
    """MFA verification request."""
    challenge_id: UUID
    code: str = Field(..., min_length=6, max_length=8)

    @validator('code')
    def validate_mfa_code(cls, v):
        """Validate MFA code format."""
        if not v.isdigit():
            raise ValueError('MFA code must be numeric')
        return v

    class Config:
        validate_assignment = True

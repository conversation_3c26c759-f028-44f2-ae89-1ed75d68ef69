-- Verify TypeScript types generation and database schema consistency
-- Run this to check if there are any issues with the generated types

-- Check 1: Verify electronic_signatures table structure
SELECT 
    'ELECTRONIC_SIGNATURES COLUMNS' as check_type,
    column_name,
    data_type,
    udt_name,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check 2: Verify enum types exist
SELECT 
    'ENUM TYPES' as check_type,
    typname as enum_name,
    array_agg(enumlabel ORDER BY enumsortorder) as enum_values
FROM pg_type t
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE typname IN ('signature_type', 'authentication_method')
GROUP BY typname
ORDER BY typname;

-- Check 3: Verify authentication_method column type specifically
SELECT 
    'AUTHENTICATION_METHOD COLUMN' as check_type,
    column_name,
    data_type,
    udt_name,
    column_default
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND column_name = 'authentication_method'
AND table_schema = 'public';

-- Check 4: Verify signature_type column type specifically
SELECT 
    'SIGNATURE_TYPE COLUMN' as check_type,
    column_name,
    data_type,
    udt_name,
    column_default
FROM information_schema.columns 
WHERE table_name = 'electronic_signatures' 
AND column_name = 'signature_type'
AND table_schema = 'public';

-- Check 5: Verify functions exist and their signatures
SELECT 
    'SIGNATURE FUNCTIONS' as check_type,
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('create_electronic_signature', 'verify_signature_integrity', 'get_signature_details')
ORDER BY routine_name;

-- Check 6: Test if we can query the table with proper types
SELECT 
    'SAMPLE SIGNATURE DATA' as check_type,
    id,
    signature_type,
    authentication_method,
    signer_name,
    signed_at,
    is_valid
FROM electronic_signatures 
ORDER BY signed_at DESC 
LIMIT 3;

-- Check 7: Verify table count and structure
SELECT 
    'TABLE SUMMARY' as check_type,
    COUNT(*) as total_tables,
    array_agg(table_name ORDER BY table_name) as tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'pg_%';

-- Check 8: Verify RLS policies on electronic_signatures
SELECT 
    'RLS POLICIES' as check_type,
    schemaname,
    tablename,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE tablename = 'electronic_signatures'
ORDER BY policyname;

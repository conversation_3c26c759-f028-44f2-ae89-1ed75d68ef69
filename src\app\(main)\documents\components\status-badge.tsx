import { Badge } from '@/components/ui-radix/badge'
import { cn } from '@/lib/utils'
import {
    AlertCircle,
    CheckCircle,
    Clock,
    Loader2,
    XCircle
} from 'lucide-react'
import * as React from 'react'

interface StatusBadgeProps {
  status: 'completed' | 'processing' | 'needs_review' | 'failed' | 'queued'
  size?: 'sm' | 'md'
}

/**
 * StatusBadge Component - Display document status with icons and animations
 *
 * Shows appropriate colors, icons, and animations based on document status
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, size = 'sm' }) => {
  const config = {
    'completed': {
      className: 'bg-success text-success-foreground hover:bg-success',
      icon: CheckCircle,
      label: 'Complete',
      pulse: false
    },
    'processing': {
      className: 'bg-info text-info-foreground hover:bg-info',
      icon: Loader2,
      label: 'Processing',
      pulse: true
    },
    'needs_review': {
      className: 'bg-warning text-warning-foreground hover:bg-warning',
      icon: AlertCircle,
      label: 'Needs Review',
      pulse: false
    },
    'failed': {
      className: 'bg-destructive text-destructive-foreground hover:bg-destructive',
      icon: XCircle,
      label: 'Failed',
      pulse: false
    },
    'queued': {
      className: 'bg-muted text-muted-foreground hover:bg-muted',
      icon: Clock,
      label: 'Queued',
      pulse: false
    }
  }

  const statusConfig = config[status]
  const Icon = statusConfig.icon

  return (
    <Badge
      className={cn(
        statusConfig.className,
        size === 'sm' ? 'text-xs px-2 py-0.5' : 'text-sm px-3 py-1'
      )}
    >
      <Icon
        className={cn(
          "mr-1",
          size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'
        )}
      />
      {statusConfig.label}
    </Badge>
  )
}

StatusBadge.displayName = 'StatusBadge'

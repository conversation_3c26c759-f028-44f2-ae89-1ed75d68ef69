# API Endpoints Test Suite for Multi-Agent Orchestrator System
# Testing all API implementations following DEVELOPMENT_RULES.md and DEVELOPMENT_RULES_2.md

"""
Comprehensive test suite for the API endpoints of the multi-agent orchestrator system.
Tests all FastAPI endpoints and their functionality.
"""

import asyncio
import json
import requests
import time
from datetime import datetime, timezone
from typing import Dict, Any, List

# Configure test logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestAPIEndpoints:
    """Test suite for API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize API test suite."""
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def test_health_endpoint(self) -> bool:
        """Test health check endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                assert "status" in data
                assert "timestamp" in data
                print("✅ Health endpoint test passed")
                return True
            else:
                print(f"❌ Health endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health endpoint test failed: {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """Test root endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/")
            
            if response.status_code == 200:
                data = response.json()
                assert "name" in data
                assert "version" in data
                assert "status" in data
                print("✅ Root endpoint test passed")
                return True
            else:
                print(f"❌ Root endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Root endpoint test failed: {e}")
            return False
    
    def test_version_endpoint(self) -> bool:
        """Test version endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/version")
            
            if response.status_code == 200:
                data = response.json()
                assert "version" in data
                assert "name" in data
                print("✅ Version endpoint test passed")
                return True
            else:
                print(f"❌ Version endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Version endpoint test failed: {e}")
            return False
    
    def test_ai_models_endpoint(self) -> bool:
        """Test AI models endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/ai/models")
            
            if response.status_code == 200:
                data = response.json()
                assert "available_models" in data
                print("✅ AI models endpoint test passed")
                return True
            else:
                print(f"❌ AI models endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ AI models endpoint test failed: {e}")
            return False
    
    def test_ai_validate_endpoint(self) -> bool:
        """Test AI validation endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/ai/validate")
            
            if response.status_code in [200, 503]:  # 503 is acceptable if services not available
                data = response.json()
                assert "status" in data
                print("✅ AI validate endpoint test passed")
                return True
            else:
                print(f"❌ AI validate endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ AI validate endpoint test failed: {e}")
            return False
    
    def test_orchestrator_status_endpoint(self) -> bool:
        """Test orchestrator status endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/ai/orchestrator-status")
            
            if response.status_code == 200:
                data = response.json()
                assert "status" in data
                assert "timestamp" in data
                print("✅ Orchestrator status endpoint test passed")
                return True
            else:
                print(f"❌ Orchestrator status endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Orchestrator status endpoint test failed: {e}")
            return False
    
    def test_integrated_compliance_endpoint(self) -> bool:
        """Test integrated compliance query endpoint."""
        try:
            test_data = {
                "query": "What are the basic requirements for electronic records?",
                "context": "Testing pharmaceutical compliance",
                "mode": "hybrid"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/ai/integrated-compliance",
                json=test_data
            )
            
            if response.status_code in [200, 503]:  # 503 is acceptable if services not available
                data = response.json()
                assert "status" in data
                print("✅ Integrated compliance endpoint test passed")
                return True
            else:
                print(f"❌ Integrated compliance endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Integrated compliance endpoint test failed: {e}")
            return False
    
    def test_knowledge_search_endpoint(self) -> bool:
        """Test knowledge base search endpoint."""
        try:
            test_data = {
                "query": "21 CFR Part 11",
                "max_results": 5
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/ai/knowledge-search",
                json=test_data
            )
            
            if response.status_code in [200, 503]:  # 503 is acceptable if services not available
                data = response.json()
                assert "status" in data or "results" in data
                print("✅ Knowledge search endpoint test passed")
                return True
            else:
                print(f"❌ Knowledge search endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Knowledge search endpoint test failed: {e}")
            return False
    
    def test_knowledge_base_stats_endpoint(self) -> bool:
        """Test knowledge base stats endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/ai/knowledge-base-stats")
            
            if response.status_code in [200, 503]:  # 503 is acceptable if services not available
                data = response.json()
                assert "status" in data or "total_documents" in data
                print("✅ Knowledge base stats endpoint test passed")
                return True
            else:
                print(f"❌ Knowledge base stats endpoint returned status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Knowledge base stats endpoint test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all API endpoint tests."""
        print("🚀 Starting API endpoints test suite...")
        
        tests = [
            ("Root Endpoint", self.test_root_endpoint),
            ("Health Endpoint", self.test_health_endpoint),
            ("Version Endpoint", self.test_version_endpoint),
            ("AI Models Endpoint", self.test_ai_models_endpoint),
            ("AI Validate Endpoint", self.test_ai_validate_endpoint),
            ("Orchestrator Status Endpoint", self.test_orchestrator_status_endpoint),
            ("Integrated Compliance Endpoint", self.test_integrated_compliance_endpoint),
            ("Knowledge Search Endpoint", self.test_knowledge_search_endpoint),
            ("Knowledge Base Stats Endpoint", self.test_knowledge_base_stats_endpoint),
        ]
        
        tests_run = 0
        tests_passed = 0
        tests_failed = 0
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing {test_name}...")
            tests_run += 1
            
            try:
                if test_func():
                    tests_passed += 1
                else:
                    tests_failed += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                tests_failed += 1
        
        print(f"\n🎉 API TESTS COMPLETED!")
        print(f"📊 Results: {tests_passed}/{tests_run} tests passed, {tests_failed} failed")
        
        return {
            "status": "success" if tests_failed == 0 else "partial",
            "tests_run": tests_run,
            "tests_passed": tests_passed,
            "tests_failed": tests_failed,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


def main():
    """Main function for running API tests."""
    
    print("🔧 Multi-Agent Orchestrator System - API Endpoint Tests")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running at http://localhost:8000")
        else:
            print("⚠️ Server responded but with unexpected status")
    except requests.exceptions.RequestException:
        print("❌ Server is not running at http://localhost:8000")
        print("Please start the server with: uvicorn main:app --reload")
        return
    
    # Run tests
    test_suite = TestAPIEndpoints()
    results = test_suite.run_all_tests()
    
    print(f"\n📊 Final Test Results:")
    print(json.dumps(results, indent=2))
    
    return results


if __name__ == "__main__":
    main()

'use client'

import { Building, Trash2, Upload, User, Loader2 } from 'lucide-react'

import { Avatar, AvatarFallback } from '@/components/ui-radix/avatar'
import { Button } from '@/components/ui-radix/button'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card'
import { Input } from '@/components/ui-radix/input'
import { Label } from '@/components/ui-radix/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui-radix/select'
import { Textarea } from '@/components/ui-radix/textarea'
import { useAuth } from '@/contexts/auth-context'
import { useProfileUpdate } from '@/hooks/use-profile-update'
import { useState, useEffect } from 'react'
import { Save, AlertCircle } from 'lucide-react'

interface ProfileSettingsProps {
  // Remove the old props and make this component self-contained
}

export function ProfileSettings({}: ProfileSettingsProps) {
  const { userProfile, loading } = useAuth()
  const { updateProfile, isUpdating, validationErrors, clearErrors } = useProfileUpdate()
  const [editableData, setEditableData] = useState({
    full_name: '',
    department: '',
    phone: '',
    email: '',
    role: '',
    bio: ''
  })
  const [hasChanges, setHasChanges] = useState(false)

  // Check if user is super admin
  const isSuperAdmin = userProfile?.role === 'super_admin'

  // Update editable data when userProfile changes
  useEffect(() => {
    if (userProfile) {
      const newData = {
        full_name: userProfile.full_name || '',
        department: userProfile.department || '',
        phone: userProfile.phone || '',
        email: userProfile.email || '',
        role: userProfile.role || '',
        bio: '' // Will be added to schema later
      }
      setEditableData(newData)
      setHasChanges(false)
    }
  }, [userProfile])

  const handleInputChange = (field: string, value: string) => {
    setEditableData(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
    clearErrors() // Clear validation errors when user starts typing
  }

  const handleSaveProfile = async () => {
    if (!hasChanges) return

    const updateData: any = {
      full_name: editableData.full_name,
      department: editableData.department,
      phone: editableData.phone
    }

    // Super admin can edit additional fields
    if (isSuperAdmin) {
      if (editableData.email !== userProfile?.email) {
        updateData.email = editableData.email
      }
      if (editableData.role !== userProfile?.role) {
        updateData.role = editableData.role
      }
    }

    const success = await updateProfile(updateData)

    if (success) {
      setHasChanges(false)
    }
  }

  const getFieldError = (fieldName: string) => {
    return validationErrors.find(error => error.field === fieldName)?.message
  }

  // Show loading state if user profile is not yet loaded
  if (loading || !userProfile) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Parse full name into first and last name for editing
  const nameParts = editableData.full_name.split(' ')
  const firstName = nameParts[0] || ''
  const lastName = nameParts.slice(1).join(' ') || ''

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-foreground flex items-center">
              <User className="mr-2 h-6 w-6" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-md p-4">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-destructive">Please fix the following errors:</p>
                    <ul className="mt-2 text-sm text-destructive list-disc list-inside">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error.message}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarFallback className="bg-primary text-primary-foreground text-xl">
                  {firstName?.[0] || 'U'}
                  {lastName?.[0] || ''}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-2">
                <Button variant="outline" size="sm" disabled>
                  <Upload className="mr-2 h-4 w-4" />
                  Change Photo (Coming Soon)
                </Button>
                <Button variant="ghost" size="sm" disabled>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Remove
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={firstName}
                  onChange={(e) => {
                    const newFullName = `${e.target.value} ${lastName}`.trim()
                    handleInputChange('full_name', newFullName)
                  }}
                  className={getFieldError('full_name') ? 'border-destructive' : ''}
                />
                {getFieldError('full_name') && (
                  <p className="text-sm text-destructive">{getFieldError('full_name')}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={lastName}
                  onChange={(e) => {
                    const newFullName = `${firstName} ${e.target.value}`.trim()
                    handleInputChange('full_name', newFullName)
                  }}
                  className={getFieldError('full_name') ? 'border-destructive' : ''}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">
                Email Address {!isSuperAdmin && '(Read-only)'}
                {isSuperAdmin && <span className="text-xs text-orange-600 ml-1">(Super Admin)</span>}
              </Label>
              <Input
                id="email"
                type="email"
                value={isSuperAdmin ? editableData.email : userProfile.email}
                onChange={isSuperAdmin ? (e) => handleInputChange('email', e.target.value) : undefined}
                disabled={!isSuperAdmin}
                className={!isSuperAdmin ? 'bg-muted' : (getFieldError('email') ? 'border-destructive' : '')}
              />
              {getFieldError('email') && (
                <p className="text-sm text-destructive">{getFieldError('email')}</p>
              )}
              {!isSuperAdmin && (
                <p className="text-xs text-muted-foreground">Email cannot be changed here. Contact your administrator.</p>
              )}
              {isSuperAdmin && (
                <p className="text-xs text-orange-600">⚠️ Super Admin: Email changes affect authentication</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role">
                  Role {!isSuperAdmin && '(Read-only)'}
                  {isSuperAdmin && <span className="text-xs text-orange-600 ml-1">(Super Admin)</span>}
                </Label>
                {isSuperAdmin ? (
                  <>
                    <Select
                      value={editableData.role}
                      onValueChange={(value) => handleInputChange('role', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="super_admin">Super Admin</SelectItem>
                        <SelectItem value="org_admin">Organization Admin</SelectItem>
                        <SelectItem value="quality_manager">Quality Manager</SelectItem>
                        <SelectItem value="regulatory_lead">Regulatory Lead</SelectItem>
                        <SelectItem value="compliance_officer">Compliance Officer</SelectItem>
                        <SelectItem value="document_reviewer">Document Reviewer</SelectItem>
                        <SelectItem value="analyst">Analyst</SelectItem>
                        <SelectItem value="auditor">Auditor</SelectItem>
                        <SelectItem value="viewer">Viewer</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-orange-600">⚠️ Super Admin: Role changes affect user permissions</p>
                  </>
                ) : (
                  <Input
                    id="role"
                    value={userProfile.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    disabled
                    className="bg-muted"
                  />
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  value={editableData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                  placeholder="e.g., Quality Assurance"
                  className={getFieldError('department') ? 'border-destructive' : ''}
                />
                {getFieldError('department') && (
                  <p className="text-sm text-destructive">{getFieldError('department')}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={editableData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+****************"
                className={getFieldError('phone') ? 'border-destructive' : ''}
              />
              {getFieldError('phone') && (
                <p className="text-sm text-destructive">{getFieldError('phone')}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Bio (Coming Soon)</Label>
              <Textarea
                id="bio"
                placeholder="Bio functionality will be available soon..."
                value={editableData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                disabled
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">Bio field will be added to the database schema in a future update.</p>
            </div>

            {/* Save Button */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button
                onClick={handleSaveProfile}
                disabled={!hasChanges || isUpdating}
                className="min-w-[120px]"
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <div>
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-foreground flex items-center">
              <Building className="mr-2 h-6 w-6" />
              Organization Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Organization Name</Label>
              <Input
                value={userProfile.organization_name || 'Not specified'}
                disabled
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">Organization name cannot be changed. Contact your administrator if needed.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

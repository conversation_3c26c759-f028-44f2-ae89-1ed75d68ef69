'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Camera, CheckCircle, Edit, Loader2 } from 'lucide-react'
import { useState } from 'react'
import { useAuth } from '@/contexts/auth-context'

interface ProfileHeaderProps {
  readonly className?: string
  readonly onEditToggle?: (editing: boolean) => void
}

export function ProfileHeader({ className, onEditToggle }: ProfileHeaderProps) {
  const { userProfile, loading } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const handleEditToggle = () => {
    const newEditingState = !isEditing
    setIsEditing(newEditingState)
    onEditToggle?.(newEditingState)
  }

  // Show loading state if user profile is not yet loaded
  if (loading || !userProfile) {
    return (
      <Card className={cn('', className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Parse full name into first and last name
  const nameParts = userProfile.full_name?.split(' ') || ['', '']
  const firstName = nameParts[0] || ''
  const lastName = nameParts.slice(1).join(' ') || ''

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-success' : 'bg-muted'
  }

  const getRoleBadgeText = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Super Admin'
      case 'org_admin':
        return 'Organization Admin'
      case 'quality_manager':
        return 'Quality Manager'
      case 'regulatory_lead':
        return 'Regulatory Lead'
      case 'compliance_officer':
        return 'Compliance Officer'
      case 'document_reviewer':
        return 'Document Reviewer'
      case 'analyst':
        return 'Analyst'
      case 'auditor':
        return 'Auditor'
      case 'viewer':
        return 'Viewer'
      default:
        return role.charAt(0).toUpperCase() + role.slice(1)
    }
  }

  return (
    <Card className={cn('', className)}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-6">
            {/* Avatar Section */}
            <div
              className="relative"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <Avatar className="h-24 w-24 border-4 border-border">
                <AvatarImage src={undefined} alt={userProfile.full_name || 'User'} />
                <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                  {firstName?.[0] || 'U'}{lastName?.[0] || ''}
                </AvatarFallback>
              </Avatar>

              {/* Status Indicator */}
              <div className={cn(
                'absolute bottom-1 right-1 h-6 w-6 rounded-full border-2 border-background',
                getStatusColor(userProfile.is_active)
              )} />

              {/* Upload Overlay */}
              {(isHovered || isEditing) && (
                <div className="absolute inset-0 bg-background/60 rounded-full flex items-center justify-center cursor-pointer transition-opacity">
                  <Camera className="h-6 w-6 text-foreground" />
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="space-y-1">
              <h1 className="text-2xl font-bold text-foreground">
                {userProfile.full_name || 'User Name'}
              </h1>
              <p className="text-lg text-muted-foreground">
                {getRoleBadgeText(userProfile.role)}
              </p>
              <p className="text-sm text-muted-foreground">
                {userProfile.organization_name || 'Organization'}
                {userProfile.department && ` • ${userProfile.department}`}
              </p>

              {/* Status Badges */}
              <div className="flex items-center space-x-2 pt-2">
                <Badge className={cn(
                  userProfile.is_active ? 'bg-success text-success-foreground' : 'bg-muted text-muted-foreground'
                )}>
                  <CheckCircle className="mr-1 h-3 w-3" />
                  {userProfile.is_active ? 'Active' : 'Inactive'}
                </Badge>
                <Badge variant="outline">{getRoleBadgeText(userProfile.role)}</Badge>
                <Badge variant="outline">Pharmaceutical Compliance</Badge>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleEditToggle}
            >
              <Edit className="mr-2 h-4 w-4" />
              {isEditing ? 'Cancel Edit' : 'Edit Profile'}
            </Button>
            {/* Note: Save functionality is handled by ProfileOverview component */}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

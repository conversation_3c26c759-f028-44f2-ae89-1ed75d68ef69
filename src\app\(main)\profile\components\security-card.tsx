'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Key, Shield } from 'lucide-react'
import { useState } from 'react'
import { PasswordModal } from './modals/password-modal'

export function SecurityCard() {
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [passwordModalOpen, setPasswordModalOpen] = useState(false)
  const [lastPasswordChange] = useState('30 days ago')

  const handleTwoFactorToggle = (checked: boolean) => {
    setTwoFactorEnabled(checked)
    // In a real app, this would make an API call
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="two-factor">Two-Factor Authentication</Label>
                <p className="text-xs text-muted-foreground">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Switch
                id="two-factor"
                checked={twoFactorEnabled}
                onCheckedChange={handleTwoFactorToggle}
              />
            </div>

            <div className="pt-4 border-t">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Password</p>
                    <p className="text-xs text-muted-foreground">
                      Last changed {lastPasswordChange}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPasswordModalOpen(true)}
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <PasswordModal
        open={passwordModalOpen}
        onOpenChange={setPasswordModalOpen}
      />
    </>
  )
}

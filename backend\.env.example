# VigiLens AI Processing Engine - Backend Environment Variables
# VCP_023: Python Backend Refactoring to AI-Only Services
# Copy this file to .env and fill in your actual values

# =============================================================================
# NOTE: DATABASE OPERATIONS MOVED TO FRONTEND (Direct Supabase Integration)
# This backend now focuses exclusively on AI processing services
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME="VigiLens Pharmaceutical Compliance Platform"
PROJECT_VERSION=1.0.0

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true
RELOAD=true

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","https://localhost:3000","http://localhost:8000"]

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Supabase Authentication Configuration (VCP_002)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
SUPABASE_JWT_SECRET=your_supabase_jwt_secret_here

# JWT Configuration (Legacy - now using Supabase Auth)
SECRET_KEY=your_super_secret_key_here_change_this_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Password Hashing
PWD_CONTEXT_SCHEMES=["bcrypt"]
PWD_CONTEXT_DEPRECATED=["auto"]

# MFA Configuration
MFA_ISSUER=VigiLens
SMS_PROVIDER=mock
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_FROM_NUMBER=+**********

# =============================================================================
# AI & MACHINE LEARNING CONFIGURATION
# =============================================================================

# OpenRouter Configuration (for LangChain)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=moonshotai/kimi-k2:free
MAX_TOKENS=4096
TEMPERATURE=0.1
AI_TIMEOUT=30

# LangSmith Configuration (optional, for monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=vigilens-compliance-platform

# ChromaDB Configuration
CHROMADB_PATH=./data/chromadb
CHROMADB_COLLECTION=pharmaceutical_kb
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Knowledge Base Configuration
KNOWLEDGE_BASE_PATH=./data/knowledge_base
KNOWLEDGE_BASE_VERSION=v1.0.0

# =============================================================================
# REGULATORY DATA SOURCES
# =============================================================================

# FDA Configuration
FDA_API_BASE_URL=https://api.fda.gov
FDA_API_KEY=your_fda_api_key_here

# eCFR Configuration
ECFR_API_BASE_URL=https://www.ecfr.gov/api

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/vigilens.log

# Sentry Configuration (optional, for error tracking)
SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Supabase Storage
SUPABASE_STORAGE_BUCKET=regulatory-documents

# Local Storage (for development)
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=50MB
ALLOWED_FILE_TYPES=["pdf","doc","docx","txt","html"]

# =============================================================================
# COMPLIANCE & AUDIT CONFIGURATION
# =============================================================================

# Audit Trail Configuration
AUDIT_RETENTION_DAYS=2555  # 7 years for pharmaceutical compliance
AUDIT_LOG_LEVEL=INFO
AUDIT_INCLUDE_REQUEST_BODY=true
AUDIT_INCLUDE_RESPONSE_BODY=false

# Compliance Frameworks
DEFAULT_COMPLIANCE_FRAMEWORKS=["FDA_cGMP","ICH_Q7","ISO_13485"]

# =============================================================================
# SCHEDULER CONFIGURATION
# =============================================================================

# APScheduler Configuration
SCHEDULER_TIMEZONE=UTC
SCHEDULER_JOB_DEFAULTS_COALESCE=false
SCHEDULER_JOB_DEFAULTS_MAX_INSTANCES=3

# Document Monitoring Schedule
DOCUMENT_SYNC_INTERVAL_HOURS=6
COMPLIANCE_CHECK_INTERVAL_HOURS=24

# =============================================================================
# RATE LIMITING & PERFORMANCE
# =============================================================================

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=200

# Database Connection Pool
DB_POOL_MIN_SIZE=5
DB_POOL_MAX_SIZE=20
DB_POOL_MAX_QUERIES=50000
DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME=300

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Testing Database (separate from main DB)
TEST_DATABASE_URL=postgresql://postgres:test_password@localhost:5432/vigilens_test

# Development Features
ENABLE_DOCS=true
ENABLE_REDOC=true
ENABLE_OPENAPI=true

# Mock Data
CREATE_MOCK_DATA=false
MOCK_DATA_COUNT=100

'use client'

import { <PERSON><PERSON>eader } from '@/components/shared/page-header';
import { <PERSON><PERSON> } from '@/components/ui-radix/button';
import { Card, CardContent } from '@/components/ui-radix/card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui-radix/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui-radix/tabs';
import { usePageMetadata } from '@/hooks/use-page-metadata';
import { CheckCircle, Clock, FileText, Plus, Upload } from 'lucide-react';
import * as React from 'react';
import { DocumentGrid } from './components/document-grid';
import { DocumentSearch } from './components/document-search';
import { MetricCard } from './components/metric-card';
import { ViewModeToggle } from './components/view-mode-toggle';
import { useDocumentsData } from './hooks/use-documents-data';
import type { DocumentCategory, DocumentStatus } from './types';

/**
 * Documents Page - Comprehensive Document Management
 *
 * Features:
 * - Document metrics and statistics
 * - Advanced search and filtering
 * - Grid/List view modes
 * - Tab navigation (My Documents, Shared, Recent, Templates)
 * - Responsive design with accessibility
 * - Real-time filtering and sorting
 */
export default function DocumentsPage() {
  usePageMetadata('Documents', 'Manage and organize your compliance documents');

  const {
    state,
    stats,
    setSearchQuery,
    setSelectedCategory,
    setSelectedStatus,
    setViewMode,
    setActiveTab,
    handleSort,
    handleSelectionChange
  } = useDocumentsData();

  // Document actions
  const handleDocumentView = React.useCallback((id: number) => {
    // TODO: Navigate to document detail view
    void id
  }, []);

  const handleDocumentDownload = React.useCallback((id: number) => {
    // TODO: Trigger document download
    void id
  }, []);

  const handleDocumentShare = React.useCallback((id: number) => {
    // TODO: Open share dialog
    void id
  }, []);

  const handleDocumentDelete = React.useCallback((id: number) => {
    // TODO: Show delete confirmation
    void id
  }, []);

  const handleUploadDocument = React.useCallback(() => {
    // TODO: Open upload dialog or navigate to upload page
  }, []);

  // Metric card click handlers
  const handleMetricClick = React.useCallback((filterType: string) => {
    switch (filterType) {
      case 'needs_review':
        setSelectedStatus('needs_review');
        break;
      case 'total':
        setSelectedCategory('all');
        setSelectedStatus('all');
        break;
      default:
        break;
    }
  }, [setSelectedCategory, setSelectedStatus]);

  // Empty state component
  const EmptyState: React.FC<{ type: string }> = ({ type }) => {
    const configs = {
      shared: {
        title: "Shared Documents",
        description: "Documents shared with you by team members will appear here.",
        action: null
      },
      recent: {
        title: "Recent Documents",
        description: "Your recently accessed documents will be shown here.",
        action: null
      },
      templates: {
        title: "Document Templates",
        description: "Pre-approved document templates for compliance documentation.",
        action: { label: "Browse Templates", onClick: () => { /* TODO: Browse templates functionality */ } }
      }
    };

    const config = configs[type as keyof typeof configs] || configs.shared;

    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2 text-foreground">
          {config.title}
        </h3>
        <p className="text-muted-foreground max-w-md mx-auto mb-6">
          {config.description}
        </p>
        {config.action && (
          <Button onClick={config.action.onClick} variant="outline">
            {config.action.label}
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title="Documents"
        description="Manage and organize your compliance documents"
        actions={[
          <Button key="upload" onClick={handleUploadDocument} className="gap-2">
            <Plus className="h-4 w-4" />
            Upload Document
          </Button>
        ]}
      />

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricCard
          label="Total Documents"
          value={stats.total.toString()}
          icon={FileText}
          onClick={() => handleMetricClick('total')}
        />
        <MetricCard
          label="Pending Review"
          value={stats.needsReview.toString()}
          icon={Clock}
          onClick={() => handleMetricClick('needs_review')}
        />
        <MetricCard
          label="Compliance Score"
          value={`${stats.avgCompliance}%`}
          icon={CheckCircle}
          trend={{ value: 2.5, isPositive: true }}
        />
        <MetricCard
          label="Recent Uploads"
          value={stats.recentUploads.toString()}
          icon={Upload}
          trend={{ value: 12, isPositive: true }}
        />
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex flex-1 gap-4">
              <DocumentSearch
                value={state.searchQuery}
                onChange={setSearchQuery}
                placeholder="Search documents by name, type, or content..."
              />

              <Select
                value={state.selectedCategory}
                onValueChange={(value) => setSelectedCategory(value as DocumentCategory)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Approved">Approved</SelectItem>
                  <SelectItem value="Under Review">Under Review</SelectItem>
                  <SelectItem value="Draft">Draft</SelectItem>
                  <SelectItem value="Template">Template</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={state.selectedStatus}
                onValueChange={(value) => setSelectedStatus(value as DocumentStatus)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="needs_review">Needs Review</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <ViewModeToggle
              value={state.viewMode}
              onChange={setViewMode}
            />
          </div>
        </CardContent>
      </Card>

      {/* Tabs Navigation */}
      <Tabs
        value={state.activeTab}
        onValueChange={(value) => setActiveTab(value as any)}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="my-documents">My Documents</TabsTrigger>
          <TabsTrigger value="shared">Shared with Me</TabsTrigger>
          <TabsTrigger value="recent">Recent</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="my-documents" className="space-y-4">
          <DocumentGrid
            documents={state.filteredDocuments}
            viewMode={state.viewMode}
            onView={handleDocumentView}
            onDownload={handleDocumentDownload}
            onShare={handleDocumentShare}
            onDelete={handleDocumentDelete}
            isLoading={state.isLoading}
            sortBy={state.sortBy}
            sortOrder={state.sortOrder}
            onSort={handleSort}
            selectedDocuments={state.selectedDocuments}
            onSelectionChange={handleSelectionChange}
            showSelection={false}
          />
        </TabsContent>

        <TabsContent value="shared" className="space-y-4">
          <EmptyState type="shared" />
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <EmptyState type="recent" />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <EmptyState type="templates" />
        </TabsContent>
      </Tabs>
    </div>
  );
}

# AI Development Rules for VigiLens Pharmaceutical Compliance Platform

**Document Version:** 2.0.0
**Last Updated:** July 15, 2025
**Scope:** Enterprise-grade AI development standards for pharmaceutical compliance systems
**Compliance:** 21 CFR Part 11, GxP, HIPAA, ISO 13485
**Methodology:** 6-Expert Synthesis Protocol + Context7 + Latest July 2025 Documentation
**Standards Compliance:** DEVELOPMENT_RULES.md + DEVELOPMENT_RULES_2.md

---

## 🎯 **MANDATORY DEVELOPMENT PROTOCOL**

### 6-Expert Synthesis Protocol
**Phase 1: Silent Generation - The Council of Masters**: where everyone will take the input and find the solution or the root cause (if `user request` is about a problem), And come up with their best solution considering all their knowledge, edge-cases, following all rules, doing sequentialthinking with CoT, ToT and using context7 mcp protocol to fetch latest information/documentation about the technology/library and if needed using web search for July 2025 up to date answers.

#### **Phase 2: Silent Analysis & Critique**
Once the six AI solutions are generated, simulate an internal peer review:
1. **Automated Audit:** Each solution is checked against strict pharmaceutical and coding best practices criteria
2. **Cross-Critique:** Each expert analyzes the other five solutions, genuinely critiquing trade-offs

#### **Phase 3: Silent Synthesis & Final Implementation**
1. **Voting & Synthesis:** The experts vote on the optimal approach
2. **Create Master Solution:** Synthesize the single best implementation, potentially combining elements
3. **Final Output:** Present only the final, synthesized AI solution with complete implementation and rationale

### **MANDATORY RESEARCH PROTOCOL**
- ✅ **Always use web search for latest July 2025 technology documentation**
- ✅ **Always use Context7 MCP protocol for library documentation**
- ✅ **Always follow DEVELOPMENT_RULES.md + DEVELOPMENT_RULES_2.md**
- ✅ **Always apply 6-Expert Synthesis Protocol for complex decisions**

---

## 📋 Table of Contents

1. [Mandatory Development Standards](#mandatory-development-standards)
2. [Library-Specific Best Practices](#library-specific-best-practices)
3. [Enterprise Error Handling & Logging](#enterprise-error-handling--logging)
4. [Performance Optimization Guidelines](#performance-optimization-guidelines)
5. [Security Considerations](#security-considerations)
6. [Code Organization & Architecture](#code-organization--architecture)
7. [Testing Strategies](#testing-strategies)
8. [Monitoring & Observability](#monitoring--observability)
9. [Pharmaceutical AI Compliance](#pharmaceutical-ai-compliance)
10. [RAG Pipeline Implementation Best Practices](#rag-pipeline-implementation-best-practices)
11. [Type Safety and Error Prevention](#type-safety-and-error-prevention)

---

## 🏛️ **MANDATORY DEVELOPMENT STANDARDS**

### **Production-First AI Development**
```python
# ✅ CORRECT: Production-ready AI implementation following DEVELOPMENT_RULES.md
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime, timezone
import logging
import hashlib
import secrets

class PharmaceuticalAIRequest(BaseModel):
    """Production-ready AI request model with full validation."""

    # Core request data
    request_id: str = Field(..., description="Unique request identifier")
    user_id: str = Field(..., description="Authenticated user ID")
    document_content: str = Field(..., min_length=1, max_length=100000)
    analysis_type: str = Field(..., regex="^(compliance|summary|risk|regulatory)$")

    # Pharmaceutical context
    regulatory_framework: str = Field(..., regex="^(21_cfr_part_11|eu_gmp|ich_q7|iso_13485)$")
    organization_id: str = Field(..., description="Organization identifier")

    # Security and audit
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    content_hash: str = Field(..., description="SHA-256 hash of content")

    @validator('content_hash')
    def validate_content_hash(cls, v, values):
        """Validate content hash matches document content."""
        if 'document_content' in values:
            expected_hash = hashlib.sha256(values['document_content'].encode()).hexdigest()
            if v != expected_hash:
                raise ValueError("Content hash does not match document content")
        return v

    class Config:
        # Follow DEVELOPMENT_RULES_2.md - No 'any' types
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"  # Strict validation

class PharmaceuticalAIResponse(BaseModel):
    """Production-ready AI response with comprehensive audit trail."""

    # Response identification
    response_id: str = Field(..., description="Unique response identifier")
    request_id: str = Field(..., description="Original request identifier")

    # AI processing results
    analysis_results: Dict[str, Any] = Field(..., description="Structured analysis results")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="AI confidence (0-1)")
    processing_time_ms: int = Field(..., ge=0, description="Processing time in milliseconds")

    # Model information
    model_name: str = Field(..., description="AI model used")
    model_version: str = Field(..., description="Model version")

    # Compliance and audit
    compliance_validated: bool = Field(..., description="Whether response meets compliance standards")
    audit_trail: List[Dict[str, Any]] = Field(..., description="Complete processing audit trail")

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Config:
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadAIRequest:
    def __init__(self, data: Any):  # No 'any' types allowed
        self.data = data  # No validation
        # No error handling, no type safety
```

### **Security-First AI Implementation**
```python
# ✅ CORRECT: Secure AI processing following DEVELOPMENT_RULES_2.md
import asyncio
from typing import Dict, Any, Optional
import httpx
from cryptography.fernet import Fernet

class SecureAIProcessor:
    """Secure AI processor with input sanitization and error handling."""

    def __init__(self, api_key: str, encryption_key: Optional[bytes] = None):
        # Follow DEVELOPMENT_RULES_2.md - Input sanitization mandatory
        self.api_key = self._sanitize_api_key(api_key)
        self.encryption_key = encryption_key or Fernet.generate_key()
        self.cipher_suite = Fernet(self.encryption_key)

        # Configure secure HTTP client
        self.client = httpx.AsyncClient(
            timeout=30.0,
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            headers={
                "User-Agent": "VigiLens-AI/1.0",
                "Accept": "application/json"
            }
        )

    def _sanitize_api_key(self, api_key: str) -> str:
        """Sanitize API key input following DEVELOPMENT_RULES_2.md."""
        if not isinstance(api_key, str):
            raise ValueError("API key must be a string")

        # Remove whitespace and validate format
        sanitized = api_key.strip()
        if len(sanitized) < 10:
            raise ValueError("API key too short")

        return sanitized

    async def process_pharmaceutical_document(
        self,
        request: PharmaceuticalAIRequest
    ) -> PharmaceuticalAIResponse:
        """Process pharmaceutical document with full security and audit trail."""

        start_time = datetime.now(timezone.utc)
        audit_trail = []

        try:
            # Step 1: Input validation and sanitization
            audit_trail.append({
                "step": "input_validation",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "started"
            })

            # Sanitize document content (remove potential XSS, SQL injection)
            sanitized_content = self._sanitize_document_content(request.document_content)

            audit_trail.append({
                "step": "input_validation",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "completed",
                "content_length": len(sanitized_content)
            })

            # Step 2: AI processing with retry logic
            audit_trail.append({
                "step": "ai_processing",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "started",
                "model": "moonshot/kimi-k2"
            })

            ai_results = await self._process_with_ai(sanitized_content, request.analysis_type)

            audit_trail.append({
                "step": "ai_processing",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "completed",
                "confidence": ai_results.get("confidence", 0.0)
            })

            # Step 3: Compliance validation
            compliance_validated = await self._validate_compliance(ai_results, request.regulatory_framework)

            # Calculate processing time
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            return PharmaceuticalAIResponse(
                response_id=str(secrets.token_urlsafe(16)),
                request_id=request.request_id,
                analysis_results=ai_results,
                confidence_score=ai_results.get("confidence", 0.0),
                processing_time_ms=int(processing_time),
                model_name="moonshot/kimi-k2",
                model_version="1.0",
                compliance_validated=compliance_validated,
                audit_trail=audit_trail
            )

        except Exception as e:
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = str(secrets.token_urlsafe(8))

            # Log error with ID only (no sensitive data)
            logging.error(f"AI processing failed with error ID: {error_id}")

            # Add error to audit trail
            audit_trail.append({
                "step": "error_handling",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error_id": error_id,
                "error_type": type(e).__name__
            })

            raise ValueError(f"AI processing failed. Error ID: {error_id}")

    def _sanitize_document_content(self, content: str) -> str:
        """Sanitize document content following DEVELOPMENT_RULES_2.md."""
        # Remove potential XSS vectors
        import html
        import re

        # HTML escape
        sanitized = html.escape(content)

        # Remove script tags and javascript
        sanitized = re.sub(r'<script[^>]*>.*?</script>', '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        sanitized = re.sub(r'javascript:', '', sanitized, flags=re.IGNORECASE)

        # Limit length to prevent DoS
        if len(sanitized) > 100000:
            sanitized = sanitized[:100000]

        return sanitized

    async def _process_with_ai(self, content: str, analysis_type: str) -> Dict[str, Any]:
        """Process content with AI using exponential backoff retry."""

        for attempt in range(3):
            try:
                # Prepare AI request
                ai_request = {
                    "model": "moonshot/kimi-k2",
                    "messages": [
                        {
                            "role": "system",
                            "content": f"You are a pharmaceutical compliance AI assistant. Analyze the following document for {analysis_type}. Provide structured results with confidence scores."
                        },
                        {
                            "role": "user",
                            "content": content
                        }
                    ],
                    "temperature": 0.1,
                    "max_tokens": 4096
                }

                # Make API request
                response = await self.client.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    json=ai_request,
                    headers={"Authorization": f"Bearer {self.api_key}"}
                )

                response.raise_for_status()
                result = response.json()

                # Extract and structure results
                ai_content = result["choices"][0]["message"]["content"]

                return {
                    "analysis": ai_content,
                    "confidence": 0.85,  # Would be calculated based on model response
                    "analysis_type": analysis_type,
                    "model_response": result
                }

            except Exception as e:
                if attempt == 2:  # Last attempt
                    raise e

                # Exponential backoff
                await asyncio.sleep(2 ** attempt)

        raise RuntimeError("AI processing failed after all retries")

    async def _validate_compliance(self, ai_results: Dict[str, Any], framework: str) -> bool:
        """Validate AI results against pharmaceutical compliance standards."""

        # Implement compliance validation logic
        required_elements = {
            "21_cfr_part_11": ["electronic_signature_compliance", "audit_trail_requirements"],
            "eu_gmp": ["quality_assurance", "risk_management"],
            "ich_q7": ["quality_systems", "personnel_requirements"],
            "iso_13485": ["quality_management", "risk_management"]
        }

        framework_requirements = required_elements.get(framework, [])

        # Check if AI results contain required compliance elements
        analysis_content = ai_results.get("analysis", "").lower()

        compliance_score = 0
        for requirement in framework_requirements:
            if requirement.replace("_", " ") in analysis_content:
                compliance_score += 1

        # Return True if at least 50% of requirements are addressed
        return compliance_score >= len(framework_requirements) * 0.5
```

---

## 🔧 Library-Specific Best Practices

### **LangChain 0.3.26+ Integration Patterns (July 2025 Latest)**

#### **OpenRouter Integration with MoonshotAI: Kimi K2**
```python
# ✅ CORRECT: Latest July 2025 LangChain patterns with Context7 research
# NOTE: We use ChatOpenAI class but point it to OpenRouter API via base_url override
from langchain_community.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.callbacks.base import BaseCallbackHandler
from langchain.memory import ConversationBufferWindowMemory
from langchain.chains import ConversationChain
from typing import Dict, Any, List, Optional
import asyncio
import logging

class PharmaceuticalCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for pharmaceutical compliance logging."""

    def __init__(self):
        self.audit_trail = []

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """Log LLM start for audit trail."""
        self.audit_trail.append({
            "event": "llm_start",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "model": serialized.get("name", "unknown"),
            "prompt_count": len(prompts)
        })

    def on_llm_end(self, response, **kwargs) -> None:
        """Log LLM completion for audit trail."""
        self.audit_trail.append({
            "event": "llm_end",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "token_usage": response.llm_output.get("token_usage", {}) if response.llm_output else {}
        })

class EnterpriseOpenRouterClient:
    """Enterprise-grade OpenRouter client following DEVELOPMENT_RULES.md."""

    def __init__(self, api_key: str, model: str = "moonshot/kimi-k2"):
        # Follow DEVELOPMENT_RULES_2.md - Input sanitization
        if not api_key or len(api_key.strip()) < 10:
            raise ValueError("Invalid API key provided")

        self.callback_handler = PharmaceuticalCallbackHandler()

        # Latest July 2025 LangChain configuration for OpenRouter
        # ChatOpenAI class configured to use OpenRouter API instead of OpenAI
        self.client = ChatOpenAI(
            openai_api_key=api_key.strip(),  # OpenRouter API key (not OpenAI)
            openai_api_base="https://openrouter.ai/api/v1",  # OpenRouter endpoint
            model_name=model,  # moonshot/kimi-k2 model
            temperature=0.1,  # Low temperature for pharmaceutical compliance
            max_tokens=4096,
            timeout=30,
            max_retries=3,
            request_timeout=30,
            callbacks=[self.callback_handler],
            streaming=False,  # Disable streaming for audit compliance
            verbose=False  # No verbose logging in production
        )

        # Memory for conversation context (pharmaceutical compliance)
        self.memory = ConversationBufferWindowMemory(
            k=5,  # Keep last 5 exchanges
            return_messages=True,
            memory_key="chat_history"
        )

        # Conversation chain for pharmaceutical analysis
        self.conversation = ConversationChain(
            llm=self.client,
            memory=self.memory,
            verbose=False
        )

    async def analyze_pharmaceutical_document(
        self,
        document_content: str,
        analysis_type: str,
        regulatory_framework: str
    ) -> Dict[str, Any]:
        """Analyze pharmaceutical document with full audit trail."""

        # Follow DEVELOPMENT_RULES_2.md - Input sanitization
        if not document_content or len(document_content.strip()) == 0:
            raise ValueError("Document content cannot be empty")

        if analysis_type not in ["compliance", "summary", "risk", "regulatory"]:
            raise ValueError("Invalid analysis type")

        # Create pharmaceutical-specific system prompt
        system_prompt = self._create_pharmaceutical_system_prompt(analysis_type, regulatory_framework)

        # Create message chain
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"Analyze this pharmaceutical document:\n\n{document_content}")
        ]

        try:
            # Process with retry logic
            response = await self._process_with_retry(messages)

            # Extract audit trail
            audit_trail = self.callback_handler.audit_trail.copy()
            self.callback_handler.audit_trail.clear()

            return {
                "analysis": response.content,
                "analysis_type": analysis_type,
                "regulatory_framework": regulatory_framework,
                "model_used": "moonshot/kimi-k2",
                "audit_trail": audit_trail,
                "confidence_score": self._calculate_confidence_score(response.content),
                "compliance_validated": self._validate_pharmaceutical_compliance(response.content, regulatory_framework)
            }

        except Exception as e:
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = secrets.token_urlsafe(8)
            logging.error(f"Pharmaceutical analysis failed. Error ID: {error_id}")
            raise ValueError(f"Analysis failed. Error ID: {error_id}")

    def _create_pharmaceutical_system_prompt(self, analysis_type: str, framework: str) -> str:
        """Create pharmaceutical-specific system prompt."""

        base_prompt = """You are a pharmaceutical compliance AI assistant with expertise in regulatory requirements.
        You must provide accurate, compliant analysis following pharmaceutical industry standards."""

        framework_specific = {
            "21_cfr_part_11": "Focus on electronic records, electronic signatures, and FDA requirements.",
            "eu_gmp": "Focus on European GMP requirements, quality assurance, and EMA guidelines.",
            "ich_q7": "Focus on API manufacturing, quality systems, and ICH guidelines.",
            "iso_13485": "Focus on medical device quality management and ISO requirements."
        }

        analysis_specific = {
            "compliance": "Analyze for regulatory compliance gaps and requirements.",
            "summary": "Provide executive summary with key regulatory points.",
            "risk": "Assess regulatory and compliance risks.",
            "regulatory": "Identify regulatory requirements and obligations."
        }

        return f"""{base_prompt}

        Regulatory Framework: {framework}
        {framework_specific.get(framework, "")}

        Analysis Type: {analysis_type}
        {analysis_specific.get(analysis_type, "")}

        Requirements:
        1. Provide structured analysis with clear sections
        2. Include confidence scores for each finding
        3. Reference specific regulatory requirements
        4. Highlight critical compliance issues
        5. Suggest remediation actions where applicable
        """

    async def _process_with_retry(self, messages: List) -> AIMessage:
        """Process messages with exponential backoff retry."""

        for attempt in range(3):
            try:
                # Use conversation chain for context awareness
                response = await self.client.ainvoke(messages)
                return response

            except Exception as e:
                if attempt == 2:  # Last attempt
                    raise e

                # Exponential backoff
                await asyncio.sleep(2 ** attempt)
                logging.warning(f"Retry attempt {attempt + 1} for LangChain processing")

        raise RuntimeError("LangChain processing failed after all retries")

    def _calculate_confidence_score(self, content: str) -> float:
        """Calculate confidence score based on response content."""

        # Simple confidence calculation based on content analysis
        confidence_indicators = [
            "specific regulation",
            "cfr",
            "compliance requirement",
            "regulatory standard",
            "pharmaceutical",
            "validation",
            "audit trail"
        ]

        content_lower = content.lower()
        matches = sum(1 for indicator in confidence_indicators if indicator in content_lower)

        # Base confidence + indicator bonus
        base_confidence = 0.6
        indicator_bonus = min(matches * 0.05, 0.3)

        return min(base_confidence + indicator_bonus, 0.95)

    def _validate_pharmaceutical_compliance(self, content: str, framework: str) -> bool:
        """Validate response meets pharmaceutical compliance standards."""

        required_elements = {
            "21_cfr_part_11": ["electronic", "signature", "record", "audit"],
            "eu_gmp": ["quality", "gmp", "manufacturing", "assurance"],
            "ich_q7": ["api", "quality", "system", "manufacturing"],
            "iso_13485": ["quality", "management", "device", "risk"]
        }

        framework_requirements = required_elements.get(framework, [])
        content_lower = content.lower()

        matches = sum(1 for req in framework_requirements if req in content_lower)
        return matches >= len(framework_requirements) * 0.5

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadLangChainUsage:
    def __init__(self, api_key):
        # No input validation
        self.client = ChatOpenAI(api_key)  # Missing configuration

    def process(self, content):
        # No error handling, no type hints, no audit trail
        return self.client.invoke(content)
```

### **Pydantic 2.11.7+ Validation Patterns (July 2025 Latest)**

#### **Pharmaceutical Data Modeling with Runtime Validation**
```python
# ✅ CORRECT: Latest July 2025 Pydantic patterns with Context7 research
from pydantic import BaseModel, Field, validator, root_validator, ValidationError
from pydantic.types import constr, conint, confloat
from typing import Dict, Any, List, Optional, Union, Literal
from enum import Enum
from datetime import datetime, timezone
import re

class RegulatoryFramework(str, Enum):
    """Pharmaceutical regulatory frameworks."""
    FDA_21_CFR_PART_11 = "21_cfr_part_11"
    EU_GMP = "eu_gmp"
    ICH_Q7 = "ich_q7"
    ISO_13485 = "iso_13485"
    HIPAA = "hipaa"
    GDPR = "gdpr"

class DocumentType(str, Enum):
    """Pharmaceutical document types."""
    SOP = "standard_operating_procedure"
    VALIDATION_PROTOCOL = "validation_protocol"
    BATCH_RECORD = "batch_record"
    DEVIATION_REPORT = "deviation_report"
    CHANGE_CONTROL = "change_control"
    AUDIT_REPORT = "audit_report"

class PharmaceuticalDocument(BaseModel):
    """Enterprise-grade pharmaceutical document model."""

    # Core identification
    document_id: constr(regex=r'^DOC-[A-Z0-9]{8}$') = Field(
        ...,
        description="Document ID in format DOC-XXXXXXXX"
    )
    title: constr(min_length=5, max_length=200) = Field(
        ...,
        description="Document title"
    )
    document_type: DocumentType = Field(
        ...,
        description="Type of pharmaceutical document"
    )

    # Content and metadata
    content: constr(min_length=10, max_length=1000000) = Field(
        ...,
        description="Document content"
    )
    version: constr(regex=r'^\d+\.\d+$') = Field(
        ...,
        description="Version in format X.Y"
    )

    # Regulatory compliance
    regulatory_frameworks: List[RegulatoryFramework] = Field(
        ...,
        min_items=1,
        description="Applicable regulatory frameworks"
    )
    compliance_status: Literal["compliant", "non_compliant", "under_review"] = Field(
        default="under_review",
        description="Current compliance status"
    )

    # Audit and tracking
    created_by: constr(regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') = Field(
        ...,
        description="Creator email address"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp"
    )
    last_modified_by: Optional[constr(regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')] = None
    last_modified_at: Optional[datetime] = None

    # Security and integrity
    content_hash: constr(regex=r'^[a-f0-9]{64}$') = Field(
        ...,
        description="SHA-256 hash of content"
    )
    digital_signature: Optional[str] = Field(
        None,
        description="Digital signature for 21 CFR Part 11 compliance"
    )

    # Organization context
    organization_id: constr(regex=r'^ORG-[A-Z0-9]{6}$') = Field(
        ...,
        description="Organization ID in format ORG-XXXXXX"
    )
    department: constr(min_length=2, max_length=50) = Field(
        ...,
        description="Department name"
    )

    @validator('content_hash')
    def validate_content_hash(cls, v, values):
        """Validate content hash matches document content."""
        if 'content' in values:
            import hashlib
            expected_hash = hashlib.sha256(values['content'].encode()).hexdigest()
            if v != expected_hash:
                raise ValueError("Content hash does not match document content")
        return v

    @validator('last_modified_at')
    def validate_modification_timestamp(cls, v, values):
        """Ensure modification timestamp is after creation."""
        if v and 'created_at' in values:
            if v <= values['created_at']:
                raise ValueError("Modification timestamp must be after creation timestamp")
        return v

    @root_validator
    def validate_regulatory_compliance(cls, values):
        """Validate regulatory framework requirements."""
        frameworks = values.get('regulatory_frameworks', [])
        document_type = values.get('document_type')

        # Specific validation rules for document types
        if document_type == DocumentType.VALIDATION_PROTOCOL:
            required_frameworks = [RegulatoryFramework.FDA_21_CFR_PART_11, RegulatoryFramework.EU_GMP]
            if not any(fw in frameworks for fw in required_frameworks):
                raise ValueError("Validation protocols must include FDA 21 CFR Part 11 or EU GMP")

        return values

    @validator('version')
    def validate_version_format(cls, v):
        """Validate version follows semantic versioning."""
        parts = v.split('.')
        if len(parts) != 2:
            raise ValueError("Version must be in format X.Y")

        try:
            major, minor = int(parts[0]), int(parts[1])
            if major < 1 or minor < 0:
                raise ValueError("Version numbers must be positive")
        except ValueError:
            raise ValueError("Version parts must be integers")

        return v

    class Config:
        # Follow DEVELOPMENT_RULES_2.md - Strict validation
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PharmaceuticalAnalysisRequest(BaseModel):
    """Request model for pharmaceutical document analysis."""

    # Request identification
    request_id: constr(regex=r'^REQ-[A-Z0-9]{12}$') = Field(
        ...,
        description="Request ID in format REQ-XXXXXXXXXXXX"
    )

    # Document reference
    document: PharmaceuticalDocument = Field(
        ...,
        description="Document to analyze"
    )

    # Analysis parameters
    analysis_types: List[Literal["compliance", "risk", "summary", "regulatory"]] = Field(
        ...,
        min_items=1,
        max_items=4,
        description="Types of analysis to perform"
    )
    priority: Literal["low", "medium", "high", "critical"] = Field(
        default="medium",
        description="Analysis priority level"
    )

    # AI model configuration
    model_temperature: confloat(ge=0.0, le=1.0) = Field(
        default=0.1,
        description="AI model temperature (0.0-1.0)"
    )
    max_tokens: conint(ge=100, le=8192) = Field(
        default=4096,
        description="Maximum tokens for AI response"
    )

    # User context
    requested_by: constr(regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') = Field(
        ...,
        description="Requester email address"
    )
    user_role: Literal["quality_manager", "regulatory_lead", "compliance_officer", "analyst"] = Field(
        ...,
        description="User role for access control"
    )

    @validator('analysis_types')
    def validate_analysis_types(cls, v):
        """Ensure no duplicate analysis types."""
        if len(v) != len(set(v)):
            raise ValueError("Duplicate analysis types not allowed")
        return v

    @root_validator
    def validate_user_permissions(cls, values):
        """Validate user has permission for requested analysis types."""
        user_role = values.get('user_role')
        analysis_types = values.get('analysis_types', [])

        # Define role-based permissions
        role_permissions = {
            "quality_manager": ["compliance", "risk", "summary", "regulatory"],
            "regulatory_lead": ["compliance", "regulatory", "summary"],
            "compliance_officer": ["compliance", "summary"],
            "analyst": ["summary"]
        }

        allowed_types = role_permissions.get(user_role, [])

        for analysis_type in analysis_types:
            if analysis_type not in allowed_types:
                raise ValueError(f"User role '{user_role}' not authorized for '{analysis_type}' analysis")

        return values

    class Config:
        validate_assignment = True
        use_enum_values = True
        extra = "forbid"

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadPydanticModel:
    def __init__(self, data: Any):  # No type safety
        self.data = data  # No validation
        # No field constraints, no error handling
```

### **ChromaDB 1.0.15+ Vector Store Optimization (July 2025 Latest)**

#### **Pharmaceutical Knowledge Base with Embedding Strategies**
```python
# ✅ CORRECT: Latest July 2025 ChromaDB patterns with Context7 research
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
import hashlib
import json
import os

class PharmaceuticalVectorStore:
    """Enterprise-grade ChromaDB vector store for pharmaceutical knowledge."""

    def __init__(self, persist_directory: str = "./data/chromadb"):
        # Follow DEVELOPMENT_RULES.md - Ensure directory exists
        os.makedirs(persist_directory, exist_ok=True)

        # Latest July 2025 ChromaDB configuration
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                anonymized_telemetry=False,  # Disable for pharmaceutical compliance
                allow_reset=False,  # Prevent accidental data loss
                is_persistent=True
            )
        )

        # Use pharmaceutical-optimized embedding model
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

        # Custom embedding function for pharmaceutical documents
        self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="all-MiniLM-L6-v2",
            normalize_embeddings=True  # Improve similarity search
        )

        # Create pharmaceutical knowledge collection
        self.collection = self._get_or_create_collection()

    def _get_or_create_collection(self):
        """Get or create pharmaceutical knowledge collection."""
        collection_name = "pharmaceutical_knowledge_base"

        try:
            # Try to get existing collection
            collection = self.client.get_collection(
                name=collection_name,
                embedding_function=self.embedding_function
            )
            logging.info(f"Retrieved existing collection: {collection_name}")

        except Exception:
            # Create new collection
            collection = self.client.create_collection(
                name=collection_name,
                embedding_function=self.embedding_function,
                metadata={
                    "description": "Pharmaceutical regulatory knowledge base",
                    "version": "1.0.0",
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "compliance_frameworks": ["21_cfr_part_11", "eu_gmp", "ich_q7", "iso_13485"]
                }
            )
            logging.info(f"Created new collection: {collection_name}")

        return collection

    async def add_pharmaceutical_documents(
        self,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Add pharmaceutical documents to vector store with optimization."""

        if not documents:
            raise ValueError("Documents list cannot be empty")

        # Prepare documents for ChromaDB
        doc_ids = []
        doc_texts = []
        doc_metadatas = []

        for doc in documents:
            # Follow DEVELOPMENT_RULES_2.md - Input validation
            if not isinstance(doc, dict) or 'content' not in doc:
                raise ValueError("Each document must be a dict with 'content' field")

            # Generate unique document ID
            doc_id = self._generate_document_id(doc)
            doc_ids.append(doc_id)

            # Extract and optimize text content
            optimized_content = self._optimize_document_content(doc['content'])
            doc_texts.append(optimized_content)

            # Prepare metadata with pharmaceutical context
            metadata = self._prepare_pharmaceutical_metadata(doc)
            doc_metadatas.append(metadata)

        try:
            # Add documents to collection with batch processing
            batch_size = 100  # Optimize for ChromaDB performance

            for i in range(0, len(documents), batch_size):
                batch_ids = doc_ids[i:i + batch_size]
                batch_texts = doc_texts[i:i + batch_size]
                batch_metadatas = doc_metadatas[i:i + batch_size]

                self.collection.add(
                    documents=batch_texts,
                    metadatas=batch_metadatas,
                    ids=batch_ids
                )

            return {
                "status": "success",
                "documents_added": len(documents),
                "collection_size": self.collection.count(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = secrets.token_urlsafe(8)
            logging.error(f"ChromaDB add operation failed. Error ID: {error_id}")
            raise ValueError(f"Failed to add documents. Error ID: {error_id}")

    async def search_pharmaceutical_knowledge(
        self,
        query: str,
        regulatory_framework: Optional[str] = None,
        document_type: Optional[str] = None,
        n_results: int = 5
    ) -> Dict[str, Any]:
        """Search pharmaceutical knowledge base with advanced filtering."""

        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not query or len(query.strip()) == 0:
            raise ValueError("Query cannot be empty")

        if n_results < 1 or n_results > 50:
            raise ValueError("n_results must be between 1 and 50")

        # Prepare search filters
        where_filter = {}
        if regulatory_framework:
            where_filter["regulatory_framework"] = regulatory_framework
        if document_type:
            where_filter["document_type"] = document_type

        try:
            # Perform semantic search
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where_filter if where_filter else None,
                include=["documents", "metadatas", "distances"]
            )

            # Process and rank results
            processed_results = self._process_search_results(results, query)

            return {
                "query": query,
                "results": processed_results,
                "total_results": len(processed_results),
                "search_filters": where_filter,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = secrets.token_urlsafe(8)
            logging.error(f"ChromaDB search failed. Error ID: {error_id}")
            raise ValueError(f"Search failed. Error ID: {error_id}")

    def _generate_document_id(self, doc: Dict[str, Any]) -> str:
        """Generate unique document ID based on content hash."""
        content = doc.get('content', '')
        title = doc.get('title', '')

        # Create deterministic ID based on content
        id_string = f"{title}:{content}"
        content_hash = hashlib.sha256(id_string.encode()).hexdigest()

        return f"doc_{content_hash[:16]}"

    def _optimize_document_content(self, content: str) -> str:
        """Optimize document content for vector embedding."""

        # Remove excessive whitespace
        import re
        optimized = re.sub(r'\s+', ' ', content.strip())

        # Limit content length for embedding efficiency
        max_length = 8000  # Optimal for sentence transformers
        if len(optimized) > max_length:
            # Truncate at sentence boundary
            sentences = optimized.split('. ')
            truncated = ''
            for sentence in sentences:
                if len(truncated + sentence) > max_length:
                    break
                truncated += sentence + '. '
            optimized = truncated.strip()

        return optimized

    def _prepare_pharmaceutical_metadata(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare pharmaceutical-specific metadata."""

        metadata = {
            "title": doc.get('title', 'Unknown'),
            "document_type": doc.get('document_type', 'unknown'),
            "regulatory_framework": doc.get('regulatory_framework', 'general'),
            "source": doc.get('source', 'unknown'),
            "date_created": doc.get('date_created', datetime.now(timezone.utc).isoformat()),
            "content_length": len(doc.get('content', '')),
            "language": doc.get('language', 'en'),
            "compliance_level": doc.get('compliance_level', 'standard')
        }

        # Add pharmaceutical-specific tags
        content_lower = doc.get('content', '').lower()

        # Detect pharmaceutical keywords
        pharma_keywords = {
            "gmp": "good_manufacturing_practice",
            "validation": "validation_protocol",
            "audit": "audit_documentation",
            "cfr": "fda_regulation",
            "ich": "ich_guideline",
            "iso": "iso_standard"
        }

        detected_tags = []
        for keyword, tag in pharma_keywords.items():
            if keyword in content_lower:
                detected_tags.append(tag)

        metadata["pharmaceutical_tags"] = detected_tags

        return metadata

    def _process_search_results(self, results: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Process and enhance search results."""

        processed_results = []

        if not results['documents'] or not results['documents'][0]:
            return processed_results

        documents = results['documents'][0]
        metadatas = results['metadatas'][0] if results['metadatas'] else []
        distances = results['distances'][0] if results['distances'] else []

        for i, document in enumerate(documents):
            metadata = metadatas[i] if i < len(metadatas) else {}
            distance = distances[i] if i < len(distances) else 1.0

            # Calculate similarity score (1 - distance for cosine similarity)
            similarity_score = max(0.0, 1.0 - distance)

            # Extract relevant snippet
            snippet = self._extract_relevant_snippet(document, query)

            processed_result = {
                "document": document,
                "snippet": snippet,
                "metadata": metadata,
                "similarity_score": round(similarity_score, 4),
                "relevance_rank": i + 1
            }

            processed_results.append(processed_result)

        return processed_results

    def _extract_relevant_snippet(self, document: str, query: str, snippet_length: int = 200) -> str:
        """Extract most relevant snippet from document."""

        # Simple keyword-based snippet extraction
        query_words = query.lower().split()
        doc_lower = document.lower()

        # Find best position for snippet
        best_position = 0
        best_score = 0

        for i in range(0, len(document) - snippet_length, 50):
            snippet_text = doc_lower[i:i + snippet_length]
            score = sum(1 for word in query_words if word in snippet_text)

            if score > best_score:
                best_score = score
                best_position = i

        # Extract snippet and clean up
        snippet = document[best_position:best_position + snippet_length]

        # Ensure snippet starts and ends at word boundaries
        words = snippet.split()
        if len(words) > 1:
            snippet = ' '.join(words[1:-1])  # Remove partial words at edges

        return snippet + "..." if len(snippet) < len(document) else snippet

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadVectorStore:
    def __init__(self):
        self.client = chromadb.Client()  # No persistence, no configuration

    def add_docs(self, docs):
        # No validation, no error handling, no optimization
        self.client.add(docs)
```

### **FastAPI 0.115.5+ Enterprise Patterns (July 2025 Latest)**

#### **Pharmaceutical API with Security and Validation**
```python
# ✅ CORRECT: Latest July 2025 FastAPI patterns with Context7 research
from fastapi import FastAPI, HTTPException, Depends, Security, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exception_handlers import http_exception_handler
import asyncio
import time
from typing import Dict, Any, Optional
import logging

# Security scheme
security = HTTPBearer(auto_error=False)

class PharmaceuticalAPIApp:
    """Enterprise-grade FastAPI application for pharmaceutical compliance."""

    def __init__(self):
        # Follow DEVELOPMENT_RULES.md - Latest FastAPI configuration
        self.app = FastAPI(
            title="VigiLens Pharmaceutical Compliance API",
            description="AI-powered pharmaceutical compliance analysis",
            version="1.0.0",
            docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
            redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
            openapi_url="/openapi.json" if os.getenv("ENVIRONMENT") != "production" else None
        )

        self._configure_middleware()
        self._configure_exception_handlers()
        self._configure_routes()

    def _configure_middleware(self):
        """Configure FastAPI middleware following DEVELOPMENT_RULES.md."""

        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["http://localhost:3000", "https://vigilens.ai"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE"],
            allow_headers=["*"],
        )

        # Trusted host middleware for production
        if os.getenv("ENVIRONMENT") == "production":
            self.app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=["*.vigilens.ai", "localhost"]
            )

        # Request timing middleware
        @self.app.middleware("http")
        async def add_process_time_header(request: Request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            return response

        # Security headers middleware
        @self.app.middleware("http")
        async def add_security_headers(request: Request, call_next):
            response = await call_next(request)
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            return response

    def _configure_exception_handlers(self):
        """Configure secure exception handlers following DEVELOPMENT_RULES_2.md."""

        @self.app.exception_handler(HTTPException)
        async def custom_http_exception_handler(request: Request, exc: HTTPException):
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = secrets.token_urlsafe(8)

            # Log error with ID only (no sensitive data)
            logging.error(f"HTTP exception occurred. Error ID: {error_id}, Status: {exc.status_code}")

            return JSONResponse(
                status_code=exc.status_code,
                content={
                    "error": "Request failed",
                    "error_id": error_id,
                    "status_code": exc.status_code,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

        @self.app.exception_handler(ValidationError)
        async def validation_exception_handler(request: Request, exc: ValidationError):
            # Follow DEVELOPMENT_RULES_2.md - Secure validation error handling
            error_id = secrets.token_urlsafe(8)

            logging.warning(f"Validation error occurred. Error ID: {error_id}")

            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "error": "Validation failed",
                    "error_id": error_id,
                    "validation_errors": len(exc.errors()),  # Count only, not details
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

    def _configure_routes(self):
        """Configure API routes with pharmaceutical compliance focus."""

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint for monitoring."""
            return {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0",
                "service": "pharmaceutical_compliance_api"
            }

        @self.app.post("/api/v1/analyze/document")
        async def analyze_pharmaceutical_document(
            request: PharmaceuticalAnalysisRequest,
            credentials: HTTPAuthorizationCredentials = Security(security)
        ):
            """Analyze pharmaceutical document for compliance."""

            # Follow DEVELOPMENT_RULES_2.md - Authentication required
            if not credentials:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )

            try:
                # Validate JWT token (implementation depends on auth system)
                user_context = await self._validate_jwt_token(credentials.credentials)

                # Process document analysis
                ai_processor = SecureAIProcessor(
                    api_key=os.getenv("OPENROUTER_API_KEY")
                )

                # Create AI request
                ai_request = PharmaceuticalAIRequest(
                    request_id=request.request_id,
                    user_id=user_context["user_id"],
                    document_content=request.document.content,
                    analysis_type=request.analysis_types[0],  # Primary analysis type
                    regulatory_framework=request.document.regulatory_frameworks[0].value,
                    organization_id=request.document.organization_id,
                    content_hash=request.document.content_hash
                )

                # Process with AI
                result = await ai_processor.process_pharmaceutical_document(ai_request)

                return {
                    "status": "success",
                    "request_id": request.request_id,
                    "analysis_result": result,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }

            except Exception as e:
                # Follow DEVELOPMENT_RULES_2.md - Secure error handling
                error_id = secrets.token_urlsafe(8)
                logging.error(f"Document analysis failed. Error ID: {error_id}")

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Analysis failed. Error ID: {error_id}"
                )

        @self.app.post("/api/v1/search/knowledge")
        async def search_pharmaceutical_knowledge(
            query: str,
            regulatory_framework: Optional[str] = None,
            n_results: int = 5,
            credentials: HTTPAuthorizationCredentials = Security(security)
        ):
            """Search pharmaceutical knowledge base."""

            # Follow DEVELOPMENT_RULES_2.md - Input validation
            if not query or len(query.strip()) < 3:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Query must be at least 3 characters"
                )

            if n_results < 1 or n_results > 20:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="n_results must be between 1 and 20"
                )

            try:
                # Initialize vector store
                vector_store = PharmaceuticalVectorStore()

                # Perform search
                results = await vector_store.search_pharmaceutical_knowledge(
                    query=query.strip(),
                    regulatory_framework=regulatory_framework,
                    n_results=n_results
                )

                return {
                    "status": "success",
                    "search_results": results,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }

            except Exception as e:
                error_id = secrets.token_urlsafe(8)
                logging.error(f"Knowledge search failed. Error ID: {error_id}")

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Search failed. Error ID: {error_id}"
                )

    async def _validate_jwt_token(self, token: str) -> Dict[str, Any]:
        """Validate JWT token and return user context."""

        # Placeholder for JWT validation logic
        # In production, this would validate against your auth system

        if not token or len(token) < 10:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

        # Return mock user context for demonstration
        return {
            "user_id": "user_123",
            "email": "<EMAIL>",
            "role": "quality_manager",
            "organization_id": "ORG-ABC123"
        }

# Create application instance
pharmaceutical_api = PharmaceuticalAPIApp()
app = pharmaceutical_api.app

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
app_bad = FastAPI()  # No configuration, no security, no error handling

@app_bad.post("/analyze")
def bad_analyze(data: Any):  # No type safety, no validation
    # No authentication, no error handling, no audit trail
    return {"result": "processed"}
```

### **HTTPX Async HTTP Client Patterns (July 2025 Latest)**

#### **Enterprise HTTP Client with Retry Logic and Security**
```python
# ✅ CORRECT: Latest July 2025 HTTPX patterns with Context7 research
import httpx
import asyncio
from typing import Dict, Any, Optional, List
import logging
import time
from urllib.parse import urljoin

class EnterpriseHTTPClient:
    """Enterprise-grade HTTPX client for pharmaceutical API integrations."""

    def __init__(
        self,
        base_url: str,
        api_key: Optional[str] = None,
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not base_url or not base_url.startswith(('http://', 'https://')):
            raise ValueError("Invalid base URL provided")

        self.base_url = base_url.rstrip('/')
        self.max_retries = max_retries

        # Configure secure HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(
                max_keepalive_connections=10,
                max_connections=20,
                keepalive_expiry=30.0
            ),
            headers={
                "User-Agent": "VigiLens-Pharmaceutical-API/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        )

        # Add authentication if provided
        if api_key:
            self.client.headers["Authorization"] = f"Bearer {api_key}"

    async def post_with_retry(
        self,
        endpoint: str,
        data: Dict[str, Any],
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """POST request with exponential backoff retry logic."""

        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not endpoint:
            raise ValueError("Endpoint cannot be empty")

        if not isinstance(data, dict):
            raise ValueError("Data must be a dictionary")

        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        for attempt in range(self.max_retries):
            try:
                # Add request timing
                start_time = time.time()

                response = await self.client.post(
                    url,
                    json=data,
                    headers=headers or {}
                )

                request_time = time.time() - start_time

                # Log successful request
                logging.info(f"HTTP POST successful: {url} ({request_time:.2f}s)")

                # Handle response
                response.raise_for_status()

                return {
                    "status": "success",
                    "data": response.json(),
                    "status_code": response.status_code,
                    "request_time": request_time,
                    "attempt": attempt + 1
                }

            except httpx.HTTPStatusError as e:
                # Handle HTTP errors
                if e.response.status_code in [400, 401, 403, 404]:
                    # Don't retry client errors
                    logging.error(f"HTTP client error: {e.response.status_code}")
                    raise ValueError(f"HTTP {e.response.status_code}: {e.response.text}")

                if attempt == self.max_retries - 1:
                    logging.error(f"HTTP request failed after {self.max_retries} attempts")
                    raise ValueError(f"HTTP request failed: {e.response.status_code}")

                # Exponential backoff for server errors
                wait_time = 2 ** attempt
                logging.warning(f"HTTP {e.response.status_code}, retrying in {wait_time}s")
                await asyncio.sleep(wait_time)

            except httpx.TimeoutException:
                if attempt == self.max_retries - 1:
                    logging.error("HTTP request timed out after all retries")
                    raise ValueError("Request timed out")

                wait_time = 2 ** attempt
                logging.warning(f"Request timeout, retrying in {wait_time}s")
                await asyncio.sleep(wait_time)

            except Exception as e:
                if attempt == self.max_retries - 1:
                    logging.error(f"HTTP request failed: {type(e).__name__}")
                    raise ValueError("Request failed due to network error")

                wait_time = 2 ** attempt
                await asyncio.sleep(wait_time)

        raise RuntimeError("All retry attempts exhausted")

    async def get_with_cache(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        cache_ttl: int = 300
    ) -> Dict[str, Any]:
        """GET request with simple caching mechanism."""

        # Create cache key
        cache_key = f"{endpoint}:{str(params or {})}"

        # Check cache (simplified - in production use Redis or similar)
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        try:
            response = await self.client.get(url, params=params or {})
            response.raise_for_status()

            result = {
                "status": "success",
                "data": response.json(),
                "status_code": response.status_code,
                "cached": False
            }

            # Store in cache
            self._store_in_cache(cache_key, result, cache_ttl)

            return result

        except Exception as e:
            logging.error(f"GET request failed: {type(e).__name__}")
            raise ValueError("GET request failed")

    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """Simple in-memory cache (replace with Redis in production)."""
        # Placeholder for cache implementation
        return None

    def _store_in_cache(self, key: str, value: Dict[str, Any], ttl: int):
        """Store value in cache with TTL."""
        # Placeholder for cache implementation
        pass

    async def close(self):
        """Close HTTP client connection."""
        await self.client.aclose()

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadHTTPClient:
    def __init__(self):
        self.client = httpx.Client()  # No async, no configuration

    def post(self, url, data):
        # No error handling, no retries, no validation
        return self.client.post(url, json=data)
```

### **Sentence Transformers Optimization (July 2025 Latest)**

#### **Pharmaceutical Document Embedding Strategies**
```python
# ✅ CORRECT: Latest July 2025 Sentence Transformers patterns
from sentence_transformers import SentenceTransformer, util
import numpy as np
from typing import List, Dict, Any, Tuple
import torch
import logging

class PharmaceuticalEmbeddingModel:
    """Optimized embedding model for pharmaceutical documents."""

    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        # Follow DEVELOPMENT_RULES.md - Validate model availability
        supported_models = [
            "all-MiniLM-L6-v2",  # Fast, good quality
            "all-mpnet-base-v2",  # Higher quality, slower
            "multi-qa-MiniLM-L6-cos-v1"  # Optimized for Q&A
        ]

        if model_name not in supported_models:
            raise ValueError(f"Unsupported model. Use one of: {supported_models}")

        # Load model with optimization
        self.model = SentenceTransformer(model_name)

        # Configure for pharmaceutical use
        self.model.max_seq_length = 512  # Optimize for regulatory text

        # Enable GPU if available
        if torch.cuda.is_available():
            self.model = self.model.cuda()
            logging.info("Using GPU for embeddings")

        self.model_name = model_name
        self.embedding_dimension = self.model.get_sentence_embedding_dimension()

    async def embed_pharmaceutical_documents(
        self,
        documents: List[str],
        batch_size: int = 32
    ) -> np.ndarray:
        """Generate embeddings for pharmaceutical documents with optimization."""

        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not documents:
            raise ValueError("Documents list cannot be empty")

        if not all(isinstance(doc, str) for doc in documents):
            raise ValueError("All documents must be strings")

        # Preprocess documents for pharmaceutical context
        processed_docs = [
            self._preprocess_pharmaceutical_text(doc)
            for doc in documents
        ]

        try:
            # Generate embeddings in batches for memory efficiency
            embeddings = []

            for i in range(0, len(processed_docs), batch_size):
                batch = processed_docs[i:i + batch_size]

                # Generate embeddings with normalization
                batch_embeddings = self.model.encode(
                    batch,
                    batch_size=batch_size,
                    show_progress_bar=False,
                    normalize_embeddings=True,  # Improve similarity search
                    convert_to_numpy=True
                )

                embeddings.append(batch_embeddings)

            # Concatenate all embeddings
            all_embeddings = np.vstack(embeddings)

            logging.info(f"Generated embeddings for {len(documents)} documents")

            return all_embeddings

        except Exception as e:
            logging.error(f"Embedding generation failed: {type(e).__name__}")
            raise ValueError("Failed to generate embeddings")

    def _preprocess_pharmaceutical_text(self, text: str) -> str:
        """Preprocess text for pharmaceutical embedding optimization."""

        # Remove excessive whitespace
        import re
        processed = re.sub(r'\s+', ' ', text.strip())

        # Expand pharmaceutical abbreviations for better embeddings
        pharma_expansions = {
            "CFR": "Code of Federal Regulations",
            "GMP": "Good Manufacturing Practice",
            "API": "Active Pharmaceutical Ingredient",
            "QA": "Quality Assurance",
            "QC": "Quality Control",
            "SOP": "Standard Operating Procedure",
            "FDA": "Food and Drug Administration",
            "EMA": "European Medicines Agency",
            "ICH": "International Council for Harmonisation"
        }

        for abbrev, expansion in pharma_expansions.items():
            # Replace abbreviation with expansion (case-insensitive)
            pattern = r'\b' + re.escape(abbrev) + r'\b'
            processed = re.sub(pattern, f"{abbrev} ({expansion})", processed, flags=re.IGNORECASE)

        # Limit length for optimal embedding
        max_length = 8000  # Optimal for sentence transformers
        if len(processed) > max_length:
            # Truncate at sentence boundary
            sentences = processed.split('. ')
            truncated = ''
            for sentence in sentences:
                if len(truncated + sentence) > max_length:
                    break
                truncated += sentence + '. '
            processed = truncated.strip()

        return processed

    async def find_similar_documents(
        self,
        query: str,
        document_embeddings: np.ndarray,
        document_texts: List[str],
        top_k: int = 5,
        similarity_threshold: float = 0.3
    ) -> List[Dict[str, Any]]:
        """Find similar pharmaceutical documents using semantic search."""

        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not query or len(query.strip()) == 0:
            raise ValueError("Query cannot be empty")

        if document_embeddings.shape[0] != len(document_texts):
            raise ValueError("Embeddings and texts must have same length")

        # Generate query embedding
        query_processed = self._preprocess_pharmaceutical_text(query)
        query_embedding = self.model.encode(
            [query_processed],
            normalize_embeddings=True,
            convert_to_numpy=True
        )

        # Calculate similarities
        similarities = util.cos_sim(query_embedding, document_embeddings)[0]

        # Get top-k similar documents
        top_indices = torch.topk(similarities, k=min(top_k, len(similarities)))[1]

        results = []
        for idx in top_indices:
            similarity_score = similarities[idx].item()

            # Filter by similarity threshold
            if similarity_score >= similarity_threshold:
                results.append({
                    "document_index": idx.item(),
                    "document_text": document_texts[idx],
                    "similarity_score": round(similarity_score, 4),
                    "relevance_category": self._categorize_relevance(similarity_score)
                })

        return results

    def _categorize_relevance(self, score: float) -> str:
        """Categorize relevance based on similarity score."""
        if score >= 0.8:
            return "highly_relevant"
        elif score >= 0.6:
            return "relevant"
        elif score >= 0.4:
            return "somewhat_relevant"
        else:
            return "low_relevance"

    async def cluster_pharmaceutical_documents(
        self,
        embeddings: np.ndarray,
        n_clusters: int = 5
    ) -> Dict[str, Any]:
        """Cluster pharmaceutical documents by semantic similarity."""

        from sklearn.cluster import KMeans
        from sklearn.metrics import silhouette_score

        try:
            # Perform K-means clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(embeddings)

            # Calculate clustering quality
            silhouette_avg = silhouette_score(embeddings, cluster_labels)

            # Analyze clusters
            cluster_analysis = {}
            for cluster_id in range(n_clusters):
                cluster_indices = np.where(cluster_labels == cluster_id)[0]
                cluster_size = len(cluster_indices)

                # Calculate cluster centroid
                cluster_centroid = np.mean(embeddings[cluster_indices], axis=0)

                cluster_analysis[f"cluster_{cluster_id}"] = {
                    "size": cluster_size,
                    "document_indices": cluster_indices.tolist(),
                    "centroid": cluster_centroid.tolist()
                }

            return {
                "cluster_labels": cluster_labels.tolist(),
                "n_clusters": n_clusters,
                "silhouette_score": round(silhouette_avg, 4),
                "cluster_analysis": cluster_analysis
            }

        except Exception as e:
            logging.error(f"Document clustering failed: {type(e).__name__}")
            raise ValueError("Clustering failed")

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadEmbeddingModel:
    def __init__(self):
        self.model = SentenceTransformer("all-MiniLM-L6-v2")  # No optimization

    def embed(self, texts):
        # No validation, no preprocessing, no error handling
        return self.model.encode(texts)
```

---

## 🛡️ **Enterprise Error Handling & Logging**

### **Pharmaceutical-Grade Error Management**
```python
# ✅ CORRECT: Enterprise error handling following DEVELOPMENT_RULES_2.md
import logging
import traceback
import secrets
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from enum import Enum

class ErrorSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class PharmaceuticalErrorHandler:
    """Enterprise error handler for pharmaceutical compliance systems."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = self._configure_logger()

    def _configure_logger(self) -> logging.Logger:
        """Configure secure logger following DEVELOPMENT_RULES_2.md."""

        logger = logging.getLogger(self.service_name)
        logger.setLevel(logging.INFO)

        # Create formatter that excludes sensitive data
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Console handler for development
        if os.getenv("ENVIRONMENT") != "production":
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # File handler for production
        if os.getenv("ENVIRONMENT") == "production":
            file_handler = logging.FileHandler('/var/log/vigilens/app.log')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def handle_ai_processing_error(
        self,
        error: Exception,
        context: Dict[str, Any],
        severity: ErrorSeverity = ErrorSeverity.HIGH
    ) -> str:
        """Handle AI processing errors with pharmaceutical compliance."""

        # Generate unique error ID
        error_id = secrets.token_urlsafe(12)

        # Follow DEVELOPMENT_RULES_2.md - No sensitive data in logs
        safe_context = self._sanitize_context(context)

        # Log error with appropriate severity
        error_message = f"AI processing error - ID: {error_id}, Type: {type(error).__name__}"

        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(error_message, extra={"error_id": error_id, "context": safe_context})
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(error_message, extra={"error_id": error_id, "context": safe_context})
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(error_message, extra={"error_id": error_id, "context": safe_context})
        else:
            self.logger.info(error_message, extra={"error_id": error_id, "context": safe_context})

        # Store detailed error information securely (not in logs)
        self._store_error_details(error_id, error, context)

        return error_id

    def _sanitize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize context to remove sensitive data."""

        sensitive_keys = [
            "password", "token", "api_key", "secret", "credential",
            "email", "phone", "ssn", "patient_id", "user_data"
        ]

        sanitized = {}
        for key, value in context.items():
            key_lower = key.lower()

            # Check if key contains sensitive information
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, str) and len(value) > 100:
                # Truncate long strings
                sanitized[key] = value[:100] + "..."
            else:
                sanitized[key] = value

        return sanitized

    def _store_error_details(self, error_id: str, error: Exception, context: Dict[str, Any]):
        """Store detailed error information in secure storage."""

        error_details = {
            "error_id": error_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context,
            "service": self.service_name
        }

        # In production, store in secure database or encrypted file
        # For now, placeholder implementation
        pass

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
def bad_error_handling():
    try:
        # Some operation
        pass
    except Exception as e:
        print(f"Error: {e}")  # Exposes sensitive data
        print(traceback.format_exc())  # Exposes stack trace
        # No error ID, no secure logging
```

---

## 🧪 **Testing Strategies for Pharmaceutical AI**

### **Comprehensive AI Testing Framework**
```python
# ✅ CORRECT: Pharmaceutical AI testing following DEVELOPMENT_RULES_2.md
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List
import numpy as np

class PharmaceuticalAITestSuite:
    """Comprehensive test suite for pharmaceutical AI systems."""

    @pytest.fixture
    async def ai_processor(self):
        """Create AI processor for testing."""
        return SecureAIProcessor(api_key="test_key_12345")

    @pytest.fixture
    def sample_pharmaceutical_request(self):
        """Create sample pharmaceutical analysis request."""
        return PharmaceuticalAIRequest(
            request_id="REQ-TEST123456",
            user_id="user_test_123",
            document_content="This is a test pharmaceutical document for GMP compliance analysis.",
            analysis_type="compliance",
            regulatory_framework="21_cfr_part_11",
            organization_id="ORG-TEST01",
            content_hash=hashlib.sha256("This is a test pharmaceutical document for GMP compliance analysis.".encode()).hexdigest()
        )

    @pytest.mark.asyncio
    async def test_ai_processing_success(self, ai_processor, sample_pharmaceutical_request):
        """Test successful AI processing with pharmaceutical compliance."""

        # Mock OpenRouter API response
        mock_response = {
            "choices": [{
                "message": {
                    "content": "This document demonstrates compliance with 21 CFR Part 11 requirements for electronic records and signatures."
                }
            }]
        }

        with patch.object(ai_processor.client, 'post') as mock_post:
            mock_post.return_value.json.return_value = mock_response
            mock_post.return_value.raise_for_status.return_value = None

            result = await ai_processor.process_pharmaceutical_document(sample_pharmaceutical_request)

            # Assertions
            assert result.response_id is not None
            assert result.request_id == sample_pharmaceutical_request.request_id
            assert result.confidence_score >= 0.0
            assert result.confidence_score <= 1.0
            assert result.compliance_validated is not None
            assert len(result.audit_trail) > 0
            assert result.model_name == "moonshot/kimi-k2"

    @pytest.mark.asyncio
    async def test_ai_processing_input_validation(self, ai_processor):
        """Test AI processing input validation."""

        # Test empty content
        with pytest.raises(ValueError, match="Content hash does not match"):
            invalid_request = PharmaceuticalAIRequest(
                request_id="REQ-TEST123456",
                user_id="user_test_123",
                document_content="Valid content",
                analysis_type="compliance",
                regulatory_framework="21_cfr_part_11",
                organization_id="ORG-TEST01",
                content_hash="invalid_hash"
            )

        # Test invalid analysis type
        with pytest.raises(ValueError):
            PharmaceuticalAIRequest(
                request_id="REQ-TEST123456",
                user_id="user_test_123",
                document_content="Valid content",
                analysis_type="invalid_type",  # Invalid
                regulatory_framework="21_cfr_part_11",
                organization_id="ORG-TEST01",
                content_hash=hashlib.sha256("Valid content".encode()).hexdigest()
            )

    @pytest.mark.asyncio
    async def test_vector_store_operations(self):
        """Test ChromaDB vector store operations."""

        vector_store = PharmaceuticalVectorStore(persist_directory="./test_data/chromadb")

        # Test document addition
        test_documents = [
            {
                "content": "FDA 21 CFR Part 11 compliance requirements for electronic records",
                "title": "21 CFR Part 11 Guide",
                "document_type": "regulatory_guidance",
                "regulatory_framework": "21_cfr_part_11"
            },
            {
                "content": "Good Manufacturing Practice guidelines for pharmaceutical manufacturing",
                "title": "GMP Guidelines",
                "document_type": "regulatory_guidance",
                "regulatory_framework": "eu_gmp"
            }
        ]

        result = await vector_store.add_pharmaceutical_documents(test_documents)

        assert result["status"] == "success"
        assert result["documents_added"] == 2

        # Test search functionality
        search_results = await vector_store.search_pharmaceutical_knowledge(
            query="electronic records compliance",
            regulatory_framework="21_cfr_part_11",
            n_results=5
        )

        assert search_results["total_results"] > 0
        assert search_results["query"] == "electronic records compliance"

        # Cleanup test data
        import shutil
        shutil.rmtree("./test_data", ignore_errors=True)

    def test_pydantic_model_validation(self):
        """Test Pydantic model validation for pharmaceutical documents."""

        # Test valid document
        valid_doc = PharmaceuticalDocument(
            document_id="DOC-TEST1234",
            title="Test Pharmaceutical Document",
            document_type=DocumentType.SOP,
            content="This is a test standard operating procedure for pharmaceutical manufacturing.",
            version="1.0",
            regulatory_frameworks=[RegulatoryFramework.FDA_21_CFR_PART_11],
            created_by="<EMAIL>",
            content_hash=hashlib.sha256("This is a test standard operating procedure for pharmaceutical manufacturing.".encode()).hexdigest(),
            organization_id="ORG-TEST01",
            department="Quality Assurance"
        )

        assert valid_doc.document_id == "DOC-TEST1234"
        assert valid_doc.compliance_status == "under_review"

        # Test invalid document ID format
        with pytest.raises(ValueError):
            PharmaceuticalDocument(
                document_id="INVALID-ID",  # Wrong format
                title="Test Document",
                document_type=DocumentType.SOP,
                content="Test content",
                version="1.0",
                regulatory_frameworks=[RegulatoryFramework.FDA_21_CFR_PART_11],
                created_by="<EMAIL>",
                content_hash=hashlib.sha256("Test content".encode()).hexdigest(),
                organization_id="ORG-TEST01",
                department="QA"
            )

    @pytest.mark.asyncio
    async def test_error_handling_security(self, ai_processor):
        """Test secure error handling following DEVELOPMENT_RULES_2.md."""

        # Test API failure handling
        with patch.object(ai_processor.client, 'post') as mock_post:
            mock_post.side_effect = Exception("API Error")

            with pytest.raises(ValueError, match="Error ID:"):
                await ai_processor.process_pharmaceutical_document(
                    PharmaceuticalAIRequest(
                        request_id="REQ-TEST123456",
                        user_id="user_test_123",
                        document_content="Test content",
                        analysis_type="compliance",
                        regulatory_framework="21_cfr_part_11",
                        organization_id="ORG-TEST01",
                        content_hash=hashlib.sha256("Test content".encode()).hexdigest()
                    )
                )

    def test_performance_requirements(self):
        """Test performance requirements for pharmaceutical AI."""

        # Test embedding generation performance
        embedding_model = PharmaceuticalEmbeddingModel()

        test_documents = [
            "FDA regulation compliance document " * 100,  # Large document
            "Short regulatory text",
            "Medium length pharmaceutical guideline document with various compliance requirements"
        ]

        import time
        start_time = time.time()

        embeddings = asyncio.run(embedding_model.embed_pharmaceutical_documents(test_documents))

        processing_time = time.time() - start_time

        # Performance assertions
        assert processing_time < 10.0  # Should process within 10 seconds
        assert embeddings.shape[0] == len(test_documents)
        assert embeddings.shape[1] == embedding_model.embedding_dimension

    @pytest.mark.asyncio
    async def test_regulatory_compliance_validation(self):
        """Test regulatory compliance validation."""

        validator = RegulatoryComplianceValidator()

        # Create test regulatory record
        test_record = RegulatoryRecord(
            record_id="REG-TEST123456",
            record_type="validation_protocol",
            title="Test Validation Protocol",
            applicable_frameworks=[RegulatoryFramework.FDA_21_CFR_PART_11],
            content="This is a test validation protocol for pharmaceutical systems.",
            content_hash=hashlib.sha256("This is a test validation protocol for pharmaceutical systems.".encode()).hexdigest(),
            version="1.0",
            created_by_user_id="user_123",
            created_by_full_name="Test User",
            organization_id="ORG-TEST01",
            department="Quality Assurance"
        )

        # Add electronic signature
        test_record.add_electronic_signature(
            user_id="user_123",
            user_name="Test User",
            signature_meaning="approved",
            digital_signature="test_signature_hash"
        )

        # Validate compliance
        validation_result = validator.validate_record_compliance(test_record)

        assert validation_result["is_compliant"] is True
        assert validation_result["compliance_score"] > 0.0
        assert "21_cfr_part_11" in validation_result["framework_results"]

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
def bad_test():
    # No async support, no error handling, no validation
    result = some_function()
    assert result == "expected"  # No proper assertions
```

---

## 📊 **Monitoring & Observability**

### **Pharmaceutical AI Monitoring System**
```python
# ✅ CORRECT: Enterprise monitoring following DEVELOPMENT_RULES.md
import time
import psutil
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timezone
import asyncio

@dataclass
class AIPerformanceMetrics:
    """AI performance metrics for pharmaceutical compliance."""

    # Processing metrics
    request_count: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0

    # AI model metrics
    model_accuracy: float = 0.0
    confidence_scores: List[float] = None
    compliance_validation_rate: float = 0.0

    # System metrics
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0

    # Regulatory metrics
    audit_trail_completeness: float = 0.0
    electronic_signature_rate: float = 0.0

    def __post_init__(self):
        if self.confidence_scores is None:
            self.confidence_scores = []

class PharmaceuticalAIMonitor:
    """Comprehensive monitoring for pharmaceutical AI systems."""

    def __init__(self):
        self.metrics = AIPerformanceMetrics()
        self.start_time = time.time()
        self.request_times = []

    async def track_ai_request(
        self,
        request_id: str,
        processing_time: float,
        success: bool,
        confidence_score: Optional[float] = None,
        compliance_validated: bool = False
    ):
        """Track AI request metrics."""

        self.metrics.request_count += 1

        if success:
            self.metrics.successful_requests += 1
            self.request_times.append(processing_time)

            if confidence_score is not None:
                self.metrics.confidence_scores.append(confidence_score)

            if compliance_validated:
                self.metrics.compliance_validation_rate = (
                    self.metrics.compliance_validation_rate * (self.metrics.successful_requests - 1) + 1
                ) / self.metrics.successful_requests
        else:
            self.metrics.failed_requests += 1

        # Update average response time
        if self.request_times:
            self.metrics.average_response_time = sum(self.request_times) / len(self.request_times)

        # Log metrics for pharmaceutical audit
        await self._log_request_metrics(request_id, processing_time, success, compliance_validated)

    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system performance metrics."""

        # CPU and memory metrics
        self.metrics.cpu_usage = psutil.cpu_percent(interval=1)
        self.metrics.memory_usage = psutil.virtual_memory().percent
        self.metrics.disk_usage = psutil.disk_usage('/').percent

        # Calculate AI model accuracy
        if self.metrics.confidence_scores:
            self.metrics.model_accuracy = sum(self.metrics.confidence_scores) / len(self.metrics.confidence_scores)

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_seconds": time.time() - self.start_time,
            "performance_metrics": {
                "request_count": self.metrics.request_count,
                "success_rate": self.metrics.successful_requests / max(self.metrics.request_count, 1),
                "average_response_time": self.metrics.average_response_time,
                "model_accuracy": self.metrics.model_accuracy,
                "compliance_validation_rate": self.metrics.compliance_validation_rate
            },
            "system_metrics": {
                "cpu_usage": self.metrics.cpu_usage,
                "memory_usage": self.metrics.memory_usage,
                "disk_usage": self.metrics.disk_usage
            },
            "regulatory_metrics": {
                "audit_trail_completeness": self.metrics.audit_trail_completeness,
                "electronic_signature_rate": self.metrics.electronic_signature_rate
            }
        }

    async def _log_request_metrics(
        self,
        request_id: str,
        processing_time: float,
        success: bool,
        compliance_validated: bool
    ):
        """Log request metrics for pharmaceutical audit."""

        log_entry = {
            "event_type": "ai_request_processed",
            "request_id": request_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "processing_time_ms": processing_time,
            "success": success,
            "compliance_validated": compliance_validated,
            "system_metrics": {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent
            }
        }

        # In production, send to monitoring system (Prometheus, DataDog, etc.)
        logging.info(f"AI Request Metrics: {log_entry}")

    async def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate pharmaceutical compliance monitoring report."""

        current_metrics = await self.collect_system_metrics()

        compliance_report = {
            "report_id": secrets.token_urlsafe(12),
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "reporting_period": {
                "start_time": datetime.fromtimestamp(self.start_time, timezone.utc).isoformat(),
                "end_time": datetime.now(timezone.utc).isoformat(),
                "duration_hours": (time.time() - self.start_time) / 3600
            },
            "ai_performance_summary": {
                "total_requests": self.metrics.request_count,
                "success_rate": self.metrics.successful_requests / max(self.metrics.request_count, 1),
                "average_confidence": self.metrics.model_accuracy,
                "compliance_validation_rate": self.metrics.compliance_validation_rate
            },
            "system_health": {
                "average_cpu_usage": self.metrics.cpu_usage,
                "average_memory_usage": self.metrics.memory_usage,
                "disk_usage": self.metrics.disk_usage
            },
            "regulatory_compliance": {
                "audit_trail_completeness": "100%",  # All requests logged
                "data_integrity_maintained": True,
                "electronic_signature_compliance": self.metrics.electronic_signature_rate >= 0.95
            },
            "recommendations": self._generate_performance_recommendations()
        }

        return compliance_report

    def _generate_performance_recommendations(self) -> List[str]:
        """Generate performance improvement recommendations."""

        recommendations = []

        if self.metrics.average_response_time > 5000:  # 5 seconds
            recommendations.append("Consider optimizing AI model inference time")

        if self.metrics.cpu_usage > 80:
            recommendations.append("High CPU usage detected - consider scaling resources")

        if self.metrics.memory_usage > 85:
            recommendations.append("High memory usage - monitor for memory leaks")

        if self.metrics.model_accuracy < 0.8:
            recommendations.append("AI model accuracy below threshold - review training data")

        if self.metrics.compliance_validation_rate < 0.9:
            recommendations.append("Low compliance validation rate - review regulatory requirements")

        return recommendations

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadMonitoring:
    def __init__(self):
        pass  # No metrics collection, no error handling

    def log_request(self, data):
        print(data)  # No structured logging, no audit trail
```

---

## 🏥 **Pharmaceutical AI Compliance**

### **Complete Regulatory Compliance Framework**
```python
# ✅ CORRECT: Comprehensive pharmaceutical compliance system
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone, timedelta
import asyncio

class PharmaceuticalComplianceFramework:
    """Complete pharmaceutical compliance framework for AI systems."""

    def __init__(self):
        self.regulatory_requirements = self._load_regulatory_requirements()
        self.compliance_monitor = PharmaceuticalAIMonitor()
        self.error_handler = PharmaceuticalErrorHandler("pharmaceutical_ai")

    def _load_regulatory_requirements(self) -> Dict[str, Any]:
        """Load comprehensive regulatory requirements."""
        return {
            "21_cfr_part_11": {
                "electronic_records": True,
                "electronic_signatures": True,
                "audit_trails": True,
                "validation": True,
                "access_controls": True,
                "data_integrity": True
            },
            "eu_gmp_annex_11": {
                "risk_assessment": True,
                "validation": True,
                "data_integrity": True,
                "change_control": True,
                "periodic_review": True,
                "backup_recovery": True
            },
            "ich_q7": {
                "quality_system": True,
                "personnel": True,
                "buildings_facilities": True,
                "equipment": True,
                "documentation": True,
                "materials_management": True
            }
        }

    async def ensure_full_compliance(
        self,
        ai_request: PharmaceuticalAIRequest,
        ai_response: PharmaceuticalAIResponse
    ) -> Dict[str, Any]:
        """Ensure full pharmaceutical compliance for AI operations."""

        compliance_result = {
            "compliance_status": "compliant",
            "validation_results": {},
            "audit_entries": [],
            "recommendations": [],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        try:
            # 1. Validate 21 CFR Part 11 compliance
            cfr_compliance = await self._validate_21_cfr_part_11(ai_request, ai_response)
            compliance_result["validation_results"]["21_cfr_part_11"] = cfr_compliance

            # 2. Validate EU GMP Annex 11 compliance
            gmp_compliance = await self._validate_eu_gmp_annex_11(ai_request, ai_response)
            compliance_result["validation_results"]["eu_gmp_annex_11"] = gmp_compliance

            # 3. Validate ICH Q7 compliance
            ich_compliance = await self._validate_ich_q7(ai_request, ai_response)
            compliance_result["validation_results"]["ich_q7"] = ich_compliance

            # 4. Generate comprehensive audit trail
            audit_entries = await self._generate_compliance_audit_trail(ai_request, ai_response)
            compliance_result["audit_entries"] = audit_entries

            # 5. Check overall compliance status
            all_compliant = all(
                result.get("is_compliant", False)
                for result in compliance_result["validation_results"].values()
            )

            if not all_compliant:
                compliance_result["compliance_status"] = "non_compliant"
                compliance_result["recommendations"] = self._generate_compliance_recommendations(
                    compliance_result["validation_results"]
                )

            return compliance_result

        except Exception as e:
            error_id = self.error_handler.handle_ai_processing_error(
                e,
                {"request_id": ai_request.request_id},
                ErrorSeverity.HIGH
            )

            compliance_result["compliance_status"] = "validation_failed"
            compliance_result["error_id"] = error_id

            return compliance_result

    async def _validate_21_cfr_part_11(
        self,
        ai_request: PharmaceuticalAIRequest,
        ai_response: PharmaceuticalAIResponse
    ) -> Dict[str, Any]:
        """Validate 21 CFR Part 11 compliance."""

        validation_result = {
            "framework": "21_cfr_part_11",
            "is_compliant": True,
            "requirements_checked": [],
            "violations": []
        }

        # Check electronic records requirement
        if ai_response.audit_trail:
            validation_result["requirements_checked"].append("electronic_records")
        else:
            validation_result["violations"].append("Missing electronic records audit trail")
            validation_result["is_compliant"] = False

        # Check data integrity requirement
        if ai_request.content_hash:
            validation_result["requirements_checked"].append("data_integrity")
        else:
            validation_result["violations"].append("Missing content integrity hash")
            validation_result["is_compliant"] = False

        # Check access controls (user authentication)
        if ai_request.user_id:
            validation_result["requirements_checked"].append("access_controls")
        else:
            validation_result["violations"].append("Missing user authentication")
            validation_result["is_compliant"] = False

        return validation_result

    async def _validate_eu_gmp_annex_11(
        self,
        ai_request: PharmaceuticalAIRequest,
        ai_response: PharmaceuticalAIResponse
    ) -> Dict[str, Any]:
        """Validate EU GMP Annex 11 compliance."""

        validation_result = {
            "framework": "eu_gmp_annex_11",
            "is_compliant": True,
            "requirements_checked": [],
            "violations": []
        }

        # Check risk assessment requirement
        if ai_response.confidence_score is not None:
            validation_result["requirements_checked"].append("risk_assessment")
        else:
            validation_result["violations"].append("Missing AI confidence/risk assessment")
            validation_result["is_compliant"] = False

        # Check validation requirement
        if ai_response.compliance_validated:
            validation_result["requirements_checked"].append("validation")
        else:
            validation_result["violations"].append("AI response not validated for compliance")
            validation_result["is_compliant"] = False

        return validation_result

    async def _validate_ich_q7(
        self,
        ai_request: PharmaceuticalAIRequest,
        ai_response: PharmaceuticalAIResponse
    ) -> Dict[str, Any]:
        """Validate ICH Q7 compliance."""

        validation_result = {
            "framework": "ich_q7",
            "is_compliant": True,
            "requirements_checked": [],
            "violations": []
        }

        # Check documentation requirement
        if ai_response.audit_trail and ai_request.organization_id:
            validation_result["requirements_checked"].append("documentation")
        else:
            validation_result["violations"].append("Insufficient documentation for ICH Q7")
            validation_result["is_compliant"] = False

        # Check quality system requirement
        if ai_response.model_name and ai_response.model_version:
            validation_result["requirements_checked"].append("quality_system")
        else:
            validation_result["violations"].append("Missing AI model quality information")
            validation_result["is_compliant"] = False

        return validation_result

    async def _generate_compliance_audit_trail(
        self,
        ai_request: PharmaceuticalAIRequest,
        ai_response: PharmaceuticalAIResponse
    ) -> List[Dict[str, Any]]:
        """Generate comprehensive compliance audit trail."""

        audit_entries = []

        # Request received audit entry
        audit_entries.append({
            "event": "pharmaceutical_ai_request_received",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "request_id": ai_request.request_id,
            "user_id": ai_request.user_id,
            "regulatory_framework": ai_request.regulatory_framework,
            "analysis_type": ai_request.analysis_type
        })

        # Processing completed audit entry
        audit_entries.append({
            "event": "pharmaceutical_ai_processing_completed",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "request_id": ai_request.request_id,
            "response_id": ai_response.response_id,
            "model_used": ai_response.model_name,
            "confidence_score": ai_response.confidence_score,
            "processing_time_ms": ai_response.processing_time_ms
        })

        # Compliance validation audit entry
        audit_entries.append({
            "event": "pharmaceutical_compliance_validated",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "request_id": ai_request.request_id,
            "compliance_validated": ai_response.compliance_validated,
            "regulatory_frameworks_checked": ["21_cfr_part_11", "eu_gmp_annex_11", "ich_q7"]
        })

        return audit_entries

    def _generate_compliance_recommendations(
        self,
        validation_results: Dict[str, Any]
    ) -> List[str]:
        """Generate compliance improvement recommendations."""

        recommendations = []

        for framework, result in validation_results.items():
            if not result.get("is_compliant", True):
                violations = result.get("violations", [])

                for violation in violations:
                    if "audit trail" in violation.lower():
                        recommendations.append(f"Implement comprehensive audit trail for {framework}")
                    elif "signature" in violation.lower():
                        recommendations.append(f"Add electronic signature support for {framework}")
                    elif "validation" in violation.lower():
                        recommendations.append(f"Enhance validation procedures for {framework}")
                    elif "access control" in violation.lower():
                        recommendations.append(f"Strengthen access controls for {framework}")

        return list(set(recommendations))  # Remove duplicates

# Global compliance framework instance
pharmaceutical_compliance = PharmaceuticalComplianceFramework()

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadCompliance:
    def check_compliance(self, data):
        # No validation, no audit trail, no error handling
        return True  # Always returns true - not compliant!
```

---

---

## ⚡ **Performance Optimization Guidelines**

### **AI Model Performance Optimization**
```python
# ✅ CORRECT: Performance-optimized AI processing following DEVELOPMENT_RULES.md
import asyncio
import time
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
import cachetools
from functools import lru_cache

class PerformanceOptimizedAIProcessor:
    """Performance-optimized AI processor for pharmaceutical compliance."""

    def __init__(self, max_workers: int = 4):
        # Follow DEVELOPMENT_RULES_2.md - Algorithmic efficiency standards
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.response_cache = cachetools.TTLCache(maxsize=1000, ttl=3600)  # 1 hour TTL
        self.batch_queue = []
        self.batch_size = 10
        self.batch_timeout = 5.0  # seconds

    async def process_with_batching(
        self,
        requests: List[PharmaceuticalAIRequest]
    ) -> List[PharmaceuticalAIResponse]:
        """Process multiple requests with batching optimization."""

        # Follow DEVELOPMENT_RULES_2.md - Maximum O(n log n) complexity
        if len(requests) > self.batch_size:
            # Process in optimal batches
            batches = [
                requests[i:i + self.batch_size]
                for i in range(0, len(requests), self.batch_size)
            ]

            # Process batches concurrently
            tasks = [self._process_batch(batch) for batch in batches]
            batch_results = await asyncio.gather(*tasks)

            # Flatten results maintaining order
            return [result for batch in batch_results for result in batch]
        else:
            return await self._process_batch(requests)

    async def _process_batch(
        self,
        batch: List[PharmaceuticalAIRequest]
    ) -> List[PharmaceuticalAIResponse]:
        """Process a single batch of requests."""

        # Check cache first
        cached_results = []
        uncached_requests = []

        for request in batch:
            cache_key = self._generate_cache_key(request)
            cached_result = self.response_cache.get(cache_key)

            if cached_result:
                cached_results.append((request, cached_result))
            else:
                uncached_requests.append(request)

        # Process uncached requests
        if uncached_requests:
            # Use thread pool for CPU-intensive operations
            loop = asyncio.get_event_loop()

            tasks = [
                loop.run_in_executor(
                    self.executor,
                    self._process_single_request_sync,
                    request
                )
                for request in uncached_requests
            ]

            uncached_results = await asyncio.gather(*tasks)

            # Cache results
            for request, result in zip(uncached_requests, uncached_results):
                cache_key = self._generate_cache_key(request)
                self.response_cache[cache_key] = result
        else:
            uncached_results = []

        # Combine and sort results to maintain original order
        all_results = []
        cached_iter = iter(cached_results)
        uncached_iter = iter(zip(uncached_requests, uncached_results))

        for original_request in batch:
            # Find matching result
            for cached_request, cached_result in cached_results:
                if cached_request.request_id == original_request.request_id:
                    all_results.append(cached_result)
                    break
            else:
                for uncached_request, uncached_result in zip(uncached_requests, uncached_results):
                    if uncached_request.request_id == original_request.request_id:
                        all_results.append(uncached_result)
                        break

        return all_results

    @lru_cache(maxsize=128)
    def _generate_cache_key(self, request: PharmaceuticalAIRequest) -> str:
        """Generate cache key for request."""
        return f"{request.analysis_type}:{request.regulatory_framework}:{request.content_hash}"

    def _process_single_request_sync(
        self,
        request: PharmaceuticalAIRequest
    ) -> PharmaceuticalAIResponse:
        """Synchronous processing for thread pool execution."""
        # Placeholder for actual AI processing
        # In production, this would call the AI model

        return PharmaceuticalAIResponse(
            response_id=str(secrets.token_urlsafe(16)),
            request_id=request.request_id,
            analysis_results={"analysis": "Processed"},
            confidence_score=0.85,
            processing_time_ms=100,
            model_name="moonshot/kimi-k2",
            model_version="1.0",
            compliance_validated=True,
            audit_trail=[]
        )

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadPerformance:
    def process(self, requests):
        results = []
        for request in requests:  # O(n²) complexity
            for other in requests:  # Nested loop - inefficient
                # No caching, no batching, no optimization
                result = expensive_operation(request, other)
                results.append(result)
        return results
```

### **Memory Management and Resource Optimization**
```python
# ✅ CORRECT: Memory-efficient pharmaceutical AI processing
import gc
import psutil
from typing import Generator, Iterator
import weakref

class MemoryOptimizedProcessor:
    """Memory-optimized processor for large pharmaceutical datasets."""

    def __init__(self, memory_limit_mb: int = 1024):
        self.memory_limit_bytes = memory_limit_mb * 1024 * 1024
        self.active_objects = weakref.WeakSet()

    async def process_large_dataset(
        self,
        documents: Iterator[Dict[str, Any]]
    ) -> Generator[Dict[str, Any], None, None]:
        """Process large datasets with memory management."""

        processed_count = 0

        for document in documents:
            # Check memory usage
            if self._check_memory_usage():
                # Force garbage collection if memory is high
                gc.collect()

                # If still high, yield control to allow cleanup
                if self._check_memory_usage():
                    await asyncio.sleep(0.1)

            # Process document
            result = await self._process_document_memory_efficient(document)

            # Track active objects
            self.active_objects.add(result)

            yield result

            processed_count += 1

            # Periodic cleanup
            if processed_count % 100 == 0:
                gc.collect()

    def _check_memory_usage(self) -> bool:
        """Check if memory usage exceeds limit."""
        process = psutil.Process()
        memory_usage = process.memory_info().rss
        return memory_usage > self.memory_limit_bytes

    async def _process_document_memory_efficient(
        self,
        document: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process document with memory efficiency."""

        # Use generators for large text processing
        def chunk_text(text: str, chunk_size: int = 1000) -> Generator[str, None, None]:
            for i in range(0, len(text), chunk_size):
                yield text[i:i + chunk_size]

        content = document.get('content', '')

        # Process in chunks to avoid memory spikes
        processed_chunks = []
        for chunk in chunk_text(content):
            # Process chunk
            processed_chunk = await self._process_text_chunk(chunk)
            processed_chunks.append(processed_chunk)

            # Clear chunk from memory immediately
            del chunk

        # Combine results
        result = {
            "document_id": document.get('id'),
            "processed_content": ' '.join(processed_chunks),
            "processing_metadata": {
                "chunks_processed": len(processed_chunks),
                "memory_efficient": True
            }
        }

        # Clear intermediate data
        del processed_chunks

        return result

    async def _process_text_chunk(self, chunk: str) -> str:
        """Process individual text chunk."""
        # Placeholder for chunk processing
        return f"Processed: {chunk[:50]}..."

# ❌ INCORRECT: Memory inefficient
class BadMemoryUsage:
    def process_all(self, documents):
        all_data = []
        for doc in documents:
            # Load everything into memory at once
            all_data.append(self.load_full_document(doc))

        # Process everything at once - memory spike
        return self.process_everything(all_data)
```

---

## 🛡️ **Security Considerations**

### **Pharmaceutical Data Security Framework**
```python
# ✅ CORRECT: Enterprise security following DEVELOPMENT_RULES_2.md
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import secrets
import re
from typing import Dict, Any, Optional

class PharmaceuticalSecurityManager:
    """Enterprise security manager for pharmaceutical AI systems."""

    def __init__(self, master_key: Optional[bytes] = None):
        # Follow DEVELOPMENT_RULES_2.md - Input sanitization mandatory
        self.master_key = master_key or self._generate_master_key()
        self.cipher_suite = Fernet(self.master_key)

        # PII detection patterns
        self.pii_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'patient_id': r'\bPT-\d{6,}\b',
            'medical_record': r'\bMR-\d{6,}\b'
        }

    def _generate_master_key(self) -> bytes:
        """Generate cryptographically secure master key."""
        password = secrets.token_bytes(32)
        salt = secrets.token_bytes(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key

    def sanitize_pharmaceutical_data(
        self,
        data: Dict[str, Any],
        preserve_structure: bool = True
    ) -> Dict[str, Any]:
        """Sanitize pharmaceutical data removing PII and sensitive information."""

        if not isinstance(data, dict):
            raise ValueError("Data must be a dictionary")

        sanitized = {}

        for key, value in data.items():
            # Follow DEVELOPMENT_RULES_2.md - PII protection patterns
            if self._is_sensitive_field(key):
                if preserve_structure:
                    sanitized[key] = "[REDACTED]"
                # Skip field entirely if not preserving structure
            elif isinstance(value, str):
                sanitized[key] = self._sanitize_text_content(value)
            elif isinstance(value, dict):
                sanitized[key] = self.sanitize_pharmaceutical_data(value, preserve_structure)
            elif isinstance(value, list):
                sanitized[key] = [
                    self.sanitize_pharmaceutical_data(item, preserve_structure)
                    if isinstance(item, dict)
                    else self._sanitize_text_content(str(item))
                    if isinstance(item, str)
                    else item
                    for item in value
                ]
            else:
                sanitized[key] = value

        return sanitized

    def _is_sensitive_field(self, field_name: str) -> bool:
        """Check if field name indicates sensitive data."""
        sensitive_indicators = [
            'password', 'token', 'key', 'secret', 'credential',
            'email', 'phone', 'ssn', 'patient', 'medical_record',
            'birth_date', 'address', 'name', 'contact'
        ]

        field_lower = field_name.lower()
        return any(indicator in field_lower for indicator in sensitive_indicators)

    def _sanitize_text_content(self, text: str) -> str:
        """Sanitize text content removing PII patterns."""

        sanitized = text

        # Remove PII patterns
        for pii_type, pattern in self.pii_patterns.items():
            sanitized = re.sub(pattern, f'[{pii_type.upper()}_REDACTED]', sanitized)

        # Remove potential SQL injection patterns
        sql_patterns = [
            r"(?i)(union|select|insert|update|delete|drop|create|alter)\s+",
            r"(?i)(or|and)\s+\d+\s*=\s*\d+",
            r"(?i)'\s*(or|and)\s*'.*?'\s*=\s*'"
        ]

        for pattern in sql_patterns:
            sanitized = re.sub(pattern, '[SQL_PATTERN_REMOVED]', sanitized)

        # Remove script tags and javascript
        sanitized = re.sub(r'<script[^>]*>.*?</script>', '[SCRIPT_REMOVED]', sanitized, flags=re.IGNORECASE | re.DOTALL)
        sanitized = re.sub(r'javascript:', '[JAVASCRIPT_REMOVED]', sanitized, flags=re.IGNORECASE)

        return sanitized

    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive pharmaceutical data."""

        if not isinstance(data, str):
            raise ValueError("Data must be a string")

        # Follow DEVELOPMENT_RULES_2.md - Data encryption requirements
        encrypted_data = self.cipher_suite.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive pharmaceutical data."""

        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            # Follow DEVELOPMENT_RULES_2.md - Secure error logging
            error_id = secrets.token_urlsafe(8)
            logging.error(f"Decryption failed. Error ID: {error_id}")
            raise ValueError(f"Decryption failed. Error ID: {error_id}")

    def validate_api_key_security(self, api_key: str) -> Dict[str, Any]:
        """Validate API key meets security requirements."""

        validation_result = {
            "is_valid": True,
            "security_score": 0,
            "recommendations": []
        }

        # Length check
        if len(api_key) < 32:
            validation_result["is_valid"] = False
            validation_result["recommendations"].append("API key should be at least 32 characters")
        else:
            validation_result["security_score"] += 25

        # Character diversity check
        has_upper = any(c.isupper() for c in api_key)
        has_lower = any(c.islower() for c in api_key)
        has_digit = any(c.isdigit() for c in api_key)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in api_key)

        diversity_score = sum([has_upper, has_lower, has_digit, has_special])
        validation_result["security_score"] += diversity_score * 15

        if diversity_score < 3:
            validation_result["recommendations"].append("API key should include uppercase, lowercase, digits, and special characters")

        # Entropy check (simplified)
        unique_chars = len(set(api_key))
        if unique_chars < len(api_key) * 0.6:
            validation_result["recommendations"].append("API key has low entropy - consider regenerating")
        else:
            validation_result["security_score"] += 35

        validation_result["is_valid"] = validation_result["security_score"] >= 70

        return validation_result

# ❌ INCORRECT: Violates DEVELOPMENT_RULES_2.md
class BadSecurity:
    def process_data(self, data):
        # No input sanitization
        print(f"Processing: {data}")  # Logs sensitive data

        # No encryption, no PII protection
        return data  # Returns raw data with PII
```

---

## 🏗️ **Code Organization & Architecture**

### **Pharmaceutical AI Architecture Patterns**
```python
# ✅ CORRECT: Clean architecture following DEVELOPMENT_RULES.md
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Protocol
from dataclasses import dataclass
from enum import Enum

# Domain Layer - Core business logic
class AnalysisType(str, Enum):
    COMPLIANCE = "compliance"
    RISK_ASSESSMENT = "risk_assessment"
    REGULATORY_SUMMARY = "regulatory_summary"
    VALIDATION_REVIEW = "validation_review"

@dataclass
class PharmaceuticalDocument:
    """Core domain entity for pharmaceutical documents."""

    id: str
    content: str
    document_type: str
    regulatory_frameworks: List[str]
    metadata: Dict[str, Any]

    def validate_content(self) -> bool:
        """Validate document content meets pharmaceutical standards."""
        return len(self.content) > 0 and self.document_type in [
            "sop", "validation_protocol", "batch_record", "deviation_report"
        ]

# Application Layer - Use cases and business rules
class PharmaceuticalAnalysisUseCase:
    """Use case for pharmaceutical document analysis."""

    def __init__(
        self,
        ai_service: 'AIAnalysisService',
        compliance_validator: 'ComplianceValidator',
        audit_logger: 'AuditLogger'
    ):
        self.ai_service = ai_service
        self.compliance_validator = compliance_validator
        self.audit_logger = audit_logger

    async def analyze_document(
        self,
        document: PharmaceuticalDocument,
        analysis_type: AnalysisType,
        user_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute pharmaceutical document analysis use case."""

        # Validate input
        if not document.validate_content():
            raise ValueError("Invalid document content")

        # Log analysis start
        await self.audit_logger.log_analysis_start(
            document.id,
            analysis_type,
            user_context["user_id"]
        )

        try:
            # Perform AI analysis
            ai_result = await self.ai_service.analyze(document, analysis_type)

            # Validate compliance
            compliance_result = await self.compliance_validator.validate(
                ai_result,
                document.regulatory_frameworks
            )

            # Combine results
            final_result = {
                "document_id": document.id,
                "analysis_type": analysis_type.value,
                "ai_analysis": ai_result,
                "compliance_validation": compliance_result,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Log successful completion
            await self.audit_logger.log_analysis_completion(
                document.id,
                final_result,
                user_context["user_id"]
            )

            return final_result

        except Exception as e:
            # Log error
            await self.audit_logger.log_analysis_error(
                document.id,
                str(e),
                user_context["user_id"]
            )
            raise

# Infrastructure Layer - External services and adapters
class AIAnalysisService(ABC):
    """Abstract service for AI analysis."""

    @abstractmethod
    async def analyze(
        self,
        document: PharmaceuticalDocument,
        analysis_type: AnalysisType
    ) -> Dict[str, Any]:
        """Analyze pharmaceutical document."""
        pass

class OpenRouterAIService(AIAnalysisService):
    """OpenRouter implementation of AI analysis service."""

    def __init__(self, api_key: str, model: str = "moonshot/kimi-k2"):
        self.client = EnterpriseOpenRouterClient(api_key, model)

    async def analyze(
        self,
        document: PharmaceuticalDocument,
        analysis_type: AnalysisType
    ) -> Dict[str, Any]:
        """Analyze document using OpenRouter AI."""

        return await self.client.analyze_pharmaceutical_document(
            document.content,
            analysis_type.value,
            document.regulatory_frameworks[0] if document.regulatory_frameworks else "general"
        )

class ComplianceValidator(ABC):
    """Abstract compliance validator."""

    @abstractmethod
    async def validate(
        self,
        analysis_result: Dict[str, Any],
        regulatory_frameworks: List[str]
    ) -> Dict[str, Any]:
        """Validate analysis against regulatory frameworks."""
        pass

class PharmaceuticalComplianceValidator(ComplianceValidator):
    """Pharmaceutical compliance validator implementation."""

    async def validate(
        self,
        analysis_result: Dict[str, Any],
        regulatory_frameworks: List[str]
    ) -> Dict[str, Any]:
        """Validate against pharmaceutical regulations."""

        validation_results = {}

        for framework in regulatory_frameworks:
            if framework == "21_cfr_part_11":
                validation_results[framework] = await self._validate_21_cfr_part_11(analysis_result)
            elif framework == "eu_gmp":
                validation_results[framework] = await self._validate_eu_gmp(analysis_result)

        return {
            "overall_compliant": all(
                result.get("compliant", False)
                for result in validation_results.values()
            ),
            "framework_results": validation_results
        }

    async def _validate_21_cfr_part_11(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate against 21 CFR Part 11."""
        # Implementation for 21 CFR Part 11 validation
        return {"compliant": True, "details": "Validation passed"}

    async def _validate_eu_gmp(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate against EU GMP."""
        # Implementation for EU GMP validation
        return {"compliant": True, "details": "Validation passed"}

# Presentation Layer - API controllers
class PharmaceuticalAnalysisController:
    """Controller for pharmaceutical analysis endpoints."""

    def __init__(self, analysis_use_case: PharmaceuticalAnalysisUseCase):
        self.analysis_use_case = analysis_use_case

    async def analyze_document_endpoint(
        self,
        request: Dict[str, Any],
        user_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """API endpoint for document analysis."""

        # Follow DEVELOPMENT_RULES_2.md - Input validation
        if not request.get("document_content"):
            raise ValueError("Document content is required")

        if not request.get("analysis_type"):
            raise ValueError("Analysis type is required")

        # Create domain entity
        document = PharmaceuticalDocument(
            id=request.get("document_id", str(secrets.token_urlsafe(8))),
            content=request["document_content"],
            document_type=request.get("document_type", "unknown"),
            regulatory_frameworks=request.get("regulatory_frameworks", []),
            metadata=request.get("metadata", {})
        )

        # Execute use case
        analysis_type = AnalysisType(request["analysis_type"])
        result = await self.analysis_use_case.analyze_document(
            document,
            analysis_type,
            user_context
        )

        return {
            "status": "success",
            "result": result
        }

# Dependency Injection Container
class DIContainer:
    """Dependency injection container for pharmaceutical AI system."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self._services = {}

    def get_ai_service(self) -> AIAnalysisService:
        """Get AI analysis service."""
        if "ai_service" not in self._services:
            self._services["ai_service"] = OpenRouterAIService(
                api_key=self.config["openrouter_api_key"],
                model=self.config.get("ai_model", "moonshot/kimi-k2")
            )
        return self._services["ai_service"]

    def get_compliance_validator(self) -> ComplianceValidator:
        """Get compliance validator."""
        if "compliance_validator" not in self._services:
            self._services["compliance_validator"] = PharmaceuticalComplianceValidator()
        return self._services["compliance_validator"]

    def get_analysis_use_case(self) -> PharmaceuticalAnalysisUseCase:
        """Get analysis use case."""
        if "analysis_use_case" not in self._services:
            self._services["analysis_use_case"] = PharmaceuticalAnalysisUseCase(
                ai_service=self.get_ai_service(),
                compliance_validator=self.get_compliance_validator(),
                audit_logger=self.get_audit_logger()
            )
        return self._services["analysis_use_case"]

    def get_audit_logger(self) -> 'AuditLogger':
        """Get audit logger."""
        if "audit_logger" not in self._services:
            from .audit import PharmaceuticalAuditLogger
            self._services["audit_logger"] = PharmaceuticalAuditLogger()
        return self._services["audit_logger"]

# ❌ INCORRECT: Violates DEVELOPMENT_RULES.md
class BadArchitecture:
    def __init__(self):
        # Tight coupling, no separation of concerns
        self.ai_client = OpenAI()  # Direct dependency
        self.db = Database()       # Direct dependency

    def analyze(self, data):
        # Business logic mixed with infrastructure
        result = self.ai_client.complete(data)
        self.db.save(result)
        return result
```

**🎯 COMPREHENSIVE AI DEVELOPMENT STANDARDS COMPLETE**

This document provides enterprise-grade AI development standards for pharmaceutical compliance systems, incorporating:

- **6-Expert Synthesis Protocol** for all development decisions
- **Context7 + Web Search** for latest July 2025 documentation
- **DEVELOPMENT_RULES.md + DEVELOPMENT_RULES_2.md** compliance
- **Production-first mindset** with security and validation
- **Performance optimization** with batching, caching, and memory management
- **Enterprise security** with PII protection and data encryption
- **Clean architecture** with proper separation of concerns
- **Comprehensive testing strategies** for pharmaceutical AI
- **Enterprise monitoring and observability** frameworks
- **Complete regulatory compliance** for 21 CFR Part 11, EU GMP, ICH guidelines
```
```
```
                if attempt == 2:
                    raise
                await asyncio.sleep(2 ** attempt)
```

#### RAG Implementation Best Practices
```python
# ✅ CORRECT: Pharmaceutical-optimized RAG pipeline
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings

class PharmaceuticalRAG:
    def __init__(self):
        # Optimized for regulatory documents
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,  # Optimal for regulatory content
            chunk_overlap=200,  # Preserve context across chunks
            separators=["\n\n", "\n", ". ", " ", ""]
        )

        # Use pharmaceutical-optimized embedding model
        self.embeddings = HuggingFaceEmbeddings(
            model_name="all-MiniLM-L6-v2",  # Fast and accurate for regulatory text
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )

    def create_knowledge_base(self, documents: List[str]) -> Chroma:
        """Create optimized vector store for pharmaceutical documents."""
        chunks = []
        for doc in documents:
            chunks.extend(self.text_splitter.split_text(doc))

        return Chroma.from_texts(
            texts=chunks,
            embedding=self.embeddings,
            persist_directory="./data/chromadb",
            collection_metadata={"hnsw:space": "cosine"}  # Optimal for text similarity
        )
```

### Pydantic V2 Validation Patterns

#### Enterprise Data Models
```python
# ✅ CORRECT: Pharmaceutical compliance data modeling
from pydantic import BaseModel, Field, validator, root_validator
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime

class ComplianceFramework(str, Enum):
    FDA_21_CFR_PART_11 = "fda_21_cfr_part_11"
    EU_GMP = "eu_gmp"
    ICH_Q7 = "ich_q7"
    ISO_13485 = "iso_13485"
    HIPAA = "hipaa"

class RegulatoryDocument(BaseModel):
    """Enterprise-grade regulatory document model."""

    id: str = Field(..., description="Unique document identifier")
    title: str = Field(..., min_length=1, max_length=500)
    content: str = Field(..., min_length=1)
    document_type: str = Field(..., regex=r'^[a-z_]+$')
    compliance_frameworks: List[ComplianceFramework]

    # Audit trail fields (21 CFR Part 11 requirement)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(..., description="User ID who created document")
    last_modified: datetime = Field(default_factory=datetime.utcnow)
    modified_by: str = Field(..., description="User ID who last modified")

    # Validation metadata
    validation_status: str = Field(default="pending")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)

    class Config:
        # Enable validation on assignment for real-time validation
        validate_assignment = True
        # Use enum values for serialization
        use_enum_values = True
        # Exclude None values from serialization
        exclude_none = True

    @validator('content')
    def validate_content_length(cls, v):
        """Ensure content meets minimum pharmaceutical documentation standards."""
        if len(v.strip()) < 50:
            raise ValueError("Document content too short for regulatory compliance")
        return v.strip()

    @root_validator
    def validate_compliance_consistency(cls, values):
        """Ensure document type aligns with compliance frameworks."""
        doc_type = values.get('document_type')
        frameworks = values.get('compliance_frameworks', [])

        # Define valid combinations
        valid_combinations = {
            'fda_guidance': [ComplianceFramework.FDA_21_CFR_PART_11],
            'ema_guideline': [ComplianceFramework.EU_GMP],
            'ich_guideline': [ComplianceFramework.ICH_Q7]
        }

        if doc_type in valid_combinations:
            required_frameworks = valid_combinations[doc_type]
            if not any(fw in frameworks for fw in required_frameworks):
                raise ValueError(f"Document type {doc_type} requires frameworks: {required_frameworks}")

        return values
```

### ChromaDB Vector Store Optimization

#### Performance-Optimized Configuration
```python
# ✅ CORRECT: Enterprise ChromaDB configuration
import chromadb
from chromadb.config import Settings

class OptimizedVectorStore:
    def __init__(self, persist_directory: str = "./data/chromadb"):
        # Production-optimized settings
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                chroma_db_impl="duckdb+parquet",  # Fastest for read-heavy workloads
                chroma_server_host="localhost",
                chroma_server_http_port="8000",
                anonymized_telemetry=False,  # Disable for pharmaceutical compliance
                allow_reset=False,  # Prevent accidental data loss
                chroma_collection_embedding_api_impl="chromadb.utils.embedding_functions.SentenceTransformerEmbeddingFunction"
            )
        )

    def create_collection(self, name: str, metadata: Dict[str, Any] = None):
        """Create optimized collection for pharmaceutical documents."""
        return self.client.create_collection(
            name=name,
            metadata={
                "hnsw:space": "cosine",  # Best for text similarity
                "hnsw:construction_ef": 200,  # Higher quality index
                "hnsw:M": 16,  # Balanced performance/memory
                **metadata or {}
            }
        )

    async def similarity_search_with_score(
        self,
        query: str,
        collection_name: str,
        n_results: int = 5,
        where: Dict[str, Any] = None
    ) -> List[Tuple[str, float]]:
        """Optimized similarity search with confidence scoring."""
        collection = self.client.get_collection(collection_name)

        results = collection.query(
            query_texts=[query],
            n_results=n_results,
            where=where,
            include=["documents", "distances", "metadatas"]
        )

        # Convert distances to similarity scores (0-1 range)
        documents = results['documents'][0]
        distances = results['distances'][0]
        similarities = [1 - (dist / 2) for dist in distances]  # Cosine distance to similarity

        return list(zip(documents, similarities))
```

### FastAPI Enterprise Patterns

#### Async Dependency Injection
```python
# ✅ CORRECT: Enterprise FastAPI patterns
from fastapi import FastAPI, Depends, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import asyncio
from contextlib import asynccontextmanager

class AIServiceDependency:
    def __init__(self):
        self._client = None
        self._vector_store = None

    async def get_ai_client(self) -> OpenRouterClient:
        """Dependency injection for AI client with connection pooling."""
        if self._client is None:
            self._client = OpenRouterClient(
                api_key=os.getenv("OPENROUTER_API_KEY"),
                model="moonshot/kimi-k2"
            )
        return self._client

    async def get_vector_store(self) -> OptimizedVectorStore:
        """Dependency injection for vector store with lazy loading."""
        if self._vector_store is None:
            self._vector_store = OptimizedVectorStore()
        return self._vector_store

# Global dependency instance
ai_deps = AIServiceDependency()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Proper resource management for AI services."""
    # Startup
    logger.info("Initializing AI services...")
    await ai_deps.get_ai_client()
    await ai_deps.get_vector_store()

    yield

    # Shutdown
    logger.info("Cleaning up AI services...")
    if ai_deps._client:
        await ai_deps._client.close()

app = FastAPI(
    title="VigiLens AI Processing Engine",
    version="1.0.0",
    lifespan=lifespan
)

# Enterprise middleware stack
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://vigilens.ai"],  # Specific origins only
    allow_credentials=True,
    allow_methods=["GET", "POST"],  # Specific methods only
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*.vigilens.ai", "localhost"]
)

@app.post("/api/v1/ai/analyze-document")
async def analyze_document(
    request: DocumentAnalysisRequest,
    background_tasks: BackgroundTasks,
    ai_client: OpenRouterClient = Depends(ai_deps.get_ai_client),
    vector_store: OptimizedVectorStore = Depends(ai_deps.get_vector_store)
) -> DocumentAnalysisResponse:
    """Enterprise-grade document analysis endpoint."""
    try:
        # Validate input
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="Document content cannot be empty")

        # Background audit logging
        background_tasks.add_task(
            log_audit_event,
            action="document_analysis_requested",
            user_id=request.user_id,
            document_id=request.document_id
        )

        # Perform analysis with timeout
        analysis_task = asyncio.create_task(
            perform_analysis(request, ai_client, vector_store)
        )

        try:
            result = await asyncio.wait_for(analysis_task, timeout=30.0)
            return result
        except asyncio.TimeoutError:
            raise HTTPException(status_code=408, detail="Analysis timeout")

    except Exception as e:
        logger.error(f"Document analysis failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal analysis error")
```

### HTTPX Async Client Patterns

#### Enterprise HTTP Client Configuration
```python
# ✅ CORRECT: Production-ready HTTP client
import httpx
from typing import Optional, Dict, Any
import asyncio

class EnterpriseHTTPClient:
    def __init__(self):
        # Production-optimized configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0, connect=10.0),  # Separate connect timeout
            limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
            retries=3,
            headers={
                "User-Agent": "VigiLens-AI/1.0.0",
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        )

    async def request_with_retry(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        max_retries: int = 3
    ) -> httpx.Response:
        """HTTP request with exponential backoff retry."""
        for attempt in range(max_retries):
            try:
                response = await self.client.request(
                    method=method,
                    url=url,
                    json=data,
                    headers=headers
                )
                response.raise_for_status()
                return response

            except httpx.HTTPStatusError as e:
                if e.response.status_code in [429, 502, 503, 504] and attempt < max_retries - 1:
                    # Exponential backoff for rate limits and server errors
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    await asyncio.sleep(wait_time)
                    continue
                raise

            except (httpx.ConnectError, httpx.TimeoutException) as e:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt)
                    await asyncio.sleep(wait_time)
                    continue
                raise

    async def close(self):
        """Properly close the HTTP client."""
        await self.client.aclose()
```

---

**🎯 COMPREHENSIVE AI DEVELOPMENT STANDARDS COMPLETE**

This document provides enterprise-grade AI development standards for pharmaceutical compliance systems, incorporating:

- **6-Expert Synthesis Protocol** for all development decisions
- **Context7 + Web Search** for latest July 2025 documentation
- **DEVELOPMENT_RULES.md + DEVELOPMENT_RULES_2.md** compliance
- **Production-first mindset** with security and validation
- **Performance optimization** with batching, caching, and memory management
- **Enterprise security** with PII protection and data encryption
- **Clean architecture** with proper separation of concerns
- **Comprehensive testing strategies** for pharmaceutical AI
- **Enterprise monitoring and observability** frameworks
- **Complete regulatory compliance** for 21 CFR Part 11, EU GMP, ICH guidelines

### **HYPER-SPECIFIC IMPLEMENTATION REQUIREMENTS**

#### **Mandatory Performance Metrics**
- **API Response Time:** ≤ 2000ms for 95th percentile requests
- **AI Model Accuracy:** ≥ 95% for pharmaceutical compliance analysis
- **Memory Usage:** ≤ 2GB per concurrent request processing
- **Throughput:** ≥ 100 documents/minute processing capacity
- **Availability:** 99.9% uptime SLA requirement (8.76 hours downtime/year maximum)

#### **Exact Regulatory Compliance Criteria**
- **21 CFR Part 11 Compliance:** 100% audit trail completeness with zero gaps
- **ALCOA+ Data Integrity:** All 8 principles (Attributable, Legible, Contemporaneous, Original, Accurate, Complete, Consistent, Enduring, Available) implemented with automated verification
- **Electronic Signature Validation:** RSA-2048 minimum key length with SHA-256 hashing
- **Access Control:** Role-based permissions with ≤ 3 failed login attempts before account lockout
- **Data Retention:** 7-year minimum retention period with automated archival and retrieval

#### **Specific Security Requirements**
- **Encryption Standards:** AES-256 for data at rest, TLS 1.3 for data in transit
- **Authentication:** Multi-factor authentication mandatory for production environments
- **Input Validation:** 100% of user inputs sanitized against OWASP Top 10 vulnerabilities
- **Error Handling:** Zero sensitive data exposure in error messages or logs
- **Audit Logging:** Complete audit trail with SHA-256 integrity hashing for tamper detection

#### **Production Deployment Standards**
- **Environment Variables:** All secrets managed via HashiCorp Vault or AWS Secrets Manager
- **Container Security:** Non-root user execution, minimal base images (Alpine Linux preferred)
- **Monitoring:** Real-time alerts for performance degradation >10% from baseline
- **Backup Strategy:** Automated daily backups with 99.99% recovery guarantee
- **Disaster Recovery:** ≤ 4 hour Recovery Time Objective (RTO), ≤ 1 hour Recovery Point Objective (RPO)

#### **AI Model Governance Requirements**
- **Model Versioning:** Semantic versioning (MAJOR.MINOR.PATCH) with full lineage tracking
- **Performance Monitoring:** Real-time drift detection with automated retraining triggers
- **Bias Assessment:** Quarterly bias audits with documented remediation plans
- **Explainability:** Model decisions must be explainable for regulatory audit requirements
- **Validation:** Independent validation dataset with ≥ 1000 pharmaceutical documents

#### **API Integration Specifications**
- **OpenRouter Configuration:**
  - Model: `moonshot/kimi-k2` (free tier)
  - Temperature: 0.1 (low for pharmaceutical consistency)
  - Max Tokens: 4096
  - Timeout: 30 seconds
  - Retry Logic: 3 attempts with exponential backoff (2^attempt seconds)

#### **Database Requirements**
- **ChromaDB Configuration:**
  - Collection Name: `pharmaceutical_knowledge_base`
  - Embedding Model: `all-MiniLM-L6-v2` with normalization
  - Batch Size: 100 documents for optimal performance
  - Persistence: Enabled with daily backups

#### **Compliance Testing Requirements**
- **Unit Test Coverage:** ≥ 90% code coverage for all AI processing modules
- **Integration Testing:** End-to-end testing of complete pharmaceutical workflows
- **Performance Testing:** Load testing with 1000 concurrent users
- **Security Testing:** Penetration testing quarterly with OWASP compliance
- **Regulatory Testing:** Validation against 21 CFR Part 11 requirements with documented evidence

---

## 10. RAG Pipeline Implementation Best Practices

### **Core RAG Architecture Principles**

#### **Vector Store Management**
```python
# ✅ CORRECT: Production-ready vector store initialization
class PharmaceuticalRAGPipeline:
    def __init__(self, vector_store_path: str, ai_client: OpenRouterClient):
        self.vector_store_path = vector_store_path
        self.ai_client = ai_client
        self.vector_store: Optional[ChromaVectorStore] = None
        
    async def initialize(self) -> bool:
        """Initialize vector store with proper error handling."""
        try:
            self.vector_store = ChromaVectorStore(
                collection_name="pharmaceutical_knowledge_base",
                persist_directory=self.vector_store_path
            )
            await self.vector_store.initialize()
            return True
        except Exception as e:
            logger.error(f"Vector store initialization failed: {e}")
            return False
```

#### **Query Processing with Context Retrieval**
```python
# ✅ CORRECT: Robust query processing with pharmaceutical focus
async def process_query(self, query: str, max_results: int = 5) -> RAGResponse:
    """Process pharmaceutical query with context retrieval."""
    # Validate components are initialized
    if self.vector_store is None:
        raise ValueError("Vector store not initialized")
    if self.ai_client is None:
        raise ValueError("AI client not initialized")
    
    try:
        # Retrieve relevant context
        search_results = await self.vector_store.search(
            query=query,
            limit=max_results
        )
        
        # Build pharmaceutical-focused prompt
        context = "\n".join([doc.content for doc in search_results])
        pharmaceutical_prompt = f"""
        Based on the following pharmaceutical knowledge:
        {context}
        
        Question: {query}
        
        Provide a comprehensive answer focusing on:
        1. Regulatory compliance (FDA, EMA guidelines)
        2. Safety considerations
        3. Quality assurance requirements
        4. Risk assessment factors
        """
        
        # Generate response with source attribution
        response = await self.ai_client.generate_text(
            prompt=pharmaceutical_prompt,
            temperature=0.1  # Low temperature for consistency
        )
        
        return RAGResponse(
            answer=response,
            sources=[doc.metadata.get('source', 'Unknown') for doc in search_results],
            confidence=calculate_confidence_score(search_results),
            model_used=self.ai_client.config.model if self.ai_client.config else 'unknown'
        )
        
    except Exception as e:
        logger.error(f"Query processing failed: {e}")
        raise
```

#### **Pharmaceutical Knowledge Base Population**
```python
# ✅ CORRECT: Structured knowledge base population
async def populate_pharmaceutical_knowledge(vector_store: ChromaVectorStore):
    """Populate vector store with pharmaceutical documents."""
    pharmaceutical_docs = [
        {
            "content": "FDA 21 CFR Part 11 requires electronic records and signatures...",
            "metadata": {"source": "FDA_21_CFR_Part_11", "category": "regulatory"}
        },
        {
            "content": "Good Manufacturing Practice (GMP) guidelines ensure...",
            "metadata": {"source": "EU_GMP_Guidelines", "category": "quality"}
        },
        {
            "content": "ICH Q9 Quality Risk Management provides principles...",
            "metadata": {"source": "ICH_Q9", "category": "risk_management"}
        }
    ]
    
    for doc in pharmaceutical_docs:
        await vector_store.add_document(
            content=doc["content"],
            metadata=doc["metadata"]
        )
```

### **RAG Performance Optimization**

#### **Confidence Score Calculation**
```python
# ✅ CORRECT: Confidence scoring for pharmaceutical queries
def calculate_confidence_score(search_results: List[Document]) -> float:
    """Calculate confidence based on result relevance and count."""
    if not search_results:
        return 0.0
    
    # Base confidence on similarity scores and result count
    avg_similarity = sum(doc.similarity_score for doc in search_results) / len(search_results)
    result_count_factor = min(len(search_results) / 5.0, 1.0)  # Normalize to max 5 results
    
    return avg_similarity * result_count_factor
```

#### **Context Window Management**
```python
# ✅ CORRECT: Efficient context window utilization
def optimize_context_window(documents: List[Document], max_tokens: int = 3000) -> str:
    """Optimize context to fit within model's token limit."""
    context_parts = []
    current_tokens = 0
    
    for doc in documents:
        doc_tokens = estimate_tokens(doc.content)
        if current_tokens + doc_tokens <= max_tokens:
            context_parts.append(doc.content)
            current_tokens += doc_tokens
        else:
            break
    
    return "\n\n".join(context_parts)
```

---

## 11. Type Safety and Error Prevention

### **Pyright Configuration and Best Practices**

#### **Handling Optional Attributes**
```python
# ❌ INCORRECT: Direct attribute access on optional objects
class RAGPipeline:
    def __init__(self):
        self.ai_client: Optional[OpenRouterClient] = None
    
    def process_query(self, query: str):
        # This causes reportOptionalMemberAccess error
        model = self.ai_client.config.model  # Error: ai_client might be None
        return self.ai_client.generate_text(query)  # Error: ai_client might be None

# ✅ CORRECT: Explicit None checks before attribute access
class RAGPipeline:
    def __init__(self):
        self.ai_client: Optional[OpenRouterClient] = None
    
    def process_query(self, query: str):
        # Explicit None check
        if self.ai_client is None:
            raise ValueError("AI client not initialized")
        
        # Safe attribute access after None check
        model = self.ai_client.config.model if self.ai_client.config else 'unknown'
        return self.ai_client.generate_text(query)
```

#### **Defensive Programming Patterns**
```python
# ✅ CORRECT: Comprehensive None checking
def safe_attribute_access(self) -> str:
    """Safely access nested optional attributes."""
    if self.ai_client is None:
        return 'no_client'
    
    if self.ai_client.config is None:
        return 'no_config'
    
    return self.ai_client.config.model or 'unknown_model'

# ✅ CORRECT: Using Optional chaining pattern
def get_model_name(self) -> str:
    """Get model name with safe optional chaining."""
    return (
        self.ai_client.config.model 
        if self.ai_client and self.ai_client.config 
        else 'unknown'
    )
```

#### **Type Annotations for RAG Components**
```python
# ✅ CORRECT: Comprehensive type annotations
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass

@dataclass
class RAGResponse:
    """Structured response from RAG pipeline."""
    answer: str
    sources: List[str]
    confidence: float
    model_used: str
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class PharmaceuticalRAGPipeline:
    """Type-safe RAG pipeline for pharmaceutical queries."""
    
    def __init__(self, vector_store_path: str, ai_client: OpenRouterClient) -> None:
        self.vector_store_path: str = vector_store_path
        self.ai_client: Optional[OpenRouterClient] = ai_client
        self.vector_store: Optional[ChromaVectorStore] = None
        self._initialized: bool = False
    
    async def process_query(
        self, 
        query: str, 
        max_results: int = 5
    ) -> RAGResponse:
        """Process query with full type safety."""
        if not self._initialized:
            raise RuntimeError("Pipeline not initialized")
        
        # Type-safe None checks
        if self.vector_store is None:
            raise ValueError("Vector store not initialized")
        if self.ai_client is None:
            raise ValueError("AI client not initialized")
        
        # Rest of implementation...
```

#### **Error Handling Patterns**
```python
# ✅ CORRECT: Comprehensive error handling with type safety
class RAGError(Exception):
    """Base exception for RAG pipeline errors."""
    pass

class VectorStoreError(RAGError):
    """Vector store specific errors."""
    pass

class AIClientError(RAGError):
    """AI client specific errors."""
    pass

async def robust_query_processing(
    self, 
    query: str
) -> Union[RAGResponse, Dict[str, str]]:
    """Query processing with comprehensive error handling."""
    try:
        # Validate inputs
        if not query.strip():
            raise ValueError("Query cannot be empty")
        
        # Check component initialization
        if self.vector_store is None:
            raise VectorStoreError("Vector store not initialized")
        if self.ai_client is None:
            raise AIClientError("AI client not initialized")
        
        # Process query with timeout
        result = await asyncio.wait_for(
            self._process_query_internal(query),
            timeout=30.0
        )
        return result
        
    except asyncio.TimeoutError:
        logger.error(f"Query processing timeout for: {query[:50]}...")
        return {"error": "Query processing timeout", "type": "timeout"}
    
    except (VectorStoreError, AIClientError) as e:
        logger.error(f"Component error: {e}")
        return {"error": str(e), "type": "component_error"}
    
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        return {"error": "Internal processing error", "type": "unknown"}
```

### **Testing Type Safety**

#### **Type-Safe Test Patterns**
```python
# ✅ CORRECT: Type-safe testing with proper None handling
import pytest
from typing import Optional

class TestRAGPipeline:
    """Type-safe tests for RAG pipeline."""
    
    @pytest.fixture
    def rag_pipeline(self) -> PharmaceuticalRAGPipeline:
        """Create test RAG pipeline."""
        mock_client = Mock(spec=OpenRouterClient)
        return PharmaceuticalRAGPipeline(
            vector_store_path="./test_vector_store",
            ai_client=mock_client
        )
    
    async def test_none_handling(self, rag_pipeline: PharmaceuticalRAGPipeline):
        """Test proper None handling."""
        # Test uninitialized state
        rag_pipeline.vector_store = None
        rag_pipeline.ai_client = None
        
        with pytest.raises(ValueError, match="Vector store not initialized"):
            await rag_pipeline.process_query("test query")
    
    async def test_safe_attribute_access(self, rag_pipeline: PharmaceuticalRAGPipeline):
        """Test safe attribute access patterns."""
        # Mock ai_client with None config
        mock_client = Mock()
        mock_client.config = None
        rag_pipeline.ai_client = mock_client
        
        # Should handle None config gracefully
        model_name = (
            rag_pipeline.ai_client.config.model 
            if rag_pipeline.ai_client and rag_pipeline.ai_client.config 
            else 'unknown'
        )
        assert model_name == 'unknown'
```

### **Key Learnings from VCP_024 Implementation**

1. **Always use explicit None checks** before accessing attributes on optional objects
2. **Implement defensive error handling** with specific exception types
3. **Use comprehensive type annotations** for all RAG pipeline components
4. **Test None handling scenarios** explicitly in unit tests
5. **Configure Pyright properly** to catch optional member access issues early
6. **Use safe attribute access patterns** with conditional expressions
7. **Validate component initialization** before processing operations
8. **Implement timeout handling** for long-running AI operations
9. **Use structured response objects** with proper type annotations
10. **Log errors comprehensively** while maintaining type safety

---

**🎯 RAG PIPELINE AND TYPE SAFETY STANDARDS COMPLETE**

These additions provide comprehensive guidance for:
- **Production-ready RAG pipeline implementation**
- **Type-safe pharmaceutical AI systems**
- **Robust error handling and None checking**
- **Performance optimization for vector operations**
- **Comprehensive testing strategies for type safety**
- **Real-world learnings from VCP_024 implementation**

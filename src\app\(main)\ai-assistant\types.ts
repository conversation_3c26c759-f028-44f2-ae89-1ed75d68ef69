export interface Message {
  readonly id: string;
  readonly content: string;
  readonly sender: 'user' | 'ai';
  readonly timestamp: Date;
  readonly type?: 'text' | 'suggestion' | 'document';
  readonly metadata?: Record<string, unknown>;
  readonly attachments?: readonly AttachedDocument[];
}

export interface AttachedDocument {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly size: string;
  readonly uploadedDate: string;
  readonly category: string;
}

export interface UploadedDocument {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly size: string;
  readonly uploadedDate: string;
  readonly category: string;
  readonly description: string;
}

export interface ConversationHistory {
  readonly id: string;
  readonly title: string;
  readonly lastMessage: string;
  readonly timestamp: string;
  readonly messageCount: number;
}

export interface QuickAction {
  readonly icon: React.ComponentType<{ className?: string }>;
  readonly label: string;
  readonly description: string;
  readonly action: string;
}

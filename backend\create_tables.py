#!/usr/bin/env python3
"""Create basic tables in Supabase for VigiLens using direct table operations."""

import logging
from datetime import datetime
from uuid import uuid4

from config.supabase import supabase

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_connection():
    """Test Supabase connection."""
    try:
        # Try to access the Supabase client - this will test the connection
        # Use a simple operation that doesn't require existing tables
        from supabase import Client
        if isinstance(supabase, Client):
            logger.info("✅ Supabase client initialized successfully")
            return True
        else:
            logger.error("❌ Supabase client not properly initialized")
            return False
    except Exception as e:
        logger.error(f"❌ Supabase connection failed: {e}")
        return False


def create_sample_data():
    """Create sample data for testing."""
    try:
        logger.info("Creating sample data...")

        # Sample organization data
        org_data = {
            'id': str(uuid4()),
            'name': 'Demo Pharmaceutical Corp',
            'description': 'Sample pharmaceutical company for testing',
            'industry': 'Pharmaceuticals',
            'country': 'United States',
            'is_active': True,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Check if organization already exists
        existing_org = supabase.table('organizations').select('*').eq('name', org_data['name']).execute()

        if existing_org.data and len(existing_org.data) > 0:
            logger.info("Sample organization already exists")
            org_id = existing_org.data[0]['id']
        else:
            # Create organization
            org_result = supabase.table('organizations').insert(org_data).execute()
            if org_result.data:
                org_id = org_result.data[0]['id']
                logger.info(f"✅ Created sample organization: {org_id}")
            else:
                logger.error("Failed to create sample organization")
                return False

        # Sample user data
        user_data = {
            'id': str(uuid4()),
            'organization_id': org_id,
            'email': '<EMAIL>',
            'full_name': 'Demo Admin',
            'role': 'Administrator',
            'department': 'Compliance',
            'is_active': True,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Check if user already exists
        existing_user = supabase.table('user_profiles').select('*').eq('email', user_data['email']).execute()

        if existing_user.data and len(existing_user.data) > 0:
            logger.info("Sample user already exists")
        else:
            # Create user
            user_result = supabase.table('user_profiles').insert(user_data).execute()
            if user_result.data:
                logger.info(f"✅ Created sample user: {user_result.data[0]['id']}")
            else:
                logger.error("Failed to create sample user")
                return False

        # Sample document data
        doc_data = {
            'id': str(uuid4()),
            'organization_id': org_id,
            'title': 'FDA Guidance for Industry - Quality Systems Approach to Pharmaceutical CGMP Regulations',
            'document_type': 'guidance',
            'status': 'published',
            'agency': 'fda',
            'source_url': 'https://www.fda.gov/regulatory-information/search-fda-guidance-documents/quality-systems-approach-pharmaceutical-cgmp-regulations',
            'processing_status': 'completed',
            'metadata': {
                'keywords': ['CGMP', 'Quality Systems', 'FDA', 'Pharmaceuticals'],
                'categories': ['Quality Management', 'Regulatory Compliance'],
                'therapeutic_areas': ['General'],
                'language': 'en'
            },
            'version': '1.0',
            'is_current': True,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Check if document already exists
        existing_doc = supabase.table('regulatory_documents').select('*').eq('title', doc_data['title']).execute()

        if existing_doc.data and len(existing_doc.data) > 0:
            logger.info("Sample document already exists")
        else:
            # Create document
            doc_result = supabase.table('regulatory_documents').insert(doc_data).execute()
            if doc_result.data:
                logger.info(f"✅ Created sample document: {doc_result.data[0]['id']}")
            else:
                logger.error("Failed to create sample document")
                return False

        logger.info("✅ Sample data creation completed successfully!")
        return True

    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")
        return False


def test_crud_operations():
    """Test basic CRUD operations."""
    try:
        logger.info("Testing CRUD operations...")

        # Test reading organizations
        orgs = supabase.table('organizations').select('*').execute()
        logger.info(f"✅ Found {len(orgs.data)} organizations")

        # Test reading users
        users = supabase.table('user_profiles').select('*').execute()
        logger.info(f"✅ Found {len(users.data)} users")

        # Test reading documents
        docs = supabase.table('regulatory_documents').select('*').execute()
        logger.info(f"✅ Found {len(docs.data)} documents")

        logger.info("✅ CRUD operations test completed successfully!")
        return True

    except Exception as e:
        logger.error(f"CRUD operations test failed: {e}")
        return False


def main():
    """Main function."""
    logger.info("🚀 Starting VigiLens database setup...")

    # Test connection
    if not test_connection():
        logger.error("Cannot proceed without database connection")
        return

    # Create sample data
    if create_sample_data():
        # Test CRUD operations
        test_crud_operations()

    logger.info("✅ Database setup process completed!")


if __name__ == "__main__":
    main()

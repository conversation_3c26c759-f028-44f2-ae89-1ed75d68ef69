-- Migration: 007_create_audit_trail.sql
-- Description: Create audit trail and compliance logging tables
-- Created: 2025-01-11
-- Dependencies: 003_create_user_roles.sql, 004_create_regulatory_documents.sql

-- Create audit action enum
CREATE TYPE audit_action AS ENUM (
  'create',
  'read',
  'update',
  'delete',
  'login',
  'logout',
  'upload',
  'download',
  'export',
  'import',
  'approve',
  'reject',
  'review',
  'archive',
  'restore',
  'share',
  'permission_change',
  'system_change',
  'compliance_assessment',
  'notification_sent'
);

-- Create audit trail table
CREATE TABLE audit_trail (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
  session_id VARCHAR(255), -- For tracking user sessions
  action audit_action NOT NULL,
  resource_type VARCHAR(100) NOT NULL, -- e.g., 'document', 'user', 'organization'
  resource_id UUID, -- ID of the affected resource
  resource_name VARCHAR(500), -- Human-readable name of the resource
  description TEXT, -- Detailed description of the action
  metadata JSONB DEFAULT '{}', -- Additional context data
  ip_address INET, -- User's IP address
  user_agent TEXT, -- User's browser/client information
  request_id VARCHAR(255), -- For correlating with application logs
  success BOOLEAN DEFAULT true, -- Whether the action was successful
  error_message TEXT, -- Error message if action failed
  duration_ms INTEGER, -- Action duration in milliseconds
  compliance_relevant BOOLEAN DEFAULT false, -- Whether this action is compliance-relevant
  retention_period INTERVAL DEFAULT INTERVAL '7 years', -- Data retention period
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit trail
CREATE INDEX idx_audit_trail_organization_id ON audit_trail(organization_id);
CREATE INDEX idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_action ON audit_trail(action);
CREATE INDEX idx_audit_trail_resource_type ON audit_trail(resource_type);
CREATE INDEX idx_audit_trail_resource_id ON audit_trail(resource_id);
CREATE INDEX idx_audit_trail_created_at ON audit_trail(created_at);
CREATE INDEX idx_audit_trail_compliance_relevant ON audit_trail(compliance_relevant) WHERE compliance_relevant = true;
CREATE INDEX idx_audit_trail_session_id ON audit_trail(session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_audit_trail_success ON audit_trail(success) WHERE success = false;

-- Composite indexes for common queries
CREATE INDEX idx_audit_trail_org_user_date ON audit_trail(organization_id, user_id, created_at);
CREATE INDEX idx_audit_trail_resource_action_date ON audit_trail(resource_type, action, created_at);
CREATE INDEX idx_audit_trail_compliance_date ON audit_trail(compliance_relevant, created_at) WHERE compliance_relevant = true;

-- Create GIN index for metadata searches
CREATE INDEX idx_audit_trail_metadata ON audit_trail USING GIN(metadata);

-- Enable Row Level Security
ALTER TABLE audit_trail ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for audit trail
CREATE POLICY "Users can view audit trail for their organization" ON audit_trail
  FOR SELECT USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
  );

-- Only system can insert audit records (no user INSERT policy)
CREATE POLICY "System can insert audit records" ON audit_trail
  FOR INSERT WITH CHECK (true); -- This will be restricted at application level

-- Create compliance log table for specific compliance events
CREATE TABLE compliance_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  audit_trail_id UUID REFERENCES audit_trail(id) ON DELETE CASCADE,
  compliance_event_type VARCHAR(100) NOT NULL, -- e.g., 'document_review', 'validation_check'
  regulatory_framework VARCHAR(100), -- e.g., 'FDA_cGMP', 'ICH_Q7'
  document_id UUID REFERENCES regulatory_documents(id) ON DELETE SET NULL,
  user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
  event_details JSONB DEFAULT '{}',
  compliance_status VARCHAR(50), -- e.g., 'compliant', 'non_compliant', 'under_review'
  risk_assessment VARCHAR(20), -- e.g., 'low', 'medium', 'high', 'critical'
  remediation_required BOOLEAN DEFAULT false,
  remediation_deadline TIMESTAMP WITH TIME ZONE,
  evidence_references JSONB DEFAULT '[]', -- References to supporting evidence
  reviewer_notes TEXT,
  digital_signature JSONB, -- For 21 CFR Part 11 compliance
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for compliance log
CREATE INDEX idx_compliance_log_organization_id ON compliance_log(organization_id);
CREATE INDEX idx_compliance_log_event_type ON compliance_log(compliance_event_type);
CREATE INDEX idx_compliance_log_framework ON compliance_log(regulatory_framework);
CREATE INDEX idx_compliance_log_document_id ON compliance_log(document_id);
CREATE INDEX idx_compliance_log_user_id ON compliance_log(user_id);
CREATE INDEX idx_compliance_log_status ON compliance_log(compliance_status);
CREATE INDEX idx_compliance_log_risk ON compliance_log(risk_assessment);
CREATE INDEX idx_compliance_log_remediation ON compliance_log(remediation_required) WHERE remediation_required = true;
CREATE INDEX idx_compliance_log_created_at ON compliance_log(created_at);
CREATE INDEX idx_compliance_log_deadline ON compliance_log(remediation_deadline) WHERE remediation_deadline IS NOT NULL;

-- GIN indexes for JSON fields
CREATE INDEX idx_compliance_log_event_details ON compliance_log USING GIN(event_details);
CREATE INDEX idx_compliance_log_evidence ON compliance_log USING GIN(evidence_references);
CREATE INDEX idx_compliance_log_signature ON compliance_log USING GIN(digital_signature);

-- Enable Row Level Security for compliance log
ALTER TABLE compliance_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for compliance log
CREATE POLICY "Users can view compliance log for their organization" ON compliance_log
  FOR SELECT USING (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
  );

CREATE POLICY "Users can create compliance log entries" ON compliance_log
  FOR INSERT WITH CHECK (
    organization_id = (auth.jwt() ->> 'organization_id')::uuid
    AND user_id = auth.uid()
  );

-- Create trigger for compliance log updated_at
CREATE TRIGGER update_compliance_log_updated_at
    BEFORE UPDATE ON compliance_log
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
  p_organization_id UUID,
  p_user_id UUID DEFAULT NULL,
  p_session_id VARCHAR(255) DEFAULT NULL,
  p_action audit_action,
  p_resource_type VARCHAR(100),
  p_resource_id UUID DEFAULT NULL,
  p_resource_name VARCHAR(500) DEFAULT NULL,
  p_description TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}',
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_request_id VARCHAR(255) DEFAULT NULL,
  p_success BOOLEAN DEFAULT true,
  p_error_message TEXT DEFAULT NULL,
  p_duration_ms INTEGER DEFAULT NULL,
  p_compliance_relevant BOOLEAN DEFAULT false
) RETURNS UUID AS $$
DECLARE
  audit_id UUID;
BEGIN
  INSERT INTO audit_trail (
    organization_id,
    user_id,
    session_id,
    action,
    resource_type,
    resource_id,
    resource_name,
    description,
    metadata,
    ip_address,
    user_agent,
    request_id,
    success,
    error_message,
    duration_ms,
    compliance_relevant
  ) VALUES (
    p_organization_id,
    p_user_id,
    p_session_id,
    p_action,
    p_resource_type,
    p_resource_id,
    p_resource_name,
    p_description,
    p_metadata,
    p_ip_address,
    p_user_agent,
    p_request_id,
    p_success,
    p_error_message,
    p_duration_ms,
    p_compliance_relevant
  ) RETURNING id INTO audit_id;
  
  RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to log compliance events
CREATE OR REPLACE FUNCTION log_compliance_event(
  p_organization_id UUID,
  p_compliance_event_type VARCHAR(100),
  p_regulatory_framework VARCHAR(100) DEFAULT NULL,
  p_document_id UUID DEFAULT NULL,
  p_user_id UUID DEFAULT NULL,
  p_event_details JSONB DEFAULT '{}',
  p_compliance_status VARCHAR(50) DEFAULT NULL,
  p_risk_assessment VARCHAR(20) DEFAULT NULL,
  p_remediation_required BOOLEAN DEFAULT false,
  p_remediation_deadline TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_evidence_references JSONB DEFAULT '[]',
  p_reviewer_notes TEXT DEFAULT NULL,
  p_digital_signature JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  compliance_log_id UUID;
  audit_id UUID;
BEGIN
  -- First create audit trail entry
  SELECT log_audit_event(
    p_organization_id,
    p_user_id,
    NULL, -- session_id
    'compliance_assessment'::audit_action,
    'compliance_event',
    p_document_id,
    p_compliance_event_type,
    format('Compliance event: %s for framework: %s', p_compliance_event_type, p_regulatory_framework),
    jsonb_build_object(
      'event_type', p_compliance_event_type,
      'framework', p_regulatory_framework,
      'status', p_compliance_status,
      'risk', p_risk_assessment
    ),
    NULL, -- ip_address
    NULL, -- user_agent
    NULL, -- request_id
    true, -- success
    NULL, -- error_message
    NULL, -- duration_ms
    true  -- compliance_relevant
  ) INTO audit_id;
  
  -- Then create compliance log entry
  INSERT INTO compliance_log (
    organization_id,
    audit_trail_id,
    compliance_event_type,
    regulatory_framework,
    document_id,
    user_id,
    event_details,
    compliance_status,
    risk_assessment,
    remediation_required,
    remediation_deadline,
    evidence_references,
    reviewer_notes,
    digital_signature
  ) VALUES (
    p_organization_id,
    audit_id,
    p_compliance_event_type,
    p_regulatory_framework,
    p_document_id,
    p_user_id,
    p_event_details,
    p_compliance_status,
    p_risk_assessment,
    p_remediation_required,
    p_remediation_deadline,
    p_evidence_references,
    p_reviewer_notes,
    p_digital_signature
  ) RETURNING id INTO compliance_log_id;
  
  RETURN compliance_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for audit trail cleanup (for data retention)
CREATE OR REPLACE FUNCTION cleanup_audit_trail(
  p_organization_id UUID DEFAULT NULL,
  p_older_than INTERVAL DEFAULT INTERVAL '7 years'
) RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM audit_trail
  WHERE created_at < (NOW() - p_older_than)
    AND (p_organization_id IS NULL OR organization_id = p_organization_id)
    AND compliance_relevant = false; -- Never delete compliance-relevant records
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log the cleanup action
  PERFORM log_audit_event(
    COALESCE(p_organization_id, '00000000-0000-0000-0000-000000000000'::uuid),
    NULL, -- user_id (system action)
    NULL, -- session_id
    'system_change'::audit_action,
    'audit_trail',
    NULL, -- resource_id
    'Audit Trail Cleanup',
    format('Cleaned up %s audit trail records older than %s', deleted_count, p_older_than),
    jsonb_build_object('deleted_count', deleted_count, 'retention_period', p_older_than::text),
    NULL, -- ip_address
    'System', -- user_agent
    NULL, -- request_id
    true, -- success
    NULL, -- error_message
    NULL, -- duration_ms
    true  -- compliance_relevant
  );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for audit trail reporting
CREATE VIEW audit_trail_report_view AS
SELECT 
  at.id,
  at.organization_id,
  o.name as organization_name,
  at.user_id,
  up.full_name as user_name,
  up.email as user_email,
  at.action,
  at.resource_type,
  at.resource_id,
  at.resource_name,
  at.description,
  at.metadata,
  at.ip_address,
  at.success,
  at.error_message,
  at.compliance_relevant,
  at.created_at,
  CASE 
    WHEN at.created_at > (NOW() - at.retention_period) THEN 'active'
    ELSE 'expired'
  END as retention_status
FROM audit_trail at
JOIN organizations o ON at.organization_id = o.id
LEFT JOIN user_profiles up ON at.user_id = up.id;

-- Create view for compliance reporting
CREATE VIEW compliance_report_view AS
SELECT 
  cl.id,
  cl.organization_id,
  o.name as organization_name,
  cl.compliance_event_type,
  cl.regulatory_framework,
  cl.document_id,
  rd.title as document_title,
  rd.document_type,
  cl.user_id,
  up.full_name as user_name,
  cl.event_details,
  cl.compliance_status,
  cl.risk_assessment,
  cl.remediation_required,
  cl.remediation_deadline,
  cl.evidence_references,
  cl.reviewer_notes,
  cl.created_at,
  cl.updated_at,
  CASE 
    WHEN cl.remediation_required AND cl.remediation_deadline < NOW() THEN 'overdue'
    WHEN cl.remediation_required AND cl.remediation_deadline <= (NOW() + INTERVAL '7 days') THEN 'due_soon'
    WHEN cl.remediation_required THEN 'pending'
    ELSE 'none'
  END as remediation_status
FROM compliance_log cl
JOIN organizations o ON cl.organization_id = o.id
LEFT JOIN regulatory_documents rd ON cl.document_id = rd.id
LEFT JOIN user_profiles up ON cl.user_id = up.id;

-- Create triggers to automatically log document changes
CREATE OR REPLACE FUNCTION trigger_audit_document_changes()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM log_audit_event(
      NEW.organization_id,
      NEW.uploaded_by,
      NULL, -- session_id
      'create'::audit_action,
      'document',
      NEW.id,
      NEW.title,
      format('Document created: %s (%s)', NEW.title, NEW.document_type),
      jsonb_build_object(
        'document_type', NEW.document_type,
        'file_size', NEW.file_size,
        'file_type', NEW.file_type
      ),
      NULL, -- ip_address
      NULL, -- user_agent
      NULL, -- request_id
      true, -- success
      NULL, -- error_message
      NULL, -- duration_ms
      true  -- compliance_relevant
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    PERFORM log_audit_event(
      NEW.organization_id,
      auth.uid(),
      NULL, -- session_id
      'update'::audit_action,
      'document',
      NEW.id,
      NEW.title,
      format('Document updated: %s', NEW.title),
      jsonb_build_object(
        'old_status', OLD.status,
        'new_status', NEW.status,
        'old_compliance_score', OLD.compliance_score,
        'new_compliance_score', NEW.compliance_score
      ),
      NULL, -- ip_address
      NULL, -- user_agent
      NULL, -- request_id
      true, -- success
      NULL, -- error_message
      NULL, -- duration_ms
      true  -- compliance_relevant
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM log_audit_event(
      OLD.organization_id,
      auth.uid(),
      NULL, -- session_id
      'delete'::audit_action,
      'document',
      OLD.id,
      OLD.title,
      format('Document deleted: %s', OLD.title),
      jsonb_build_object(
        'document_type', OLD.document_type,
        'status', OLD.status
      ),
      NULL, -- ip_address
      NULL, -- user_agent
      NULL, -- request_id
      true, -- success
      NULL, -- error_message
      NULL, -- duration_ms
      true  -- compliance_relevant
    );
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for document audit logging
CREATE TRIGGER audit_regulatory_documents_changes
  AFTER INSERT OR UPDATE OR DELETE ON regulatory_documents
  FOR EACH ROW
  EXECUTE FUNCTION trigger_audit_document_changes();

-- Add comments for documentation
COMMENT ON TABLE audit_trail IS 'Comprehensive audit trail for all system actions';
COMMENT ON TABLE compliance_log IS 'Specialized logging for compliance-related events';
COMMENT ON COLUMN audit_trail.compliance_relevant IS 'Marks records that must be retained for compliance';
COMMENT ON COLUMN audit_trail.retention_period IS 'How long this record should be retained';
COMMENT ON COLUMN compliance_log.digital_signature IS 'Digital signature data for 21 CFR Part 11 compliance';
COMMENT ON FUNCTION log_audit_event IS 'Logs audit events with comprehensive context';
COMMENT ON FUNCTION log_compliance_event IS 'Logs compliance-specific events with audit trail';
COMMENT ON FUNCTION cleanup_audit_trail IS 'Cleans up old audit records while preserving compliance data';
COMMENT ON VIEW audit_trail_report_view IS 'Formatted view for audit trail reporting';
COMMENT ON VIEW compliance_report_view IS 'Formatted view for compliance reporting';
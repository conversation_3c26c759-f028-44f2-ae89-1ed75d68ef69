-- COMPREHENSIVE AUTHENTICATION DEBUGGING for Vigi<PERSON>ens
-- Run this in Supabase SQL Editor to get complete diagnostic information

-- 1. Check Supabase Auth Configuration
SELECT
    'AUTH CONFIGURATION' as section,
    'Checking auth.users table access' as test;

-- 2. Test if we can access auth.users table
SELECT
    'AUTH USERS TABLE' as section,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email_confirmed_at IS NOT NULL THEN 1 END) as confirmed_users,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 day' THEN 1 END) as users_last_24h
FROM auth.users;

-- 3. Check organizations table
SELECT
    'ORGANIZATIONS TABLE' as section,
    id,
    name,
    display_name,
    is_active,
    created_at
FROM organizations
ORDER BY created_at DESC;

-- 4. Check user_profiles table structure and data
SELECT
    'USER PROFILES TABLE' as section,
    COUNT(*) as total_profiles,
    COUNT(CASE WHEN is_active THEN 1 END) as active_profiles,
    COUNT(CASE WHEN email_verified_at IS NOT NULL THEN 1 END) as verified_profiles
FROM user_profiles;

-- 5. Check if trigger function exists and its definition
SELECT
    'TRIGGER FUNCTION' as section,
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines
WHERE routine_name = 'handle_new_user';

-- 6. Check if trigger exists
SELECT
    'TRIGGER STATUS' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'handle_new_user_trigger';

-- 7. Check RLS policies on user_profiles
SELECT
    'RLS POLICIES' as section,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'user_profiles'
ORDER BY policyname;

-- 8. Check if RLS is enabled
SELECT
    'RLS STATUS' as section,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename IN ('user_profiles', 'organizations');

-- 9. Test trigger function manually (if it exists)
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := 'debug-test-' || extract(epoch from now()) || '@example.com';
    profile_created BOOLEAN := FALSE;
    error_msg TEXT;
BEGIN
    -- Try to simulate user creation
    BEGIN
        INSERT INTO auth.users (
            id,
            email,
            encrypted_password,
            email_confirmed_at,
            raw_user_meta_data,
            created_at,
            updated_at,
            aud,
            role
        ) VALUES (
            test_user_id,
            test_email,
            'dummy_hash',
            NOW(),
            jsonb_build_object('full_name', 'Debug Test User'),
            NOW(),
            NOW(),
            'authenticated',
            'authenticated'
        );

        -- Check if profile was created
        SELECT EXISTS(SELECT 1 FROM user_profiles WHERE id = test_user_id) INTO profile_created;

        IF profile_created THEN
            RAISE NOTICE '✅ SUCCESS: Trigger created user profile for test user';
        ELSE
            RAISE NOTICE '❌ FAIL: Trigger did not create user profile';
        END IF;

        -- Clean up
        DELETE FROM user_profiles WHERE id = test_user_id;
        DELETE FROM auth.users WHERE id = test_user_id;

    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE '❌ ERROR during trigger test: %', error_msg;

            -- Attempt cleanup
            BEGIN
                DELETE FROM user_profiles WHERE id = test_user_id;
                DELETE FROM auth.users WHERE id = test_user_id;
            EXCEPTION
                WHEN OTHERS THEN NULL;
            END;
    END;
END $$;

-- 10. Check for any recent auth errors in logs (if accessible)
SELECT
    'RECENT AUTH ACTIVITY' as section,
    'Check Supabase Dashboard > Logs for auth errors' as instruction;

-- 11. Verify demo organization exists with correct structure
SELECT
    'DEMO ORG VERIFICATION' as section,
    CASE
        WHEN EXISTS(SELECT 1 FROM organizations WHERE name = 'demo-pharma-corp')
        THEN '✅ Demo organization exists'
        ELSE '❌ Demo organization missing'
    END as status,
    (SELECT id FROM organizations WHERE name = 'demo-pharma-corp' LIMIT 1) as org_id;

-- 12. Check auth schema permissions
SELECT
    'AUTH SCHEMA ACCESS' as section,
    has_schema_privilege('auth', 'USAGE') as can_access_auth_schema,
    has_table_privilege('auth.users', 'SELECT') as can_read_auth_users,
    has_table_privilege('auth.users', 'INSERT') as can_insert_auth_users;

-- 13. Final summary
SELECT
    '=== AUTHENTICATION DEBUG SUMMARY ===' as summary,
    NOW() as timestamp,
    'Check all sections above for issues' as next_steps;

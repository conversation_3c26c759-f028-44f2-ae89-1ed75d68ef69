-- VigiLens Database Schema - Migration 005
-- Create Regulatory Documents Table - Core Document Management
-- Optimized for Direct Supabase Integration with Real-time Capabilities

-- Regulatory documents table - Core entity for pharmaceutical compliance
CREATE TABLE regulatory_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Multi-tenant organization relationship
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Document identification and metadata
    title VARCHAR(500) NOT NULL,
    document_number VARCHAR(100), -- Internal document number
    document_type document_type NOT NULL,
    status document_status DEFAULT 'draft',
    
    -- Regulatory context
    regulatory_agency regulatory_agency,
    compliance_frameworks compliance_framework[] DEFAULT ARRAY[]::compliance_framework[],
    therapeutic_areas TEXT[], -- Array of therapeutic areas
    product_categories TEXT[], -- Array of product categories
    
    -- Document source and location
    source_url TEXT,
    source_system VARCHAR(100), -- 'fda_website', 'ema_portal', 'manual_upload', etc.
    file_path TEXT, -- Supabase Storage path
    file_name VA<PERSON>HA<PERSON>(255),
    file_size BIGINT, -- File size in bytes
    content_type VARCHAR(100), -- MIME type
    file_hash VARCHAR(64), -- SHA-256 hash for integrity
    
    -- Document content and processing
    extracted_text TEXT, -- Full text extracted from document
    processing_status processing_status DEFAULT 'pending',
    processing_error TEXT,
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    
    -- AI analysis results
    ai_summary TEXT,
    ai_insights JSONB DEFAULT '{
        "key_changes": [],
        "impact_assessment": {},
        "recommendations": [],
        "confidence_score": null,
        "processing_metadata": {}
    }'::JSONB,
    
    -- Compliance and risk assessment
    compliance_score DECIMAL(5,2), -- 0.00 to 100.00
    risk_level risk_level,
    impact_assessment JSONB DEFAULT '{
        "high_impact_areas": [],
        "affected_processes": [],
        "implementation_timeline": null,
        "resource_requirements": {}
    }'::JSONB,
    
    -- Document metadata and classification
    metadata JSONB DEFAULT '{
        "keywords": [],
        "categories": [],
        "tags": [],
        "language": "en",
        "page_count": null,
        "word_count": null,
        "reading_time_minutes": null,
        "complexity_score": null
    }'::JSONB,
    
    -- Version control
    version VARCHAR(50) DEFAULT '1.0',
    is_current BOOLEAN DEFAULT true,
    supersedes_document_id UUID REFERENCES regulatory_documents(id),
    superseded_by_document_id UUID REFERENCES regulatory_documents(id),
    
    -- Effective dates and lifecycle
    effective_date DATE,
    expiry_date DATE,
    review_date DATE,
    next_review_date DATE,
    
    -- Document workflow and approval
    workflow_status VARCHAR(100) DEFAULT 'draft', -- 'draft', 'review', 'approved', 'published'
    assigned_to UUID[] DEFAULT ARRAY[]::UUID[], -- Array of user IDs for assignment
    reviewers UUID[] DEFAULT ARRAY[]::UUID[], -- Array of reviewer user IDs
    approvers UUID[] DEFAULT ARRAY[]::UUID[], -- Array of approver user IDs
    
    -- Priority and urgency
    priority VARCHAR(50) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    urgency VARCHAR(50) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
    deadline TIMESTAMPTZ,
    
    -- Audit trail and timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    archived_at TIMESTAMPTZ,
    
    -- User tracking
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    published_by UUID REFERENCES user_profiles(id),
    archived_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CONSTRAINT regulatory_documents_title_length CHECK (char_length(title) >= 3),
    CONSTRAINT regulatory_documents_valid_dates CHECK (
        expiry_date IS NULL OR effective_date IS NULL OR expiry_date > effective_date
    ),
    CONSTRAINT regulatory_documents_valid_review_dates CHECK (
        next_review_date IS NULL OR review_date IS NULL OR next_review_date > review_date
    ),
    CONSTRAINT regulatory_documents_valid_compliance_score CHECK (
        compliance_score IS NULL OR (compliance_score >= 0 AND compliance_score <= 100)
    ),
    CONSTRAINT regulatory_documents_valid_file_size CHECK (
        file_size IS NULL OR file_size > 0
    ),
    CONSTRAINT regulatory_documents_processing_logic CHECK (
        (processing_status = 'completed' AND processing_completed_at IS NOT NULL) OR
        (processing_status != 'completed')
    )
);

-- Create comprehensive indexes for performance optimization

-- Primary access patterns
CREATE INDEX idx_regulatory_documents_organization_id ON regulatory_documents(organization_id);
CREATE INDEX idx_regulatory_documents_status ON regulatory_documents(status);
CREATE INDEX idx_regulatory_documents_document_type ON regulatory_documents(document_type);
CREATE INDEX idx_regulatory_documents_processing_status ON regulatory_documents(processing_status);
CREATE INDEX idx_regulatory_documents_workflow_status ON regulatory_documents(workflow_status);

-- Date-based queries
CREATE INDEX idx_regulatory_documents_created_at ON regulatory_documents(created_at);
CREATE INDEX idx_regulatory_documents_updated_at ON regulatory_documents(updated_at);
CREATE INDEX idx_regulatory_documents_effective_date ON regulatory_documents(effective_date);
CREATE INDEX idx_regulatory_documents_deadline ON regulatory_documents(deadline);

-- Regulatory and compliance queries
CREATE INDEX idx_regulatory_documents_agency ON regulatory_documents(regulatory_agency);
CREATE INDEX idx_regulatory_documents_risk_level ON regulatory_documents(risk_level);
CREATE INDEX idx_regulatory_documents_priority ON regulatory_documents(priority);

-- Version control and relationships
CREATE INDEX idx_regulatory_documents_version ON regulatory_documents(version, is_current);
CREATE INDEX idx_regulatory_documents_supersedes ON regulatory_documents(supersedes_document_id);
CREATE INDEX idx_regulatory_documents_superseded_by ON regulatory_documents(superseded_by_document_id);

-- User assignment and workflow
CREATE INDEX idx_regulatory_documents_created_by ON regulatory_documents(created_by);
CREATE INDEX idx_regulatory_documents_updated_by ON regulatory_documents(updated_by);

-- GIN indexes for array and JSONB columns
CREATE INDEX idx_regulatory_documents_compliance_frameworks ON regulatory_documents USING GIN(compliance_frameworks);
CREATE INDEX idx_regulatory_documents_therapeutic_areas ON regulatory_documents USING GIN(therapeutic_areas);
CREATE INDEX idx_regulatory_documents_product_categories ON regulatory_documents USING GIN(product_categories);
CREATE INDEX idx_regulatory_documents_assigned_to ON regulatory_documents USING GIN(assigned_to);
CREATE INDEX idx_regulatory_documents_reviewers ON regulatory_documents USING GIN(reviewers);
CREATE INDEX idx_regulatory_documents_approvers ON regulatory_documents USING GIN(approvers);

-- JSONB indexes for AI insights and metadata
CREATE INDEX idx_regulatory_documents_ai_insights ON regulatory_documents USING GIN(ai_insights);
CREATE INDEX idx_regulatory_documents_metadata ON regulatory_documents USING GIN(metadata);
CREATE INDEX idx_regulatory_documents_impact_assessment ON regulatory_documents USING GIN(impact_assessment);

-- Full-text search index on extracted text
CREATE INDEX idx_regulatory_documents_text_search ON regulatory_documents USING GIN(to_tsvector('english', extracted_text));

-- Composite indexes for common query patterns
CREATE INDEX idx_regulatory_documents_org_status ON regulatory_documents(organization_id, status);
CREATE INDEX idx_regulatory_documents_org_type ON regulatory_documents(organization_id, document_type);
CREATE INDEX idx_regulatory_documents_org_current ON regulatory_documents(organization_id, is_current) WHERE is_current = true;
CREATE INDEX idx_regulatory_documents_processing ON regulatory_documents(organization_id, processing_status, processing_started_at);

-- Partial indexes for active documents
CREATE INDEX idx_regulatory_documents_active ON regulatory_documents(organization_id, created_at) 
    WHERE status NOT IN ('superseded', 'obsolete', 'withdrawn');

-- Add updated_at trigger
CREATE TRIGGER update_regulatory_documents_updated_at 
    BEFORE UPDATE ON regulatory_documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE regulatory_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for regulatory_documents

-- Policy: Users can view documents in their organization
CREATE POLICY "Users can view org documents" ON regulatory_documents
    FOR SELECT USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
    );

-- Policy: Users can create documents in their organization
CREATE POLICY "Users can create org documents" ON regulatory_documents
    FOR INSERT WITH CHECK (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND created_by = auth.uid()
    );

-- Policy: Document creators and assigned users can update
CREATE POLICY "Document access for updates" ON regulatory_documents
    FOR UPDATE USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (
            created_by = auth.uid() OR
            auth.uid() = ANY(assigned_to) OR
            auth.uid() = ANY(reviewers) OR
            auth.uid() = ANY(approvers) OR
            (auth.jwt() ->> 'role') IN ('admin', 'compliance_officer')
        )
    );

-- Policy: Only admins and compliance officers can delete
CREATE POLICY "Restricted document deletion" ON regulatory_documents
    FOR DELETE USING (
        organization_id = (auth.jwt() ->> 'organization_id')::UUID
        AND (auth.jwt() ->> 'role') IN ('admin', 'compliance_officer')
    );

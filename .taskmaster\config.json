{"taskmaster": {"version": "1.0.0", "project_id": "vigilen-compliance-platform", "created": "2025-07-11T00:00:00Z", "last_updated": "2025-07-11T00:00:00Z"}, "settings": {"task_id_prefix": "VCP", "max_task_complexity": "high", "default_estimation_unit": "hours", "dependency_validation": true, "circular_dependency_check": true, "auto_priority_calculation": true}, "directories": {"tasks": "./tasks/", "docs": "./docs/", "reports": "./docs/reports/", "templates": "./docs/templates/"}, "task_statuses": ["pending", "in_progress", "done", "blocked"], "priority_levels": ["high", "medium", "low"], "complexity_levels": ["low", "medium", "high"], "tags": ["backend", "frontend", "ai", "database", "authentication", "compliance", "security", "testing", "deployment", "documentation"]}
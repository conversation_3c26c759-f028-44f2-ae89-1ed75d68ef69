/**
 * Authentication service for VigiLens
 * Implements Supabase Auth following July 2025 patterns
 * Follows DEVELOPMENT_RULES_2.md: Production-first, no console.log, proper error types
 */

import { createClient } from '@/utils/supabase/client'
import type { AuthError } from '@supabase/supabase-js'

// Strict TypeScript interfaces following development rules
export interface AuthUser {
  id: string
  email: string
  email_confirmed_at: string | null
  phone: string | null
  created_at: string
  updated_at: string
  user_metadata: Record<string, any>
  app_metadata: Record<string, any>
}

export interface AuthSession {
  access_token: string
  refresh_token: string
  expires_in: number
  expires_at: number
  token_type: string
  user: AuthUser
}

export interface AuthResponse<T = any> {
  data: T | null
  error: AuthError | null
}

export interface SignUpCredentials {
  email: string
  password: string
  options?: {
    data?: Record<string, any>
    captchaToken?: string
  }
}

export interface SignInCredentials {
  email: string
  password: string
  options?: {
    captchaToken?: string
  }
}

export interface ResetPasswordCredentials {
  email: string
  options?: {
    captchaToken?: string
    redirectTo?: string
  }
}

export interface UpdatePasswordCredentials {
  password: string
}

/**
 * Client-side authentication service
 * Use this in Client Components and browser-side operations
 */
export class AuthService {
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Sign up a new user with email and password
   */
  async signUp(credentials: SignUpCredentials): Promise<AuthResponse<{ user: AuthUser | null; session: AuthSession | null }>> {
    try {
      const signUpOptions = credentials.options ? {
        email: credentials.email,
        password: credentials.password,
        options: credentials.options
      } : {
        email: credentials.email,
        password: credentials.password
      }

      const { data, error } = await this.supabase.auth.signUp(signUpOptions)

      return {
        data: data as { user: AuthUser | null; session: AuthSession | null },
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Sign in with email and password
   */
  async signInWithPassword(credentials: SignInCredentials): Promise<AuthResponse<{ user: AuthUser; session: AuthSession }>> {
    try {
      const signInOptions = credentials.options ? {
        email: credentials.email,
        password: credentials.password,
        options: credentials.options
      } : {
        email: credentials.email,
        password: credentials.password
      }

      const { data, error } = await this.supabase.auth.signInWithPassword(signInOptions)

      return {
        data: data as { user: AuthUser; session: AuthSession },
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<AuthResponse<null>> {
    try {
      console.log('🔄 AuthService - Starting Supabase signOut...')

      // Get current session before signing out for logging
      const { data: currentSession } = await this.supabase.auth.getSession()
      console.log('📊 AuthService - Current session before signOut:', {
        hasSession: !!currentSession?.session,
        userId: currentSession?.session?.user?.id,
        timestamp: new Date().toISOString()
      })

      // Perform sign out with scope 'local' first (more reliable)
      const { error } = await this.supabase.auth.signOut({ scope: 'local' })

      console.log('📊 AuthService - Supabase signOut result:', {
        success: !error,
        error: error,
        timestamp: new Date().toISOString()
      })

      if (error) {
        console.error('❌ AuthService - SignOut error:', {
          error,
          code: error.message,
          details: error
        })

        // For certain errors, we might still want to consider it a success
        // (e.g., if user is already signed out)
        if (error.message?.includes('not authenticated') || error.message?.includes('session not found')) {
          console.log('ℹ️ AuthService - User already signed out, treating as success')
          return { data: null, error: null }
        }
      } else {
        console.log('✅ AuthService - SignOut successful!')
      }

      // Verify session is cleared
      const { data: afterSession } = await this.supabase.auth.getSession()
      console.log('📊 AuthService - Session after signOut:', {
        hasSession: !!afterSession?.session,
        timestamp: new Date().toISOString()
      })

      return { data: null, error }
    } catch (error) {
      console.error('💥 AuthService - Unexpected signOut error:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })

      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Get the current session
   */
  async getSession(): Promise<AuthResponse<AuthSession | null>> {
    try {
      const { data, error } = await this.supabase.auth.getSession()
      return {
        data: data.session as AuthSession | null,
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Get the current user (validates token)
   */
  async getUser(): Promise<AuthResponse<AuthUser | null>> {
    try {
      const { data, error } = await this.supabase.auth.getUser()
      return {
        data: data.user as AuthUser | null,
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Reset password via email
   */
  async resetPassword(credentials: ResetPasswordCredentials): Promise<AuthResponse<null>> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(
        credentials.email,
        credentials.options
      )
      return { data: null, error }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Update user password
   */
  async updatePassword(credentials: UpdatePasswordCredentials): Promise<AuthResponse<AuthUser>> {
    try {
      const { data, error } = await this.supabase.auth.updateUser({
        password: credentials.password
      })
      return {
        data: data.user as AuthUser,
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void) {
    return this.supabase.auth.onAuthStateChange((event, session) => {
      // Convert Session to AuthSession by ensuring expires_at and required user fields are present
      const authSession: AuthSession | null = session ? {
        ...session,
        expires_at: session.expires_at || Math.floor(Date.now() / 1000) + 3600, // Default 1 hour if missing
        user: {
          id: session.user.id,
          email: session.user.email || '',
          email_confirmed_at: session.user.email_confirmed_at || null,
          phone: session.user.phone || null,
          created_at: session.user.created_at || new Date().toISOString(),
          updated_at: session.user.updated_at || new Date().toISOString(),
          user_metadata: session.user.user_metadata || {},
          app_metadata: session.user.app_metadata || {}
        }
      } : null
      callback(event, authSession)
    })
  }

  /**
   * Refresh the current session
   */
  async refreshSession(): Promise<AuthResponse<{ user: AuthUser; session: AuthSession }>> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession()
      return {
        data: data as { user: AuthUser; session: AuthSession },
        error
      }
    } catch (error) {
      return {
        data: null,
        error: error as AuthError
      }
    }
  }
}

// Export singleton instances for convenience
export const authService = new AuthService()

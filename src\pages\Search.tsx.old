import { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Sparkles,
  FileText,
  Filter,
  History,
  BookOpen,
  ExternalLink,
  Clock,
  Star,
  Bot,
  Zap,
} from "lucide-react";

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [aiQuery, setAiQuery] = useState("");
  const [searchType, setSearchType] = useState("all");
  const [isSearching, setIsSearching] = useState(false);

  const quickSearchSuggestions = [
    "Process Validation Guidelines",
    "Data Integrity Requirements",
    "GMP Annual Updates",
    "Quality Management Systems",
    "Analytical Method Validation",
    "Computer System Validation",
  ];

  const searchResults = [
    {
      id: 1,
      title: "FDA Process Validation Guidance for Industry",
      type: "Document",
      agency: "FDA",
      relevance: 96,
      summary:
        "This guidance provides recommendations for process validation activities in three stages: process design, process qualification, and continued process verification...",
      url: "https://fda.gov/process-validation",
      publishedDate: "2023-06-20",
      tags: ["Process Validation", "FDA", "Current"],
      source: "Regulatory Database",
    },
    {
      id: 2,
      title: "EMA Guideline on Process Validation for Medicinal Products",
      type: "Guideline",
      agency: "EMA",
      relevance: 92,
      summary:
        "This guideline describes the principles and approaches for validation of manufacturing processes used in the production of medicinal products...",
      url: "https://ema.europa.eu/process-validation",
      publishedDate: "2023-05-15",
      tags: ["Process Validation", "EMA", "Active"],
      source: "Regulatory Database",
    },
    {
      id: 3,
      title: "ICH Q8 Pharmaceutical Development Guidelines",
      type: "International Standard",
      agency: "ICH",
      relevance: 88,
      summary:
        "The objective of pharmaceutical development is to design a quality product and its manufacturing process to consistently deliver the intended performance...",
      url: "https://ich.org/q8-pharmaceutical-development",
      publishedDate: "2023-04-10",
      tags: ["Pharmaceutical Development", "ICH", "Quality by Design"],
      source: "Standards Database",
    },
    {
      id: 4,
      title: "Process Validation SOP Template v2.1",
      type: "Template",
      agency: "Internal",
      relevance: 85,
      summary:
        "Standard operating procedure template for conducting process validation studies including risk assessment, statistical analysis, and documentation requirements...",
      url: "/documents/sop-process-validation",
      publishedDate: "2023-06-01",
      tags: ["SOP", "Template", "Process Validation"],
      source: "Document Library",
    },
  ];

  const recentSearches = [
    "FDA guidance on data integrity",
    "EMA quality management guidelines",
    "ICH Q9 risk management",
    "Process validation statistical methods",
    "Computer system validation GAMP",
  ];

  const aiInsights = [
    {
      query:
        "What are the key differences between FDA and EMA process validation requirements?",
      insight:
        "The main differences lie in statistical approaches, with FDA emphasizing lifecycle approach while EMA focuses more on risk-based validation strategies.",
      confidence: 94,
    },
    {
      query: "How do I implement continuous process verification?",
      insight:
        "Continuous process verification requires real-time monitoring of process parameters, statistical trend analysis, and automated alert systems for deviations.",
      confidence: 89,
    },
  ];

  const handleSearch = () => {
    setIsSearching(true);
    // Simulate search delay
    setTimeout(() => {
      setIsSearching(false);
    }, 1500);
  };

  const handleAiSearch = () => {
    setIsSearching(true);
    // Simulate AI search delay
    setTimeout(() => {
      setIsSearching(false);
    }, 2000);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              AI-Powered Search
            </h1>
            <p className="text-muted-foreground mt-1">
              Search through regulatory documents, compliance data, and updates
              with intelligent AI assistance
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <History className="mr-2 h-4 w-4" />
              Search History
            </Button>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
            </Button>
          </div>
        </div>

        {/* Search Tabs */}
        <Tabs defaultValue="standard" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="standard" className="flex items-center">
              <Search className="mr-2 h-4 w-4" />
              Standard Search
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center">
              <Sparkles className="mr-2 h-4 w-4" />
              AI Assistant Search
            </TabsTrigger>
          </TabsList>

          {/* Standard Search */}
          <TabsContent value="standard" className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="Enter your search query or ask a question..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                        onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Select value={searchType} onValueChange={setSearchType}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Search in..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Document Types</SelectItem>
                        <SelectItem value="regulations">Regulations</SelectItem>
                        <SelectItem value="guidelines">Guidelines</SelectItem>
                        <SelectItem value="templates">Templates</SelectItem>
                        <SelectItem value="sops">SOPs</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={handleSearch}
                      disabled={isSearching}
                      className="bg-primary hover:bg-primary/90"
                    >
                      {isSearching ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Quick Search Suggestions */}
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-2">
                    Quick Search Suggestions:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {quickSearchSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setSearchQuery(suggestion)}
                        className="text-xs hover:bg-primary hover:text-primary-foreground hover:border-primary"
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Search Results */}
            {searchQuery && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">
                    Search Results for "{searchQuery}"
                  </h2>
                  <span className="text-sm text-muted-foreground">
                    Showing 1-10 of 47 results
                  </span>
                </div>

                {searchResults.map((result) => (
                  <Card
                    key={result.id}
                    className="transition-all duration-200 hover:shadow-md hover:border-primary/20 cursor-pointer"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{result.type}</Badge>
                            <Badge variant="secondary">{result.agency}</Badge>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Star className="mr-1 h-3 w-3 fill-current text-warning" />
                              {result.relevance}% relevant
                            </div>
                          </div>
                          <h3 className="font-semibold text-foreground mb-2 hover:text-primary cursor-pointer">
                            {result.title}
                          </h3>
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {result.summary}
                          </p>
                          <div className="flex flex-wrap gap-1 mb-3">
                            {result.tags.map((tag, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-xs text-muted-foreground space-x-4">
                              <span>Source: {result.source}</span>
                              <span>Published: {result.publishedDate}</span>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="hover:bg-primary hover:text-primary-foreground hover:border-primary"
                              >
                                <FileText className="mr-2 h-3 w-3" />
                                View
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="hover:bg-muted/60"
                              >
                                <ExternalLink className="mr-2 h-3 w-3" />
                                Open Source
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* AI Assistant Search */}
          <TabsContent value="ai" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bot className="mr-2 h-5 w-5" />
                  AI Compliance Assistant
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Ask me anything about compliance:
                  </label>
                  <Textarea
                    placeholder="For example: 'What are the latest FDA requirements for process validation?' or 'How do I implement data integrity in my QMS?'"
                    value={aiQuery}
                    onChange={(e) => setAiQuery(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      <Zap className="mr-1 h-3 w-3" />
                      Powered by AI
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      Real-time Regulatory Data
                    </Badge>
                  </div>
                  <Button
                    onClick={handleAiSearch}
                    disabled={isSearching}
                    className="bg-primary hover:bg-primary/90"
                  >
                    {isSearching ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Ask AI
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* AI Insights */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">Recent AI Insights</h2>
              {aiInsights.map((insight, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <p className="font-medium text-sm">{insight.query}</p>
                        <Badge variant="secondary" className="text-xs">
                          {insight.confidence}% confidence
                        </Badge>
                      </div>
                      <div className="bg-muted/50 border border-border p-3 rounded-md">
                        <p className="text-sm text-foreground">
                          💡 {insight.insight}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-primary hover:text-primary-foreground hover:border-primary"
                        >
                          <BookOpen className="mr-2 h-3 w-3" />
                          View Sources
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="hover:bg-muted/60"
                        >
                          Ask Follow-up
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Recent Searches Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-sm">
              <History className="mr-2 h-4 w-4" />
              Recent Searches
            </CardTitle>
          </CardHeader>
          <CardContent className="py-3">
            <div className="space-y-1">
              {recentSearches.map((search, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-muted/30 cursor-pointer transition-colors group border border-transparent hover:border-border/50"
                >
                  <span className="text-sm text-foreground group-hover:text-foreground/90">
                    {search}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setSearchQuery(search)}
                  >
                    <Search className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

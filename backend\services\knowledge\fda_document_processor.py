"""
FDA Document Processor

Comprehensive system for processing FDA regulatory documents (21 CFR) and populating
the knowledge base with embeddings for pharmaceutical compliance AI.

Features:
- PyMuPDF-based PDF text extraction
- Regulatory-aware text chunking
- BGE-M3 embedding generation
- Qdrant vector storage
- FDA-specific metadata extraction
- Progress tracking and error recovery
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import re

import pymupdf
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from ..ai.bge_m3_embeddings import BGEM3EmbeddingsService, EmbeddingRequest
from ..ai.qdrant_store import QdrantVectorStore, DocumentMetadata

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FDADocumentProcessor:
    """
    Main orchestrator for FDA document processing and knowledge base population.

    Handles the complete pipeline from PDF extraction to vector storage.
    """

    def __init__(
        self,
        fda_docs_path: str = "./fda_docs",
        vector_store_path: str = "./data/qdrant",
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ):
        """
        Initialize the FDA document processor.

        Args:
            fda_docs_path: Path to FDA documents directory
            vector_store_path: Path for Qdrant vector storage
            chunk_size: Size of text chunks for processing
            chunk_overlap: Overlap between chunks
        """
        self.fda_docs_path = Path(fda_docs_path)
        self.vector_store_path = vector_store_path
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Initialize services
        self.pdf_extractor = PDFExtractor()
        self.regulatory_chunker = RegulatoryChunker(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        self.embedding_service = BGEM3EmbeddingsService()
        self.vector_store = QdrantVectorStore(
            path=vector_store_path,
            collection_name="fda_regulatory_documents"
        )
        self.metadata_extractor = MetadataExtractor()

        # Processing statistics
        self.stats = {
            "documents_processed": 0,
            "chunks_created": 0,
            "embeddings_generated": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None
        }

    async def initialize(self) -> None:
        """Initialize all services."""
        logger.info("Initializing FDA document processor...")

        await self.embedding_service.initialize()
        await self.vector_store.initialize()

        logger.info("FDA document processor initialized successfully")

    async def process_all_documents(self) -> Dict[str, Any]:
        """
        Process all FDA documents in the specified directory.

        Returns:
            Processing statistics and results
        """
        self.stats["start_time"] = datetime.now(timezone.utc)
        logger.info(f"Starting FDA document processing from: {self.fda_docs_path}")

        try:
            # Find all PDF files
            pdf_files = list(self.fda_docs_path.glob("*.pdf"))
            logger.info(f"Found {len(pdf_files)} PDF files to process")

            if not pdf_files:
                logger.warning("No PDF files found in the specified directory")
                return self.stats

            # Process each PDF file
            for pdf_file in pdf_files:
                try:
                    await self.process_single_document(pdf_file)
                    self.stats["documents_processed"] += 1
                except Exception as e:
                    logger.error(f"Failed to process {pdf_file}: {e}")
                    self.stats["errors"] += 1
                    continue

            self.stats["end_time"] = datetime.now(timezone.utc)
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()

            logger.info(f"FDA document processing completed in {duration:.2f} seconds")
            logger.info(f"Statistics: {self.stats}")

            return self.stats

        except Exception as e:
            logger.error(f"Fatal error in document processing: {e}")
            self.stats["errors"] += 1
            raise

    async def process_single_document(self, pdf_path: Path) -> None:
        """
        Process a single FDA PDF document.

        Args:
            pdf_path: Path to the PDF file
        """
        logger.info(f"Processing document: {pdf_path.name}")

        # Extract text from PDF
        text_content, doc_metadata = await self.pdf_extractor.extract_text(pdf_path)

        if not text_content.strip():
            logger.warning(f"No text content extracted from {pdf_path.name}")
            return

        # Create document object
        document = Document(
            page_content=text_content,
            metadata=doc_metadata
        )

        # Split into chunks
        chunks = await self.regulatory_chunker.split_document(document)
        logger.info(f"Created {len(chunks)} chunks from {pdf_path.name}")
        self.stats["chunks_created"] += len(chunks)

        # Process chunks in batches
        batch_size = 10
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            await self.process_chunk_batch(batch)

    async def process_chunk_batch(self, chunks: List[Document]) -> None:
        """
        Process a batch of document chunks.

        Args:
            chunks: List of document chunks to process
        """
        try:
            # Extract text content for embedding
            texts = [chunk.page_content for chunk in chunks]

            # Generate embeddings
            embedding_request = EmbeddingRequest(texts=texts)
            embedding_response = await self.embedding_service.generate_embeddings(embedding_request)

            if len(embedding_response.embeddings) != len(chunks):
                raise ValueError("Mismatch between embeddings and chunks")

            # Prepare metadata for vector store
            metadatas = []
            for chunk in chunks:
                # Extract FDA-specific metadata
                fda_metadata = self.metadata_extractor.extract_metadata(chunk)

                # Create DocumentMetadata for vector store
                title = fda_metadata.get("title")
                # Ensure title is None if empty string (our validator rejects empty strings)
                if title == "":
                    title = None

                doc_metadata = DocumentMetadata(
                    source="FDA",
                    title=title,
                    document_type=fda_metadata.get("document_type", "cfr"),
                    regulatory_framework="FDA"
                )
                metadatas.append(doc_metadata)

            # Store in vector database
            await self.vector_store.add_documents(
                contents=texts,
                vectors=embedding_response.embeddings,
                metadatas=metadatas
            )

            self.stats["embeddings_generated"] += len(embedding_response.embeddings)
            logger.info(f"Processed batch of {len(chunks)} chunks")

        except Exception as e:
            logger.error(f"Failed to process chunk batch: {e}")
            self.stats["errors"] += 1
            raise


class PDFExtractor:
    """PDF text extraction using PyMuPDF."""

    async def extract_text(self, pdf_path: Path) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text content and metadata from PDF.

        Args:
            pdf_path: Path to PDF file

        Returns:
            Tuple of (text_content, metadata)
        """
        try:
            doc = pymupdf.open(str(pdf_path))

            # Extract metadata
            metadata = {
                "source_file": pdf_path.name,
                "page_count": len(doc),
                "title": doc.metadata.get("title", pdf_path.stem),
                "author": doc.metadata.get("author", "FDA"),
                "creation_date": doc.metadata.get("creationDate"),
                "modification_date": doc.metadata.get("modDate"),
                "processed_at": datetime.now(timezone.utc).isoformat()
            }

            # Extract text from all pages
            text_content = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()

                if page_text.strip():
                    # Add page separator for context
                    text_content += f"\n\n--- Page {page_num + 1} ---\n\n"
                    text_content += page_text

            doc.close()

            logger.info(f"Extracted {len(text_content)} characters from {pdf_path.name}")
            return text_content, metadata

        except Exception as e:
            logger.error(f"Failed to extract text from {pdf_path}: {e}")
            raise


class RegulatoryChunker:
    """Specialized text splitter for FDA regulatory documents."""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Initialize regulatory chunker.

        Args:
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between chunks
        """
        # FDA-specific separators for better regulatory document chunking
        fda_separators = [
            "\n\n--- Page",  # Page breaks
            "\n\nPart ",     # CFR Parts
            "\n\nSubpart ",  # CFR Subparts
            "\n\nSec. ",     # CFR Sections
            "\n\n§ ",        # Section symbol
            "\n\n",          # Double newlines
            "\n",            # Single newlines
            ". ",            # Sentences
            ", ",            # Clauses
            " ",             # Words
            ""               # Characters
        ]

        self.text_splitter = RecursiveCharacterTextSplitter(
            separators=fda_separators,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False,
            add_start_index=True
        )

    async def split_document(self, document: Document) -> List[Document]:
        """
        Split document into regulatory-aware chunks.

        Args:
            document: Document to split

        Returns:
            List of document chunks
        """
        try:
            chunks = self.text_splitter.split_documents([document])

            # Enhance chunks with regulatory context
            enhanced_chunks = []
            for i, chunk in enumerate(chunks):
                # Add chunk metadata
                chunk.metadata.update({
                    "chunk_id": i,
                    "chunk_size": len(chunk.page_content),
                    "total_chunks": len(chunks)
                })

                # Extract regulatory references
                chunk.metadata.update(
                    self._extract_regulatory_references(chunk.page_content)
                )

                enhanced_chunks.append(chunk)

            return enhanced_chunks

        except Exception as e:
            logger.error(f"Failed to split document: {e}")
            raise

    def _extract_regulatory_references(self, text: str) -> Dict[str, Any]:
        """Extract regulatory references from text."""
        references = {
            "cfr_parts": [],
            "cfr_sections": [],
            "has_definitions": False,
            "has_requirements": False
        }

        # Extract CFR parts (e.g., "Part 11", "Part 210")
        part_matches = re.findall(r'Part\s+(\d+)', text, re.IGNORECASE)
        references["cfr_parts"] = list(set(part_matches))

        # Extract CFR sections (e.g., "§ 11.10", "Sec. 210.3")
        section_matches = re.findall(r'(?:§|Sec\.)\s*(\d+\.\d+)', text, re.IGNORECASE)
        references["cfr_sections"] = list(set(section_matches))

        # Check for definitions and requirements
        references["has_definitions"] = bool(re.search(r'\bdefinition\b|\bdefine[ds]?\b|\bmeans\b', text, re.IGNORECASE))
        references["has_requirements"] = bool(re.search(r'\brequire[ds]?\b|\bshall\b|\bmust\b', text, re.IGNORECASE))

        return references


class MetadataExtractor:
    """Extract FDA-specific metadata from documents."""

    def extract_metadata(self, document: Document) -> Dict[str, Any]:
        """
        Extract FDA-specific metadata from document.

        Args:
            document: Document to extract metadata from

        Returns:
            Dictionary of extracted metadata
        """
        metadata = document.metadata.copy()
        text = document.page_content

        # Determine document type based on content
        doc_type = self._determine_document_type(text, metadata.get("source_file", ""))
        metadata["document_type"] = doc_type

        # Extract title if not present
        if not metadata.get("title"):
            title = self._extract_title(text)
            if title:
                metadata["title"] = title

        # Add FDA-specific classifications
        metadata.update({
            "regulatory_framework": "FDA",
            "compliance_level": "federal",
            "document_category": "regulatory"
        })

        return metadata

    def _determine_document_type(self, text: str, filename: str) -> str:
        """Determine FDA document type."""
        filename_lower = filename.lower()
        text_lower = text.lower()

        if "cfr" in filename_lower or "code of federal regulations" in text_lower:
            return "cfr"
        elif "guidance" in filename_lower or "guidance" in text_lower:
            return "guidance"
        elif "policy" in filename_lower or "policy" in text_lower:
            return "policy"
        elif "form" in filename_lower:
            return "form"
        else:
            return "regulation"

    def _extract_title(self, text: str) -> Optional[str]:
        """Extract document title from text."""
        lines = text.split('\n')[:20]  # Check first 20 lines

        for line in lines:
            line = line.strip()
            if len(line) > 10 and len(line) < 200:
                # Look for title-like patterns
                if any(keyword in line.lower() for keyword in ['part', 'title', 'cfr', 'regulation', 'subpart', 'section']):
                    return line

                # Look for lines that are all caps (often titles)
                if line.isupper() and len(line) > 15:
                    return line

                # Look for lines with specific CFR patterns
                if re.search(r'\b\d+\s*CFR\b|\bPart\s+\d+\b|\bSubpart\s+[A-Z]\b', line, re.IGNORECASE):
                    return line

        return None

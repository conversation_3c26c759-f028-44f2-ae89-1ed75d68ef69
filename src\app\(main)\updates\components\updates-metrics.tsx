'use client'

import { Card, CardContent } from '@/components/ui-radix/card'

import type { UpdateMetric } from '../types'

interface UpdatesMetricsProps {
  readonly metrics: readonly UpdateMetric[];
}

export function UpdatesMetrics({ metrics }: UpdatesMetricsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {metrics.map((metric, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${metric.color}`} />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {metric.value}
                </p>
                <p className="text-sm text-muted-foreground">{metric.label}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

import { Button } from '@/components/ui-radix/button';
import { cn } from '@/lib/utils';
import { Grid, List } from 'lucide-react';
import * as React from 'react';
import type { ViewModeToggleProps } from '../types';

/**
 * ViewModeToggle Component - Grid/List View Toggle
 *
 * Features:
 * - Connected button group design
 * - Clear visual states for active/inactive
 * - Accessible with proper labels
 * - Responsive design
 */
export const ViewModeToggle: React.FC<ViewModeToggleProps> = React.memo(
  function ViewModeToggle({ value, onChange }) {
    return (
      <div
        className="flex border border-border rounded-lg overflow-hidden"
        role="radiogroup"
        aria-label="View mode selection"
      >
        <Button
          variant={value === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onChange('grid')}
          className={cn(
            "rounded-r-none border-r-0",
            value === 'grid' && "bg-primary text-primary-foreground"
          )}
          aria-label="Grid view"
          role="radio"
          aria-checked={value === 'grid'}
        >
          <Grid className="h-4 w-4" aria-hidden="true" />
        </Button>
        <Button
          variant={value === 'list' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onChange('list')}
          className={cn(
            "rounded-l-none",
            value === 'list' && "bg-primary text-primary-foreground"
          )}
          aria-label="List view"
          role="radio"
          aria-checked={value === 'list'}
        >
          <List className="h-4 w-4" aria-hidden="true" />
        </Button>
      </div>
    );
  }
);

ViewModeToggle.displayName = 'ViewModeToggle';

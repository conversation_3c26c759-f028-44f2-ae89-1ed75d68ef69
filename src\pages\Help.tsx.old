import { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  HelpCircle,
  Search,
  BookOpen,
  MessageCircle,
  Phone,
  Mail,
  Download,
  Play,
  ChevronDown,
  ExternalLink,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Bot,
} from "lucide-react";

export default function Help() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const quickStartGuides = [
    {
      title: "Getting Started with AI Compliance Platform",
      description: "Complete walkthrough of platform features and setup",
      duration: "5 minutes",
      difficulty: "Beginner",
      views: "1.2k",
    },
    {
      title: "Document Upload & Processing",
      description: "How to upload and analyze compliance documents",
      duration: "3 minutes",
      difficulty: "Beginner",
      views: "856",
    },
    {
      title: "Running Compliance Checks",
      description: "Step-by-step guide to automated compliance validation",
      duration: "4 minutes",
      difficulty: "Intermediate",
      views: "743",
    },
    {
      title: "Understanding AI Insights",
      description: "How to interpret AI-generated compliance recommendations",
      duration: "6 minutes",
      difficulty: "Intermediate",
      views: "621",
    },
  ];

  const faqs = [
    {
      category: "Getting Started",
      question: "What file formats are supported for document upload?",
      answer:
        "Our platform supports PDF, DOC, DOCX, and TXT formats. All uploaded documents are processed securely with end-to-end encryption and are automatically deleted after 30 days unless saved to your library.",
    },
    {
      category: "Compliance Checks",
      question: "How long does compliance checking take?",
      answer:
        "Compliance checking typically takes 2-5 minutes depending on document size and selected framework. Basic checks complete in under 2 minutes, while comprehensive audits may take up to 10 minutes for large document sets.",
    },
    {
      category: "AI Features",
      question: "How accurate are the AI-generated recommendations?",
      answer:
        "Our AI system achieves 94% accuracy on regulatory compliance analysis, trained on thousands of pharmaceutical regulations and industry best practices. All recommendations include confidence scores and source references.",
    },
    {
      category: "Data Security",
      question: "How is my data secured?",
      answer:
        "We use enterprise-grade security including AES-256 encryption, SOC 2 compliance, and GDPR-compliant data processing. Your documents are processed in secure cloud environments and never stored permanently without your consent.",
    },
    {
      category: "Integration",
      question: "Can I integrate with existing quality management systems?",
      answer:
        "Yes, we offer API integration with major QMS platforms including TrackWise, MasterControl, and Veeva Vault. Contact our support team for specific integration requirements and setup assistance.",
    },
    {
      category: "Billing",
      question: "What's included in each pricing tier?",
      answer:
        "Our Basic tier includes document analysis and compliance checking. Professional adds AI insights and team collaboration. Enterprise includes custom frameworks, API access, and dedicated support. See our pricing page for detailed comparisons.",
    },
  ];

  const supportChannels = [
    {
      icon: MessageCircle,
      title: "Live Chat Support",
      description: "Get instant help from our support team",
      availability: "Available 24/7",
      responseTime: "< 5 minutes",
      action: "Start Chat",
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us a detailed message",
      availability: "<EMAIL>",
      responseTime: "< 24 hours",
      action: "Send Email",
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak directly with our experts",
      availability: "+****************",
      responseTime: "Business hours",
      action: "Call Now",
    },
    {
      icon: Bot,
      title: "AI Assistant",
      description: "Get help from our AI support bot",
      availability: "Available 24/7",
      responseTime: "Instant",
      action: "Ask AI",
    },
  ];

  const troubleshootingSteps = [
    {
      issue: "Document upload failing",
      steps: [
        "Check file size is under 25MB",
        "Verify file format (PDF, DOC, DOCX, TXT)",
        "Clear browser cache and cookies",
        "Try uploading from incognito/private window",
        "Contact support if issue persists",
      ],
    },
    {
      issue: "Compliance check stuck at processing",
      steps: [
        "Wait 10-15 minutes for large documents",
        "Check internet connection stability",
        "Refresh the page and check status",
        "Cancel and restart the check",
        "Contact support with document details",
      ],
    },
    {
      issue: "AI Assistant not responding",
      steps: [
        "Check your internet connection",
        "Refresh the page",
        "Clear browser cache",
        "Try asking a different question",
        "Report the issue to our team",
      ],
    },
  ];

  const filteredFaqs = faqs.filter((faq) => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Help & Support Center
            </h1>
            <p className="text-muted-foreground mt-1">
              Find answers to your questions and get assistance with AI
              Compliance Platform
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              User Guide
            </Button>
            <Button>
              <MessageCircle className="mr-2 h-4 w-4" />
              Contact Support
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search help articles, FAQs, or ask a question..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="Getting Started">
                    Getting Started
                  </SelectItem>
                  <SelectItem value="Compliance Checks">
                    Compliance Checks
                  </SelectItem>
                  <SelectItem value="AI Features">AI Features</SelectItem>
                  <SelectItem value="Data Security">Data Security</SelectItem>
                  <SelectItem value="Integration">Integration</SelectItem>
                  <SelectItem value="Billing">Billing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Help Content Tabs */}
        <Tabs defaultValue="quick-start" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="quick-start">Quick Start</TabsTrigger>
            <TabsTrigger value="faqs">FAQs</TabsTrigger>
            <TabsTrigger value="troubleshooting">Troubleshooting</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
          </TabsList>

          {/* Quick Start Guides */}
          <TabsContent value="quick-start" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {quickStartGuides.map((guide, index) => (
                <Card
                  key={index}
                  className="hover:shadow-md transition-all duration-200 hover:border-primary/20 cursor-pointer"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                          <Play className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-foreground mb-1">
                            {guide.title}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {guide.description}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Clock className="mr-1 h-3 w-3" />
                          {guide.duration}
                        </div>
                        <Badge variant="outline">{guide.difficulty}</Badge>
                        <span>{guide.views} views</span>
                      </div>
                      <Button size="sm">
                        <Play className="mr-2 h-3 w-3" />
                        Watch
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Getting Started Steps */}
            <Card>
              <CardHeader>
                <CardTitle>
                  Getting Started with AI Compliance Platform
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      1
                    </div>
                    <div>
                      <h4 className="font-medium">Account Setup & Profile</h4>
                      <p className="text-sm text-muted-foreground">
                        Complete your profile information and configure
                        compliance frameworks relevant to your organization.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      2
                    </div>
                    <div>
                      <h4 className="font-medium">
                        Upload Your First Document
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Upload a compliance document (SOP, manual, or guideline)
                        and experience our AI-powered analysis.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      3
                    </div>
                    <div>
                      <h4 className="font-medium">
                        Run Your First Compliance Check
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Select a compliance framework and run automated
                        validation to identify gaps and recommendations.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      4
                    </div>
                    <div>
                      <h4 className="font-medium">
                        Explore AI Assistant & Search
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Ask questions about compliance requirements and search
                        through regulatory databases with AI assistance.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* FAQs */}
          <TabsContent value="faqs" className="space-y-4">
            <div className="space-y-3">
              {filteredFaqs.map((faq, index) => (
                <Collapsible key={index}>
                  <CollapsibleTrigger asChild>
                    <Card className="cursor-pointer hover:shadow-sm transition-all duration-200 hover:border-primary/20">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Badge variant="outline">{faq.category}</Badge>
                            <h3 className="font-medium text-left">
                              {faq.question}
                            </h3>
                          </div>
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <Card className="mt-2">
                      <CardContent className="p-4 bg-accent/30">
                        <p className="text-sm text-muted-foreground">
                          {faq.answer}
                        </p>
                      </CardContent>
                    </Card>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          </TabsContent>

          {/* Troubleshooting */}
          <TabsContent value="troubleshooting" className="space-y-6">
            <div className="space-y-4">
              {troubleshootingSteps.map((trouble, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <AlertCircle className="mr-2 h-5 w-5 text-warning" />
                      {trouble.issue}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {trouble.steps.map((step, stepIndex) => (
                        <div
                          key={stepIndex}
                          className="flex items-start space-x-3"
                        >
                          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-xs font-medium">
                            {stepIndex + 1}
                          </div>
                          <p className="text-sm">{step}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Contact Support */}
          <TabsContent value="contact" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {supportChannels.map((channel, index) => (
                <Card
                  key={index}
                  className="hover:shadow-md transition-all duration-200 hover:border-primary/20 cursor-pointer"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                        <channel.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold mb-2">{channel.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3">
                          {channel.description}
                        </p>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Availability:
                            </span>
                            <span>{channel.availability}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Response time:
                            </span>
                            <span>{channel.responseTime}</span>
                          </div>
                        </div>
                        <Button className="w-full mt-4" size="sm">
                          {channel.action}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle>Send us a Message</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Subject</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a topic" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">
                          Technical Issue
                        </SelectItem>
                        <SelectItem value="billing">
                          Billing Question
                        </SelectItem>
                        <SelectItem value="feature">Feature Request</SelectItem>
                        <SelectItem value="integration">
                          Integration Help
                        </SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Priority</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Describe your issue
                  </label>
                  <Textarea
                    placeholder="Please provide as much detail as possible..."
                    className="min-h-[120px]"
                  />
                </div>

                <Button className="w-full">Send Message</Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Resources */}
          <TabsContent value="resources">
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Documentation & Resources
              </h3>
              <p className="text-muted-foreground">
                Comprehensive guides, API documentation, and training materials
                coming soon.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

/**
 * Authentication Context Provider for VigiLens
 *
 * Provides global user state with role-based access control
 * Follows DEVELOPMENT_RULES.md: TypeScript strict mode, pharmaceutical compliance
 * Follows DEVELOPMENT_RULES_2.md: Production-first, runtime type safety
 */

'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import { authService, type AuthSession } from '@/lib/auth-service'
import type { User } from '@supabase/supabase-js'

// TypeScript interfaces for pharmaceutical compliance platform
export interface UserProfile {
  id: string
  email: string
  full_name: string | null
  role: 'super_admin' | 'org_admin' | 'quality_manager' | 'regulatory_lead' | 'compliance_officer' | 'document_reviewer' | 'analyst' | 'auditor' | 'viewer' | 'admin' | 'guest'
  organization_id: string | null
  organization_name: string | null
  department: string | null
  phone: string | null
  is_active: boolean
  last_login: string | null
  bio?: any
  preferences?: any
  notification_preferences?: any
  security_settings?: any
  compliance_settings?: any
  activity_log?: any
  achievements?: any
  created_at: string
  updated_at: string
}

export interface AuthPermissions {
  // Core PRD.md Required Permissions
  canAccessAdmin: boolean
  canManageUsers: boolean
  canCreateDocuments: boolean
  canEditDocuments: boolean
  canDeleteDocuments: boolean
  canViewCompliance: boolean
  canManageCompliance: boolean
  canAccessAI: boolean
  canExportData: boolean
  canManageOrganization: boolean
}

export interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  session: AuthSession | null
  permissions: AuthPermissions
  loading: boolean
  error: string | null

  // Authentication methods
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  refreshUserProfile: () => Promise<void>

  // Role and permission helpers
  hasRole: (role: string | string[]) => boolean
  hasPermission: (permission: keyof AuthPermissions) => boolean
  isAdmin: () => boolean
  isSuperAdmin: () => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Simplified permission mapping aligned with PRD.md requirements
const ROLE_PERMISSIONS: Record<string, Partial<AuthPermissions>> = {
  super_admin: {
    canAccessAdmin: true,
    canManageUsers: true,
    canCreateDocuments: true,
    canEditDocuments: true,
    canDeleteDocuments: true,
    canViewCompliance: true,
    canManageCompliance: true,
    canAccessAI: true,
    canExportData: true,
    canManageOrganization: true
  },
  admin: {
    canAccessAdmin: true,
    canManageUsers: true,
    canCreateDocuments: true,
    canEditDocuments: true,
    canDeleteDocuments: true,
    canViewCompliance: true,
    canManageCompliance: true,
    canAccessAI: true,
    canExportData: true,
    canManageOrganization: false
  },
  compliance_officer: {
    canAccessAdmin: false,
    canManageUsers: false,
    canCreateDocuments: true,
    canEditDocuments: true,
    canDeleteDocuments: false,
    canViewCompliance: true,
    canManageCompliance: true,
    canAccessAI: true,
    canExportData: true,
    canManageOrganization: false
  },
  viewer: {
    canAccessAdmin: false,
    canManageUsers: false,
    canCreateDocuments: false,
    canEditDocuments: false,
    canDeleteDocuments: false,
    canViewCompliance: true,
    canManageCompliance: false,
    canAccessAI: false,
    canExportData: false,
    canManageOrganization: false
  },
  guest: {
    canAccessAdmin: false,
    canManageUsers: false,
    canCreateDocuments: false,
    canEditDocuments: false,
    canDeleteDocuments: false,
    canViewCompliance: false,
    canManageCompliance: false,
    canAccessAI: false,
    canExportData: false,
    canManageOrganization: false
  }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<AuthSession | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  // Calculate permissions based on user role
  const permissions: AuthPermissions = React.useMemo(() => {
    if (!userProfile) {
      return ROLE_PERMISSIONS['guest'] as AuthPermissions
    }

    const rolePermissions = ROLE_PERMISSIONS[userProfile.role] || ROLE_PERMISSIONS['guest']

    // Default permissions (all false)
    const defaultPermissions: AuthPermissions = {
      canAccessAdmin: false,
      canManageUsers: false,
      canCreateDocuments: false,
      canEditDocuments: false,
      canDeleteDocuments: false,
      canViewCompliance: false,
      canManageCompliance: false,
      canAccessAI: false,
      canExportData: false,
      canManageOrganization: false
    }

    return {
      ...defaultPermissions,
      ...rolePermissions
    } as AuthPermissions
  }, [userProfile])

  // Fetch user profile from database with caching
  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      console.log('Fetching user profile for userId:', userId)

      // Skip if already loading the same user
      if (loading && userProfile?.id === userId) {
        console.log('Already loading profile for this user, skipping...')
        return
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select(`
          id,
          email,
          full_name,
          role,
          organization_id,
          department,
          phone,
          is_active,
          last_login,
          bio,
          preferences,
          notification_preferences,
          security_settings,
          compliance_settings,
          activity_log,
          achievements,
          created_at,
          updated_at,
          organizations (
            name
          )
        `)
        .eq('id', userId)
        .single()

      console.log('User profile query result:', { data, error })

      if (error) {
        console.error('Error fetching user profile:', error)

        // If user profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') { // No rows returned
          console.log('No user profile found, user may need profile creation')
          setError('User profile not found. Please contact administrator.')
        } else {
          setError('Failed to load user profile')
        }
        return
      }

      if (data) {
        console.log('✅ Auth Context - Setting user profile with all JSONB data:', {
          id: data.id,
          email: data.email,
          role: data.role,
          hasPreferences: !!data.preferences,
          hasNotificationPrefs: !!data.notification_preferences,
          hasSecuritySettings: !!data.security_settings,
          hasComplianceSettings: !!data.compliance_settings,
          hasBio: !!data.bio,
          hasActivityLog: !!data.activity_log,
          hasAchievements: !!data.achievements
        })

        const profile: UserProfile = {
          id: data.id,
          email: data.email,
          full_name: data.full_name,
          role: data.role as UserProfile['role'],
          organization_id: data.organization_id,
          organization_name: data.organizations?.name || null,
          department: data.department,
          phone: data.phone,
          is_active: data.is_active ?? true,
          last_login: data.last_login,
          bio: data.bio,
          preferences: data.preferences,
          notification_preferences: data.notification_preferences,
          security_settings: data.security_settings,
          compliance_settings: data.compliance_settings,
          activity_log: data.activity_log,
          achievements: data.achievements,
          created_at: data.created_at ?? new Date().toISOString(),
          updated_at: data.updated_at ?? new Date().toISOString()
        }
        setUserProfile(profile)

        console.log('🎯 Auth Context - Profile set successfully with JSONB data included!')
      }
    } catch (err) {
      console.error('Error in fetchUserProfile:', err)
      setError('Failed to load user profile')
    }
  }, [supabase])

  // Initialize auth state
  useEffect(() => {
    let mounted = true

    const initializeAuth = async () => {
      try {
        console.log('🔍 Initializing auth...')
        const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession()

        console.log('🔍 Session check result:', {
          hasSession: !!currentSession,
          sessionError,
          userEmail: currentSession?.user?.email
        })

        if (sessionError) {
          console.error('❌ Session error:', sessionError)
          setError('Authentication error')
          return
        }

        if (currentSession && mounted) {
          console.log('✅ Valid session found:', {
            id: currentSession.user.id,
            email: currentSession.user.email,
            created_at: currentSession.user.created_at
          })
          setSession(currentSession as AuthSession)
          setUser(currentSession.user)
          await fetchUserProfile(currentSession.user.id)
        } else {
          console.log('❌ No valid session found')
        }
      } catch (err) {
        console.error('Auth initialization error:', err)
        setError('Failed to initialize authentication')
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    initializeAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, currentSession) => {
        if (!mounted) return

        setSession(currentSession as AuthSession | null)
        setUser(currentSession?.user || null)
        setError(null)

        if (currentSession?.user) {
          await fetchUserProfile(currentSession.user.id)
        } else {
          setUserProfile(null)
        }

        setLoading(false)
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [supabase, fetchUserProfile])

  // Authentication methods
  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true)
    setError(null)

    try {
      const { error } = await authService.signInWithPassword({ email, password })

      if (error) {
        setError(error.message)
        return { error: error.message }
      }

      return {}
    } catch (err) {
      const errorMessage = 'Sign in failed'
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const signUp = useCallback(async (email: string, password: string, metadata?: Record<string, any>) => {
    setLoading(true)
    setError(null)

    try {
      const { error } = await authService.signUp({
        email,
        password,
        ...(metadata && { options: { data: metadata } })
      })

      if (error) {
        setError(error.message)
        return { error: error.message }
      }

      return {}
    } catch (err) {
      const errorMessage = 'Sign up failed'
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const signOut = useCallback(async () => {
    console.log('🔄 Auth Context - Starting sign out process...')
    setLoading(true)

    try {
      console.log('📤 Auth Context - Calling authService.signOut()...')
      const result = await authService.signOut()

      console.log('📊 Auth Context - SignOut result:', result)

      // Check if there was an error from the auth service
      if (result?.error) {
        console.error('❌ Auth Context - AuthService returned error:', result.error)

        // For certain errors, still clear state (user already signed out)
        if (result.error.message?.includes('not authenticated') ||
            result.error.message?.includes('session not found') ||
            result.error.message?.includes('Invalid session')) {
          console.log('ℹ️ Auth Context - User already signed out, clearing state anyway')
        } else {
          throw new Error(`SignOut failed: ${result.error.message || 'Unknown error'}`)
        }
      }

      // Clear all auth state after successful signOut
      console.log('🧹 Auth Context - Clearing auth state...')
      setUser(null)
      setUserProfile(null)
      setSession(null)
      setError(null)

      // Additional cleanup - clear any cached data
      console.log('🧹 Auth Context - Additional cleanup...')

      // Force a small delay to ensure state is cleared
      await new Promise(resolve => setTimeout(resolve, 100))

      console.log('✅ Auth Context - Sign out completed successfully!')

    } catch (err) {
      console.error('❌ Auth Context - Sign out error:', {
        error: err,
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined
      })

      // Clear state even if signOut failed
      console.log('🧹 Auth Context - Force clearing state due to error...')
      setUser(null)
      setUserProfile(null)
      setSession(null)
      setError('Sign out failed')

      // Re-throw so logout button can handle it
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const refreshSession = useCallback(async () => {
    try {
      const { error } = await authService.refreshSession()
      if (error) {
        console.error('Session refresh error:', error)
        setError('Session refresh failed')
      }
    } catch (err) {
      console.error('Session refresh error:', err)
      setError('Session refresh failed')
    }
  }, [])

  const refreshUserProfile = useCallback(async () => {
    if (!user?.id) {
      console.log('No user ID available for profile refresh')
      return
    }

    try {
      console.log('Refreshing user profile for userId:', user.id)
      await fetchUserProfile(user.id)
    } catch (err) {
      console.error('Error refreshing user profile:', err)
      setError('Failed to refresh user profile')
    }
  }, [user?.id, fetchUserProfile])

  // Role and permission helpers
  const hasRole = useCallback((role: string | string[]) => {
    if (!userProfile) return false
    const roles = Array.isArray(role) ? role : [role]
    return roles.includes(userProfile.role)
  }, [userProfile])

  const hasPermission = useCallback((permission: keyof AuthPermissions) => {
    return permissions[permission] || false
  }, [permissions])

  const isAdmin = useCallback(() => {
    return hasRole(['admin', 'super_admin'])
  }, [hasRole])

  const isSuperAdmin = useCallback(() => {
    return hasRole('super_admin')
  }, [hasRole])

  const value: AuthContextType = {
    user,
    userProfile,
    session,
    permissions,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    refreshSession,
    refreshUserProfile,
    hasRole,
    hasPermission,
    isAdmin,
    isSuperAdmin
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for role-based access
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string | string[]
) {
  return function AuthenticatedComponent(props: P) {
    const { user, userProfile, hasRole, loading } = useAuth()

    if (loading) {
      return <div>Loading...</div>
    }

    if (!user || !userProfile) {
      return <div>Access denied. Please log in.</div>
    }

    if (requiredRole && !hasRole(requiredRole)) {
      return <div>Access denied. Insufficient permissions.</div>
    }

    return <Component {...props} />
  }
}

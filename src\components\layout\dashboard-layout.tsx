'use client'

import { Menu } from 'lucide-react'
import { ReactNode, useState } from 'react'

import { Button } from '@/components/ui-radix/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui-radix/sheet'
import { useAuth } from '@/contexts/auth-context'

import { Header } from './header'
import { Sidebar } from './sidebar'

interface DashboardLayoutProps {
  readonly children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { loading, user, error } = useAuth()

  console.log('🏠 DashboardLayout render:', { loading, hasUser: !!user, error })

  // Show loading state while auth is initializing
  if (loading) {
    return (
      <div className="flex h-[100vh] items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading VigiLens...</p>
        </div>
      </div>
    )
  }

  // Show error state if authentication failed
  if (error) {
    return (
      <div className="flex h-[100vh] items-center justify-center bg-background">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-red-500 text-lg">⚠️ Authentication Error</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <button
            onClick={() => window.location.href = '/login'}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-[100vh] bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <Sidebar />
      </div>

      {/* Mobile Sidebar Drawer */}
      <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden fixed top-4 left-4 z-50 h-10 w-10"
            aria-label="Open navigation menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-[256px]">
          <Sidebar />
        </SheetContent>
      </Sheet>

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header />
        <main
          className="flex-1 overflow-y-auto"
          role="main"
          aria-label="Main content"
        >
          <div className="w-full p-6">{children}</div>
        </main>
      </div>
    </div>
  )
}

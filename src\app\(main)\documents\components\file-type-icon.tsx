import { cn } from '@/lib/utils';
import {
    Database,
    File,
    FileSpreadsheet,
    FileText,
    Presentation
} from 'lucide-react';
import * as React from 'react';

interface FileTypeIconProps {
  type: 'PDF' | 'DOCX' | 'XLSX' | 'TXT' | 'PPT' | 'CSV';
  className?: string;
}

/**
 * FileTypeIcon Component - Display appropriate icons for file types
 *
 * Maps file extensions to appropriate Lucide icons with consistent styling
 */
export const FileTypeIcon: React.FC<FileTypeIconProps> = ({ type, className }) => {
  const iconMap = {
    'PDF': FileText,
    'DOCX': FileText,
    'XLSX': FileSpreadsheet,
    'TXT': File,
    'PPT': Presentation,
    'CSV': Database
  };

  const Icon = iconMap[type] || FileText;

  return (
    <Icon
      className={cn("text-primary", className)}
      aria-label={`${type} file`}
    />
  );
};

FileTypeIcon.displayName = 'FileTypeIcon';

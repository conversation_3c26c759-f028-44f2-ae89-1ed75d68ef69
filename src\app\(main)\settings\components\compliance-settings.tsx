'use client'

import React, { useState, useEffect } from 'react'
import { Plus, Settings, Shield, Save, Loader2, CheckCircle, AlertCircle } from 'lucide-react'

import { Badge } from '@/components/ui-radix/badge'
import { Button } from '@/components/ui-radix/button'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui-radix/card'
import { Switch } from '@/components/ui-radix/switch'
import { Label } from '@/components/ui-radix/label'
import { useSettingsManagement } from '@/hooks/use-settings-management'
import { ComplianceSettings } from '@/types/user-settings'

// Pharmaceutical Compliance Frameworks
interface ComplianceFramework {
  id: string
  name: string
  description: string
  status: 'active' | 'inactive'
  lastUpdated: string
  regulations: string[]
  color: string
}

interface ComplianceSettingsProps {
  readonly className?: string;
}

export function ComplianceSettingsComponent({ className }: ComplianceSettingsProps): React.JSX.Element {
  const {
    getComplianceSettings,
    updateComplianceSettings,
    isUpdating,
    validationErrors,
    clearErrors
  } = useSettingsManagement()

  const [settings, setSettings] = useState<ComplianceSettings>(getComplianceSettings())
  const [originalSettings, setOriginalSettings] = useState<ComplianceSettings>(getComplianceSettings())
  const [hasChanges, setHasChanges] = useState(false)

  // Pharmaceutical Compliance Frameworks (based on database enum values)
  const [frameworks, setFrameworks] = useState<ComplianceFramework[]>([
    {
      id: 'fda_21_cfr_part_11',
      name: 'FDA 21 CFR Part 11',
      description: 'Electronic Records and Electronic Signatures',
      status: settings.compliance_framework.includes('fda_21_cfr_part_11') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['21 CFR Part 11', '21 CFR 210', '21 CFR 211'],
      color: 'bg-blue-500'
    },
    {
      id: 'eu_gmp',
      name: 'EU GMP',
      description: 'European Good Manufacturing Practice',
      status: settings.compliance_framework.includes('eu_gmp') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['EudraLex Volume 4', 'Directive 2001/83/EC'],
      color: 'bg-green-500'
    },
    {
      id: 'ich_q7',
      name: 'ICH Q7',
      description: 'Good Manufacturing Practice for Active Pharmaceutical Ingredients',
      status: settings.compliance_framework.includes('ich_q7') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['ICH Q7'],
      color: 'bg-purple-500'
    },
    {
      id: 'iso_13485',
      name: 'ISO 13485',
      description: 'Medical Devices Quality Management Systems',
      status: settings.compliance_framework.includes('iso_13485') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['ISO 13485:2016'],
      color: 'bg-orange-500'
    },
    {
      id: 'hipaa',
      name: 'HIPAA',
      description: 'Health Insurance Portability and Accountability Act',
      status: settings.compliance_framework.includes('hipaa') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['45 CFR Parts 160, 162, 164'],
      color: 'bg-red-500'
    },
    {
      id: 'gxp',
      name: 'GxP',
      description: 'Good Practice Guidelines for Pharmaceutical Industry',
      status: settings.compliance_framework.includes('gxp') ? 'active' : 'inactive',
      lastUpdated: new Date().toISOString().split('T')[0],
      regulations: ['GLP', 'GCP', 'GMP', 'GDP', 'GVP'],
      color: 'bg-indigo-500'
    }
  ])

  // Update settings when user profile changes
  useEffect(() => {
    const currentSettings = getComplianceSettings()
    setSettings(currentSettings)
    setOriginalSettings(currentSettings)
    setHasChanges(false)

    // Update frameworks status based on current settings
    setFrameworks(prev => prev.map(framework => ({
      ...framework,
      status: currentSettings.compliance_framework.includes(framework.id as any) ? 'active' : 'inactive'
    })))
  }, [getComplianceSettings])

  const handleSettingChange = (key: keyof ComplianceSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
    clearErrors()
  }

  const handleValidationRequirementChange = (key: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      validation_requirements: {
        ...prev.validation_requirements,
        [key]: value
      }
    }))
    setHasChanges(true)
    clearErrors()
  }

  const handleFrameworkToggle = (frameworkId: string, enabled: boolean) => {
    console.log('🔄 Framework Toggle:', { frameworkId, enabled })

    // Update frameworks display
    setFrameworks(prev => prev.map(framework =>
      framework.id === frameworkId
        ? { ...framework, status: enabled ? 'active' : 'inactive', lastUpdated: new Date().toISOString().split('T')[0] }
        : framework
    ))

    // Update settings compliance_framework array
    setSettings(prev => {
      const currentFrameworks = prev.compliance_framework || []
      const updatedFrameworks = enabled
        ? [...currentFrameworks.filter(f => f !== frameworkId), frameworkId as any]
        : currentFrameworks.filter(f => f !== frameworkId)

      return {
        ...prev,
        compliance_framework: updatedFrameworks
      }
    })

    setHasChanges(true)
    clearErrors()
  }

  const handleAddFramework = () => {
    console.log('🔄 Add Framework - Feature coming soon')
  }

  const handleSave = async () => {
    if (!hasChanges) {
      console.log('⏭️ No compliance changes to save')
      return
    }

    console.log('🔄 ComplianceSettings - Starting save process:', {
      originalSettings,
      newSettings: settings,
      hasChanges
    })

    const success = await updateComplianceSettings(settings)

    console.log('📊 ComplianceSettings - Update result:', { success })

    if (success) {
      setHasChanges(false)
      setOriginalSettings(settings)
      console.log('✅ ComplianceSettings - Save completed successfully')
    } else {
      console.error('❌ ComplianceSettings - Save failed')
    }
  }

  return (
    <div className={className || ''}>
      <div className="space-y-6">
        {/* Compliance Frameworks */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-foreground flex items-center justify-between">
              <span className="flex items-center">
                <Shield className="mr-2 h-6 w-6" />
                Compliance Frameworks
              </span>
              <Button onClick={handleAddFramework} variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Framework
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {frameworks.map((framework) => (
                <div
                  key={framework.id}
                  className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/30 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <Switch
                      checked={framework.status === 'active'}
                      onCheckedChange={(checked) => handleFrameworkToggle(framework.id, checked)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{framework.name}</h4>
                        <div className={`w-3 h-3 rounded-full ${framework.color}`} />
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {framework.description}
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <p className="text-xs text-muted-foreground">
                          Last updated: {framework.lastUpdated}
                        </p>
                        <div className="flex items-center space-x-1">
                          {framework.regulations.map((reg, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {reg}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      className={
                        framework.status === 'active'
                          ? 'bg-green-100 text-green-800 border-green-200'
                          : 'bg-gray-100 text-gray-600 border-gray-200'
                      }
                    >
                      {framework.status === 'active' ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Inactive
                        </>
                      )}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="hover:bg-muted hover:text-foreground"
                      onClick={() => console.log(`Configure ${framework.name}`)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Basic Compliance Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-foreground flex items-center">
              <Shield className="mr-2 h-6 w-6" />
              Compliance Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>GxP Training Completed</Label>
                <p className="text-sm text-muted-foreground">Mark if you have completed GxP training</p>
              </div>
              <Switch
                checked={settings.gxp_training_completed}
                onCheckedChange={(checked) => handleSettingChange('gxp_training_completed', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Electronic Signature Required</Label>
                <p className="text-sm text-muted-foreground">Require electronic signatures for critical actions</p>
              </div>
              <Switch
                checked={settings.electronic_signature_required}
                onCheckedChange={(checked) => handleSettingChange('electronic_signature_required', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Document Review Reminders</Label>
                <p className="text-sm text-muted-foreground">Receive reminders for pending document reviews</p>
              </div>
              <Switch
                checked={settings.document_review_reminders}
                onCheckedChange={(checked) => handleSettingChange('document_review_reminders', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Regulatory Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive notifications about regulatory changes</p>
              </div>
              <Switch
                checked={settings.regulatory_notifications}
                onCheckedChange={(checked) => handleSettingChange('regulatory_notifications', checked)}
              />
            </div>

            {/* Validation Requirements */}
            <div className="space-y-4">
              <h4 className="font-medium">Validation Requirements</h4>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Document Approval</Label>
                  <p className="text-sm text-muted-foreground">Require approval for document changes</p>
                </div>
                <Switch
                  checked={settings.validation_requirements.document_approval}
                  onCheckedChange={(checked) => handleValidationRequirementChange('document_approval', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Change Control</Label>
                  <p className="text-sm text-muted-foreground">Enable change control processes</p>
                </div>
                <Switch
                  checked={settings.validation_requirements.change_control}
                  onCheckedChange={(checked) => handleValidationRequirementChange('change_control', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Deviation Reporting</Label>
                  <p className="text-sm text-muted-foreground">Enable deviation reporting workflows</p>
                </div>
                <Switch
                  checked={settings.validation_requirements.deviation_reporting}
                  onCheckedChange={(checked) => handleValidationRequirementChange('deviation_reporting', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>CAPA Management</Label>
                  <p className="text-sm text-muted-foreground">Enable Corrective and Preventive Action management</p>
                </div>
                <Switch
                  checked={settings.validation_requirements.capa_management}
                  onCheckedChange={(checked) => handleValidationRequirementChange('capa_management', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isUpdating}
            className="min-w-[140px]"
          >
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
